import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  duration: number;
  delay: number;
  char: string;
}

export function ModernParticleBackground() {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    // Set initial dimensions
    setDimensions({
      width: window.innerWidth,
      height: window.innerHeight
    });

    // Generate particles with hemp-data theme
    const hempChars = ['0', '1', 'H', 'E', 'M', 'P', '△', '◇', '○'];
    const particleCount = 40; // Reduced from 5000 for better performance
    
    const newParticles = Array.from({ length: particleCount }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 120 - 20, // Start some above viewport
      size: Math.random() * 3 + 1,
      duration: Math.random() * 20 + 15, // 15-35s for slower movement
      delay: Math.random() * 10,
      char: hempChars[Math.floor(Math.random() * hempChars.length)]
    }));
    
    setParticles(newParticles);

    // Handle resize
    const handleResize = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden bg-black">
      {/* Subtle gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-950/20 via-black to-emerald-950/10" />
      
      {/* Additional radial gradients for organic feel */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-green-900/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/3 w-[600px] h-[600px] bg-emerald-900/5 rounded-full blur-3xl" />
      </div>

      {/* Floating particles with Matrix-style characters */}
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute font-mono text-green-500/30 select-none pointer-events-none"
          style={{
            left: `${particle.x}%`,
            fontSize: `${particle.size * 8 + 10}px`,
            textShadow: '0 0 10px currentColor',
            filter: 'blur(0.5px)',
          }}
          initial={{
            y: `${particle.y}vh`,
            opacity: 0,
            rotate: Math.random() * 360,
          }}
          animate={{
            y: [`${particle.y}vh`, `${particle.y - 150}vh`],
            opacity: [0, 0.3, 0.3, 0],
            rotate: [0, Math.random() * 180 - 90],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            ease: "linear",
            delay: particle.delay,
            times: [0, 0.1, 0.9, 1], // Fade in/out timing
          }}
        >
          {particle.char}
        </motion.div>
      ))}

      {/* Data stream lines - subtle vertical streaks */}
      {Array.from({ length: 8 }).map((_, i) => (
        <motion.div
          key={`line-${i}`}
          className="absolute w-px bg-gradient-to-b from-transparent via-green-500/10 to-transparent"
          style={{
            left: `${(i + 1) * 12.5}%`,
            height: '200px',
          }}
          animate={{
            y: ['-200px', `${dimensions.height + 200}px`],
          }}
          transition={{
            duration: Math.random() * 10 + 20,
            repeat: Infinity,
            ease: "linear",
            delay: Math.random() * 5,
          }}
        />
      ))}

      {/* Optional: Pulse effect for extra depth */}
      <motion.div
        className="absolute inset-0 bg-green-500/[0.02]"
        animate={{
          opacity: [0.02, 0.05, 0.02],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </div>
  );
}