#!/usr/bin/env python3
"""Test database connection and operations."""

import os

url = os.environ.get('SUPABASE_URL', '')
key = os.environ.get('SUPABASE_ANON_KEY', '')

print(f'SUPABASE_URL present: {bool(url)}')
print(f'SUPABASE_ANON_KEY present: {bool(key)}')

if url and key:
    print('✅ Credentials are set, attempting connection...')
    try:
        from lib.supabase_client import get_supabase_client
        client = get_supabase_client()
        # Try a simple query
        result = client.table('uses_products').select('id').limit(1).execute()
        print('✅ Database connection successful!')
        print(f'   Sample query returned {len(result.data if hasattr(result, "data") else [])} records')
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
else:
    print('❌ Missing database credentials')