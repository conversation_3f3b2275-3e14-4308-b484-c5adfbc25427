#!/usr/bin/env python3
"""Debug what the agent is receiving"""

import asyncio
import os
import sys
import logging

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ['DEEPSEEK_API_KEY'] = '***********************************'

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

from lib.supabase_client import get_supabase_client
from utils.simple_ai_wrapper import get_simple_ai_provider
from agents.research.unified_research_agent import UnifiedResearchAgent, ResearchConfig, ResearchFeatures


class DebugAgent(UnifiedResearchAgent):
    """Debug version of research agent"""
    
    async def _structure_product_data(self, raw_data):
        """Override to add debugging"""
        logger.info(f"=== _structure_product_data called ===")
        logger.info(f"Raw data: {raw_data}")
        
        if not self.config.use_ai_analysis or not self.ai_provider:
            logger.info("Not using AI analysis")
            return super()._structure_product_data(raw_data)
        
        # Build the prompt
        prompt = f"""
        Extract and structure hemp product information from this data:
        
        Source: {raw_data.get('source', 'Unknown')}
        Title: {raw_data.get('title', '')}
        Content: {raw_data.get('description', '')}
        
        Return a JSON object with:
        {{
            "name": "product name",
            "description": "detailed description",
            "plant_part": "seeds|fiber|oil|flower|hurds|roots|leaves|biomass",
            "industry": "main industry category",
            "sub_industry": "specific sub-industry",
            "benefits_advantages": ["list", "of", "benefits"],
            "sustainability_aspects": ["list", "of", "sustainability", "points"],
            "technical_specifications": {{"key": "value"}},
            "commercialization_stage": "R&D|Pilot|Niche|Growing|Established",
            "companies": ["list", "of", "companies"] if company extraction is enabled
        }}

        IMPORTANT: Return ONLY the raw JSON object or array. Do not include any explanatory text, markdown formatting, or code blocks. Just the JSON data itself.
        """
        
        logger.info(f"Prompt length: {len(prompt)}")
        
        try:
            logger.info("Calling AI provider...")
            response = await self.ai_provider.generate(prompt, response_format="json")
            
            logger.info(f"Response type: {type(response)}")
            logger.info(f"Response length: {len(response) if response else 'None'}")
            logger.info(f"Response preview: {repr(response[:200]) if response else 'None'}")
            
            # Handle tuple response
            if isinstance(response, tuple):
                logger.info("Response is tuple, extracting first element")
                response = response[0]
                logger.info(f"Extracted response: {repr(response[:200]) if response else 'None'}")
            
            # Try to parse
            import json
            parsed = json.loads(response)
            logger.info(f"✅ Parsed successfully!")
            return parsed
            
        except Exception as e:
            logger.error(f"AI structuring failed: {e}")
            logger.error(f"Response was: {repr(response) if 'response' in locals() else 'No response'}")
            return None


async def test_debug_agent():
    """Test with debug agent"""
    
    # Setup
    ai_provider = get_simple_ai_provider("deepseek")
    supabase = get_supabase_client()
    
    # Create debug agent
    config = ResearchConfig(
        enabled_features={ResearchFeatures.BASIC},
        use_ai_analysis=True,
        company_extraction=False
    )
    
    agent = DebugAgent(supabase, ai_provider=ai_provider, config=config)
    
    # Test with a single product
    test_data = {
        'title': 'Hemp Bioplastic Packaging',
        'description': 'New biodegradable packaging made from hemp fibers',
        'source': 'Test'
    }
    
    result = await agent._structure_product_data(test_data)
    
    if result:
        logger.info(f"✅ Success! Result: {result}")
    else:
        logger.info("❌ Failed to structure product")


if __name__ == "__main__":
    asyncio.run(test_debug_agent())