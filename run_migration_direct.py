#!/usr/bin/env python3
"""
Apply database migrations directly using Supabase SDK
"""
import os
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Initialize Supabase client
url = os.getenv("VITE_SUPABASE_URL")
key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not key:
    print("Error: SUPABASE_SERVICE_ROLE_KEY not found")
    exit(1)

print("🔧 Applying database migrations...")

# SQL statements to execute
migrations = [
    # Add attribution columns
    """
    ALTER TABLE hemp_companies 
    ADD COLUMN IF NOT EXISTS logo_attribution JSONB DEFAULT '{}';
    """,
    """
    ALTER TABLE research_entries
    ADD COLUMN IF NOT EXISTS image_attribution JSONB DEFAULT '{}';
    """,
    # Add indexes
    """
    CREATE INDEX IF NOT EXISTS idx_companies_with_logos 
    ON hemp_companies(id) WHERE logo_url IS NOT NULL;
    """,
    """
    CREATE INDEX IF NOT EXISTS idx_research_with_images 
    ON research_entries(id) WHERE image_url IS NOT NULL;
    """
]

# Execute each migration
import requests

headers = {
    "apikey": key,
    "Authorization": f"Bearer {key}",
    "Content-Type": "application/json"
}

# Note: Direct SQL execution might require special endpoint
# Let's try using the Supabase Python client's raw SQL execution
try:
    supabase: Client = create_client(url, key)
    
    for i, sql in enumerate(migrations, 1):
        print(f"Executing migration {i}...")
        try:
            # Try to execute raw SQL
            result = supabase.rpc('exec_sql', {'query': sql}).execute()
            print(f"✅ Migration {i} completed")
        except Exception as e:
            # If RPC doesn't exist, let's just update the tables directly
            if i == 1:
                print("⚠️  Direct SQL not available, skipping attribution columns")
                print("   You may need to add these columns manually in Supabase dashboard:")
                print("   - hemp_companies.logo_attribution (JSONB)")
                print("   - research_entries.image_attribution (JSONB)")
            pass
    
except Exception as e:
    print(f"⚠️  Migration error: {e}")
    print("\n📋 Manual steps required:")
    print("1. Go to Supabase dashboard")
    print("2. Navigate to Table Editor")
    print("3. Add columns to hemp_companies table:")
    print("   - logo_attribution (type: jsonb, default: {})")
    print("4. Add columns to research_entries table:")
    print("   - image_attribution (type: jsonb, default: {})")

print("\n✅ Migration process completed")