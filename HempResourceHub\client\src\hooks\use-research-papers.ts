import { useQuery } from "@tanstack/react-query";
import { ResearchPaper } from "@shared/schema";
import * as api from "../lib/supabase-api";

export function useResearchPapers() {
  return useQuery<ResearchPaper[]>({
    queryKey: ["research-papers"],
    queryFn: api.getAllResearchPapers,
    retry: false,
    // Return empty array on error to prevent UI crashes
    select: (data) => data || [],
    placeholderData: [],
  });
}

export function useResearchPaper(id: number | null) {
  return useQuery<ResearchPaper>({
    queryKey: ["research-papers", id],
    queryFn: () => api.getResearchPaper(id!),
    enabled: id !== null,
  });
}

export function useResearchPapersByPlantType(plantTypeId: number | null) {
  return useQuery<ResearchPaper[]>({
    queryKey: ["research-papers", "plant-type", plantTypeId],
    queryFn: () => api.getResearchPapersByPlantType(plantTypeId!),
    enabled: plantTypeId !== null,
  });
}

export function useResearchPapersByPlantPart(plantPartId: number | null) {
  return useQuery<ResearchPaper[]>({
    queryKey: ["research-papers", "plant-part", plantPartId],
    queryFn: () => api.getResearchPapersByPlantPart(plantPartId!),
    enabled: plantPartId !== null,
  });
}

export function useResearchPapersByIndustry(industryId: number | null) {
  return useQuery<ResearchPaper[]>({
    queryKey: ["research-papers", "industry", industryId],
    queryFn: () => api.getResearchPapersByIndustry(industryId!),
    enabled: industryId !== null,
  });
}

export function useSearchResearchPapers(query: string) {
  return useQuery<ResearchPaper[]>({
    queryKey: ["research-papers", "search", query],
    queryFn: () => api.searchResearchPapers(query),
    enabled: query.length > 0,
  });
}