import os
from supabase import create_client, Client
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)
else:
    # Try loading from current directory
    load_dotenv()

# Initialize Supabase client
url = os.getenv("VITE_SUPABASE_URL", "https://ktoqznqmlnxrtvubewyz.supabase.co")
# Use service role key for admin operations (bypasses RLS)
key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
anon_key = os.getenv("VITE_SUPABASE_ANON_KEY")

if not key:
    print("Warning: SUPABASE_SERVICE_ROLE_KEY not found, trying anon key...")
    key = anon_key

if not key:
    print("Error: Neither SUPABASE_SERVICE_ROLE_KEY nor VITE_SUPABASE_ANON_KEY found")
    exit(1)

supabase: Client = create_client(url, key)

def add_hemp_product(
    name: str,
    description: str,
    plant_part_ids: list[int],
    applications: list[str],
    benefits: list[str],
    companies: list[str] = None,
    market_stage: str = "Research & Development",
    sustainability_score: int = None
):
    """Add a new hemp product to the database"""
    
    # Check if product already exists
    existing = supabase.table('uses_products').select("id").ilike('name', f'%{name}%').execute()
    
    if existing.data:
        print(f"Product '{name}' already exists, skipping...")
        return None
    
    # Prepare product data matching actual database schema
    product_data = {
        "name": name,
        "description": description,
        "plant_part_id": plant_part_ids[0] if plant_part_ids else 1,  # Take first plant part
        "industry_sub_category_id": 1,  # Default
        "benefits_advantages": benefits,
        "commercialization_stage": market_stage,
        "sustainability_aspects": [f"Sustainability score: {sustainability_score}/100"] if sustainability_score else [],
        "keywords": applications,  # Use applications as keywords
        "image_url": "/images/unknown-hemp-image.png",
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat()
    }
    
    # Insert product
    result = supabase.table('uses_products').insert(product_data).execute()
    
    if result.data:
        product_id = result.data[0]['id']
        print(f"✅ Added product: {name} (ID: {product_id})")
        
        # Add companies if provided
        if companies:
            for company_name in companies:
                # Check if company exists
                company_result = supabase.table('hemp_companies').select("id").eq('name', company_name).execute()
                
                if not company_result.data:
                    # Create company
                    new_company = supabase.table('hemp_companies').insert({
                        "name": company_name,
                        "company_type": "manufacturer",
                        "verified": False,
                        "description": f"Manufactures {name}"
                    }).execute()
                    
                    if new_company.data:
                        company_id = new_company.data[0]['id']
                        print(f"  Created company: {company_name}")
                else:
                    company_id = company_result.data[0]['id']
                
                # Create relationship
                supabase.table('hemp_company_products').insert({
                    "company_id": company_id,
                    "product_id": product_id,
                    "relationship_type": "primary"
                }).execute()
                
        return product_id
    else:
        print(f"❌ Failed to add product: {name}")
        return None


# Example usage - Add some products
if __name__ == "__main__":
    print("Adding sample hemp products...")
    
    # Plant part IDs reference:
    # 1 = Cannabinoids, 2 = Hemp Bast (Fiber), 3 = Hemp Flowers
    # 4 = Hemp Leaves, 5 = Hemp Stalks, 6 = Hemp Seeds
    # 7 = Hemp Roots, 8 = Hemp Hurds, 9 = Whole Plant
    
    products = [
        {
            "name": "Hemp-Based Circuit Boards",
            "description": "Biodegradable PCB substrates made from hemp fiber composites, offering eco-friendly alternative to traditional fiberglass boards",
            "plant_part_ids": [2, 8],  # Fiber and hurds
            "applications": ["electronics", "circuit boards", "IoT devices"],
            "benefits": ["biodegradable", "non-toxic", "heat resistant", "lightweight"],
            "companies": ["GreenTech Electronics", "HempTech Industries"],
            "market_stage": "Growing",
            "sustainability_score": 92
        },
        {
            "name": "Hemp Graphene Supercapacitors",
            "description": "High-performance energy storage devices using hemp-derived graphene nanosheets for rapid charging applications",
            "plant_part_ids": [2],  # Fiber
            "applications": ["energy storage", "electric vehicles", "power banks"],
            "benefits": ["fast charging", "long lifespan", "cost-effective", "sustainable"],
            "companies": ["NanoHemp Technologies"],
            "market_stage": "Research",
            "sustainability_score": 95
        },
        {
            "name": "Hemp Acoustic Panels",
            "description": "Sound-absorbing panels made from compressed hemp fibers for architectural acoustics and noise reduction",
            "plant_part_ids": [2, 8],  # Fiber and hurds
            "applications": ["recording studios", "offices", "home theaters", "restaurants"],
            "benefits": ["excellent sound absorption", "fire resistant", "mold resistant", "VOC-free"],
            "companies": ["AcoustiHemp", "Sound Solutions Hemp"],
            "market_stage": "Established",
            "sustainability_score": 88
        }
    ]
    
    for product in products:
        add_hemp_product(**product)
    
    print("\n✅ Product addition complete!")
    
    # Show total product count
    count_result = supabase.table('uses_products').select("id", count='exact').execute()
    print(f"\nTotal products in database: {count_result.count}")