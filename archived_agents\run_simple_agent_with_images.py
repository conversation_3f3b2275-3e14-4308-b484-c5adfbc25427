#!/usr/bin/env python3
"""
Simple standalone version of the research agent with automatic image generation
No complex dependencies - just the essentials
"""

import asyncio
import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv
from supabase import create_client, Client
import aiohttp

# Load .env from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
load_dotenv(env_path)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleResearchAgentWithImages:
    """Simplified research agent that discovers products and generates images"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        self.supabase: Client = create_client(supabase_url, supabase_key)
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        
    async def discover_products_from_hardcoded_data(self) -> List[Dict]:
        """Use hardcoded product data as a fallback"""
        products = [
            {
                "name": "Hemp Fiber Automotive Door Panels",
                "description": "Lightweight, durable door panels made from compressed hemp fibers for automotive interiors",
                "plant_part": "Fiber",
                "industry": "Automotive",
                "sub_industry": "Interior Components",
                "companies": ["BMW", "Mercedes-Benz"],
                "commercialization_stage": "Commercial",
                "benefits_advantages": ["30% lighter than traditional materials", "Biodegradable", "Better sound insulation"]
            },
            {
                "name": "Hemp Bioplastic Packaging Film",
                "description": "Biodegradable packaging film made from hemp cellulose for food packaging applications",
                "plant_part": "Fiber",
                "industry": "Packaging",
                "sub_industry": "Flexible Packaging",
                "companies": ["Hemp Plastic Company"],
                "commercialization_stage": "Pilot",
                "benefits_advantages": ["100% biodegradable", "Food safe", "UV resistant"]
            },
            {
                "name": "Hempcrete Building Blocks",
                "description": "Carbon-negative building blocks made from hemp hurd and lime for sustainable construction",
                "plant_part": "Hurd/Shiv",
                "industry": "Construction",
                "sub_industry": "Building Materials",
                "companies": ["IsoHemp", "HempBLOCK USA"],
                "commercialization_stage": "Commercial",
                "benefits_advantages": ["Carbon negative", "Fire resistant", "Excellent insulation"]
            },
            {
                "name": "Hemp Seed Protein Powder",
                "description": "High-quality plant-based protein powder containing all essential amino acids",
                "plant_part": "Seeds",
                "industry": "Food & Beverage",
                "sub_industry": "Nutritional Supplements",
                "companies": ["Manitoba Harvest", "Nutiva"],
                "commercialization_stage": "Commercial",
                "benefits_advantages": ["Complete protein", "Easy to digest", "Rich in omega-3"]
            },
            {
                "name": "Hemp-Based Supercapacitors",
                "description": "High-performance energy storage devices using hemp-derived carbon nanosheets",
                "plant_part": "Fiber",
                "industry": "Energy",
                "sub_industry": "Energy Storage",
                "companies": [""],
                "commercialization_stage": "R&D",
                "benefits_advantages": ["Outperforms graphene", "Cost-effective", "Sustainable production"]
            }
        ]
        
        logger.info(f"Using {len(products)} hardcoded products")
        return products
    
    def _create_image_prompt(self, product_name: str, product_description: str) -> str:
        """Create an optimized prompt for image generation"""
        base_prompt = f"Industrial hemp product: {product_name}"
        
        desc_lower = product_description.lower()
        
        # Add context-specific details
        if any(word in desc_lower for word in ['fiber', 'textile', 'fabric']):
            base_prompt += ", showing textile fibers and fabric texture"
        elif any(word in desc_lower for word in ['plastic', 'bioplastic', 'packaging']):
            base_prompt += ", modern industrial bioplastic material"
        elif any(word in desc_lower for word in ['construction', 'hempcrete', 'block']):
            base_prompt += ", construction material blocks showing texture"
        elif any(word in desc_lower for word in ['food', 'seed', 'protein']):
            base_prompt += ", nutritious food product with hemp seeds"
        elif any(word in desc_lower for word in ['energy', 'capacitor', 'battery']):
            base_prompt += ", high-tech energy storage device"
        
        base_prompt += ", professional product photography, clean white background, high quality, commercial style, realistic"
        
        return base_prompt
    
    async def save_products_with_images(self, products: List[Dict]) -> int:
        """Save products and queue image generation"""
        saved_count = 0
        queue_ids = []
        
        for product in products:
            try:
                # Check if product exists
                existing = self.supabase.table('uses_products').select('id').eq('name', product['name']).execute()
                
                if not existing.data:
                    # Get plant part ID
                    plant_part = self.supabase.table('plant_parts').select('id').eq('name', product['plant_part']).execute()
                    plant_part_id = plant_part.data[0]['id'] if plant_part.data else 1
                    
                    # Get industry subcategory ID
                    industry = self.supabase.table('industries').select('id').eq('name', product['industry']).execute()
                    if industry.data:
                        industry_id = industry.data[0]['id']
                        subcategory = self.supabase.table('industry_sub_categories').select('id').eq('industry_id', industry_id).execute()
                        industry_sub_category_id = subcategory.data[0]['id'] if subcategory.data else 1
                    else:
                        industry_sub_category_id = 1
                    
                    # Create product record
                    db_product = {
                        'name': product['name'],
                        'description': product['description'],
                        'plant_part_id': plant_part_id,
                        'industry_sub_category_id': industry_sub_category_id,
                        'benefits_advantages': product.get('benefits_advantages', []),
                        'commercialization_stage': product.get('commercialization_stage', 'R&D'),
                        'image_url': '/api/placeholder/400/300',  # Start with placeholder
                        'created_at': datetime.now().isoformat()
                    }
                    
                    # Insert product
                    result = self.supabase.table('uses_products').insert(db_product).execute()
                    
                    if result.data:
                        saved_count += 1
                        product_id = result.data[0]['id']
                        logger.info(f"✅ Saved product: {product['name']} (ID: {product_id})")
                        
                        # Queue image generation
                        prompt = self._create_image_prompt(product['name'], product['description'])
                        
                        queue_entry = {
                            'reference_type': 'product',
                            'reference_id': product_id,
                            'prompt': prompt,
                            'provider': 'imagen_3',
                            'status': 'pending',
                            'metadata': {
                                'type': 'product',
                                'product_id': product_id,
                                'product_name': product['name'],
                                'auto_generated': True
                            },
                            'created_at': datetime.now().isoformat()
                        }
                        
                        queue_result = self.supabase.table('image_generation_queue').insert(queue_entry).execute()
                        if queue_result.data:
                            queue_id = queue_result.data[0]['id']
                            queue_ids.append(queue_id)
                            logger.info(f"🖼️ Queued image generation (Queue ID: {queue_id})")
                else:
                    logger.info(f"⚠️ Product already exists: {product['name']}")
                    
            except Exception as e:
                logger.error(f"❌ Error saving product {product['name']}: {e}")
        
        if queue_ids:
            logger.info(f"\n🎯 Queued {len(queue_ids)} images for generation")
            # Here you would trigger the edge function, but for now just log
            logger.info("💡 Images will be generated when you run the edge function")
        
        return saved_count

async def main():
    """Run the simple agent"""
    
    # Get credentials
    supabase_url = os.getenv('VITE_SUPABASE_URL')
    supabase_key = os.getenv('VITE_SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials")
        return
    
    print("🚀 Simple Research Agent with Automatic Image Generation")
    print("=" * 60)
    
    # Create agent
    agent = SimpleResearchAgentWithImages(supabase_url, supabase_key)
    
    # Discover products (using hardcoded data)
    print("\n📚 Discovering products...")
    products = await agent.discover_products_from_hardcoded_data()
    
    print(f"✅ Found {len(products)} products to process")
    
    # Save products with image generation
    print("\n💾 Saving products and queueing images...")
    saved = await agent.save_products_with_images(products)
    
    print(f"\n✅ Saved {saved} new products with images queued!")
    
    if saved > 0:
        print("\n📝 Next Steps:")
        print("1. Check the image_generation_queue table in Supabase")
        print("2. Run your edge function to process the queue")
        print("3. Or use the existing image generation scripts")

if __name__ == "__main__":
    asyncio.run(main())