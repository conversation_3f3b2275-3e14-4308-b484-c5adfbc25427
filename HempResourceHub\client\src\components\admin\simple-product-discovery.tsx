import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Loader2, 
  CheckCircle, 
  Save,
  Plus,
  Trash2
} from 'lucide-react';
import { supabase } from '@/lib/supabase-client';
import { useToast } from '@/hooks/use-toast';

interface ManualProduct {
  name: string;
  description: string;
  plantParts: string[];
  industry: string;
  applications: string;
  benefits: string;
  companies: string;
  sustainabilityScore?: number;
  marketStage: 'research' | 'growing' | 'established' | 'speculative';
}

export function SimpleProductDiscovery() {
  const [isSaving, setIsSaving] = useState(false);
  const [product, setProduct] = useState<ManualProduct>({
    name: '',
    description: '',
    plantParts: [],
    industry: '',
    applications: '',
    benefits: '',
    companies: '',
    marketStage: 'research'
  });
  const { toast } = useToast();

  // Plant part mapping
  const plantPartOptions = [
    { id: 1, name: 'Cannabinoids' },
    { id: 2, name: 'Hemp Bast (Fiber)' },
    { id: 3, name: 'Hemp Flowers' },
    { id: 4, name: 'Hemp Leaves' },
    { id: 5, name: 'Hemp Stalks' },
    { id: 6, name: 'Hemp Seeds' },
    { id: 7, name: 'Hemp Roots' },
    { id: 8, name: 'Hemp Hurds' },
    { id: 9, name: 'Whole Plant' }
  ];

  const handleSave = async () => {
    if (!product.name || !product.description) {
      toast({
        title: "Missing Information",
        description: "Please provide at least a name and description",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);

    try {
      // Map plant part names to IDs
      const plantPartIds = product.plantParts.map(partName => {
        const part = plantPartOptions.find(p => p.name === partName);
        return part?.id || 1;
      });

      // Prepare product data
      const productData = {
        name: product.name,
        description: product.description,
        plant_part_ids: plantPartIds,
        industry_sub_category_id: 1, // Default for now
        applications: product.applications.split(',').map(a => a.trim()).filter(Boolean),
        benefits: product.benefits.split(',').map(b => b.trim()).filter(Boolean),
        sustainability_score: product.sustainabilityScore,
        market_stage: product.marketStage,
        image_url: '/images/unknown-hemp-image.png'
      };

      // Save to database
      const { data, error } = await supabase
        .from('uses_products')
        .insert(productData)
        .select('id')
        .single();

      if (error) throw error;

      // Save companies if provided
      if (data && product.companies) {
        const companyNames = product.companies.split(',').map(c => c.trim()).filter(Boolean);
        
        for (const companyName of companyNames) {
          // Check if company exists
          let { data: company } = await supabase
            .from('hemp_companies')
            .select('id')
            .eq('name', companyName)
            .single();

          if (!company) {
            // Create new company
            const { data: newCompany } = await supabase
              .from('hemp_companies')
              .insert({
                name: companyName,
                company_type: 'manufacturer',
                verified: false
              })
              .select('id')
              .single();
            
            company = newCompany;
          }

          if (company) {
            // Create relationship
            await supabase
              .from('hemp_company_products')
              .insert({
                company_id: company.id,
                product_id: data.id,
                relationship_type: 'primary'
              });
          }
        }
      }

      toast({
        title: "Product Saved!",
        description: `Successfully added "${product.name}" to the database`,
      });

      // Reset form
      setProduct({
        name: '',
        description: '',
        plantParts: [],
        industry: '',
        applications: '',
        benefits: '',
        companies: '',
        marketStage: 'research'
      });

    } catch (error) {
      console.error('Error saving product:', error);
      toast({
        title: "Save Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const togglePlantPart = (partName: string) => {
    setProduct(prev => ({
      ...prev,
      plantParts: prev.plantParts.includes(partName)
        ? prev.plantParts.filter(p => p !== partName)
        : [...prev.plantParts, partName]
    }));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Hemp Product Manually
          </CardTitle>
          <CardDescription>
            Manually add a new hemp product to the database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Product Name *</Label>
              <Input
                value={product.name}
                onChange={(e) => setProduct(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., HempWood Flooring"
              />
            </div>
            <div>
              <Label>Market Stage</Label>
              <select
                className="w-full px-3 py-2 border rounded-md"
                value={product.marketStage}
                onChange={(e) => setProduct(prev => ({ 
                  ...prev, 
                  marketStage: e.target.value as any 
                }))}
              >
                <option value="research">Research</option>
                <option value="growing">Growing</option>
                <option value="established">Established</option>
                <option value="speculative">Speculative</option>
              </select>
            </div>
          </div>

          <div>
            <Label>Description *</Label>
            <Textarea
              value={product.description}
              onChange={(e) => setProduct(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Detailed description of the product..."
              rows={3}
            />
          </div>

          <div>
            <Label>Plant Parts Used</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {plantPartOptions.map(part => (
                <Badge
                  key={part.id}
                  variant={product.plantParts.includes(part.name) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => togglePlantPart(part.name)}
                >
                  {part.name}
                </Badge>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Applications (comma-separated)</Label>
              <Input
                value={product.applications}
                onChange={(e) => setProduct(prev => ({ ...prev, applications: e.target.value }))}
                placeholder="e.g., flooring, furniture, panels"
              />
            </div>
            <div>
              <Label>Benefits (comma-separated)</Label>
              <Input
                value={product.benefits}
                onChange={(e) => setProduct(prev => ({ ...prev, benefits: e.target.value }))}
                placeholder="e.g., sustainable, durable, cost-effective"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Companies (comma-separated)</Label>
              <Input
                value={product.companies}
                onChange={(e) => setProduct(prev => ({ ...prev, companies: e.target.value }))}
                placeholder="e.g., Fibonacci LLC, Hemp Inc"
              />
            </div>
            <div>
              <Label>Sustainability Score (0-100)</Label>
              <Input
                type="number"
                min="0"
                max="100"
                value={product.sustainabilityScore || ''}
                onChange={(e) => setProduct(prev => ({ 
                  ...prev, 
                  sustainabilityScore: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                placeholder="Optional"
              />
            </div>
          </div>

          <Button 
            onClick={handleSave} 
            disabled={isSaving || !product.name || !product.description}
            className="w-full"
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Product
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      <Alert>
        <AlertDescription>
          <strong>Tip:</strong> You can also use the AI Product Discovery feature (coming soon) to automatically find and add products from web sources.
        </AlertDescription>
      </Alert>
    </div>
  );
}