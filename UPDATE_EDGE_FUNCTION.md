# Update Edge Function Instructions

## Step 1: Add to PROVIDERS object (around line 43)

Find this section in your Edge Function:
```typescript
const PROVIDERS: { [key: string]: ProviderConfig } = {
  stable_diffusion: {
    name: 'stable_diffusion',
    apiKeyName: 'STABILITY_API_KEY',
    generateImage: generateStableDiffusion
  },
  // ... other providers
```

Add these two new providers:
```typescript
  replicate: {
    name: 'replicate',
    apiKeyName: 'REPLICATE_API_KEY',
    generateImage: generateReplicate
  },
  together_ai: {
    name: 'together_ai', 
    apiKeyName: 'TOGETHER_API_KEY',
    generateImage: generateTogetherAI
  },
```

## Step 2: Add these functions at the end of the file

Copy and paste both functions from the `edge-function-additions.ts` file.

## Step 3: Deploy

Click the "Deploy" button in the Supabase Edge Function editor.

## Alternative: Let me generate images with placeholder for now

If you want to see progress while updating the Edge Function, I can generate placeholder images for products that need better ones.