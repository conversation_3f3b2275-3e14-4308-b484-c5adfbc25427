import { useEffect, useRef, useState } from 'react';
import { HempCompany } from '@/hooks/use-companies';

// Try direct import first
let Globe: any = null;
try {
  // @ts-ignore
  Globe = require('globe.gl').default;
} catch (e) {
  console.warn('Failed to load Globe.gl:', e);
}

interface HempCompaniesGlobeProps {
  companies: HempCompany[];
  onCompanyClick?: (company: HempCompany) => void;
}

export function HempCompaniesGlobeSimple({ companies, onCompanyClick }: HempCompaniesGlobeProps) {
  const globeEl = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!globeEl.current) return;

    // If Globe wasn't loaded via require, try dynamic import
    if (!Globe) {
      import('globe.gl')
        .then((module) => {
          Globe = module.default;
          initializeGlobe();
        })
        .catch((err) => {
          console.error('Failed to dynamically import Globe.gl:', err);
          setError('Unable to load 3D globe visualization library');
          setIsLoading(false);
        });
    } else {
      initializeGlobe();
    }

    function initializeGlobe() {
      if (!Globe || !globeEl.current) {
        setError('Globe library not available');
        setIsLoading(false);
        return;
      }

      try {
        const globe = Globe(globeEl.current)
          .globeImageUrl('//unpkg.com/three-globe/example/img/earth-dark.jpg')
          .bumpImageUrl('//unpkg.com/three-globe/example/img/earth-topology.png')
          .backgroundColor('rgba(0,0,0,0)')
          .width(globeEl.current.offsetWidth)
          .height(600);

        // Add companies with location data
        const companiesWithLocation = companies.filter(c => c.latitude && c.longitude);
        
        if (companiesWithLocation.length > 0) {
          globe
            .pointsData(companiesWithLocation.map(company => ({
              ...company,
              lat: company.latitude,
              lng: company.longitude,
            })))
            .pointAltitude(0.01)
            .pointRadius(0.5)
            .pointColor((d: any) => {
              const company = d as HempCompany;
              switch (company.company_type) {
                case 'manufacturer': return '#3B82F6';
                case 'distributor': return '#10B981';
                case 'retailer': return '#8B5CF6';
                case 'brand': return '#F97316';
                default: return '#FFD700';
              }
            })
            .pointLabel((d: any) => {
              const company = d as HempCompany;
              return `<div style="text-align: center; color: white;">${company.name}</div>`;
            })
            .onPointClick((point: any) => {
              if (onCompanyClick) {
                onCompanyClick(point as HempCompany);
              }
            });
        }

        // Set controls after a delay
        setTimeout(() => {
          if (globe.controls) {
            const controls = globe.controls();
            controls.autoRotate = true;
            controls.autoRotateSpeed = 0.5;
          }
        }, 500);

        setIsLoading(false);
      } catch (err) {
        console.error('Error initializing globe:', err);
        setError('Failed to initialize globe visualization');
        setIsLoading(false);
      }
    }

    return () => {
      // Cleanup if needed
    };
  }, [companies, onCompanyClick]);

  if (error) {
    return (
      <div className="relative w-full h-[600px] flex items-center justify-center bg-black/40 rounded-lg">
        <div className="text-center">
          <p className="text-red-400 mb-2">{error}</p>
          <p className="text-gray-400 text-sm">
            Try refreshing the page or use the list view instead
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center h-[600px] bg-black/40 rounded-lg z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading 3D globe visualization...</p>
          </div>
        </div>
      )}
      
      <div ref={globeEl} style={{ width: '100%', height: '600px' }} />
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-black/80 backdrop-blur-sm rounded-lg p-4 border border-gray-700">
        <h4 className="text-sm font-semibold text-gray-100 mb-2">Company Types</h4>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-xs text-gray-300">Manufacturer</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-xs text-gray-300">Distributor</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-purple-500"></div>
            <span className="text-xs text-gray-300">Retailer</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
            <span className="text-xs text-gray-300">Brand</span>
          </div>
        </div>
      </div>

      {/* Controls hint */}
      <div className="absolute top-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-700">
        <p className="text-xs text-gray-400">
          Click and drag to rotate • Scroll to zoom • Click markers for details
        </p>
      </div>
    </div>
  );
}