#!/bin/bash

# Script to regenerate all placeholder images with new AI providers

echo "=== Hemp Product Image Regeneration Script ==="
echo ""
echo "This script will:"
echo "1. Queue all placeholder products for AI image generation"
echo "2. Use Replicate and Together AI providers"
echo "3. Generate high-quality images to replace placeholders"
echo ""

ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs"
URL="https://ktoqznqmlnxrtvubewyz.supabase.co/functions/v1/hemp-image-generator"

echo "Step 1: Testing providers are working..."
echo ""

# Test Replicate
echo "Testing Replicate..."
TEST_REPLICATE=$(curl -s -X POST $URL \
  -H "Authorization: Bearer $ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 1, "forceProvider": "replicate"}')

if echo "$TEST_REPLICATE" | grep -q "not implemented"; then
    echo "❌ Replicate provider not yet implemented in Edge Function"
    echo "Please update the Edge Function first!"
    exit 1
else
    echo "✅ Replicate provider is ready"
fi

# Test Together AI
echo "Testing Together AI..."
TEST_TOGETHER=$(curl -s -X POST $URL \
  -H "Authorization: Bearer $ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 1, "forceProvider": "together_ai"}')

if echo "$TEST_TOGETHER" | grep -q "not implemented"; then
    echo "❌ Together AI provider not yet implemented in Edge Function"
    echo "Please update the Edge Function first!"
    exit 1
else
    echo "✅ Together AI provider is ready"
fi

echo ""
echo "Step 2: Processing placeholder images..."
echo ""

# Process in batches
echo "Processing batch 1 (4 images with Replicate)..."
curl -X POST $URL \
  -H "Authorization: Bearer $ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 4, "forceProvider": "replicate"}' \
  | python3 -m json.tool

sleep 2

echo ""
echo "Processing batch 2 (4 images with Together AI)..."
curl -X POST $URL \
  -H "Authorization: Bearer $ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 4, "forceProvider": "together_ai"}' \
  | python3 -m json.tool

echo ""
echo "✅ Image regeneration complete!"
echo ""
echo "Note: If any images failed, you can run this script again."
echo "The system will automatically retry failed items."