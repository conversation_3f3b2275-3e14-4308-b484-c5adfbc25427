// Test script for AI API endpoints
const baseUrl = 'http://localhost:3000/api/ai';

async function testAPI() {
  console.log('Testing AI API endpoints...\n');

  // Test 1: Get agents
  console.log('1. Testing GET /agents');
  try {
    const response = await fetch(`${baseUrl}/agents`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✓ Success! Found agents:', data.length);
      data.forEach(agent => {
        console.log(`  - ${agent.name} (${agent.id})`);
      });
    } else {
      console.log('✗ Error:', data);
    }
  } catch (error) {
    console.log('✗ Failed to connect:', error.message);
  }

  console.log('\n2. Testing POST /conversations');
  try {
    const response = await fetch(`${baseUrl}/conversations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ agentId: 'product-discovery' })
    });
    const data = await response.json();
    
    if (response.ok) {
      console.log('✓ Success! Created conversation:', data.conversationId);
      
      // Test sending a message
      console.log('\n3. Testing POST /conversations/:id/messages');
      const msgResponse = await fetch(`${baseUrl}/conversations/${data.conversationId}/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          message: 'What are the top 3 industrial uses of hemp?',
          stream: false 
        })
      });
      
      const msgData = await msgResponse.json();
      if (msgResponse.ok) {
        console.log('✓ Success! Got response:');
        console.log(msgData.message.substring(0, 200) + '...');
      } else {
        console.log('✗ Error sending message:', msgData);
      }
    } else {
      console.log('✗ Error creating conversation:', data);
    }
  } catch (error) {
    console.log('✗ Failed:', error.message);
  }
}

// Run the test
testAPI().then(() => {
  console.log('\nTest complete!');
}).catch(console.error);