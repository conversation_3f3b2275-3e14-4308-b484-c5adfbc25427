"""Simple AI wrapper that works with the research agent"""

from typing import Optional
import logging

logger = logging.getLogger(__name__)


class SimpleAIWrapper:
    """Wrapper that makes MultiProviderAI work with the research agent"""
    
    def __init__(self, multi_provider):
        self.provider = multi_provider
        
    async def generate(self, prompt: str, **kwargs):
        """Generate text, returning just the text (not tuple)"""
        try:
            result = await self.provider.generate(prompt, **kwargs)
            
            # If it returns a tuple, extract just the text
            if isinstance(result, tuple):
                result = result[0]
                
            # If JSON format was requested and result contains markdown, extract it
            if kwargs.get('response_format') == 'json' and result and isinstance(result, str):
                logger.debug(f"SimpleAIWrapper: Checking for JSON extraction. Result preview: {result[:100]}...")
                
                if '```' in result:
                    import re
                    json_match = re.search(r'```(?:json)?\s*\n?([\s\S]*?)\n?```', result)
                    if json_match:
                        result = json_match.group(1).strip()
                        logger.debug(f"SimpleAIWrapper: Extracted JSON from markdown: {result[:100]}...")
                    else:
                        logger.debug("SimpleAIWrapper: No JSON found in markdown blocks")
                else:
                    # Try to find JSON without code blocks
                    json_start = result.find('{')
                    if json_start == -1:
                        json_start = result.find('[')
                    if json_start != -1:
                        # Extract everything from the first { or [ to the end
                        result = result[json_start:]
                        logger.debug(f"SimpleAIWrapper: Extracted JSON without markdown: {result[:100]}...")
                    
            return result
                
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            raise


def get_simple_ai_provider(provider_name: Optional[str] = None):
    """Get a simple AI provider that works with the research agent"""
    from .ai_providers import get_ai_provider
    
    multi_provider = get_ai_provider(provider_name)
    if multi_provider:
        return SimpleAIWrapper(multi_provider)
    return None