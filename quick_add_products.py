#!/usr/bin/env python3
"""
Quick Product Addition Script - No Dependencies Required
Run this to manually add some hemp products to your database
"""

import os
import json
import urllib.request
import urllib.parse
from datetime import datetime

# Supabase configuration from environment
SUPABASE_URL = "https://ktoqznqmlnxrtvubewyz.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTE3NzYsImV4cCI6MjA2NDA2Nzc3Nn0.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs"

def add_product(product_data):
    """Add a product using Supabase REST API"""
    url = f"{SUPABASE_URL}/rest/v1/uses_products"
    
    headers = {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }
    
    data = json.dumps(product_data).encode('utf-8')
    
    try:
        req = urllib.request.Request(url, data=data, headers=headers, method='POST')
        response = urllib.request.urlopen(req)
        return response.status == 201
    except Exception as e:
        print(f"Error adding product: {e}")
        return False

# Sample products to add
new_products = [
    {
        "name": "Hemp Biocomposite Car Parts",
        "description": "Lightweight automotive components made from hemp fiber and bio-resin, reducing vehicle weight by 30% while maintaining strength.",
        "plant_part_id": 2,  # Fiber
        "industry_sub_category_id": 51,  # Automotive
        "sustainability_score": 95,
        "market_stage": "growing",
        "benefits": ["30% lighter than traditional parts", "Carbon negative production", "Recyclable"],
        "potential_applications": ["Door panels", "Dashboard components", "Trunk linings"],
        "key_companies": ["BMW", "Mercedes-Benz", "Lotus Cars"]
    },
    {
        "name": "Hemp-Based Quantum Dots",
        "description": "Nanoscale semiconductor particles derived from hemp for use in advanced displays and solar cells.",
        "plant_part_id": 5,  # Leaves
        "industry_sub_category_id": 49,  # Electronics
        "sustainability_score": 92,
        "market_stage": "research",
        "benefits": ["Non-toxic alternative to heavy metals", "Cost-effective production", "Biodegradable"],
        "potential_applications": ["QLED displays", "Solar cells", "Medical imaging"],
        "key_companies": ["Samsung", "LG Display"]
    },
    {
        "name": "Hemp Mycelium Packaging",
        "description": "100% biodegradable packaging grown from hemp hurds and mushroom mycelium, replacing polystyrene foam.",
        "plant_part_id": 4,  # Hurds
        "industry_sub_category_id": 52,  # Packaging
        "sustainability_score": 98,
        "market_stage": "growing",
        "benefits": ["Compostable in 30 days", "Fire resistant", "Customizable shapes"],
        "potential_applications": ["Electronics packaging", "Food containers", "Shipping materials"],
        "key_companies": ["Dell", "IKEA", "Ecovative"]
    },
    {
        "name": "Hemp Protein Meat Alternatives",
        "description": "Plant-based meat substitutes using hemp protein isolate with complete amino acid profile.",
        "plant_part_id": 1,  # Seeds
        "industry_sub_category_id": 2,  # Food Products
        "sustainability_score": 93,
        "market_stage": "established",
        "benefits": ["Complete protein source", "Allergen-free", "90% less water than beef"],
        "potential_applications": ["Burgers", "Sausages", "Nuggets"],
        "key_companies": ["Beyond Meat", "Impossible Foods", "Good Hemp"]
    },
    {
        "name": "Hemp Carbon Fiber",
        "description": "High-performance carbon fiber alternative made from hemp stalks for aerospace and sports equipment.",
        "plant_part_id": 2,  # Fiber
        "industry_sub_category_id": 51,  # Materials/Composites
        "sustainability_score": 94,
        "market_stage": "growing",
        "benefits": ["40% cheaper than carbon fiber", "Similar tensile strength", "Renewable source"],
        "potential_applications": ["Aircraft components", "Racing bicycles", "Wind turbine blades"],
        "key_companies": ["Boeing", "Airbus", "Trek Bicycles"]
    }
]

def main():
    print("🌿 Quick Hemp Product Addition Tool")
    print("=" * 50)
    
    added_count = 0
    
    for product in new_products:
        print(f"\nAdding: {product['name']}")
        if add_product(product):
            added_count += 1
            print("✅ Success!")
        else:
            print("❌ Failed")
    
    print(f"\n✨ Added {added_count} new products to the database!")
    print("\nThese products will need images generated. Run:")
    print("python image_generation/hemp_image_generator.py")

if __name__ == "__main__":
    main()