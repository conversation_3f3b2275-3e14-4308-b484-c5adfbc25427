#!/usr/bin/env python3
"""Direct test of DeepSeek without dotenv"""

import asyncio
import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set the API key directly
os.environ['DEEPSEEK_API_KEY'] = '***********************************'

from utils.ai_providers import DeepSeekProvider


async def test_deepseek():
    """Test DeepSeek directly"""
    
    print("Creating DeepSeek provider...")
    deepseek = DeepSeekProvider()
    
    print("Testing generation...")
    try:
        result = await deepseek.generate("List 2 hemp construction materials with brief descriptions")
        print(f"\n✅ Success! Response:\n{result}")
        return True
    except Exception as e:
        print(f"\n❌ Failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    asyncio.run(test_deepseek())