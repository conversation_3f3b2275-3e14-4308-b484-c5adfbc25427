import Anthropic from '@anthropic-ai/sdk';
import { v4 as uuidv4 } from 'uuid';
import { retryWithBackoff } from '../utils/retry';
import { db } from '../../db';
import { aiUsage, aiModelPricing } from '../../shared/schema-ai-usage';
import { eq } from 'drizzle-orm';

// Model pricing as of Jan 2025 (per million tokens)
export const MODEL_PRICING = {
  'claude-3-haiku-20240307': {
    input: 0.25,   // $0.25 per million input tokens
    output: 1.25,  // $1.25 per million output tokens
    contextWindow: 200000,
  },
  'claude-3-sonnet-20240229': {
    input: 3.00,   // $3 per million input tokens
    output: 15.00, // $15 per million output tokens
    contextWindow: 200000,
  },
  'claude-3-opus-20240229': {
    input: 15.00,  // $15 per million input tokens
    output: 75.00, // $75 per million output tokens
    contextWindow: 200000,
  },
};

// Response caching configuration
export interface CacheConfig {
  enabled: boolean;
  ttlSeconds: number;
  maxSize: number;
}

export interface OptimizedClaudeAgent {
  id: string;
  name: string;
  systemPrompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
  cacheConfig?: CacheConfig;
  fallbackModel?: string; // Use cheaper model for fallback
  costLimit?: number; // Max cost per request in USD
}

export interface Message {
  role: 'user' | 'assistant';
  content: string;
  cached?: boolean;
}

export interface ConversationContext {
  agentId: string;
  conversationId: string;
  messages: Message[];
  totalCost: number;
  totalTokens: {
    input: number;
    output: number;
  };
}

export class OptimizedClaudeService {
  private anthropic: Anthropic;
  private agents: Map<string, OptimizedClaudeAgent> = new Map();
  private conversations: Map<string, ConversationContext> = new Map();
  private responseCache: Map<string, { response: string; timestamp: number }> = new Map();
  private costTrackingEnabled: boolean = true;

  constructor(apiKey: string) {
    this.anthropic = new Anthropic({
      apiKey: apiKey,
    });

    // Initialize optimized default agents
    this.initializeOptimizedAgents();
    
    // Start cache cleanup interval
    this.startCacheCleanup();
  }

  private initializeOptimizedAgents() {
    // Product Discovery Agent - Optimized
    this.registerAgent({
      id: 'product-discovery',
      name: 'Product Discovery Assistant',
      systemPrompt: `You are an expert industrial hemp product discovery assistant. Be concise and structured.
Key tasks:
- Search for industrial hemp products
- Extract: name, description, plant parts, industry, companies
- Assess market stage and sustainability
- Provide sources

Respond with structured JSON-like data for easy parsing. Be concise to save tokens.`,
      model: 'claude-3-haiku-20240307',
      temperature: 0.5, // Lower for more consistent responses
      maxTokens: 1500, // Reduced from 2000
      cacheConfig: {
        enabled: true,
        ttlSeconds: 3600, // 1 hour cache
        maxSize: 100
      },
      costLimit: 0.002 // $0.002 per request max
    });

    // Code Generator - Optimized
    this.registerAgent({
      id: 'code-generator',
      name: 'Code Generator',
      systemPrompt: `Expert TypeScript/React developer. Generate clean, concise code.
Rules:
- TypeScript with types
- React hooks
- Tailwind CSS
- Error handling
- Brief comments only
- No verbose explanations`,
      model: 'claude-3-haiku-20240307',
      temperature: 0.2, // Lower for more deterministic code
      maxTokens: 2000, // Reduced from 3000
      cacheConfig: {
        enabled: true,
        ttlSeconds: 7200, // 2 hour cache for code
        maxSize: 50
      },
      fallbackModel: 'claude-3-haiku-20240307', // Same model but could use different
      costLimit: 0.003
    });

    // Quick Answer Agent - New ultra-efficient agent
    this.registerAgent({
      id: 'quick-answer',
      name: 'Quick Answer Assistant',
      systemPrompt: `Provide ultra-concise, direct answers. Maximum 2-3 sentences. No fluff.`,
      model: 'claude-3-haiku-20240307',
      temperature: 0.3,
      maxTokens: 150, // Very small for quick answers
      cacheConfig: {
        enabled: true,
        ttlSeconds: 3600,
        maxSize: 200
      },
      costLimit: 0.0001
    });
  }

  registerAgent(agent: OptimizedClaudeAgent) {
    this.agents.set(agent.id, agent);
  }

  // Calculate cost for a request
  private calculateCost(model: string, inputTokens: number, outputTokens: number): number {
    const pricing = MODEL_PRICING[model];
    if (!pricing) return 0;

    const inputCost = (inputTokens / 1_000_000) * pricing.input;
    const outputCost = (outputTokens / 1_000_000) * pricing.output;
    
    return inputCost + outputCost;
  }

  // Check if response is cached
  private getCachedResponse(cacheKey: string, agent: OptimizedClaudeAgent): string | null {
    if (!agent.cacheConfig?.enabled) return null;

    const cached = this.responseCache.get(cacheKey);
    if (!cached) return null;

    const age = Date.now() - cached.timestamp;
    if (age > agent.cacheConfig.ttlSeconds * 1000) {
      this.responseCache.delete(cacheKey);
      return null;
    }

    return cached.response;
  }

  // Generate cache key
  private generateCacheKey(agentId: string, messages: Message[]): string {
    const content = messages.map(m => `${m.role}:${m.content}`).join('|');
    return `${agentId}:${this.hashString(content)}`;
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString(36);
  }

  // Optimized message sending with cost tracking
  async sendMessage(
    conversationId: string,
    message: string,
    stream: boolean = false
  ): Promise<any> {
    const startTime = Date.now();
    const context = this.conversations.get(conversationId);
    if (!context) {
      throw new Error(`Conversation ${conversationId} not found`);
    }

    const agent = this.agents.get(context.agentId);
    if (!agent) {
      throw new Error(`Agent ${context.agentId} not found`);
    }

    // Add user message to context
    context.messages.push({ role: 'user', content: message });

    // Check cache first
    const cacheKey = this.generateCacheKey(agent.id, context.messages);
    const cachedResponse = this.getCachedResponse(cacheKey, agent);
    if (cachedResponse) {
      context.messages.push({ role: 'assistant', content: cachedResponse, cached: true });
      console.log(`[Cache Hit] Agent: ${agent.id}, saved ~${agent.maxTokens} tokens`);
      return { 
        content: [{ type: 'text', text: cachedResponse }],
        usage: { input_tokens: 0, output_tokens: 0, cache_read_tokens: agent.maxTokens }
      };
    }

    // Check cost limit
    if (agent.costLimit) {
      const estimatedInputTokens = this.estimateTokens(context.messages);
      const estimatedCost = this.calculateCost(
        agent.model, 
        estimatedInputTokens, 
        agent.maxTokens
      );
      
      if (estimatedCost > agent.costLimit) {
        console.warn(`Cost limit exceeded for ${agent.id}: $${estimatedCost.toFixed(4)}`);
        
        // Use fallback model if available
        if (agent.fallbackModel && agent.fallbackModel !== agent.model) {
          console.log(`Using fallback model: ${agent.fallbackModel}`);
          agent.model = agent.fallbackModel;
        } else {
          // Reduce max tokens to fit budget
          const reducedTokens = Math.floor(agent.maxTokens * (agent.costLimit / estimatedCost));
          console.log(`Reducing max tokens from ${agent.maxTokens} to ${reducedTokens}`);
          agent.maxTokens = reducedTokens;
        }
      }
    }

    try {
      const response = await retryWithBackoff(async () =>
        this.anthropic.messages.create({
          model: agent.model,
          max_tokens: agent.maxTokens,
          temperature: agent.temperature,
          system: agent.systemPrompt,
          messages: context.messages,
          stream: stream,
        })
      );

      if (!stream) {
        const assistantMessage = response.content[0].type === 'text' 
          ? response.content[0].text 
          : '';
        
        // Update context
        context.messages.push({ role: 'assistant', content: assistantMessage });
        
        // Update token counts
        const usage = response.usage;
        context.totalTokens.input += usage.input_tokens;
        context.totalTokens.output += usage.output_tokens;
        
        // Calculate and track cost
        const cost = this.calculateCost(agent.model, usage.input_tokens, usage.output_tokens);
        context.totalCost += cost;
        
        // Cache the response
        if (agent.cacheConfig?.enabled) {
          this.responseCache.set(cacheKey, {
            response: assistantMessage,
            timestamp: Date.now()
          });
          
          // Enforce cache size limit
          if (this.responseCache.size > agent.cacheConfig.maxSize) {
            const oldestKey = Array.from(this.responseCache.entries())
              .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
            this.responseCache.delete(oldestKey);
          }
        }
        
        // Track usage in database
        if (this.costTrackingEnabled) {
          await this.trackUsage({
            conversationId,
            agentId: agent.id,
            model: agent.model,
            inputTokens: usage.input_tokens,
            outputTokens: usage.output_tokens,
            totalTokens: usage.input_tokens + usage.output_tokens,
            inputCost: (usage.input_tokens / 1_000_000) * MODEL_PRICING[agent.model].input,
            outputCost: (usage.output_tokens / 1_000_000) * MODEL_PRICING[agent.model].output,
            totalCost: cost,
            responseTimeMs: Date.now() - startTime,
            requestType: 'conversation',
          });
        }
        
        console.log(`[AI Request] Agent: ${agent.id}, Cost: $${cost.toFixed(6)}, Tokens: ${usage.input_tokens}/${usage.output_tokens}`);
        
        return response;
      }
      
      return response;
    } catch (error) {
      // Track error
      if (this.costTrackingEnabled) {
        await this.trackUsage({
          conversationId,
          agentId: agent.id,
          model: agent.model,
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          inputCost: 0,
          outputCost: 0,
          totalCost: 0,
          responseTimeMs: Date.now() - startTime,
          requestType: 'conversation',
          error: error.message,
        });
      }
      
      throw error;
    }
  }

  // Estimate token count (rough approximation)
  private estimateTokens(messages: Message[]): number {
    const text = messages.map(m => m.content).join(' ');
    // Rough estimate: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  // Track usage in database
  private async trackUsage(data: any): Promise<void> {
    try {
      await db.insert(aiUsage).values({
        conversationId: data.conversationId,
        agentId: data.agentId,
        model: data.model,
        inputTokens: data.inputTokens,
        outputTokens: data.outputTokens,
        totalTokens: data.totalTokens,
        inputCost: data.inputCost.toString(),
        outputCost: data.outputCost.toString(),
        totalCost: data.totalCost.toString(),
        responseTimeMs: data.responseTimeMs,
        requestType: data.requestType,
        error: data.error,
      });
    } catch (error) {
      console.error('Failed to track AI usage:', error);
    }
  }

  // Get usage statistics
  async getUsageStats(agentId?: string, days: number = 30): Promise<any> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    let query = db.select()
      .from(aiUsage)
      .where(aiUsage.createdAt >= startDate);
    
    if (agentId) {
      query = query.where(eq(aiUsage.agentId, agentId));
    }
    
    const results = await query;
    
    // Calculate totals
    const stats = {
      totalRequests: results.length,
      totalInputTokens: results.reduce((sum, r) => sum + r.inputTokens, 0),
      totalOutputTokens: results.reduce((sum, r) => sum + r.outputTokens, 0),
      totalCost: results.reduce((sum, r) => sum + parseFloat(r.totalCost), 0),
      averageResponseTime: results.reduce((sum, r) => sum + (r.responseTimeMs || 0), 0) / results.length,
      errorRate: results.filter(r => r.error).length / results.length,
      byAgent: {},
      byModel: {},
    };
    
    // Group by agent
    results.forEach(r => {
      if (!stats.byAgent[r.agentId]) {
        stats.byAgent[r.agentId] = {
          requests: 0,
          cost: 0,
          tokens: 0,
        };
      }
      stats.byAgent[r.agentId].requests++;
      stats.byAgent[r.agentId].cost += parseFloat(r.totalCost);
      stats.byAgent[r.agentId].tokens += r.totalTokens;
    });
    
    return stats;
  }

  // Cleanup old cache entries
  private startCacheCleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, value] of this.responseCache.entries()) {
        // Remove entries older than 24 hours regardless of TTL
        if (now - value.timestamp > 24 * 60 * 60 * 1000) {
          this.responseCache.delete(key);
        }
      }
    }, 60 * 60 * 1000); // Run every hour
  }

  // Existing methods remain the same...
  getAgent(agentId: string): OptimizedClaudeAgent | undefined {
    return this.agents.get(agentId);
  }

  getAllAgents(): OptimizedClaudeAgent[] {
    return Array.from(this.agents.values());
  }

  async createConversation(agentId: string): Promise<string> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    const conversationId = uuidv4();
    this.conversations.set(conversationId, {
      agentId,
      conversationId,
      messages: [],
      totalCost: 0,
      totalTokens: { input: 0, output: 0 },
    });

    return conversationId;
  }

  getConversation(conversationId: string): ConversationContext | undefined {
    return this.conversations.get(conversationId);
  }

  clearConversation(conversationId: string) {
    const context = this.conversations.get(conversationId);
    if (context) {
      context.messages = [];
    }
  }

  deleteConversation(conversationId: string) {
    this.conversations.delete(conversationId);
  }

  // Optimized query method with caching
  async query(agentId: string, prompt: string): Promise<string> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    // Check cache first for one-off queries
    const cacheKey = this.generateCacheKey(agentId, [{ role: 'user', content: prompt }]);
    const cachedResponse = this.getCachedResponse(cacheKey, agent);
    if (cachedResponse) {
      console.log(`[Cache Hit] One-off query for agent: ${agentId}`);
      return cachedResponse;
    }

    const response = await this.anthropic.messages.create({
      model: agent.model,
      max_tokens: agent.maxTokens,
      temperature: agent.temperature,
      system: agent.systemPrompt,
      messages: [{ role: 'user', content: prompt }],
    });

    const result = response.content[0].type === 'text' ? response.content[0].text : '';
    
    // Cache the response
    if (agent.cacheConfig?.enabled) {
      this.responseCache.set(cacheKey, {
        response: result,
        timestamp: Date.now()
      });
    }

    return result;
  }
}

// Singleton instance
let optimizedClaudeServiceInstance: OptimizedClaudeService | null = null;

export function getOptimizedClaudeService(): OptimizedClaudeService {
  if (!optimizedClaudeServiceInstance) {
    const apiKey = process.env.ANTHROPIC_API_KEY || process.env.CLAUDE_API_KEY;
    if (!apiKey) {
      throw new Error('ANTHROPIC_API_KEY or CLAUDE_API_KEY environment variable is required');
    }
    optimizedClaudeServiceInstance = new OptimizedClaudeService(apiKey);
  }
  return optimizedClaudeServiceInstance;
}