#!/usr/bin/env python3
"""
Hemp Company Hunter Agent
Specialized agent for discovering and cataloging hemp companies globally
"""

import asyncio
import json
import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

import aiohttp
from bs4 import BeautifulSoup
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core.enhanced_base_agent import EnhancedBaseAgent, rate_limited, track_performance

logger = logging.getLogger(__name__)


@dataclass
class CompanyInfo:
    """Structure for company information"""
    name: str
    type: str  # manufacturer, distributor, retailer, brand, etc.
    description: Optional[str] = None
    website: Optional[str] = None
    location: Optional[str] = None
    country: Optional[str] = None
    products: List[str] = None
    certifications: List[str] = None
    contact_info: Dict[str, str] = None
    logo_url: Optional[str] = None
    social_media: Dict[str, str] = None
    verified: bool = False
    data_source: str = "Company Hunter Agent"
    

class HempCompanyHunterAgent(EnhancedBaseAgent):
    """Agent specialized in discovering hemp companies and their products"""
    
    def __init__(self, supabase_client, ai_provider=None):
        super().__init__(supabase_client, ai_provider)
        self.session = None
        self.company_sources = self._initialize_sources()
        self._company_cache = {}
        
    def _initialize_sources(self) -> List[Dict]:
        """Initialize company discovery sources"""
        return [
            # Industry Associations
            {
                'name': 'European Industrial Hemp Association',
                'url': 'https://eiha.org/members/',
                'type': 'association',
                'pattern': 'member_list'
            },
            {
                'name': 'Vote Hemp',
                'url': 'https://votehemp.com/resources/hemp-businesses/',
                'type': 'association',
                'pattern': 'business_directory'
            },
            {
                'name': 'Hemp Industries Association',
                'url': 'https://thehia.org/directory/',
                'type': 'association',
                'pattern': 'directory'
            },
            # Trade Shows
            {
                'name': 'NoCo Hemp Expo',
                'url': 'https://nocohempexpo.com/exhibitors/',
                'type': 'trade_show',
                'pattern': 'exhibitor_list'
            },
            # Business Directories
            {
                'name': 'Hemp Business Directory',
                'url': 'https://www.hempbusinessdirectory.com/',
                'type': 'directory',
                'pattern': 'company_listings'
            },
            # Marketplaces
            {
                'name': 'Alibaba Hemp Suppliers',
                'url': 'https://www.alibaba.com/corporations/hemp.html',
                'type': 'marketplace',
                'pattern': 'supplier_list'
            }
        ]
        
    async def discover_companies(self, search_criteria: Dict = None, max_results: int = 100) -> List[CompanyInfo]:
        """Main entry point for company discovery"""
        all_companies = []
        
        # Strategy 1: Scrape known directories
        directory_companies = await self._scrape_directories(max_results)
        all_companies.extend(directory_companies)
        
        # Strategy 2: Search engine discovery
        if len(all_companies) < max_results:
            search_companies = await self._search_engine_discovery(search_criteria, max_results - len(all_companies))
            all_companies.extend(search_companies)
            
        # Strategy 3: Social media discovery
        if len(all_companies) < max_results:
            social_companies = await self._social_media_discovery(max_results - len(all_companies))
            all_companies.extend(social_companies)
            
        # Process and enrich company data
        enriched_companies = []
        for company in all_companies[:max_results]:
            enriched = await self._enrich_company_data(company)
            if enriched:
                enriched_companies.append(enriched)
                
        # Save to database
        saved_companies = []
        for company in enriched_companies:
            saved = await self._save_company(company)
            if saved:
                saved_companies.append(saved)
                
        return saved_companies
        
    @rate_limited(calls_per_minute=30)
    async def _scrape_directories(self, max_results: int) -> List[CompanyInfo]:
        """Scrape company directories"""
        companies = []
        
        async with aiohttp.ClientSession() as session:
            for source in self.company_sources:
                if len(companies) >= max_results:
                    break
                    
                try:
                    companies_from_source = await self._scrape_source(session, source)
                    companies.extend(companies_from_source)
                except Exception as e:
                    logger.error(f"Failed to scrape {source['name']}: {e}")
                    
        return companies[:max_results]
        
    async def _scrape_source(self, session: aiohttp.ClientSession, source: Dict) -> List[CompanyInfo]:
        """Scrape a specific source for companies"""
        companies = []
        
        try:
            async with session.get(source['url'], timeout=10) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Pattern-based extraction
                    if source['pattern'] == 'member_list':
                        companies.extend(self._extract_member_list(soup, source))
                    elif source['pattern'] == 'business_directory':
                        companies.extend(self._extract_business_directory(soup, source))
                    elif source['pattern'] == 'exhibitor_list':
                        companies.extend(self._extract_exhibitor_list(soup, source))
                    elif source['pattern'] == 'directory':
                        companies.extend(self._extract_directory(soup, source))
                        
        except Exception as e:
            logger.error(f"Error scraping {source['url']}: {e}")
            
        return companies
        
    def _extract_member_list(self, soup: BeautifulSoup, source: Dict) -> List[CompanyInfo]:
        """Extract companies from member list format"""
        companies = []
        
        # Look for member cards/listings
        for member in soup.find_all(['div', 'article'], class_=['member', 'company', 'listing']):
            name_elem = member.find(['h2', 'h3', 'h4', 'a'])
            if name_elem:
                company = CompanyInfo(
                    name=name_elem.text.strip(),
                    type='member',
                    data_source=source['name']
                )
                
                # Extract website
                link = member.find('a', href=True)
                if link and 'http' in link['href']:
                    company.website = link['href']
                    
                # Extract description
                desc = member.find(['p', 'div'], class_=['description', 'summary'])
                if desc:
                    company.description = desc.text.strip()
                    
                companies.append(company)
                
        return companies
        
    def _extract_business_directory(self, soup: BeautifulSoup, source: Dict) -> List[CompanyInfo]:
        """Extract companies from business directory format"""
        companies = []
        
        # Look for business listings
        for listing in soup.find_all(['div', 'li'], class_=['business', 'company', 'result']):
            name = listing.find(['h2', 'h3', 'a', 'span'], class_=['name', 'title'])
            if name:
                company = CompanyInfo(
                    name=name.text.strip(),
                    type='business',
                    data_source=source['name']
                )
                
                # Extract contact info
                contact = listing.find(['div', 'span'], class_=['contact', 'phone', 'email'])
                if contact:
                    company.contact_info = {'general': contact.text.strip()}
                    
                # Extract location
                location = listing.find(['span', 'div'], class_=['location', 'address'])
                if location:
                    company.location = location.text.strip()
                    
                companies.append(company)
                
        return companies
        
    def _extract_exhibitor_list(self, soup: BeautifulSoup, source: Dict) -> List[CompanyInfo]:
        """Extract companies from trade show exhibitor list"""
        companies = []
        
        # Look for exhibitor entries
        for exhibitor in soup.find_all(['div', 'tr'], class_=['exhibitor', 'vendor']):
            name = exhibitor.find(['td', 'h3', 'a'])
            if name:
                company = CompanyInfo(
                    name=name.text.strip(),
                    type='exhibitor',
                    data_source=source['name']
                )
                
                # Extract booth number or category
                booth = exhibitor.find(['td', 'span'], class_=['booth', 'category'])
                if booth:
                    company.description = f"Booth/Category: {booth.text.strip()}"
                    
                companies.append(company)
                
        return companies
        
    def _extract_directory(self, soup: BeautifulSoup, source: Dict) -> List[CompanyInfo]:
        """Generic directory extraction"""
        companies = []
        
        # Look for any listing-like structure
        for item in soup.find_all(['div', 'article', 'li'], class_=re.compile('(company|business|listing|item)')):
            name = item.find(['h1', 'h2', 'h3', 'h4', 'a'])
            if name and name.text.strip():
                company = CompanyInfo(
                    name=name.text.strip(),
                    type='directory_listing',
                    data_source=source['name']
                )
                companies.append(company)
                
        return companies
        
    async def _search_engine_discovery(self, criteria: Dict, max_results: int) -> List[CompanyInfo]:
        """Discover companies through search engines"""
        companies = []
        
        search_queries = [
            "industrial hemp manufacturers",
            "hemp product companies",
            "hemp suppliers directory",
            "hemp brands list",
            "hemp processors",
            "hemp textile companies",
            "hemp food manufacturers",
            "hemp construction companies",
            "CBD hemp companies"
        ]
        
        # If specific criteria provided, add targeted searches
        if criteria:
            if criteria.get('location'):
                search_queries.extend([
                    f"hemp companies {criteria['location']}",
                    f"hemp manufacturers {criteria['location']}"
                ])
            if criteria.get('product_type'):
                search_queries.append(f"hemp {criteria['product_type']} companies")
                
        # Note: Actual search engine integration would require API keys
        # This is a placeholder for the search logic
        logger.info(f"Would search for: {search_queries[:3]}")
        
        return companies
        
    async def _social_media_discovery(self, max_results: int) -> List[CompanyInfo]:
        """Discover companies through social media"""
        companies = []
        
        # Placeholder for social media discovery
        # Would integrate with LinkedIn, Twitter, Instagram APIs
        social_platforms = [
            "LinkedIn hemp companies",
            "Instagram #hempbusiness",
            "Twitter hemp manufacturers"
        ]
        
        logger.info(f"Would search social media: {social_platforms}")
        
        return companies
        
    async def _enrich_company_data(self, company: CompanyInfo) -> Optional[CompanyInfo]:
        """Enrich company data with additional information"""
        try:
            # If we have a website, scrape it for more info
            if company.website:
                enriched_data = await self._scrape_company_website(company.website)
                if enriched_data:
                    # Merge enriched data
                    if enriched_data.get('products'):
                        company.products = enriched_data['products']
                    if enriched_data.get('contact'):
                        company.contact_info = enriched_data['contact']
                    if enriched_data.get('social_media'):
                        company.social_media = enriched_data['social_media']
                        
            # Use AI to extract company type if available
            if self.ai_provider and company.description:
                company_type = await self._classify_company_type(company)
                if company_type:
                    company.type = company_type
                    
            return company
            
        except Exception as e:
            logger.error(f"Failed to enrich company {company.name}: {e}")
            return company
            
    @rate_limited(calls_per_minute=20)
    async def _scrape_company_website(self, url: str) -> Optional[Dict]:
        """Scrape company website for additional information"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        data = {}
                        
                        # Extract products
                        products = []
                        for product in soup.find_all(['div', 'li'], class_=re.compile('product')):
                            product_name = product.find(['h2', 'h3', 'h4', 'a'])
                            if product_name:
                                products.append(product_name.text.strip())
                        if products:
                            data['products'] = products[:20]  # Limit to 20 products
                            
                        # Extract contact info
                        contact = {}
                        email = soup.find('a', href=re.compile(r'^mailto:'))
                        if email:
                            contact['email'] = email['href'].replace('mailto:', '')
                        phone = soup.find(text=re.compile(r'[\+\d\s\-\(\)]{10,}'))
                        if phone:
                            contact['phone'] = phone.strip()
                        if contact:
                            data['contact'] = contact
                            
                        # Extract social media
                        social = {}
                        for platform in ['facebook', 'twitter', 'linkedin', 'instagram']:
                            link = soup.find('a', href=re.compile(f'{platform}\\.com'))
                            if link:
                                social[platform] = link['href']
                        if social:
                            data['social_media'] = social
                            
                        return data
                        
        except Exception as e:
            logger.error(f"Failed to scrape {url}: {e}")
            return None
            
    async def _classify_company_type(self, company: CompanyInfo) -> Optional[str]:
        """Use AI to classify company type"""
        if not self.ai_provider:
            return None
            
        prompt = f"""
        Classify this hemp company into one of these categories:
        - manufacturer (makes products)
        - distributor (distributes products)
        - retailer (sells to consumers)
        - brand (owns brands)
        - supplier (supplies raw materials)
        - processor (processes hemp)
        - farm (grows hemp)
        - research (R&D company)
        - technology (tech/equipment)
        
        Company: {company.name}
        Description: {company.description}
        
        Return just the category name.
        """
        
        try:
            response = await self.ai_provider.generate(prompt)
            company_type = response.strip().lower()
            if company_type in ['manufacturer', 'distributor', 'retailer', 'brand', 
                               'supplier', 'processor', 'farm', 'research', 'technology']:
                return company_type
        except Exception as e:
            logger.error(f"Failed to classify company type: {e}")
            
        return 'manufacturer'  # Default
        
    async def _save_company(self, company: CompanyInfo) -> Optional[Dict]:
        """Save company to database"""
        try:
            # Check if company already exists
            existing = self.supabase.table('hemp_companies').select('*').eq('name', company.name).execute()
            
            if existing.data:
                # Update existing company
                company_id = existing.data[0]['id']
                update_data = {
                    'description': company.description or existing.data[0].get('description'),
                    'website': company.website or existing.data[0].get('website'),
                    'type': company.type,
                    'location': company.location,
                    'country': company.country,
                    'verified': company.verified,
                    'updated_at': datetime.now().isoformat()
                }
                
                result = self.supabase.table('hemp_companies').update(update_data).eq('id', company_id).execute()
                saved_company = result.data[0] if result.data else None
            else:
                # Create new company
                company_data = {
                    'name': company.name,
                    'description': company.description,
                    'website': company.website,
                    'type': company.type,
                    'location': company.location,
                    'country': company.country,
                    'logo_url': company.logo_url,
                    'verified': company.verified,
                    'data_source': company.data_source,
                    'created_by': 'company_hunter_agent'
                }
                
                result = self.supabase.table('hemp_companies').insert(company_data).execute()
                saved_company = result.data[0] if result.data else None
                
            # Save products if we have them
            if saved_company and company.products:
                await self._save_company_products(saved_company['id'], company.products)
                
            return saved_company
            
        except Exception as e:
            logger.error(f"Failed to save company {company.name}: {e}")
            return None
            
    async def _save_company_products(self, company_id: int, products: List[str]):
        """Save company products as placeholder entries"""
        for product_name in products:
            try:
                # Check if product exists
                existing = self.supabase.table('uses_products').select('id').eq('name', product_name).execute()
                
                if not existing.data:
                    # Create placeholder product
                    product_data = {
                        'name': product_name,
                        'description': f"Product from company catalog",
                        'data_source': 'Company Hunter Agent',
                        'created_by': 'company_hunter_agent'
                    }
                    
                    result = self.supabase.table('uses_products').insert(product_data).execute()
                    if result.data:
                        product_id = result.data[0]['id']
                    else:
                        continue
                else:
                    product_id = existing.data[0]['id']
                    
                # Create company-product relationship
                self.supabase.table('hemp_company_products').insert({
                    'company_id': company_id,
                    'product_id': product_id,
                    'relationship_type': 'catalog',
                    'is_primary': True
                }).execute()
                
            except Exception as e:
                logger.error(f"Failed to save product relationship: {e}")
                
    async def find_purchase_links(self, company: Dict, products: List[Dict]) -> Dict[str, List[str]]:
        """Find purchase links for company products"""
        purchase_links = {}
        
        if company.get('website'):
            # Check for e-commerce on company site
            shop_urls = [
                f"{company['website']}/shop",
                f"{company['website']}/products",
                f"{company['website']}/store",
                f"{company['website']}/buy"
            ]
            
            for product in products:
                product_links = []
                
                # Direct website links
                for shop_url in shop_urls:
                    product_links.append({
                        'type': 'direct',
                        'url': shop_url,
                        'platform': 'Company Website'
                    })
                    
                # Marketplace searches
                marketplaces = [
                    {
                        'name': 'Amazon',
                        'search_url': f"https://www.amazon.com/s?k={product['name']}+hemp"
                    },
                    {
                        'name': 'Alibaba',
                        'search_url': f"https://www.alibaba.com/trade/search?SearchText={product['name']}"
                    }
                ]
                
                for marketplace in marketplaces:
                    product_links.append({
                        'type': 'marketplace',
                        'url': marketplace['search_url'],
                        'platform': marketplace['name']
                    })
                    
                purchase_links[product['name']] = product_links
                
        return purchase_links
        
    async def __aenter__(self):
        """Async context manager entry"""
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()


# Convenience function
def create_company_hunter(supabase_client, ai_provider=None) -> HempCompanyHunterAgent:
    """Create a company hunter agent"""
    return HempCompanyHunterAgent(supabase_client, ai_provider)