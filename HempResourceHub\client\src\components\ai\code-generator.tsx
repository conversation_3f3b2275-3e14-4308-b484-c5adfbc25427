import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Code2, Copy, Check, Loader2 } from 'lucide-react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { getClaudeAPI } from '@/lib/claude-api';

export function CodeGenerator() {
  const [prompt, setPrompt] = useState('');
  const [generatedCode, setGeneratedCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerate = async () => {
    if (!prompt.trim()) return;

    setIsLoading(true);
    setError(null);
    
    try {
      const claudeAPI = getClaudeAPI();
      const response = await claudeAPI.generateCode(prompt, 'TypeScript/React');
      
      // Extract code blocks from the response
      const codeMatch = response.match(/```[\w]*\n([\s\S]*?)```/);
      setGeneratedCode(codeMatch ? codeMatch[1].trim() : response);
    } catch (error) {
      console.error('Code generation error:', error);
      setError('Failed to generate code. Please try again.');
      setGeneratedCode('');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(generatedCode);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code2 className="h-5 w-5" />
          AI Code Generator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">
            Describe what you want to build:
          </label>
          <Textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="e.g., Create a React component that shows hemp product statistics with a bar chart"
            rows={4}
            className="w-full"
          />
        </div>

        <Button 
          onClick={handleGenerate} 
          disabled={isLoading || !prompt.trim()}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Code2 className="mr-2 h-4 w-4" />
              Generate Code
            </>
          )}
        </Button>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {generatedCode && (
          <div className="relative">
            <div className="absolute top-2 right-2 z-10">
              <Button
                size="sm"
                variant="secondary"
                onClick={handleCopy}
              >
                {isCopied ? (
                  <>
                    <Check className="h-4 w-4 mr-1" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-1" />
                    Copy
                  </>
                )}
              </Button>
            </div>
            
            <SyntaxHighlighter
              language="typescript"
              style={vscDarkPlus}
              customStyle={{
                borderRadius: '0.5rem',
                fontSize: '0.875rem',
                maxHeight: '500px',
                overflow: 'auto'
              }}
            >
              {generatedCode}
            </SyntaxHighlighter>
          </div>
        )}
      </CardContent>
    </Card>
  );
}