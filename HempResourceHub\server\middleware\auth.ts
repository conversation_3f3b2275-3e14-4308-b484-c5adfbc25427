import { Request, Response, NextFunction } from 'express';

// Simple API key authentication middleware
export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'] || req.query.apiKey;
  
  // For development, allow requests without auth
  if (process.env.NODE_ENV === 'development' && !process.env.REQUIRE_AUTH) {
    return next();
  }

  // Check if API key is provided and valid
  const validApiKey = process.env.AI_API_KEY || process.env.ADMIN_API_KEY;
  
  if (!apiKey) {
    return res.status(401).json({ error: 'API key required' });
  }

  if (apiKey !== validApiKey) {
    return res.status(403).json({ error: 'Invalid API key' });
  }

  next();
};

// Optional: Bearer token authentication for future use
export const bearerAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Bearer token required' });
  }

  const token = authHeader.substring(7);
  
  // TODO: Verify JWT token or session token
  // For now, just check if it matches a simple token
  if (token !== process.env.BEARER_TOKEN) {
    return res.status(403).json({ error: 'Invalid token' });
  }

  next();
};