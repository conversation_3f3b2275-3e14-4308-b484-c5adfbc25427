// Test API health and rate limits
async function testAPI() {
  const baseUrl = 'http://localhost:3001/api/ai';
  
  console.log('Testing Claude AI API...\n');
  
  // 1. Test health endpoint
  try {
    const healthResponse = await fetch(`${baseUrl}/health`);
    const health = await healthResponse.json();
    console.log('✓ Health Check:', health);
  } catch (error) {
    console.log('✗ Health Check Failed:', error.message);
  }
  
  // 2. Test the test endpoint
  console.log('\nTesting Claude query (no rate limit)...');
  try {
    const response = await fetch(`${baseUrl}/test`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message: 'What are the uses of hemp fiber?' })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✓ Claude Response (first 200 chars):', 
        data.response.substring(0, 200) + '...');
    } else {
      const error = await response.json();
      console.log('✗ Error:', error);
    }
  } catch (error) {
    console.log('✗ Request Failed:', error.message);
  }
}

testAPI();