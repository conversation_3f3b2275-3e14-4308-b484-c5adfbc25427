# Setup New AI Image Providers - Replicate & Together AI

## API Keys to Add

### 1. Replicate
- **Key Name**: `REPLICATE_API_KEY`
- **Key Value**: `[YOUR_REPLICATE_API_KEY]`
- **Cost**: ~$0.0004 per image (SDXL Lightning)

### 2. Together AI
- **Key Name**: `TOGETHER_API_KEY`
- **Key Value**: `[YOUR_TOGETHER_API_KEY]`
- **Cost**: $0.0015 per image
- **Free Credits**: $25 (about 16,666 images!)

## Steps to Implement

1. **Add Secrets to Supabase Edge Function**:
   - Go to: https://supabase.com/dashboard/project/ktoqznqmlnxrtvubewyz/functions/hemp-image-generator/details
   - Click "Secrets" tab
   - Add both API keys as new secrets

2. **Update Edge Function Code**:
   - Add to PROVIDERS object (around line 43):
   ```typescript
   replicate: {
     name: 'replicate',
     apiKeyName: 'REPLICATE_API_KEY',
     generateImage: generateReplicate
   },
   together_ai: {
     name: 'together_ai', 
     apiKeyName: 'TOGETHER_API_KEY',
     generateImage: generateTogetherAI
   },
   ```

3. **Add the new provider functions** (at the end of the file):
   - Copy the functions from `edge-function-additions.ts`

4. **Deploy the updated Edge Function**

## Testing

Run the test script:
```bash
bash test-new-providers.sh
```

Or test manually:
```bash
# Test Replicate
curl -X POST https://ktoqznqmlnxrtvubewyz.supabase.co/functions/v1/hemp-image-generator \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 1, "forceProvider": "replicate"}'

# Test Together AI
curl -X POST https://ktoqznqmlnxrtvubewyz.supabase.co/functions/v1/hemp-image-generator \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 1, "forceProvider": "together_ai"}'
```

## Provider Comparison

| Provider | Cost/Image | Speed | Quality | Notes |
|----------|-----------|-------|---------|-------|
| Replicate | $0.0004 | Fast (4 steps) | Good | SDXL Lightning model |
| Together AI | $0.0015 | Medium (20 steps) | Very Good | Full SDXL, $25 free credits |
| Stable Diffusion | $0.0020 | Medium | Very Good | No balance |
| DALL-E 3 | $0.0400 | Slow | Excellent | No credits |

## Recommended Strategy

1. Use **Together AI** as primary (best quality + free credits)
2. Use **Replicate** as fallback (cheaper, still good)
3. Keep **placeholder** as last resort