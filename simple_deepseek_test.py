#!/usr/bin/env python3
"""Simple DeepSeek test"""

import asyncio
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set API key
os.environ['DEEPSEEK_API_KEY'] = '***********************************'

import logging
logging.basicConfig(level=logging.DEBUG)

from openai import AsyncOpenAI


async def test_deepseek_directly():
    """Test DeepSeek API directly"""
    
    client = AsyncOpenAI(
        api_key=os.environ['DEEPSEEK_API_KEY'],
        base_url="https://api.deepseek.com/v1"
    )
    
    print("Testing DeepSeek directly...")
    
    # Simple test
    response = await client.chat.completions.create(
        model="deepseek-chat",
        messages=[{"role": "user", "content": "Say 'Hello World' and nothing else"}],
        temperature=0.7,
        max_tokens=100
    )
    
    content = response.choices[0].message.content
    print(f"Response: '{content}'")
    print(f"Type: {type(content)}")
    print(f"Length: {len(content) if content else 'None'}")
    
    # JSON test
    print("\n\nTesting JSON response...")
    response2 = await client.chat.completions.create(
        model="deepseek-chat",
        messages=[{"role": "user", "content": 'Return this exact JSON: {"status": "ok"}'}],
        temperature=0.1,
        max_tokens=100
    )
    
    content2 = response2.choices[0].message.content
    print(f"JSON Response: '{content2}'")
    
    # Check if it's actually JSON
    try:
        import json
        parsed = json.loads(content2)
        print(f"Parsed as JSON: {parsed}")
    except Exception as e:
        print(f"Failed to parse as JSON: {e}")


if __name__ == "__main__":
    asyncio.run(test_deepseek_directly())