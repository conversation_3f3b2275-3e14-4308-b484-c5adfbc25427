import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function checkAugmentImages() {
  const { data } = await supabase
    .from('uses_products')
    .select('id, name, image_url')
    .gte('id', 190)
    .lte('id', 199)
    .order('id');
    
  console.log('Augment Products with Images:');
  console.log('='.repeat(60));
  data.forEach(p => {
    const hasImage = p.image_url && !p.image_url.includes('placeholder');
    console.log(`[${p.id}] ${p.name}`);
    console.log(`   Image: ${hasImage ? '✅ Generated' : '❌ Placeholder'}`);
    if (hasImage && p.image_url) {
      console.log(`   URL: ${p.image_url.substring(0, 80)}...`);
    }
  });
}

checkAugmentImages();