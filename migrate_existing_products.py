#!/usr/bin/env python3
"""
Migrate existing products to separate company names from product names
Identifies products with brand names and creates proper company relationships
"""

import os
import sys
import re
from supabase import create_client

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

# Known company name patterns and indicators
COMPANY_INDICATORS = [
    "Manitoba Harvest",
    "Nutiva",
    "Good Hemp",
    "Victory Hemp",
    "Fresh Hemp Foods",
    "Hemp Foods Australia",
    "Hemp Fortex",
    "Toad&Co",
    "tentree",
    "Hempsmith",
    "American Hemp",
    "Hempitecture",
    "HempTraders",
    "BioFiber Industries",
    "Doylestown Hemp",
    "HempWood",
    "Patagonia",
    "WAMA",
    "Ki<PERSON><PERSON>'s",
    "The Body Shop",
    "Buddha Teas",
    "Navitas",
    "Bob's Red Mill",
    "365",
    "NOW Sports",
    "Garden of Life",
    "Pacific Foods",
    "Living Harvest",
    "Jungmaven",
    "prAna",
    "Levi's",
    "Outerknown",
    "Nudie Jeans",
    "Charlotte's Web",
    "Lazarus Naturals",
    "NuLeaf Naturals",
    "Joy Organics",
    "CBDistillery"
]

def extract_company_from_name(product_name):
    """
    Extract company name from product name
    Returns (clean_product_name, company_name) or (product_name, None)
    """
    # Check for known company patterns
    for company in COMPANY_INDICATORS:
        if company.lower() in product_name.lower():
            # Remove company name from product
            clean_name = product_name.replace(company, "").strip()
            # Remove extra spaces and clean up
            clean_name = re.sub(r'\s+', ' ', clean_name).strip()
            # Remove leading/trailing punctuation
            clean_name = clean_name.strip('-–—:')
            return clean_name, company
    
    # Check for patterns like "Brand Name Product Type"
    # Common patterns: First 1-2 words might be brand if capitalized
    words = product_name.split()
    if len(words) >= 3:
        # Check if first word is all caps or title case (likely brand)
        if words[0].isupper() or (words[0][0].isupper() and len(words[0]) > 3):
            # Check if it's not a common product word
            common_product_words = ['Hemp', 'Organic', 'Raw', 'Natural', 'Premium', 'Pure', 'Whole']
            if words[0] not in common_product_words:
                possible_company = words[0]
                # Check if second word is also part of brand
                if len(words) > 1 and words[1][0].isupper() and words[1] not in common_product_words:
                    possible_company = f"{words[0]} {words[1]}"
                clean_name = product_name.replace(possible_company, "").strip()
                return clean_name, possible_company
    
    return product_name, None

def get_or_create_company(supabase, company_name):
    """Get existing company or create new one"""
    # Check if company exists
    result = supabase.table('hemp_companies').select('id').eq('name', company_name).execute()
    
    if result.data:
        return result.data[0]['id']
    
    # Create new company
    new_company = {
        'name': company_name,
        'description': f'{company_name} is a hemp product manufacturer/brand.',
        'verified': True,
        'company_type': 'manufacturer'
    }
    
    result = supabase.table('hemp_companies').insert(new_company).execute()
    if result.data:
        print(f"  ✅ Created company: {company_name}")
        return result.data[0]['id']
    return None

def add_product_company_relationship(supabase, product_id, company_id):
    """Add relationship between product and company"""
    # Check if relationship exists
    existing = supabase.table('hemp_company_products')\
        .select('id')\
        .eq('product_id', product_id)\
        .eq('company_id', company_id)\
        .execute()
    
    if not existing.data:
        # Create relationship
        relationship = {
            'product_id': product_id,
            'company_id': company_id,
            'is_primary': True,
            'verified': True,
            'relationship_type': 'manufacturer'
        }
        result = supabase.table('hemp_company_products').insert(relationship).execute()
        if result.data:
            return True
    return False

def update_product_name(supabase, product_id, new_name):
    """Update product name in database"""
    result = supabase.table('uses_products')\
        .update({'name': new_name})\
        .eq('id', product_id)\
        .execute()
    return result.data is not None

def migrate_existing_products():
    """Process all existing products and extract company names"""
    # Initialize Supabase
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials!")
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("🔄 Migrating Existing Products - Separating Company Names")
    print("=" * 60)
    
    # Get all products
    products = supabase.table('uses_products').select('id, name').execute()
    
    if not products.data:
        print("No products found!")
        return
    
    print(f"Found {len(products.data)} products to process\n")
    
    products_updated = 0
    companies_created = 0
    relationships_created = 0
    products_skipped = 0
    
    for product in products.data:
        product_id = product['id']
        original_name = product['name']
        
        # Skip if already has company relationship
        existing_rel = supabase.table('hemp_company_products')\
            .select('id')\
            .eq('product_id', product_id)\
            .execute()
        
        if existing_rel.data:
            products_skipped += 1
            continue
        
        # Try to extract company name
        clean_name, company_name = extract_company_from_name(original_name)
        
        if company_name and clean_name != original_name:
            print(f"\n📦 Processing: {original_name}")
            print(f"   Product: {clean_name}")
            print(f"   Company: {company_name}")
            
            # Create or get company
            company_id = get_or_create_company(supabase, company_name)
            if company_id:
                companies_created += 1
                
                # Create relationship
                if add_product_company_relationship(supabase, product_id, company_id):
                    relationships_created += 1
                    print(f"   ✅ Linked to company")
                
                # Update product name to remove company
                if clean_name and clean_name != original_name:
                    if update_product_name(supabase, product_id, clean_name):
                        products_updated += 1
                        print(f"   ✅ Updated product name")
            else:
                print(f"   ❌ Failed to create company")
    
    print(f"\n📊 Migration Summary:")
    print(f"   - Products processed: {len(products.data)}")
    print(f"   - Products updated: {products_updated}")
    print(f"   - Products skipped (already linked): {products_skipped}")
    print(f"   - Companies created/found: {companies_created}")
    print(f"   - Relationships created: {relationships_created}")
    
    # Show sample results
    print(f"\n📋 Sample Products with Companies:")
    sample = supabase.table('products_with_companies')\
        .select('product_name, companies, company_count')\
        .gt('company_count', 0)\
        .limit(10)\
        .execute()
    
    if sample.data:
        for item in sample.data:
            if item['companies']:
                print(f"   - {item['product_name']}")
                print(f"     Companies: {item['companies']}")

def show_products_needing_companies():
    """Show products that still don't have companies assigned"""
    # Initialize Supabase
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    # Get products without companies
    products_without = supabase.table('uses_products')\
        .select('id, name')\
        .execute()
    
    orphaned = []
    for product in products_without.data:
        rel = supabase.table('hemp_company_products')\
            .select('id')\
            .eq('product_id', product['id'])\
            .execute()
        if not rel.data:
            orphaned.append(product)
    
    if orphaned:
        print(f"\n⚠️  Products without companies ({len(orphaned)} found):")
        for p in orphaned[:10]:  # Show first 10
            print(f"   - {p['name']}")
        if len(orphaned) > 10:
            print(f"   ... and {len(orphaned) - 10} more")

if __name__ == "__main__":
    migrate_existing_products()
    show_products_needing_companies()