#!/usr/bin/env python3
"""
Fast cleanup script using batch operations
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv
import logging
from collections import defaultdict

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Supabase client
supabase_url = os.getenv('SUPABASE_URL')
supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY') or os.getenv('SUPABASE_ANON_KEY')
supabase: Client = create_client(supabase_url, supabase_key)

def fast_cleanup():
    """Fast cleanup using batch operations"""
    logger.info("=== Fast Image Generation Cleanup ===")
    
    # Get all queue entries at once
    logger.info("Fetching all queue entries...")
    queue_response = supabase.table('image_generation_queue').select('*').execute()
    all_queue_entries = queue_response.data or []
    logger.info(f"Found {len(all_queue_entries)} total queue entries")
    
    # Group by product_id
    products = defaultdict(list)
    for entry in all_queue_entries:
        products[entry['product_id']].append(entry)
    
    # Find entries to keep and delete
    entries_to_keep = []
    entries_to_delete = []
    history_to_check = []
    
    for product_id, entries in products.items():
        if len(entries) == 1:
            entries_to_keep.append(entries[0]['id'])
            continue
        
        # Sort by priority: completed > pending > processing > failed
        entries.sort(key=lambda e: (
            0 if e['status'] == 'completed' else 
            1 if e['status'] == 'pending' else 
            2 if e['status'] == 'processing' else 3,
            e['created_at']
        ))
        
        # Keep best entry
        entries_to_keep.append(entries[0]['id'])
        
        # Mark others for deletion
        for entry in entries[1:]:
            entries_to_delete.append(entry['id'])
            history_to_check.append(entry['id'])
    
    logger.info(f"Entries to keep: {len(entries_to_keep)}")
    logger.info(f"Entries to delete: {len(entries_to_delete)}")
    
    if not entries_to_delete:
        logger.info("No duplicates found!")
        return
    
    # Delete history entries in bulk
    if history_to_check:
        logger.info("Deleting related history entries...")
        try:
            # Delete history for queue entries we're removing
            supabase.table('image_generation_history').delete().in_('queue_id', entries_to_delete).execute()
            logger.info(f"Deleted history entries for {len(entries_to_delete)} queue items")
        except Exception as e:
            logger.warning(f"Error deleting history: {e}")
    
    # Delete queue entries in batches
    logger.info("Deleting duplicate queue entries...")
    batch_size = 100
    for i in range(0, len(entries_to_delete), batch_size):
        batch = entries_to_delete[i:i+batch_size]
        try:
            supabase.table('image_generation_queue').delete().in_('id', batch).execute()
            logger.info(f"Deleted batch {i//batch_size + 1} ({len(batch)} entries)")
        except Exception as e:
            logger.error(f"Error deleting batch: {e}")
    
    # Final stats
    logger.info("\n=== Cleanup Complete ===")
    final_count = supabase.table('image_generation_queue').select('id', count='exact').execute()
    logger.info(f"Queue entries after cleanup: {final_count.count}")
    
    # Generate SQL constraints
    logger.info("\nGenerating database constraints...")
    sql = """
-- Run this SQL in Supabase to prevent future duplicates:

-- 1. Unique constraint for active entries
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_product_active 
ON image_generation_queue(product_id) 
WHERE status IN ('pending', 'processing');

-- 2. Auto-cleanup trigger
CREATE OR REPLACE FUNCTION auto_cleanup_completed()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' THEN
        DELETE FROM image_generation_queue 
        WHERE product_id = NEW.product_id 
        AND id != NEW.id 
        AND status = 'completed';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cleanup_completed_entries
AFTER UPDATE ON image_generation_queue
FOR EACH ROW
WHEN (NEW.status = 'completed')
EXECUTE FUNCTION auto_cleanup_completed();
"""
    
    with open('constraints.sql', 'w') as f:
        f.write(sql)
    logger.info("SQL constraints saved to constraints.sql")

if __name__ == "__main__":
    fast_cleanup()