#!/usr/bin/env python3
"""
Fix company URLs by adding https:// scheme if missing
"""
import os
import requests
from dotenv import load_dotenv
from datetime import datetime, timezone

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

def fix_urls():
    """Add https:// to URLs that don't have a scheme"""
    print("🔧 Fixing company URLs...")
    
    # Get all companies with websites
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/hemp_companies",
        headers=headers,
        params={"website": "not.is.null", "select": "id,name,website"}
    )
    
    companies = response.json()
    print(f"Found {len(companies)} companies with websites")
    
    fixed_count = 0
    
    for company in companies:
        website = company['website']
        
        # Check if URL needs fixing
        if website and not website.startswith(('http://', 'https://')):
            # Add https:// prefix
            fixed_url = f"https://{website}"
            
            # Update in database
            update_data = {
                "website": fixed_url,
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            update_response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/hemp_companies?id=eq.{company['id']}",
                headers=headers,
                json=update_data
            )
            
            if update_response.status_code in [200, 204]:
                fixed_count += 1
                print(f"✅ Fixed: {company['name']} - {website} → {fixed_url}")
            else:
                print(f"❌ Failed to fix: {company['name']}")
    
    print(f"\n📊 Fixed {fixed_count} company URLs")
    
    return fixed_count

if __name__ == "__main__":
    fix_urls()