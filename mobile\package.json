{"name": "hemp-database-mobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"react": "18.3.1", "react-native": "0.76.3", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.29.0", "react-native-safe-area-context": "^4.8.2", "react-native-gesture-handler": "^2.14.1", "react-native-reanimated": "^3.6.1", "react-native-vector-icons": "^10.0.3", "react-native-svg": "^14.1.0", "react-native-paper": "^5.12.3", "@tanstack/react-query": "^5.17.9", "axios": "^1.6.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.76.3", "@react-native/eslint-config": "0.76.3", "@react-native/metro-config": "0.76.3", "@react-native/typescript-config": "0.76.3", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}