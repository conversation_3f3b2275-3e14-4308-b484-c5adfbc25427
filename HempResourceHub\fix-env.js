#!/usr/bin/env node

/**
 * Fix environment variables for database connection
 * <PERSON>perly encodes the password with special characters
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read .env file
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.error('.env file not found!');
  process.exit(1);
}

const envContent = fs.readFileSync(envPath, 'utf8');
const lines = envContent.split('\n');

// Find and fix DATABASE_URL
const updatedLines = lines.map(line => {
  if (line.startsWith('DATABASE_URL=')) {
    // Extract the current URL
    const currentUrl = line.substring('DATABASE_URL='.length);
    
    // Parse the URL
    try {
      const urlParts = currentUrl.match(/postgresql:\/\/([^:]+):([^@]+)@(.+)/);
      if (urlParts) {
        const username = urlParts[1];
        const password = urlParts[2];
        const rest = urlParts[3];
        
        // URL encode the password
        const encodedPassword = encodeURIComponent(password);
        
        // Reconstruct the URL
        const newUrl = `postgresql://${username}:${encodedPassword}@${rest}`;
        
        console.log('Original password:', password);
        console.log('Encoded password:', encodedPassword);
        console.log('New DATABASE_URL:', newUrl);
        
        return `DATABASE_URL=${newUrl}`;
      }
    } catch (e) {
      console.error('Error parsing DATABASE_URL:', e);
    }
  }
  return line;
});

// Write back to .env
fs.writeFileSync(envPath, updatedLines.join('\n'));
console.log('\n✅ .env file updated successfully!');
console.log('The password has been properly URL-encoded.');
console.log('\nYou can now run: npm run dev');