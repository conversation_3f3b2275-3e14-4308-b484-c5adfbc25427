# GitHub Actions Cleanup - Continuation Prompt

## 🎯 **Task Context**
I need help implementing GitHub Actions cleanup and consolidation for the HempQuarterz hemp database project. The current workflows are failing consistently and wasting resources.

## 📊 **Current Situation**
- **Repository**: HempQuarterz/HQz-Ai-DB-MCP-3
- **Problem**: GitHub Actions running ~28 times/day, mostly failing
- **Impact**: Wasting GitHub Actions minutes, creating noise
- **Root Cause**: Hemp CLI infrastructure issues, missing scripts

## 🔴 **Failing Workflows (Need Immediate Attention)**

### 1. `automated-operations.yml`
- **Runs**: Every 6 hours
- **Purpose**: Hemp agent automation (research, content, SEO)
- **Issue**: `python hemp agent research` commands failing
- **Action Needed**: Disable immediately

### 2. `monitoring-and-reporting.yml`
- **Runs**: Every hour + daily + weekly
- **Purpose**: System health monitoring and reporting
- **Issue**: Missing `scripts/monitor_and_alert.py`, complex jq parsing failures
- **Action Needed**: Disable and replace with simple version

## ✅ **Working Workflows (Keep These)**
- `simple-test.yml` - Basic hello world test
- `test-basic-clean.yml` - Import and connection testing

## 📋 **Implementation Plan**

### **Phase 1: Immediate Cleanup** (Priority 1)
1. **Disable failing workflows**:
   ```bash
   mv .github/workflows/automated-operations.yml .github/workflows/automated-operations.yml.disabled
   mv .github/workflows/monitoring-and-reporting.yml .github/workflows/monitoring-and-reporting.yml.disabled
   ```

2. **Remove redundant files**:
   - `automated-operations.yml.old`
   - Other backup files

### **Phase 2: Create Replacements** (Priority 2)
1. **Simple Health Check** (daily instead of hourly):
   ```yaml
   name: Simple Health Check
   on:
     schedule:
       - cron: '0 12 * * *'  # Once daily
     workflow_dispatch:
   ```

2. **Manual Agent Operations** (replace automated):
   ```yaml
   name: Manual Agent Operations
   on:
     workflow_dispatch:
       inputs:
         operation: [test, research, content]
   ```

3. **Enhanced Testing** (improve existing):
   - Expand `test-basic-clean.yml`
   - Add more comprehensive checks

## 🔧 **Technical Details**

### **Working Infrastructure**:
- Python 3.10 setup ✅
- Supabase secrets configured ✅
- Basic imports working ✅
- GitHub Actions environment ✅

### **Broken Infrastructure**:
- Hemp CLI (`python hemp` commands) ❌
- Monitoring scripts ❌
- Agent automation system ❌

### **Environment Variables Available**:
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `OPENAI_API_KEY`
- `PYTHONPATH`

## 📁 **File Locations**
```
.github/workflows/
├── automated-operations.yml          # DISABLE THIS
├── monitoring-and-reporting.yml      # DISABLE THIS
├── simple-test.yml                   # KEEP
├── test-basic-clean.yml             # KEEP & ENHANCE
└── [various .disabled files]         # LEAVE ALONE
```

## 🎯 **Specific Tasks to Complete**

### **Immediate (Do First)**:
1. Rename failing workflows to `.disabled`
2. Create simple daily health check workflow
3. Test the new health check manually

### **Secondary**:
1. Create manual agent operation workflow
2. Enhance the existing test workflow
3. Clean up redundant files

### **Documentation**:
1. Update workflow documentation
2. Document what was changed and why
3. Create troubleshooting guide

## 📝 **Success Criteria**
- ✅ No more failing automated runs
- ✅ Simple daily health check working
- ✅ Manual control over agent operations
- ✅ Existing functionality preserved
- ✅ Clear documentation of changes

## 🚨 **Important Notes**
- **Don't delete workflows** - rename to `.disabled` for safety
- **Test manually** before committing changes
- **Keep it simple** - avoid complex logic that might fail
- **Document everything** for future reference

## 💡 **Context for AI Assistant**
This is a hemp industry database project with:
- Supabase backend
- Python automation agents
- React frontend
- Complex data scraping and processing
- GitHub Actions for automation

The user prefers:
- Simple, reliable solutions over complex ones
- Manual control over full automation
- Gradual improvements over big changes
- Good documentation for future reference

## 🔗 **Related Files to Review**
- `.github/workflows/` directory (all workflow files)
- `requirements.txt` (Python dependencies)
- `hemp` CLI script (if exists)
- `scripts/` directory (monitoring scripts)

---

**Ready to implement? Start with Phase 1 (disabling failing workflows) and work through the plan systematically.**
