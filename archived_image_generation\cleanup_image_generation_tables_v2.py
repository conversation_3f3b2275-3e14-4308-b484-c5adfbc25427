#!/usr/bin/env python3
"""
Cleanup script for image generation table overpopulation - V2
Handles foreign key constraints by cleaning history first
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv
from datetime import datetime
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Supabase client
supabase_url = os.getenv('SUPABASE_URL')
supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY') or os.getenv('SUPABASE_ANON_KEY')
supabase: Client = create_client(supabase_url, supabase_key)

def cleanup_orphaned_history():
    """Clean up history entries for duplicate queue items"""
    logger.info("Cleaning up orphaned history entries...")
    
    # Get all queue entries
    queue_response = supabase.table('image_generation_queue').select('id, product_id').execute()
    queue_entries = {entry['id']: entry for entry in (queue_response.data or [])}
    
    # Group by product to find duplicates
    products = {}
    for queue_id, entry in queue_entries.items():
        product_id = entry['product_id']
        if product_id not in products:
            products[product_id] = []
        products[product_id].append(queue_id)
    
    # For products with duplicates, keep only the best queue entry's history
    history_to_delete = []
    for product_id, queue_ids in products.items():
        if len(queue_ids) > 1:
            # Sort queue IDs by status (completed first) and keep first
            queue_data = []
            for qid in queue_ids:
                q_response = supabase.table('image_generation_queue').select('*').eq('id', qid).single().execute()
                if q_response.data:
                    queue_data.append(q_response.data)
            
            # Sort by status priority
            queue_data.sort(key=lambda x: (
                0 if x['status'] == 'completed' else 1 if x['status'] == 'pending' else 2,
                x['created_at']
            ))
            
            # Keep first, mark others for deletion
            keep_queue_id = queue_data[0]['id'] if queue_data else None
            if keep_queue_id:
                for qid in queue_ids:
                    if qid != keep_queue_id:
                        # Get history entries for this queue_id
                        hist_response = supabase.table('image_generation_history').select('id').eq('queue_id', qid).execute()
                        history_to_delete.extend([h['id'] for h in (hist_response.data or [])])
    
    logger.info(f"Found {len(history_to_delete)} history entries to delete")
    
    # Delete history in batches
    if history_to_delete:
        batch_size = 100
        for i in range(0, len(history_to_delete), batch_size):
            batch = history_to_delete[i:i+batch_size]
            try:
                supabase.table('image_generation_history').delete().in_('id', batch).execute()
                logger.info(f"Deleted history batch {i//batch_size + 1} ({len(batch)} entries)")
            except Exception as e:
                logger.error(f"Error deleting history batch: {e}")
    
    return len(history_to_delete)

def cleanup_queue_duplicates():
    """Clean up duplicate queue entries after history is cleaned"""
    logger.info("Cleaning up duplicate queue entries...")
    
    # Get all queue entries
    response = supabase.table('image_generation_queue').select('*').order('created_at', desc=True).execute()
    all_entries = response.data if response.data else []
    
    # Group by product_id
    products = {}
    for entry in all_entries:
        product_id = entry['product_id']
        if product_id not in products:
            products[product_id] = []
        products[product_id].append(entry)
    
    entries_to_delete = []
    stats = {
        'products_processed': 0,
        'entries_kept': 0,
        'entries_deleted': 0,
        'completed_kept': 0,
        'pending_kept': 0
    }
    
    for product_id, entries in products.items():
        if len(entries) <= 1:
            stats['entries_kept'] += len(entries)
            continue
            
        stats['products_processed'] += 1
        
        # Sort by status priority: completed > pending > failed > processing
        def status_priority(entry):
            priorities = {'completed': 0, 'pending': 1, 'processing': 2, 'failed': 3}
            return priorities.get(entry['status'], 99)
        
        entries.sort(key=lambda e: (status_priority(e), e['created_at']), reverse=False)
        
        # Keep the best entry
        keep_entry = entries[0]
        stats['entries_kept'] += 1
        if keep_entry['status'] == 'completed':
            stats['completed_kept'] += 1
        elif keep_entry['status'] == 'pending':
            stats['pending_kept'] += 1
        
        # Delete others
        for entry in entries[1:]:
            try:
                supabase.table('image_generation_queue').delete().eq('id', entry['id']).execute()
                stats['entries_deleted'] += 1
                logger.debug(f"Deleted queue entry {entry['id']} for product {product_id}")
            except Exception as e:
                logger.error(f"Error deleting queue entry {entry['id']}: {e}")
    
    logger.info(f"Cleanup complete:")
    logger.info(f"  - Products with duplicates: {stats['products_processed']}")
    logger.info(f"  - Entries kept: {stats['entries_kept']}")
    logger.info(f"  - Entries deleted: {stats['entries_deleted']}")
    logger.info(f"  - Completed entries kept: {stats['completed_kept']}")
    logger.info(f"  - Pending entries kept: {stats['pending_kept']}")
    
    return stats

def add_constraints_sql():
    """Generate SQL for database constraints"""
    sql = """
-- IMPORTANT: Run this SQL in Supabase SQL Editor after cleanup

-- 1. Add unique constraint to prevent multiple active entries per product
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_product_active_status 
ON image_generation_queue(product_id) 
WHERE status IN ('pending', 'processing');

-- 2. Add check constraint to prevent re-queuing products with images
CREATE OR REPLACE FUNCTION check_product_needs_image()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if product already has a non-placeholder image
    IF EXISTS (
        SELECT 1 FROM uses_products 
        WHERE id = NEW.product_id 
        AND image_url IS NOT NULL 
        AND image_url != ''
        AND image_url NOT LIKE '%placeholder.com%'
    ) THEN
        -- Allow if it's a retry for recently failed generation
        IF NOT EXISTS (
            SELECT 1 FROM image_generation_queue 
            WHERE product_id = NEW.product_id 
            AND status = 'failed'
            AND updated_at > NOW() - INTERVAL '1 hour'
        ) THEN
            RAISE EXCEPTION 'Product % already has an image URL: %', 
                NEW.product_id, 
                (SELECT image_url FROM uses_products WHERE id = NEW.product_id);
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER prevent_unnecessary_queue
BEFORE INSERT ON image_generation_queue
FOR EACH ROW
EXECUTE FUNCTION check_product_needs_image();

-- 3. Auto-cleanup old completed entries
CREATE OR REPLACE FUNCTION auto_cleanup_old_entries()
RETURNS TRIGGER AS $$
BEGIN
    -- When a new completed entry is added, remove old completed entries for same product
    IF NEW.status = 'completed' THEN
        DELETE FROM image_generation_queue 
        WHERE product_id = NEW.product_id 
        AND status = 'completed' 
        AND id != NEW.id
        AND created_at < NEW.created_at;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cleanup_old_completed
AFTER INSERT OR UPDATE ON image_generation_queue
FOR EACH ROW
WHEN (NEW.status = 'completed')
EXECUTE FUNCTION auto_cleanup_old_entries();

-- 4. Add index for better performance
CREATE INDEX IF NOT EXISTS idx_queue_product_status 
ON image_generation_queue(product_id, status);

CREATE INDEX IF NOT EXISTS idx_queue_created_at 
ON image_generation_queue(created_at DESC);
"""
    return sql

def verify_cleanup():
    """Verify the cleanup results"""
    logger.info("\nVerifying cleanup results...")
    
    # Check current stats
    queue_response = supabase.table('image_generation_queue').select('product_id, status', count='exact').execute()
    total_queue = queue_response.count or 0
    
    history_response = supabase.table('image_generation_history').select('id', count='exact').execute()
    total_history = history_response.count or 0
    
    products_response = supabase.table('uses_products').select('id', count='exact').execute()
    total_products = products_response.count or 0
    
    # Check for remaining duplicates
    queue_data = queue_response.data or []
    product_counts = {}
    for entry in queue_data:
        pid = entry['product_id']
        product_counts[pid] = product_counts.get(pid, 0) + 1
    
    remaining_duplicates = sum(1 for count in product_counts.values() if count > 1)
    
    logger.info(f"Current state:")
    logger.info(f"  - Total queue entries: {total_queue}")
    logger.info(f"  - Total history entries: {total_history}")
    logger.info(f"  - Total products: {total_products}")
    logger.info(f"  - Products with duplicates: {remaining_duplicates}")
    
    return {
        'queue_entries': total_queue,
        'history_entries': total_history,
        'total_products': total_products,
        'remaining_duplicates': remaining_duplicates
    }

def main():
    """Main cleanup process"""
    logger.info("=== Image Generation Cleanup Script V2 ===")
    logger.info("This version handles foreign key constraints properly")
    
    # Get initial stats
    initial_stats = verify_cleanup()
    logger.info(f"\nInitial state: {initial_stats['queue_entries']} queue entries for {initial_stats['total_products']} products")
    
    if initial_stats['queue_entries'] <= initial_stats['total_products']:
        logger.info("No cleanup needed - queue size is reasonable")
        return
    
    # Ask for confirmation
    response = input(f"\nProceed with cleanup? This will:\n"
                    f"1. Delete orphaned history entries\n"
                    f"2. Remove duplicate queue entries\n"
                    f"3. Keep the best entry per product\n"
                    f"Proceed? (yes/no): ")
    
    if response.lower() != 'yes':
        logger.info("Cleanup cancelled")
        return
    
    # Step 1: Clean history
    logger.info("\n--- Step 1: Cleaning History ---")
    history_deleted = cleanup_orphaned_history()
    
    # Step 2: Clean queue
    logger.info("\n--- Step 2: Cleaning Queue ---")
    queue_stats = cleanup_queue_duplicates()
    
    # Step 3: Verify results
    logger.info("\n--- Step 3: Verifying Results ---")
    final_stats = verify_cleanup()
    
    # Show improvement
    logger.info(f"\n=== Cleanup Summary ===")
    logger.info(f"Queue entries: {initial_stats['queue_entries']} → {final_stats['queue_entries']} "
               f"(reduced by {initial_stats['queue_entries'] - final_stats['queue_entries']})")
    logger.info(f"History entries: {initial_stats['history_entries']} → {final_stats['history_entries']} "
               f"(reduced by {initial_stats['history_entries'] - final_stats['history_entries']})")
    
    # Show SQL constraints
    logger.info("\n=== Database Constraints ===")
    logger.info("To prevent future duplicates, run this SQL in Supabase:")
    print("\n" + add_constraints_sql())
    
    logger.info("\n=== Cleanup Complete ===")

if __name__ == "__main__":
    main()