import React, { useState, useEffect, useRef, useMemo } from "react";
import { useLocation } from "wouter";
import { Input } from "@/components/ui/input";
import { Search, Package, Leaf, Factory, FileText, X } from "lucide-react";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";
import { cn } from "@/lib/utils";

interface GlobalSearchProps {
  className?: string;
  onClose?: () => void;
  isOpen?: boolean;
}

interface SearchResult {
  type: 'product' | 'plantPart' | 'industry' | 'page';
  id: string | number;
  title: string;
  subtitle?: string;
  url: string;
  icon: React.ReactNode;
}

export function GlobalSearch({ className, onClose, isOpen = true }: GlobalSearchProps) {
  const [location, setLocation] = useLocation();
  const [query, setQuery] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const { data: products } = useAllHempProducts();
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();

  // Static pages for search
  const staticPages = [
    { title: "About", url: "/about", icon: <FileText className="h-4 w-4" /> },
    { title: "Plant Types", url: "/plant-types", icon: <Leaf className="h-4 w-4" /> },
    { title: "Research", url: "/research", icon: <FileText className="h-4 w-4" /> },
    { title: "Admin Dashboard", url: "/admin", icon: <FileText className="h-4 w-4" /> }
  ];

  // Generate search results
  const searchResults = useMemo<SearchResult[]>(() => {
    if (!query.trim()) return [];

    const searchLower = query.toLowerCase();
    const results: SearchResult[] = [];

    // Search products
    if (products) {
      const productResults = products
        .filter(product => 
          product.name.toLowerCase().includes(searchLower) ||
          product.description?.toLowerCase().includes(searchLower) ||
          product.benefits_advantages?.some(b => b.toLowerCase().includes(searchLower))
        )
        .slice(0, 5)
        .map(product => ({
          type: 'product' as const,
          id: product.id,
          title: product.name,
          subtitle: product.plant_part?.name || 'Product',
          url: `/product/${product.id}`,
          icon: <Package className="h-4 w-4" />
        }));
      results.push(...productResults);
    }

    // Search plant parts
    if (plantParts) {
      const partResults = plantParts
        .filter(part => part.name.toLowerCase().includes(searchLower))
        .slice(0, 3)
        .map(part => ({
          type: 'plantPart' as const,
          id: part.id,
          title: part.name,
          subtitle: 'Plant Part',
          url: `/hemp-dex?tab=plant-parts&filter=${part.id}`,
          icon: <Leaf className="h-4 w-4" />
        }));
      results.push(...partResults);
    }

    // Search industries
    if (industries) {
      const industryResults = industries
        .filter(industry => industry.name.toLowerCase().includes(searchLower))
        .slice(0, 3)
        .map(industry => ({
          type: 'industry' as const,
          id: industry.id,
          title: industry.name,
          subtitle: 'Industry',
          url: `/hemp-dex?tab=industries&filter=${industry.id}`,
          icon: <Factory className="h-4 w-4" />
        }));
      results.push(...industryResults);
    }

    // Search static pages
    const pageResults = staticPages
      .filter(page => page.title.toLowerCase().includes(searchLower))
      .map(page => ({
        type: 'page' as const,
        id: page.url,
        title: page.title,
        url: page.url,
        icon: page.icon
      }));
    results.push(...pageResults);

    return results.slice(0, 8); // Limit total results
  }, [query, products, plantParts, industries]);

  // Handle navigation
  const handleSelect = (result: SearchResult) => {
    setLocation(result.url);
    setQuery("");
    setIsFocused(false);
    onClose?.();
  };

  // Keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => (prev + 1) % searchResults.length);
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev === 0 ? searchResults.length - 1 : prev - 1);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (searchResults[selectedIndex]) {
        handleSelect(searchResults[selectedIndex]);
      } else if (query.trim()) {
        // If no specific result selected, go to search page
        setLocation(`/hemp-dex?search=${encodeURIComponent(query)}`);
        setQuery("");
        setIsFocused(false);
        onClose?.();
      }
    } else if (e.key === 'Escape') {
      setIsFocused(false);
      inputRef.current?.blur();
      onClose?.();
    }
  };

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Reset selected index when results change
  useEffect(() => {
    setSelectedIndex(0);
  }, [searchResults]);

  const showResults = isFocused && query.length > 0 && isOpen;

  return (
    <div ref={searchRef} className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onKeyDown={handleKeyDown}
          placeholder="Search"
          className={cn(
            "w-full pl-10 pr-10 py-2 rounded-full",
            "bg-gray-900/80 border-gray-700",
            "focus:bg-gray-900 focus:border-green-400",
            "placeholder:text-gray-400",
            "transition-all duration-200"
          )}
        />
        {query && (
          <button
            onClick={() => {
              setQuery("");
              inputRef.current?.focus();
            }}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-300"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {showResults && (
        <div className="absolute top-full mt-2 w-full bg-gray-900/95 backdrop-blur-sm rounded-lg shadow-2xl border border-green-500/30 overflow-hidden z-50 animate-in fade-in slide-in-from-top-2 duration-200">
          {searchResults.length > 0 ? (
            <>
              <div className="p-2 border-b border-gray-800">
                <p className="text-xs text-gray-400">
                  {searchResults.length} results for "{query}"
                </p>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {searchResults.map((result, index) => (
                  <button
                    key={`${result.type}-${result.id}`}
                    onClick={() => handleSelect(result)}
                    onMouseEnter={() => setSelectedIndex(index)}
                    className={cn(
                      "w-full px-4 py-3 flex items-start gap-3 hover:bg-gray-800 transition-colors text-left",
                      selectedIndex === index && "bg-gray-800"
                    )}
                  >
                    <div className="text-green-400 mt-0.5">{result.icon}</div>
                    <div className="flex-1 min-w-0">
                      <div className="text-white font-medium truncate">{result.title}</div>
                      {result.subtitle && (
                        <div className="text-gray-400 text-sm">{result.subtitle}</div>
                      )}
                    </div>
                    <div className="text-gray-500 text-xs mt-0.5">
                      {result.type === 'product' ? 'Product' : 
                       result.type === 'plantPart' ? 'Category' :
                       result.type === 'industry' ? 'Industry' : 'Page'}
                    </div>
                  </button>
                ))}
              </div>
              <div className="p-2 border-t border-gray-800">
                <button
                  onClick={() => {
                    setLocation(`/hemp-dex?search=${encodeURIComponent(query)}`);
                    setQuery("");
                    setIsFocused(false);
                    onClose?.();
                  }}
                  className="text-sm text-green-400 hover:text-green-300 flex items-center gap-2"
                >
                  <Search className="h-3 w-3" />
                  View all results for "{query}"
                </button>
              </div>
            </>
          ) : (
            <div className="p-4 text-center text-gray-400">
              <p>No results found for "{query}"</p>
              <button
                onClick={() => {
                  setLocation(`/hemp-dex?search=${encodeURIComponent(query)}`);
                  setQuery("");
                  setIsFocused(false);
                  onClose?.();
                }}
                className="text-sm text-green-400 hover:text-green-300 mt-2"
              >
                Search anyway →
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}