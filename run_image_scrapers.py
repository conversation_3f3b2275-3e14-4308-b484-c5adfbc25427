#!/usr/bin/env python3
"""
Run all image scrapers with attribution
"""
import os
import subprocess
import requests
from dotenv import load_dotenv

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

def run_migration():
    """Apply database migration for attribution columns"""
    print("📊 Applying database migration...")
    
    headers = {
        "apikey": SUPABASE_KEY,
        "Authorization": f"Bearer {SUPABASE_KEY}",
        "Content-Type": "application/json"
    }
    
    # Read migration SQL
    with open('add_attribution_columns.sql', 'r') as f:
        sql = f.read()
    
    # Execute via Supabase API
    response = requests.post(
        f"{SUPABASE_URL}/rest-admin/v1/query",
        headers=headers,
        json={"query": sql}
    )
    
    if response.status_code == 200:
        print("✅ Migration applied successfully")
    else:
        print(f"⚠️  Migration may have already been applied or encountered an issue")
        print(f"   Response: {response.text}")

def main():
    print("🚀 Hemp Resource Hub - Image Scraping with Attribution\n")
    
    # Step 1: Apply migration
    run_migration()
    print()
    
    # Step 2: Fix research image fields
    print("🔧 Fixing research image fields...")
    subprocess.run(["python", "fix_research_image_field.py"])
    print()
    
    # Step 3: Run enhanced scrapers
    print("🌐 Running enhanced scrapers with attribution...")
    subprocess.run(["python", "enhanced_scraper_with_attribution.py"])
    print()
    
    print("\n✅ All tasks completed!")
    print("\n📋 Next steps:")
    print("1. Check the frontend to see images with attribution")
    print("2. Hover over images to see source information")
    print("3. Run scrapers again with higher limits if needed")

if __name__ == "__main__":
    main()