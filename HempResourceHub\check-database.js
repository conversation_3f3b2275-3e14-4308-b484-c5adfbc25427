import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTE3NzYsImV4cCI6MjA2NDA2Nzc3Nn0.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function checkDatabase() {
  console.log('Checking Supabase database...\n');
  
  try {
    // Check products
    const { data: products, error: productsError, count } = await supabase
      .from('uses_products')
      .select('*', { count: 'exact' })
      .limit(5);
    
    if (productsError) {
      console.error('Error fetching products:', productsError);
    } else {
      console.log(`Products count: ${count || 0}`);
      if (products && products.length > 0) {
        console.log('\nSample products:');
        products.forEach(p => {
          console.log(`  - ID: ${p.id}, Name: ${p.name}`);
        });
      } else {
        console.log('\n❌ No products found in database!');
      }
    }
    
    // Check plant types
    const { count: plantTypesCount } = await supabase
      .from('hemp_plant_archetypes')
      .select('*', { count: 'exact', head: true });
    console.log(`\nPlant types count: ${plantTypesCount || 0}`);
    
    // Check plant parts
    const { count: plantPartsCount } = await supabase
      .from('plant_parts')
      .select('*', { count: 'exact', head: true });
    console.log(`Plant parts count: ${plantPartsCount || 0}`);
    
    // Check industries
    const { count: industriesCount } = await supabase
      .from('industries')
      .select('*', { count: 'exact', head: true });
    console.log(`Industries count: ${industriesCount || 0}`);
    
    // Check agent data
    const { count: tasksCount } = await supabase
      .from('agent_task_queue')
      .select('*', { count: 'exact', head: true });
    console.log(`Agent tasks count: ${tasksCount || 0}`);
    
    if (count === 0) {
      console.log('\n⚠️  Database is empty! You need to populate it.');
      console.log('\nTo populate the database:');
      console.log('1. Navigate to the project root');
      console.log('2. Run: python3 populate_supabase_db.py');
      console.log('\nOr run the individual population scripts in the project.');
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkDatabase();