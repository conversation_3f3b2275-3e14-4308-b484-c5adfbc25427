-- Add latitude and longitude fields to hemp_companies table for map visualization
ALTER TABLE hemp_companies 
ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS city VARCHAR(100),
ADD COLUMN IF NOT EXISTS state_province VARCHAR(100),
ADD COLUMN IF NOT EXISTS postal_code VARCHAR(20);

-- Add index for location-based queries
CREATE INDEX IF NOT EXISTS idx_companies_location ON hemp_companies(latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Update some example companies with location data
UPDATE hemp_companies SET 
    latitude = 49.8951,
    longitude = -97.1384,
    city = 'Winnipeg',
    state_province = 'Manitoba',
    country = 'Canada'
WHERE name = 'Manitoba Harvest';

UPDATE hemp_companies SET 
    latitude = 37.4875,
    longitude = -122.2263,
    city = 'Richmond',
    state_province = 'California',
    country = 'USA'
WHERE name = 'Nutiva';

UPDATE hemp_companies SET 
    latitude = 34.4208,
    longitude = -119.6982,
    city = 'Ventura',
    state_province = 'California',
    country = 'USA'
WHERE name = 'Patagonia';

UPDATE hemp_companies SET 
    latitude = 33.6846,
    longitude = -117.8265,
    city = 'Irvine',
    state_province = 'California',
    country = 'USA'
WHERE name = 'HempTraders';

UPDATE hemp_companies SET 
    latitude = 51.4540,
    longitude = -0.1790,
    city = 'London',
    state_province = NULL,
    country = 'UK'
WHERE name = 'Good Hemp';

-- Add more example companies with locations
INSERT INTO hemp_companies (name, description, website, country, city, state_province, latitude, longitude, company_type, verified) VALUES
('HempFlax', 'European leader in hemp cultivation and processing', 'www.hempflax.com', 'Netherlands', 'Oude Pekela', NULL, 53.1042, 7.0086, 'manufacturer', true),
('Hemp Inc', 'Industrial hemp products and CBD wellness', 'www.hempinc.com', 'USA', 'Spring Hope', 'North Carolina', 35.9393, -78.1119, 'manufacturer', true),
('Elixinol', 'Global leader in hemp-derived CBD products', 'www.elixinol.com', 'USA', 'Denver', 'Colorado', 39.7392, -104.9903, 'manufacturer', true),
('Charlotte''s Web', 'Premium hemp extract products', 'www.charlottesweb.com', 'USA', 'Boulder', 'Colorado', 40.0150, -105.2705, 'manufacturer', true),
('Canopy Growth', 'Cannabis and hemp company', 'www.canopygrowth.com', 'Canada', 'Smiths Falls', 'Ontario', 44.9041, -76.0251, 'manufacturer', true),
('Aurora Cannabis', 'Medical cannabis and hemp products', 'www.auroramj.com', 'Canada', 'Edmonton', 'Alberta', 53.5461, -113.4938, 'manufacturer', true),
('HempMeds', 'CBD hemp oil products', 'www.hempmeds.com', 'USA', 'San Diego', 'California', 32.7157, -117.1611, 'manufacturer', true),
('Hemp Foods Australia', 'Hemp food products', 'www.hempfoods.com.au', 'Australia', 'Bangalow', 'NSW', -28.6874, 153.5247, 'manufacturer', true),
('South Hemp Tecno', 'Hemp construction materials', 'www.southhemp.it', 'Italy', 'Crispiano', NULL, 40.6075, 17.2369, 'manufacturer', true),
('Hanf Farm', 'Hemp cultivation and products', 'www.hanffarm.de', 'Germany', 'Berlin', NULL, 52.5200, 13.4050, 'manufacturer', true)
ON CONFLICT (name) DO UPDATE SET
    latitude = EXCLUDED.latitude,
    longitude = EXCLUDED.longitude,
    city = EXCLUDED.city,
    state_province = EXCLUDED.state_province,
    country = EXCLUDED.country;