#!/usr/bin/env python3
"""Test DeepSeek JSON extraction"""

import asyncio
import os
import sys
import json
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

os.environ['DEEPSEEK_API_KEY'] = '***********************************'

from utils.ai_providers import DeepSeekProvider


async def test_json_extraction():
    """Test the updated JSON extraction"""
    
    deepseek = DeepSeekProvider()
    
    test_prompts = [
        {
            "name": "Simple JSON",
            "prompt": 'Return {"status": "ok", "count": 2}'
        },
        {
            "name": "Product list", 
            "prompt": """List 2 hemp plastic products as JSON array:
[
  {"name": "product1", "description": "desc1"},
  {"name": "product2", "description": "desc2"}
]"""
        },
        {
            "name": "Structured product",
            "prompt": """Create a JSON object for a hemp plastic product with these fields:
- name
- description
- plant_part (one of: fiber, seeds, oil)
- industry
- companies (array)"""
        }
    ]
    
    for test in test_prompts:
        print(f"\n=== Test: {test['name']} ===")
        print(f"Prompt: {test['prompt'][:100]}...")
        
        try:
            response = await deepseek.generate(test['prompt'], response_format="json")
            print(f"Raw response: '{response}'")
            
            # Try to parse
            parsed = json.loads(response)
            print(f"✅ Parsed successfully: {json.dumps(parsed, indent=2)}")
        except Exception as e:
            print(f"❌ Failed: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_json_extraction())