#!/usr/bin/env python3
"""Generate unified report from operation results."""

import json
import os
import sys
from datetime import datetime

# Get operation from command line argument
operation = sys.argv[1] if len(sys.argv) > 1 else 'unknown'

# Load report
report_file = f'reports/{operation}-report.json'
if os.path.exists(report_file):
    with open(report_file) as f:
        report = json.load(f)
else:
    report = {'error': 'No report generated'}

# Generate markdown
md = f'''# Data Operations Report

**Date**: {datetime.now().strftime('%Y-%m-%d %H:%M UTC')}
**Operation**: {operation}
**Status**: {'✅ Success' if not report.get('error') else '❌ Failed'}

## Results
'''

if operation == 'hourly-discovery' and 'products' in report.get('result', {}):
    md += f'''
- **Products Discovered**: {len(report['result']['products'])}
- **Products Saved**: {report.get('saved_count', 0)}
'''
elif operation == 'daily-health' and 'stats' in report.get('result', {}):
    stats = report['result']['stats']
    md += f'''
- **Total Products**: {stats.get('total_products', 'N/A')}
- **Total Companies**: {stats.get('total_companies', 'N/A')}
- **Added in 24h**: {stats.get('products_24h', 'N/A')}
'''
elif operation == 'weekly-research' and 'products' in report.get('result', {}):
    md += f'''
- **Research Areas**: 4
- **Products Found**: {len(report['result']['products'])}
- **Products Saved**: {report.get('saved_count', 0)}
'''
elif operation == 'monthly-expansion' and 'opportunities' in report.get('result', {}):
    md += f'''
- **Markets Analyzed**: {len(report['result']['opportunities'])}
- **Recommendations**: {len(report['result'].get('recommendations', []))}
'''

md += '''

---
*Data Operations Hub - Unified Workflow*
'''

with open('reports/unified-report.md', 'w') as f:
    f.write(md)

print(md)