# Agent Consolidation Migration Guide

## Overview
This guide documents the consolidation of hemp research agents from 19 to 9, achieving a 52% reduction while maintaining full functionality.

## Consolidation Changes

### Merged Agents

1. **flower-products** (NEW)
   - Combines: flower-pharma, flower-cbd, flower-wellness
   - Industries: pharmaceuticals, cbd_products, wellness, health_products

2. **oil-personal-care** (NEW)
   - Combines: oil-cosmetics, oil-wellness
   - Industries: cosmetics, personal_care, wellness

3. **hurds-materials** (NEW)
   - Combines: hurds-construction, hurds-bedding, hurds-hempcrete
   - Industries: construction, hempcrete, animal_bedding, building_materials

### Unchanged Agents
- seeds-food
- seeds-nutrition
- fiber-textiles
- fiber-composites
- oil-biofuel
- roots-medicine
- roots-biotech
- leaves-feed
- leaves-medicine
- biomass-energy
- whole-plant

## Testing Instructions

### 1. Test Individual Consolidated Agents

```bash
# Test the new flower-products agent (handles multiple industries)
python hemp_agent_enhanced.py flower-products --limit 5

# Test the new oil-personal-care agent
python hemp_agent_enhanced.py oil-personal-care --limit 5

# Test the new hurds-materials agent
python hemp_agent_enhanced.py hurds-materials --limit 5
```

### 2. Test All Agents
```bash
# Run all 9 consolidated agents (previously would run 19)
python hemp_agent_enhanced.py all --limit 5
```

### 3. Test via GitHub Actions
```bash
# Trigger manually via GitHub UI or CLI
gh workflow run hemp-automation.yml -f agent_type=flower-products
```

## Benefits Achieved

### 1. **Reduced Complexity**
- 52% fewer agents to maintain
- Simplified scheduling logic
- Clearer agent responsibilities

### 2. **Better Resource Utilization**
- Fewer API calls to OpenAI
- Reduced database operations
- More efficient use of GitHub Actions minutes

### 3. **Improved Data Quality**
- Better distribution across related industries
- More comprehensive product discovery
- Reduced duplicate entries

### 4. **Cost Savings**
- Estimated 40-50% reduction in API costs
- Fewer GitHub Actions runs needed
- Reduced database storage for duplicate tracking

## Monitoring the Changes

### Check Agent Performance
```python
# Create monitor_consolidated.py
import os
from supabase import create_client
from datetime import datetime, timedelta

# Initialize Supabase
supabase = create_client(
    os.environ['SUPABASE_URL'],
    os.environ['SUPABASE_ANON_KEY']
)

# Query recent runs of consolidated agents
consolidated_agents = ['flower-products', 'oil-personal-care', 'hurds-materials']

for agent in consolidated_agents:
    runs = supabase.table('hemp_agent_runs') \
        .select('*') \
        .eq('agent_name', agent) \
        .gte('timestamp', (datetime.now() - timedelta(days=7)).isoformat()) \
        .execute()
    
    print(f"\n{agent}: {len(runs.data)} runs in last 7 days")
    for run in runs.data:
        print(f"  - Products: {run['products_found']}, Saved: {run['products_saved']}")
```

### Database Verification
```sql
-- Check product distribution by industry
SELECT 
    isc.name as industry,
    pp.name as plant_part,
    COUNT(up.id) as product_count
FROM uses_products up
JOIN industry_sub_categories isc ON up.industry_sub_category_id = isc.id
JOIN plant_parts pp ON up.plant_part_id = pp.id
WHERE up.created_at > NOW() - INTERVAL '7 days'
GROUP BY isc.name, pp.name
ORDER BY product_count DESC;
```

## Rollback Instructions

If you need to rollback to the previous version:

```bash
# Revert the commits
git revert 8cfdd4a781ac304a0acf2988a8438fee8268a3b8
git revert b20706a13da26aec742265f7ac818c3eddf24f37
git push origin main
```

## Next Steps

1. **Monitor Initial Runs**: Watch the first few runs of consolidated agents
2. **Verify Data Quality**: Check that products are properly distributed across industries
3. **Adjust Limits**: You may want to increase the limit per agent since they cover more ground
4. **Update Documentation**: Update any team documentation about the agents

## FAQ

**Q: Will I lose any product discovery capabilities?**
A: No, the consolidated agents search across all the same industries, just more efficiently.

**Q: How are products distributed across industries?**
A: The AI is instructed to provide a balanced distribution across all specified industries.

**Q: Can I still run specific industry searches?**
A: The consolidated agents handle multiple related industries. For very specific searches, you may want to create custom queries.

**Q: What about the scheduling?**
A: The schedule has been optimized for the consolidated agents, with high-priority agents running more frequently.

## Support

If you encounter any issues:
1. Check the GitHub Actions logs for error details
2. Verify your environment variables are set correctly
3. Review the hemp_agent_runs table for failure patterns
4. Open an issue with the 'consolidation' label if needed
