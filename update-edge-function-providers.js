// Script to add Replicate API key and update Edge Function
// Run this with: node update-edge-function-providers.js

console.log('To add the Replicate API key to your Edge Function:');
console.log('\n1. Go to Supabase Dashboard > Edge Functions > hemp-image-generator');
console.log('2. Click on "Secrets" tab');
console.log('3. Add new secret:');
console.log('   Name: REPLICATE_API_KEY');
console.log('   Value: [YOUR_REPLICATE_API_KEY]');
console.log('\n4. For Together AI (when you get the key):');
console.log('   Name: TOGETHER_API_KEY');
console.log('   Value: [your together ai key]');

console.log('\n\nTo update the Edge Function code:');
console.log('1. Copy the content from updated-edge-function.ts');
console.log('2. Replace the existing Edge Function code');
console.log('3. Deploy the updated function');

console.log('\n\nNote: The updated Edge Function code is being generated...');