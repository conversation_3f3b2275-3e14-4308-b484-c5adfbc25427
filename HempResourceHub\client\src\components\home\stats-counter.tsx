import { useState } from "react";
import { useStats } from "@/hooks/use-plant-data";
import { SmartSearch } from "@/components/ui/smart-search";
import { useLocation } from "wouter";
import Counter from "@/components/ui/counter";

const StatsCounter = () => {
  const { data: stats, isLoading } = useStats();
  const [, setLocation] = useLocation();

  const handleSearch = (query: string) => {
    // Navigate to products with search query
    setLocation(`/products?search=${encodeURIComponent(query)}`);
  };

  return (
    <div className="bg-primary py-12" data-oid="yjfohfm">
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
        data-oid="hvwkybp"
      >
        <h2
          className="text-2xl sm:text-3xl font-heading font-bold text-white mb-6"
          data-oid="p625.42"
        >
          Explore All Hemp Applications
        </h2>
        <div className="max-w-4xl mx-auto" data-oid="-v4qaa3">
          {/* Enhanced Smart Search with improved styling */}
          <div className="mb-8 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 via-emerald-500/10 to-green-500/10 rounded-2xl blur-xl" />
            <div className="relative bg-black/20 backdrop-blur-sm rounded-2xl p-6 border border-green-400/20">
              <SmartSearch
                placeholder="🔍 Discover hemp products, applications, or ask AI questions..."
                onSearch={handleSearch}
                showAISuggestions={true}
                showVoiceSearch={true}
                showImageSearch={false}
                className="w-full"
              />
              <p className="text-sm text-gray-300 mt-3 opacity-80">
                Try: "sustainable building materials" or "What are hemp's environmental benefits?"
              </p>
            </div>
          </div>

          <div className="mt-10 text-white" data-oid="7_k171r">
            <p
              className="text-xl sm:text-2xl opacity-95 mb-3 font-medium"
              data-oid="cfc7q:c"
            >
              Total Documented Hemp Applications:
            </p>
            <div
              className="text-5xl sm:text-6xl font-bold"
              data-oid="0kl:de2"
            >
              {isLoading ? (
                <span className="opacity-50" data-oid="k1bpfmq">
                  Loading...
                </span>
              ) : (
                <Counter
                  end={stats?.totalProducts || 0}
                  suffix="+"
                  duration={2500}
                  data-oid=":s25nkz"
                />
              )}
            </div>
            <p
              className="mt-4 text-base sm:text-lg md:text-xl opacity-95 max-w-2xl mx-auto font-medium"
              data-oid="zj82nir"
            >
              Our database continues to grow as research uncovers new industrial
              hemp applications across various sectors. Check back regularly for
              the latest additions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsCounter;
