-- Script to queue all placeholder products for AI image regeneration
-- This will use the new providers (Replicate and Together AI)

-- First, check how many products have placeholder images
SELECT COUNT(*) as placeholder_count
FROM uses_products
WHERE image_url LIKE '%placeholder%';

-- Insert queue items for all placeholder products
-- Alternate between Replicate and Together AI for testing both
INSERT INTO image_generation_queue (
    product_id,
    prompt,
    status,
    priority,
    retry_with_provider,
    created_at,
    updated_at
)
SELECT 
    up.id as product_id,
    CONCAT(
        'Professional product photography of ', 
        up.name,
        CASE 
            WHEN pp.name IS NOT NULL THEN CONCAT(', made from ', pp.name)
            ELSE ', hemp-based product'
        END,
        CASE 
            WHEN i.name = 'Food and Beverage' THEN ', appetizing presentation, natural lighting'
            WHEN i.name = 'Cosmetics and Personal Care' THEN ', elegant packaging, soft lighting, premium feel'
            WHEN i.name = 'Textiles' THEN ', fabric texture visible, natural draping'
            WHEN i.name = 'Construction' THEN ', industrial setting, durability focus'
            WHEN i.name = 'Automotive' THEN ', sleek design, technical components'
            WHEN i.name = 'Medical' THEN ', clean clinical setting, professional'
            ELSE ', commercial quality, clean background'
        END,
        ', high resolution, professional lighting'
    ) as prompt,
    'pending' as status,
    CASE 
        WHEN i.name IN ('Medical', 'Cosmetics and Personal Care') THEN 8
        WHEN i.name IN ('Food and Beverage', 'Automotive') THEN 7
        ELSE 5
    END as priority,
    -- Alternate between providers
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY up.id) % 2 = 0 THEN 'replicate'
        ELSE 'together_ai'
    END as retry_with_provider,
    NOW() as created_at,
    NOW() as updated_at
FROM uses_products up
LEFT JOIN plant_parts pp ON up.plant_part_id = pp.id
LEFT JOIN industry_sub_categories isc ON up.industry_sub_category_id = isc.id
LEFT JOIN industries i ON isc.industry_id = i.id
WHERE up.image_url LIKE '%placeholder%'
ORDER BY up.id;

-- Show what was queued
SELECT 
    COUNT(*) as total_queued,
    COUNT(CASE WHEN retry_with_provider = 'replicate' THEN 1 END) as replicate_count,
    COUNT(CASE WHEN retry_with_provider = 'together_ai' THEN 1 END) as together_ai_count
FROM image_generation_queue
WHERE created_at >= NOW() - INTERVAL '1 minute';