#!/usr/bin/env node

/**
 * Test script to debug AI conversation creation
 * Run with: node test-ai-conversation.js
 */

const API_URL = 'http://localhost:3001/api/ai';

async function testConversationCreation() {
  console.log('🧪 Testing AI Conversation Creation...\n');

  try {
    // Test 1: Check health endpoint
    console.log('1️⃣ Checking health endpoint...');
    const healthResponse = await fetch(`${API_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
    console.log('');

    // Test 2: Get available agents
    console.log('2️⃣ Getting available agents...');
    const agentsResponse = await fetch(`${API_URL}/agents`);
    const agents = await agentsResponse.json();
    console.log('✅ Available agents:', agents.map(a => a.id).join(', '));
    console.log('');

    // Test 3: Create a conversation
    console.log('3️⃣ Creating a conversation...');
    const createResponse = await fetch(`${API_URL}/conversations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ agentId: 'product-discovery' }),
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      console.error('❌ Failed to create conversation:', createResponse.status, errorText);
      return;
    }

    const { conversationId } = await createResponse.json();
    console.log('✅ Created conversation:', conversationId);
    console.log('');

    // Test 4: Send a test message
    console.log('4️⃣ Sending a test message...');
    const messageResponse = await fetch(`${API_URL}/conversations/${conversationId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        message: 'What are some innovative hemp products?',
        stream: false 
      }),
    });

    if (!messageResponse.ok) {
      const errorText = await messageResponse.text();
      console.error('❌ Failed to send message:', messageResponse.status, errorText);
      return;
    }

    const messageData = await messageResponse.json();
    console.log('✅ Received response:', messageData.message.substring(0, 100) + '...');
    console.log('');

    // Test 5: Clean up
    console.log('5️⃣ Cleaning up conversation...');
    const deleteResponse = await fetch(`${API_URL}/conversations/${conversationId}`, {
      method: 'DELETE',
    });
    console.log('✅ Conversation deleted');

  } catch (error) {
    console.error('❌ Error during testing:', error);
  }
}

// Run the test
testConversationCreation();