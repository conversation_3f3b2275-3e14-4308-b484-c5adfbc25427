import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { Helmet } from 'react-helmet';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, <PERSON><PERSON><PERSON>cle, AlertTriangle } from 'lucide-react';
import { supabase } from '@/lib/supabase-client';
import HempQuarterzLogo from '@/assets/circle-logo.png?url';

export const AuthCallbackPage = () => {
  const [, setLocation] = useLocation();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing authentication...');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the session from the URL hash
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth callback error:', error);
          setStatus('error');
          setMessage(error.message || 'Authentication failed');
          return;
        }

        if (data.session) {
          setStatus('success');
          setMessage('Authentication successful! Redirecting...');
          
          // Check if user is admin
          const isAdmin = data.session.user?.user_metadata?.role === 'admin' || 
                         data.session.user?.email === '<EMAIL>' ||
                         data.session.user?.user_metadata?.is_admin === true;
          
          // Redirect after a short delay
          setTimeout(() => {
            setLocation(isAdmin ? '/admin' : '/');
          }, 2000);
        } else {
          setStatus('error');
          setMessage('No session found. Please try signing in again.');
        }
      } catch (err) {
        console.error('Unexpected error during auth callback:', err);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again.');
      }
    };

    // Handle the auth callback
    handleAuthCallback();

    // Also listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          setStatus('success');
          setMessage('Authentication successful! Redirecting...');
          
          const isAdmin = session.user?.user_metadata?.role === 'admin' || 
                         session.user?.email === '<EMAIL>' ||
                         session.user?.user_metadata?.is_admin === true;
          
          setTimeout(() => {
            setLocation(isAdmin ? '/admin' : '/');
          }, 2000);
        } else if (event === 'SIGNED_OUT') {
          setStatus('error');
          setMessage('Authentication failed. Please try again.');
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [setLocation]);

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-400" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-400" />;
      case 'error':
        return <AlertTriangle className="h-8 w-8 text-red-400" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'border-blue-500/30';
      case 'success':
        return 'border-green-500/30';
      case 'error':
        return 'border-red-500/30';
    }
  };

  return (
    <>
      <Helmet>
        <title>Authentication - HempQuarterz Database</title>
        <meta name="description" content="Processing authentication for HempQuarterz account." />
      </Helmet>

      <div className="min-h-screen bg-black flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <Card className={`bg-gray-900/60 backdrop-blur-sm ${getStatusColor()}`}>
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <img 
                  src={HempQuarterzLogo} 
                  alt="HempQuarterz Logo" 
                  className="h-16 w-16 rounded-full"
                />
              </div>
              <CardTitle className="text-2xl font-bold text-white hemp-brand-ultra">
                Authentication
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div className="flex justify-center">
                {getStatusIcon()}
              </div>
              
              <div className="space-y-2">
                <p className="text-white font-medium">
                  {status === 'loading' && 'Processing...'}
                  {status === 'success' && 'Success!'}
                  {status === 'error' && 'Authentication Failed'}
                </p>
                <p className="text-gray-400 text-sm">
                  {message}
                </p>
              </div>

              {status === 'error' && (
                <Alert className="border-red-500/50 bg-red-500/10 text-left">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-red-400">
                    <div className="space-y-2">
                      <p>If you continue to experience issues:</p>
                      <ul className="text-sm space-y-1 ml-4">
                        <li>• Clear your browser cache and cookies</li>
                        <li>• Try using an incognito/private window</li>
                        <li>• Ensure pop-ups are enabled for this site</li>
                        <li>• Contact support if the problem persists</li>
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {status === 'loading' && (
                <div className="text-xs text-gray-500">
                  This may take a few seconds...
                </div>
              )}

              {status === 'success' && (
                <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                  <p className="text-green-400 text-sm">
                    Welcome to HempQuarterz! You'll be redirected shortly.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default AuthCallbackPage;
