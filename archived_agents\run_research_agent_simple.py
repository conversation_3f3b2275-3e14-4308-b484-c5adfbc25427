#!/usr/bin/env python3
"""
Simple script to run the research agent and see results
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lib.supabase_client import get_supabase_client
from agents.research.unified_research_agent import create_research_agent, ResearchFeatures

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Run the research agent with web scraping"""
    print("\n🔍 Running Research Agent (Web Scraping Mode)")
    print("="*50)
    
    # Get Supabase client
    supabase = get_supabase_client()
    
    # Create agent without AI (will use web scraping)
    print("Creating agent without AI provider...")
    agent = create_research_agent(
        supabase, 
        ai_provider=None,  # No AI provider
        features=['web', 'feed', 'company']  # Use non-AI features
    )
    
    # Search for products
    search_query = "hemp construction materials"
    print(f"\nSearching for: {search_query}")
    print("-"*50)
    
    try:
        # Use async context manager for proper session handling
        async with agent:
            products = await agent.discover_products(
                search_query,
                max_results=10
            )
            
        print(f"\n✅ Found {len(products)} products!")
        
        # Display results
        for i, product in enumerate(products, 1):
            print(f"\n{i}. {product.get('name', 'Unknown Product')}")
            print(f"   Plant Part: {product.get('plant_part', 'Unknown')}")
            print(f"   Industry: {product.get('industry', 'Unknown')}")
            print(f"   Description: {product.get('description', 'No description')[:100]}...")
            if product.get('companies'):
                print(f"   Companies: {', '.join(product['companies'])}")
            print(f"   Source: {product.get('data_source', 'Unknown')}")
            
    except Exception as e:
        print(f"\n❌ Error: {e}")
        logger.error(f"Failed to discover products: {e}", exc_info=True)
        
    # Check database
    print("\n📊 Checking database for recent products...")
    result = supabase.table('uses_products').select('*').order(
        'created_at', desc=True
    ).limit(5).execute()
    
    print(f"Recent products in DB: {len(result.data)}")
    for product in result.data[:3]:
        print(f"  - {product['name']} (ID: {product['id']})")


if __name__ == "__main__":
    asyncio.run(main())