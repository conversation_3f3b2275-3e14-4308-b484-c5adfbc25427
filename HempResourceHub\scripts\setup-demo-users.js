/**
 * <PERSON><PERSON><PERSON> to set up demo users in Supabase
 * Run this script to create the demo users that are referenced in the login page
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Service role key needed for admin operations

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- VITE_SUPABASE_URL:', !!supabaseUrl);
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const demoUsers = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    user_metadata: {
      role: 'admin',
      is_admin: true,
      first_name: 'Admin',
      last_name: 'User',
      full_name: 'Admin User',
      company: 'HempQuarterz',
    },
    email_confirm: true
  },
  {
    email: '<EMAIL>',
    password: 'user123',
    user_metadata: {
      role: 'user',
      is_admin: false,
      first_name: 'Demo',
      last_name: 'User',
      full_name: 'Demo User',
      company: 'Demo Company',
    },
    email_confirm: true
  }
];

async function createDemoUser(userData) {
  try {
    console.log(`Creating user: ${userData.email}`);
    
    const { data, error } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      user_metadata: userData.user_metadata,
      email_confirm: userData.email_confirm
    });

    if (error) {
      if (error.message.includes('already registered')) {
        console.log(`✓ User ${userData.email} already exists`);
        return true;
      } else {
        console.error(`✗ Error creating user ${userData.email}:`, error.message);
        return false;
      }
    }

    console.log(`✓ Successfully created user: ${userData.email}`);
    return true;
  } catch (err) {
    console.error(`✗ Unexpected error creating user ${userData.email}:`, err);
    return false;
  }
}

async function setupDemoUsers() {
  console.log('🚀 Setting up demo users for HempQuarterz...\n');

  let successCount = 0;
  let totalCount = demoUsers.length;

  for (const userData of demoUsers) {
    const success = await createDemoUser(userData);
    if (success) successCount++;
    console.log(''); // Add spacing between users
  }

  console.log('📊 Setup Summary:');
  console.log(`✓ Successfully processed: ${successCount}/${totalCount} users`);
  
  if (successCount === totalCount) {
    console.log('🎉 All demo users are ready!');
    console.log('\n📝 Demo Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User:  <EMAIL> / user123');
  } else {
    console.log('⚠️  Some users could not be created. Check the errors above.');
  }

  console.log('\n🔧 Next Steps:');
  console.log('1. Test login with demo credentials');
  console.log('2. Set up Google OAuth in Supabase dashboard');
  console.log('3. Configure MFA settings if needed');
}

// Run the setup
setupDemoUsers().catch(console.error);
