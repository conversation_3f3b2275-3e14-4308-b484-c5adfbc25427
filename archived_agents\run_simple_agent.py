#!/usr/bin/env python3
"""
Simple agent runner using the basic hemp_agent.py
This is the most straightforward way to populate the database
"""

import os
import sys
import time
from datetime import datetime

# Load environment variables manually
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_basic_hemp_agent():
    """Run the basic hemp agent"""
    try:
        from hemp_agent import HempResearchAgent
        
        print("🌿 Hemp Product Discovery Agent")
        print("=" * 50)
        
        # Plant parts and industries to search
        plant_parts = ['seeds', 'fiber', 'hurds', 'flowers', 'leaves']
        industries = ['food', 'textiles', 'construction', 'cosmetics', 'medicine']
        
        total_saved = 0
        total_found = 0
        
        for plant_part in plant_parts:
            for industry in industries:
                print(f"\n🔍 Searching: {plant_part} × {industry}")
                
                # Initialize agent for this combination
                agent = HempResearchAgent(plant_part, industry)
                
                # Research products
                products = agent.research_products(limit=3)
                found = len(products)
                total_found += found
                
                if found > 0:
                    # Save products
                    saved = agent.save_products(products)
                    total_saved += saved
                    print(f"   ✅ Found {found} products, saved {saved} new ones")
                else:
                    print(f"   ⚠️  No products found")
                
                # Small delay to avoid rate limits
                time.sleep(2)
        
        print(f"\n✅ Summary:")
        print(f"   - Total products found: {total_found}")
        print(f"   - New products saved: {total_saved}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def check_database():
    """Quick database check"""
    try:
        from supabase import create_client
        
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_ANON_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ Missing Supabase credentials!")
            return
        
        supabase = create_client(supabase_url, supabase_key)
        
        # Count products
        result = supabase.table('uses_products').select('count', count='exact').execute()
        print(f"\n📊 Current products in database: {result.count}")
        
    except Exception as e:
        print(f"❌ Database error: {e}")

def main():
    print("=== Simple Hemp Agent Runner ===")
    print("This uses the basic hemp_agent.py without complex dependencies\n")
    
    # Check environment
    print("🔧 Environment Check:")
    print(f"   SUPABASE_URL: {'✅ Set' if os.environ.get('SUPABASE_URL') else '❌ Missing'}")
    print(f"   SUPABASE_ANON_KEY: {'✅ Set' if os.environ.get('SUPABASE_ANON_KEY') else '❌ Missing'}")
    print(f"   OPENAI_API_KEY: {'✅ Set' if os.environ.get('OPENAI_API_KEY') else '❌ Missing'}")
    
    missing = []
    if not os.environ.get('SUPABASE_URL'):
        missing.append('SUPABASE_URL')
    if not os.environ.get('SUPABASE_ANON_KEY'):
        missing.append('SUPABASE_ANON_KEY')
    if not os.environ.get('OPENAI_API_KEY'):
        missing.append('OPENAI_API_KEY')
    
    if missing:
        print(f"\n❌ Missing required environment variables: {', '.join(missing)}")
        print("Please check your .env file")
        return
    
    # Check database
    check_database()
    
    # Run agent
    print("\nStarting product discovery...")
    input("Press Enter to continue or Ctrl+C to cancel: ")
    
    run_basic_hemp_agent()

if __name__ == "__main__":
    main()