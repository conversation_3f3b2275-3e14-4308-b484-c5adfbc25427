#!/usr/bin/env python3
"""
Test DeepSeek AI provider for research agent
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from lib.supabase_client import get_supabase_client
from utils.ai_providers import MultiProviderAI
from agents.research.unified_research_agent import create_research_agent


async def test_deepseek():
    """Test DeepSeek integration"""
    logger.info("Testing DeepSeek AI provider...")
    
    # Check if DeepSeek API key is set
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    if not deepseek_key or deepseek_key == "your_deepseek_api_key_here":
        logger.error("❌ DEEPSEEK_API_KEY not set in .env file!")
        logger.info("To use DeepSeek:")
        logger.info("1. Sign up at https://platform.deepseek.com/")
        logger.info("2. Get your API key from the dashboard")
        logger.info("3. Add it to .env: DEEPSEEK_API_KEY=your_actual_key")
        return
    
    # Initialize AI provider with DeepSeek as primary
    try:
        ai_provider = MultiProviderAI(primary_provider="deepseek", fallback_providers=["openai"])
        logger.info("✅ DeepSeek provider initialized")
    except Exception as e:
        logger.error(f"Failed to initialize DeepSeek: {e}")
        return
    
    # Test simple generation
    logger.info("\n🧪 Testing DeepSeek generation...")
    try:
        result, provider_used, cost = await ai_provider.generate(
            "List 3 innovative hemp products in a simple JSON array format with names only"
        )
        logger.info(f"✅ Generation successful using {provider_used}")
        logger.info(f"Cost: ${cost:.6f}")
        logger.info(f"Result:\n{result}")
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        return
    
    # Test with research agent
    logger.info("\n🔬 Testing research agent with DeepSeek...")
    supabase = get_supabase_client()
    
    # Create research agent with DeepSeek
    agent = create_research_agent(
        supabase, 
        ai_provider=ai_provider,
        features=['basic', 'company', 'web', 'feed']
    )
    
    # Run a simple discovery
    try:
        products = await agent.discover_products("innovative hemp construction materials", max_results=3)
        logger.info(f"✅ Discovered {len(products)} products")
        
        for i, product in enumerate(products, 1):
            logger.info(f"\n{i}. {product.get('name', 'Unknown')}")
            logger.info(f"   Plant Part: {product.get('plant_part', 'Unknown')}")
            logger.info(f"   Industry: {product.get('industry', 'Unknown')}")
            if product.get('companies'):
                logger.info(f"   Companies: {', '.join(product['companies'])}")
    except Exception as e:
        logger.error(f"Research agent failed: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Main test function"""
    await test_deepseek()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\nTest interrupted by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()