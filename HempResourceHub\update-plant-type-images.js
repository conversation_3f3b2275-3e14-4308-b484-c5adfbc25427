import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

// Plant type image mappings
const plantTypeImages = {
  'Fiber Hemp': 'https://images.unsplash.com/photo-1519181245277-cffeb31da2e3?w=800&q=80', // Hemp fibers
  'Grain/Seed Hemp': 'https://images.unsplash.com/photo-1615486511262-c7b5c3f8c625?w=800&q=80', // Hemp seeds
  'Cannabinoid Hemp': 'https://images.unsplash.com/photo-1616902372305-6e60c8e9f8d8?w=800&q=80', // Hemp flowers
  'Oil Archetype': 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=800&q=80', // Hemp oil
  'Fiber Archetype': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&q=80', // Hemp rope/fiber
  'Seeds Archetype': 'https://images.unsplash.com/photo-1515694590185-73647ba02c10?w=800&q=80' // Seeds close-up
};

async function updatePlantTypeImages() {
  console.log('Updating plant type images...');
  
  for (const [name, imageUrl] of Object.entries(plantTypeImages)) {
    try {
      const { data, error } = await supabase
        .from('hemp_plant_archetypes')
        .update({ image_url: imageUrl })
        .eq('name', name);
      
      if (error) {
        console.error(`Error updating ${name}:`, error);
      } else {
        console.log(`✅ Updated ${name} with new image`);
      }
    } catch (err) {
      console.error(`Failed to update ${name}:`, err);
    }
  }
  
  console.log('Done updating plant type images!');
}

updatePlantTypeImages();