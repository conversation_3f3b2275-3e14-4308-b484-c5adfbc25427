#!/usr/bin/env python3
"""
HempQuarterz Unified CLI
Single entry point for all hemp database operations
"""

import argparse
import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from typing import List, Optional, Dict, Any

# Load environment variables FIRST
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging first
# Check for debug environment variable
log_level = logging.DEBUG if os.environ.get('DEBUG') else logging.INFO
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from lib.supabase_client import get_supabase_client
from lib.image_generation_service import ImageGenerationService, ImageProvider
from lib.monitoring_service import MonitoringService, get_system_health

# Try to import monitoring dashboard (requires curses, not available on Windows)
try:
    from lib.monitoring_dashboard import run_dashboard
    DASHBOARD_AVAILABLE = True
except ImportError:
    DASHBOARD_AVAILABLE = False
    logger.warning("Monitoring dashboard not available (requires curses library)")
    
# Import orchestrator if available (requires langgraph)
try:
    from agents.core.orchestrator import HQzOrchestrator
    ORCHESTRATOR_AVAILABLE = True
except ImportError:
    logger.warning("Orchestrator not available (requires langgraph library)")
    ORCHESTRATOR_AVAILABLE = False
    HQzOrchestrator = None

from agents.research.unified_research_agent import create_research_agent, ResearchFeatures

# Import AI provider utilities
try:
    from utils.ai_providers import get_ai_provider, MultiProviderAI
    from utils.simple_ai_wrapper import get_simple_ai_provider
except ImportError:
    logger.warning("AI providers not available - will use alternative methods")
    get_ai_provider = None
    MultiProviderAI = None
    get_simple_ai_provider = None


class HempCLI:
    """Unified CLI for HempQuarterz operations"""
    
    def __init__(self):
        self.supabase = get_supabase_client()
        self.orchestrator = None
        
    async def run_agent(self, agent_type: str, task: str, features: List[str] = None, **kwargs):
        """Run a specific agent with given task"""
        if agent_type == 'research':
            # Initialize AI provider if available
            ai_provider = None
            ai_provider_name = kwargs.get('ai_provider', 'deepseek')  # Default to DeepSeek
            
            if get_simple_ai_provider:
                try:
                    ai_provider = get_simple_ai_provider(ai_provider_name)
                    logger.info(f"Using AI provider: {ai_provider_name}")
                except Exception as e:
                    logger.warning(f"Could not initialize AI provider: {e}")
                    logger.info("Will use non-AI features (web scraping, feed monitoring)")
                    
            # Create agent with AI provider (or None)
            agent = create_research_agent(self.supabase, ai_provider=ai_provider, features=features)
            results = await agent.discover_products(task, **kwargs)
            return results
            
        elif agent_type == 'orchestrator':
            if not ORCHESTRATOR_AVAILABLE:
                raise Exception("Orchestrator not available. Install langgraph: pip install langgraph")
            if not self.orchestrator:
                self.orchestrator = HQzOrchestrator()
            # Run orchestrator task
            return await self.orchestrator.process_request(task)
            
        else:
            # Run specific agent through orchestrator
            task_data = {
                'type': agent_type,
                'description': task,
                'parameters': kwargs
            }
            return await self._run_agent_task(task_data)
            
    async def _run_agent_task(self, task_data: Dict):
        """Run a task through the agent system"""
        # Create task in database
        result = self.supabase.table('agent_tasks').insert({
            'agent_type': task_data['type'],
            'task_type': 'cli_request',
            'parameters': task_data,
            'status': 'pending',
            'created_by': 'hemp_cli'
        }).execute()
        
        if result.data:
            task_id = result.data[0]['id']
            logger.info(f"Created task {task_id}")
            
            # Wait for completion (with timeout)
            max_wait = 300  # 5 minutes
            start_time = datetime.now()
            
            while (datetime.now() - start_time).seconds < max_wait:
                # Check task status
                status_result = self.supabase.table('agent_tasks').select('*').eq('id', task_id).execute()
                
                if status_result.data:
                    task = status_result.data[0]
                    if task['status'] == 'completed':
                        return task['result']
                    elif task['status'] == 'failed':
                        raise Exception(f"Task failed: {task.get('error_message', 'Unknown error')}")
                        
                await asyncio.sleep(2)
                
            raise TimeoutError("Task timed out")
            
    async def populate_database(self, data_type: str = 'all'):
        """Populate database with initial data"""
        logger.info(f"Populating database: {data_type}")
        
        if data_type in ['all', 'products']:
            # Import and run population scripts
            from populate_supabase_db import main as populate_main
            await populate_main()
            
        if data_type in ['all', 'advanced']:
            from populate_hemp_products_advanced import populate_advanced_products
            await populate_advanced_products()
            
        logger.info("Database population complete")
        
    async def generate_images(self, product_ids: List[int] = None, provider: str = 'placeholder'):
        """Generate images for products using centralized service"""
        image_service = ImageGenerationService(self.supabase)
        
        try:
            provider_enum = ImageProvider(provider)
        except ValueError:
            logger.error(f"Invalid provider: {provider}")
            print(f"Invalid provider. Choose from: {', '.join([p.value for p in ImageProvider])}")
            return
            
        if product_ids:
            logger.info(f"Generating images for {len(product_ids)} specific products using {provider}")
            results = await image_service.generate_for_products(product_ids, provider_enum)
        else:
            logger.info(f"Generating images for all products without images using {provider}")
            results = await image_service.generate_all_missing(provider_enum)
            
        # Display results
        successful = [r for r in results if r.success]
        failed = [r for r in results if not r.success]
        
        print(f"\n=== Image Generation Results ===")
        print(f"✅ Successful: {len(successful)}")
        print(f"❌ Failed: {len(failed)}")
        
        if failed:
            print("\nFailed generations:")
            for result in failed[:5]:  # Show first 5 failures
                print(f"  - Product {result.product_id}: {result.error_message}")
                
        total_cost = sum(r.cost for r in results)
        print(f"\n💰 Total cost: ${total_cost:.4f}")
        
    async def check_image_status(self):
        """Check image generation queue status"""
        image_service = ImageGenerationService(self.supabase)
        status = await image_service.get_queue_status()
        
        print("\n=== Image Generation Status ===")
        print(f"Queue Status:")
        print(f"  ⏳ Pending: {status['queue']['pending']}")
        print(f"  🔄 Processing: {status['queue']['processing']}")
        print(f"  ✅ Completed: {status['queue']['completed']}")
        print(f"  ❌ Failed: {status['queue']['failed']}")
        
        if status['providers']:
            print(f"\nProvider Statistics:")
            for provider in status['providers']:
                print(f"  {provider['name']}:")
                print(f"    - Generated: {provider['total_generated']}")
                print(f"    - Success rate: {provider['success_rate']:.1f}%")
                print(f"    - Avg cost: ${provider['avg_cost']:.4f}")
                
    async def retry_failed_images(self, limit: int = 50):
        """Retry failed image generations"""
        image_service = ImageGenerationService(self.supabase)
        logger.info(f"Retrying up to {limit} failed image generations")
        
        results = await image_service.retry_failed(limit)
        
        successful = [r for r in results if r.success]
        print(f"\n✅ Successfully retried: {len(successful)} images")
        
    async def monitor_agents(self, format: str = 'report', live: bool = False):
        """Monitor agent status and performance"""
        if live:
            if not DASHBOARD_AVAILABLE:
                print("⚠️  Live dashboard not available on Windows.")
                print("   Using report format instead...")
                format = 'report'
            else:
                # Launch interactive dashboard
                from curses import wrapper
                wrapper(run_dashboard)
                return
            
        monitoring_service = MonitoringService(self.supabase)
        
        if format == 'report':
            # Generate and display report
            report = await monitoring_service.generate_report()
            print(report)
            
        elif format == 'json':
            # Export raw metrics as JSON
            metrics_json = await monitoring_service.export_metrics('json')
            print(metrics_json)
            
        elif format == 'prometheus':
            # Export in Prometheus format
            prometheus_metrics = await monitoring_service.export_metrics('prometheus')
            print(prometheus_metrics)
            
        else:
            # Quick health check
            health = await get_system_health()
            
            status = "✅ Healthy" if health['healthy'] else "⚠️ Issues Detected"
            print(f"\n=== System Health: {status} ===")
            print(f"Uptime: {health['uptime']:.1f}%")
            print(f"Active Agents: {health['summary']['agents_active']}")
            print(f"Pending Tasks: {health['summary']['tasks_pending']}")
            print(f"Total Products: {health['summary']['products_total']:,}")
            
            if health['alerts']:
                print("\n🚨 Active Alerts:")
                for alert in health['alerts']:
                    print(f"  - {alert['severity'].upper()}: {alert['message']}")
            
    async def merge_companies(self, dry_run: bool = True):
        """Merge duplicate companies"""
        from merge_agent_companies import merge_duplicate_companies
        
        logger.info(f"Merging duplicate companies (dry_run={dry_run})")
        results = await merge_duplicate_companies(dry_run=dry_run)
        
        print(f"\nFound {len(results)} potential merges")
        for merge in results:
            print(f"- {merge['name']}: {len(merge['duplicates'])} duplicates")
            
    async def validate_data(self):
        """Validate database integrity"""
        from validate_hemp_data import validate_all_data
        
        logger.info("Validating database integrity")
        results = await validate_all_data()
        
        print("\n=== Validation Results ===")
        for check, result in results.items():
            status = "✅ Passed" if result['passed'] else "❌ Failed"
            print(f"{check}: {status}")
            if not result['passed']:
                print(f"  Issues: {result['issues']}")
                
    async def export_data(self, format: str = 'json', output_dir: str = './exports'):
        """Export database data"""
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        tables = [
            'hemp_plant_archetypes',
            'plant_parts',
            'industries',
            'industry_sub_categories',
            'uses_products',
            'hemp_companies',
            'hemp_company_products'
        ]
        
        for table in tables:
            logger.info(f"Exporting {table}")
            result = self.supabase.table(table).select('*').execute()
            
            if format == 'json':
                filepath = os.path.join(output_dir, f"{table}_{timestamp}.json")
                with open(filepath, 'w') as f:
                    json.dump(result.data, f, indent=2)
                    
            elif format == 'csv':
                import csv
                filepath = os.path.join(output_dir, f"{table}_{timestamp}.csv")
                
                if result.data:
                    with open(filepath, 'w', newline='') as f:
                        writer = csv.DictWriter(f, fieldnames=result.data[0].keys())
                        writer.writeheader()
                        writer.writerows(result.data)
                        
        logger.info(f"Export complete: {output_dir}")


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="HempQuarterz Unified CLI - Manage all hemp database operations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run research agent
  hemp_cli agent research "hemp construction materials" --features company image

  # Run orchestrator
  hemp_cli agent orchestrator "Find and analyze hemp textile innovations"

  # Generate images
  hemp_cli images generate --provider stable-diffusion

  # Monitor agents
  hemp_cli monitor

  # Populate database
  hemp_cli db populate --type all

  # Export data
  hemp_cli db export --format json --output ./exports
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Command to run')
    
    # Agent commands
    agent_parser = subparsers.add_parser('agent', help='Run AI agents')
    agent_parser.add_argument('type', choices=['research', 'content', 'seo', 'outreach', 'monetization', 'compliance', 'orchestrator'])
    agent_parser.add_argument('task', help='Task description or search query')
    agent_parser.add_argument('--features', nargs='+', help='Features for research agent (basic, company, image, deep, web, feed, trend)')
    agent_parser.add_argument('--max-results', type=int, default=10, help='Maximum results')
    agent_parser.add_argument('--ai-provider', choices=['deepseek', 'openai', 'auto'], default='deepseek', 
                            help='AI provider to use (default: deepseek)')
    
    # Image commands
    image_parser = subparsers.add_parser('images', help='Manage product images')
    image_subparsers = image_parser.add_subparsers(dest='image_command')
    
    gen_parser = image_subparsers.add_parser('generate', help='Generate images')
    gen_parser.add_argument('--products', nargs='+', type=int, help='Product IDs')
    gen_parser.add_argument('--provider', default='placeholder', 
                          choices=['placeholder', 'stable-diffusion', 'dall-e', 'imagen-3', 'midjourney', 'replicate', 'together-ai'])
    
    status_parser = image_subparsers.add_parser('status', help='Check image generation status')
    
    retry_parser = image_subparsers.add_parser('retry', help='Retry failed generations')
    retry_parser.add_argument('--limit', type=int, default=50, help='Maximum items to retry')
    
    # Database commands
    db_parser = subparsers.add_parser('db', help='Database operations')
    db_subparsers = db_parser.add_subparsers(dest='db_command')
    
    pop_parser = db_subparsers.add_parser('populate', help='Populate database')
    pop_parser.add_argument('--type', default='all', choices=['all', 'products', 'advanced'])
    
    exp_parser = db_subparsers.add_parser('export', help='Export data')
    exp_parser.add_argument('--format', default='json', choices=['json', 'csv'])
    exp_parser.add_argument('--output', default='./exports', help='Output directory')
    
    val_parser = db_subparsers.add_parser('validate', help='Validate data integrity')
    
    merge_parser = db_subparsers.add_parser('merge-companies', help='Merge duplicate companies')
    merge_parser.add_argument('--execute', action='store_true', help='Actually perform merges')
    
    # Monitoring
    mon_parser = subparsers.add_parser('monitor', help='Monitor system health and performance')
    mon_parser.add_argument('--format', choices=['report', 'json', 'prometheus', 'health'], 
                          default='health', help='Output format')
    mon_parser.add_argument('--live', action='store_true', help='Launch interactive dashboard')
    mon_parser.add_argument('--export', help='Export metrics to file')
    
    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    # Initialize CLI
    cli = HempCLI()
    
    # Execute command
    try:
        if args.command == 'agent':
            results = asyncio.run(cli.run_agent(
                args.type,
                args.task,
                features=args.features,
                max_results=args.max_results,
                ai_provider=args.ai_provider
            ))
            
            print(f"\n=== Results ===")
            if isinstance(results, list):
                for i, result in enumerate(results, 1):
                    print(f"\n{i}. {result.get('name', 'Unknown')}")
                    if result.get('description'):
                        print(f"   {result['description'][:100]}...")
            else:
                print(json.dumps(results, indent=2))
                
        elif args.command == 'images':
            if args.image_command == 'generate':
                asyncio.run(cli.generate_images(args.products, args.provider))
            elif args.image_command == 'status':
                asyncio.run(cli.check_image_status())
            elif args.image_command == 'retry':
                asyncio.run(cli.retry_failed_images(args.limit))
            
        elif args.command == 'db':
            if args.db_command == 'populate':
                asyncio.run(cli.populate_database(args.type))
            elif args.db_command == 'export':
                asyncio.run(cli.export_data(args.format, args.output))
            elif args.db_command == 'validate':
                asyncio.run(cli.validate_data())
            elif args.db_command == 'merge-companies':
                asyncio.run(cli.merge_companies(dry_run=not args.execute))
                
        elif args.command == 'monitor':
            result = asyncio.run(cli.monitor_agents(format=args.format, live=args.live))
            
            # Export if requested
            if args.export and not args.live:
                with open(args.export, 'w') as f:
                    if args.format == 'json':
                        monitoring_service = MonitoringService(cli.supabase)
                        metrics = asyncio.run(monitoring_service.export_metrics('json'))
                        f.write(metrics)
                    else:
                        f.write(str(result) if result else '')
                print(f"\nMetrics exported to: {args.export}")
            
    except Exception as e:
        logger.error(f"Command failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()