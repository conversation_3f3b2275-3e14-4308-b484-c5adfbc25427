import { Helmet } from "react-helmet";
import { <PERSON> } from "wouter";
import { useState, useEffect, useRef } from "react";
import Breadcrumb from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Calendar,
  Scroll,
  Ship,
  FileText,
  Gavel,
  ChevronDown,
  ChevronUp,
  Clock,
  MapPin,
  Users,
  Lightbulb
} from "lucide-react";
import HempEcosystemImage from "@/assets/hemp-ecosystem.webp";

const AboutPage = () => {
  const [expandedTimelineItem, setExpandedTimelineItem] = useState<number | null>(null);
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const timelineRefs = useRef<(HTMLDivElement | null)[]>([]);

  const timelineData = [
    {
      id: 1,
      year: "8000 BCE",
      title: "Ancient Beginnings",
      icon: <Scroll className="h-5 w-5" />,
      location: "Mesopotamia (Present-day Iraq)",
      shortDescription: "The earliest tangible evidence of hemp use in ancient civilizations.",
      fullDescription: "Archaeological findings in ancient Mesopotamia reveal that villagers employed hemp cord in their pottery, signifying its early importance for fundamental technologies. This marks the beginning of humanity's relationship with this versatile plant.",
      significance: "First recorded use of hemp in human civilization",
      color: "from-amber-500 to-orange-500",
      bgColor: "bg-amber-500/20",
      borderColor: "border-amber-500/50"
    },
    {
      id: 2,
      year: "150 BCE",
      title: "Paper Innovation",
      icon: <FileText className="h-5 w-5" />,
      location: "China",
      shortDescription: "Revolutionary invention of paper made entirely from hemp fibers.",
      fullDescription: "A pivotal moment in the history of communication occurred with the groundbreaking invention of paper made entirely from hemp fibers. This innovation revolutionized record-keeping and the dissemination of knowledge across civilizations, laying the foundation for modern information sharing.",
      significance: "Revolutionized communication and knowledge preservation",
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-500/20",
      borderColor: "border-blue-500/50"
    },
    {
      id: 3,
      year: "1492",
      title: "Age of Exploration",
      icon: <Ship className="h-5 w-5" />,
      location: "Atlantic Ocean",
      shortDescription: "Christopher Columbus's ships relied heavily on hemp for sails and rigging.",
      fullDescription: "During the Age of Exploration, Christopher Columbus's ships, which famously embarked on voyages to North America, relied heavily on hemp for their sails and rigging. This underscores hemp's crucial role in maritime travel and trade during this transformative period in global history.",
      significance: "Enabled global exploration and trade expansion",
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-500/20",
      borderColor: "border-green-500/50"
    },
    {
      id: 4,
      year: "1776",
      title: "American History",
      icon: <Gavel className="h-5 w-5" />,
      location: "Philadelphia, USA",
      shortDescription: "The Declaration of Independence was drafted on hemp paper.",
      fullDescription: "The Declaration of Independence, a foundational document in American history, was drafted on hemp paper, further symbolizing the plant's intimate connection to the birth of the nation. Prominent figures including George Washington, Thomas Jefferson, and John Adams were all hemp farmers.",
      significance: "Integral to the founding of the United States",
      color: "from-red-500 to-pink-500",
      bgColor: "bg-red-500/20",
      borderColor: "border-red-500/50"
    },
    {
      id: 5,
      year: "2018",
      title: "Modern Revival",
      icon: <Lightbulb className="h-5 w-5" />,
      location: "United States",
      shortDescription: "The 2018 Farm Bill legalized hemp cultivation in the United States.",
      fullDescription: "After decades of prohibition, the 2018 Farm Bill effectively legalized hemp cultivation by removing it from the Controlled Substances Act. This has catalyzed a significant resurgence in hemp farming, research, and product development across multiple industries.",
      significance: "Sparked the modern hemp industry renaissance",
      color: "from-purple-500 to-violet-500",
      bgColor: "bg-purple-500/20",
      borderColor: "border-purple-500/50"
    }
  ];

  const toggleTimelineItem = (id: number) => {
    setExpandedTimelineItem(expandedTimelineItem === id ? null : id);
  };

  // Intersection Observer for scroll animations
  useEffect(() => {
    const observers: IntersectionObserver[] = [];

    timelineRefs.current.forEach((ref, index) => {
      if (ref) {
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                // Add a staggered delay based on index
                setTimeout(() => {
                  setVisibleItems(prev => new Set([...prev, index + 1]));
                }, index * 200); // 200ms delay between each item
              }
            });
          },
          {
            threshold: 0.2, // Trigger when 20% of the element is visible
            rootMargin: '-50px 0px -50px 0px' // Trigger slightly before entering viewport
          }
        );

        observer.observe(ref);
        observers.push(observer);
      }
    });

    return () => {
      observers.forEach(observer => observer.disconnect());
    };
  }, []);

  // Set ref for timeline items
  const setTimelineRef = (index: number) => (el: HTMLDivElement | null) => {
    timelineRefs.current[index] = el;
  };

  // Add a subtle pulse animation to visible timeline dots
  const getTimelineDotClasses = (item: any, index: number) => {
    const baseClasses = `w-12 h-12 rounded-full bg-gradient-to-r ${item.color} flex items-center justify-center shadow-lg shadow-black/50 border-4 border-gray-900 transition-all duration-700 hover:scale-110`;

    if (visibleItems.has(item.id)) {
      return `${baseClasses} scale-100 rotate-0 animate-pulse`;
    }

    return `${baseClasses} scale-0 rotate-180`;
  };

  return (
    <>
      <Helmet>
        <title>About Industrial Hemp | HempQuarterz</title>
        <meta
          name="description"
          content="Learn about the history, uses, and versatility of industrial hemp as a sustainable resource for numerous applications across different industries."
        />
      </Helmet>

      {/* Breadcrumb */}
      <div className="bg-black/20 backdrop-blur-sm py-6 border-b border-gray-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Breadcrumb
            items={[{ label: "Home", href: "/" }, { label: "About" }]}
          />
        </div>
      </div>

      {/* Hero section */}
      <div className="bg-gradient-to-b from-black/20 to-black/30 backdrop-blur-sm py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-green-400">
            About Industrial Hemp
          </h1>
          <p className="mt-4 text-lg text-gray-100 max-w-3xl mx-auto">
            Discover the remarkable history and diverse applications of one of
            the world's most versatile and sustainable crops.
          </p>
        </div>
      </div>

      {/* Executive Summary */}
      <div className="bg-black/20 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="prose prose-lg prose-invert max-w-none">
            <h2 className="text-2xl sm:text-3xl font-heading font-semibold text-green-400 mb-6">
              Executive Summary
            </h2>
            <p className="text-gray-100">
              Industrial hemp, a crop with a rich history dating back millennia,
              stands at the cusp of a significant resurgence due to its
              remarkable versatility. From its foundational uses in ancient
              civilizations for textiles and paper to its burgeoning
              applications in modern industries like construction, automotive,
              and pharmaceuticals, hemp demonstrates an extraordinary capacity
              to adapt and contribute across diverse sectors.
            </p>
            <p className="text-gray-100">
              Driven by a growing global emphasis on sustainability, coupled
              with advancements in processing technologies and increasing market
              demand for natural alternatives, industrial hemp is poised to play
              an increasingly vital role in the future economy.
            </p>
          </div>
        </div>
      </div>

      {/* Introduction with side image */}
      <div className="bg-black/30 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="prose prose-lg prose-invert">
              <h2 className="text-2xl sm:text-3xl font-heading font-semibold text-green-400 mb-6">
                Introduction
              </h2>
              <p className="text-gray-100">
                Industrial hemp, scientifically classified as a variety of the
                Cannabis sativa plant species, is distinguished primarily by its
                exceptionally low concentration of tetrahydrocannabinol (THC),
                the psychoactive compound predominantly associated with
                marijuana. Typically, this THC content does not exceed 0.3% on a
                dry weight basis, a crucial threshold that differentiates it
                both legally and practically from its psychoactive counterpart.
              </p>
              <p className="text-gray-100">
                The 2018 Farm Bill in the United States enshrined this THC limit
                into law, effectively legalizing hemp and removing it from the
                purview of the Controlled Substances Act. This legislative
                landmark has catalyzed widespread opportunities for hemp
                cultivation, processing, and commercialization, areas previously
                constrained by the plant's association with marijuana.
              </p>
            </div>
            <div className="rounded-lg overflow-hidden shadow-lg shadow-black/50 border border-green-500/30">
              <img
                src={HempEcosystemImage}
                alt="Comprehensive hemp ecosystem illustration showing various applications and uses"
                className="w-full h-auto object-cover"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Interactive Timeline */}
      <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-green-900/20 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-green-500/10 border border-green-500/20 rounded-full px-4 py-2 mb-6">
              <Clock className="h-4 w-4 text-green-400" />
              <span className="text-sm text-green-400 font-medium">Historical Timeline</span>
            </div>

            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-heading font-bold text-white mb-6">
              A Journey Through
              <span className="block text-green-400 drop-shadow-[0_0_8px_rgba(74,222,128,0.3)]">
                Hemp History
              </span>
            </h2>

            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Explore the remarkable journey of hemp through human civilization,
              from ancient pottery to modern industrial applications.
            </p>
          </div>

          {/* Interactive Timeline */}
          <div className="relative">
            {/* Timeline line with progressive animation */}
            <div className="absolute left-8 md:left-1/2 md:-ml-0.5 top-0 bottom-0 w-1 bg-gray-700"></div>
            <div
              className="absolute left-8 md:left-1/2 md:-ml-0.5 top-0 w-1 bg-gradient-to-b from-green-400 via-blue-400 to-purple-400 transition-all duration-2000 ease-out"
              style={{
                height: `${Math.min((visibleItems.size / timelineData.length) * 100, 100)}%`
              }}
            ></div>

            <div className="space-y-12">
              {timelineData.map((item, index) => (
                <div
                  key={item.id}
                  ref={setTimelineRef(index)}
                  className={`relative flex items-center transition-all duration-1000 ${
                    index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                  } ${
                    visibleItems.has(item.id)
                      ? 'opacity-100 translate-y-0'
                      : 'opacity-0 translate-y-8'
                  }`}
                >
                  {/* Timeline dot */}
                  <div className="absolute left-8 md:left-1/2 md:-ml-6 z-10">
                    <div className={getTimelineDotClasses(item, index)}>
                      <div className="text-white">
                        {item.icon}
                      </div>
                    </div>
                  </div>

                  {/* Content card */}
                  <div className={`w-full md:w-5/12 ml-20 md:ml-0 transition-all duration-1000 ${
                    index % 2 === 0 ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'
                  } ${
                    visibleItems.has(item.id)
                      ? 'opacity-100 translate-x-0'
                      : `opacity-0 ${
                          index % 2 === 0
                            ? 'md:-translate-x-8 translate-x-0'
                            : 'md:translate-x-8 translate-x-0'
                        }`
                  }`}>
                    <Card
                      className={`group cursor-pointer transition-all duration-300 hover:shadow-2xl hover:-translate-y-1 bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:${item.borderColor} ${
                        expandedTimelineItem === item.id ? item.borderColor : ''
                      }`}
                      onClick={() => toggleTimelineItem(item.id)}
                    >
                      <CardHeader className={`${item.bgColor} backdrop-blur-sm`}>
                        <div className="flex items-center justify-between">
                          <div>
                            <Badge className={`${item.bgColor} ${item.borderColor} text-white mb-2`}>
                              {item.year}
                            </Badge>
                            <CardTitle className="text-xl font-heading font-bold text-white group-hover:text-green-400 transition-colors">
                              {item.title}
                            </CardTitle>
                            <CardDescription className="flex items-center gap-1 text-gray-300 mt-1">
                              <MapPin className="h-3 w-3" />
                              {item.location}
                            </CardDescription>
                          </div>
                          <div className="text-gray-400 group-hover:text-white transition-colors">
                            {expandedTimelineItem === item.id ? (
                              <ChevronUp className="h-5 w-5" />
                            ) : (
                              <ChevronDown className="h-5 w-5" />
                            )}
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="p-6">
                        <p className="text-gray-300 leading-relaxed mb-4">
                          {item.shortDescription}
                        </p>

                        {expandedTimelineItem === item.id && (
                          <div className="space-y-4 animate-in slide-in-from-top-2 duration-300">
                            <div className="border-t border-gray-800/50 pt-4">
                              <p className="text-gray-300 leading-relaxed mb-4">
                                {item.fullDescription}
                              </p>

                              <div className={`${item.bgColor} backdrop-blur-sm rounded-lg p-4 border ${item.borderColor}`}>
                                <div className="flex items-center gap-2 mb-2">
                                  <Users className="h-4 w-4 text-white" />
                                  <span className="text-sm font-medium text-white">Historical Significance</span>
                                </div>
                                <p className="text-sm text-gray-200">
                                  {item.significance}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Modern Applications */}
      <div className="bg-black/30 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-heading font-semibold text-green-400 mb-12 text-center">
            The Multifaceted Applications of Modern Industrial Hemp
          </h2>

          <div className="prose prose-lg prose-invert max-w-none mb-10">
            <p className="text-gray-100">
              The industrial hemp stalk is a versatile resource, yielding two
              primary types of fiber: the outer bast fibers and the inner hurd.
              Bast fibers, which constitute approximately 14% of the plant's
              material, are long, strong, and prized for their use in textiles.
              Reaching lengths of up to 5 meters, these fibers are characterized
              by their durability, absorbency, and even antimicrobial
              properties. The inner woody core, known as hurd or shives,
              comprises shorter fibers that are lighter, dust-free, and highly
              absorbent, making them suitable for a wide array of other
              applications.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-6 border-t-4 border-green-500">
              <h3 className="text-xl font-heading font-semibold text-green-400 mb-4">
                Textiles & Fabrics
              </h3>
              <p className="text-gray-100">
                Hemp fibers are transformed into durable textiles for clothing,
                accessories, and home goods. Hemp fabric is naturally resistant
                to mold, UV light, and offers 4x the strength of cotton.
              </p>
            </div>

            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-6 border-t-4 border-green-500">
              <h3 className="text-xl font-heading font-semibold text-green-400 mb-4">
                Construction Materials
              </h3>
              <p className="text-gray-100">
                Hempcrete, made from hemp hurd mixed with lime, creates a
                lightweight insulating material that is fire-resistant,
                pest-resistant, and carbon-negative through carbon
                sequestration.
              </p>
            </div>

            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-6 border-t-4 border-green-500">
              <h3 className="text-xl font-heading font-semibold text-green-400 mb-4">
                Food & Nutrition
              </h3>
              <p className="text-gray-100">
                Hemp seeds are rich in protein, essential fatty acids, and
                numerous vitamins and minerals, making them a nutritious
                addition to diets in the form of oils, protein powders, and
                whole foods.
              </p>
            </div>

            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-6 border-t-4 border-green-500">
              <h3 className="text-xl font-heading font-semibold text-green-400 mb-4">
                Bioplastics
              </h3>
              <p className="text-gray-100">
                Hemp-based bioplastics offer a biodegradable and renewable
                alternative to petroleum-based plastics, with applications
                ranging from packaging materials to automotive components.
              </p>
            </div>

            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-6 border-t-4 border-green-500">
              <h3 className="text-xl font-heading font-semibold text-green-400 mb-4">
                Biofuels
              </h3>
              <p className="text-gray-100">
                Hemp biomass can be converted into various biofuels including
                biodiesel and ethanol, offering carbon-neutral alternatives to
                fossil fuels with high energy efficiency.
              </p>
            </div>

            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-6 border-t-4 border-green-500">
              <h3 className="text-xl font-heading font-semibold text-green-400 mb-4">
                Paper Products
              </h3>
              <p className="text-gray-100">
                Hemp paper requires fewer chemicals for processing than wood
                pulp, can be recycled more times, and grows much faster than
                trees, making it a sustainable option for the paper industry.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="bg-gradient-to-b from-black/30 to-black/40 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl sm:text-3xl font-heading font-semibold text-green-400 mb-4">
            Explore Our Hemp Database
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto mb-8">
            Discover the remarkable versatility of industrial hemp by exploring
            our comprehensive database of hemp applications, organized by plant
            parts and industries.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/plant-parts">
              <Button className="bg-green-600 hover:bg-green-700 text-white">
                Explore Plant Parts
              </Button>
            </Link>
            <Link href="/industries">
              <Button
                variant="outline"
                className="border-green-600 text-green-700 hover:bg-green-50"
              >
                Browse Industries
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default AboutPage;
