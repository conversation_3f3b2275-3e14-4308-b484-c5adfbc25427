import { ProductDiscoveryAssistant } from './product-discovery-assistant';
import { supabase } from '@/lib/supabase-client';

export interface DiscoveryStrategy {
  name: string;
  description: string;
  execute: (assistant: ProductDiscoveryAssistant) => Promise<any>;
}

// Pre-defined discovery strategies
export const discoveryStrategies: DiscoveryStrategy[] = [
  {
    name: 'Emerging Technologies',
    description: 'Find products using cutting-edge hemp processing technologies',
    execute: async (assistant) => {
      return assistant.discoverProducts({
        keywords: ['nanotechnology', 'biocomposite', '3D printing', 'graphene', 'supercapacitor'],
        limit: 10
      });
    }
  },
  {
    name: 'Sustainable Construction',
    description: 'Discover hemp-based construction and building materials',
    execute: async (assistant) => {
      return assistant.discoverProducts({
        category: 'construction materials',
        plantPart: 'hurds',
        industry: 'construction',
        keywords: ['insulation', 'hempcrete', 'panels', 'blocks'],
        limit: 10
      });
    }
  },
  {
    name: 'Food & Beverages',
    description: 'Find new hemp food products and ingredients',
    execute: async (assistant) => {
      return assistant.discoverProducts({
        plantPart: 'seeds',
        industry: 'food',
        keywords: ['protein', 'oil', 'flour', 'milk', 'snacks'],
        limit: 10
      });
    }
  },
  {
    name: 'Textiles & Fashion',
    description: 'Discover innovative hemp textile products',
    execute: async (assistant) => {
      return assistant.discoverProducts({
        plantPart: 'fiber',
        industry: 'textiles',
        keywords: ['fabric', 'clothing', 'denim', 'shoes', 'accessories'],
        limit: 10
      });
    }
  },
  {
    name: 'Bioplastics',
    description: 'Find hemp-based plastic alternatives',
    execute: async (assistant) => {
      return assistant.discoverProducts({
        category: 'bioplastics',
        keywords: ['biodegradable', 'composite', 'packaging', 'automotive'],
        limit: 10
      });
    }
  }
];

// Source-based discovery strategies
export const sourceStrategies = [
  {
    name: 'Hemp Industry Daily',
    url: 'https://hempindustrydaily.com/category/products/',
    description: 'Latest product announcements from Hemp Industry Daily'
  },
  {
    name: 'Vote Hemp',
    url: 'https://www.votehemp.com/hemp-products/',
    description: 'Comprehensive hemp product directory'
  },
  {
    name: 'EIHA Products',
    url: 'https://eiha.org/hemp-products/',
    description: 'European Industrial Hemp Association product showcase'
  }
];

export class AutomatedDiscoveryService {
  private assistant: ProductDiscoveryAssistant;
  private isRunning = false;
  private discoveryLog: any[] = [];

  constructor() {
    this.assistant = new ProductDiscoveryAssistant();
  }

  // Run all discovery strategies
  async runFullDiscovery() {
    this.isRunning = true;
    const results = {
      totalDiscovered: 0,
      totalSaved: 0,
      byStrategy: {} as Record<string, any>
    };

    // Run criteria-based strategies
    for (const strategy of discoveryStrategies) {
      console.log(`Running strategy: ${strategy.name}`);
      
      try {
        const products = await strategy.execute(this.assistant);
        const saved = await this.saveDiscoveredProducts(products, strategy.name);
        
        results.byStrategy[strategy.name] = {
          discovered: products.length,
          saved: saved
        };
        results.totalDiscovered += products.length;
        results.totalSaved += saved;

        // Log the discovery
        await this.logDiscovery({
          strategy: strategy.name,
          discovered: products.length,
          saved: saved,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error(`Strategy ${strategy.name} failed:`, error);
      }

      // Add delay to avoid rate limiting
      await this.delay(5000);
    }

    // Run source-based strategies
    for (const source of sourceStrategies) {
      console.log(`Analyzing source: ${source.name}`);
      
      try {
        const products = await this.assistant.discoverFromSource(source.url);
        const saved = await this.saveDiscoveredProducts(products, source.name);
        
        results.byStrategy[source.name] = {
          discovered: products.length,
          saved: saved
        };
        results.totalDiscovered += products.length;
        results.totalSaved += saved;

      } catch (error) {
        console.error(`Source ${source.name} failed:`, error);
      }

      await this.delay(5000);
    }

    this.isRunning = false;
    return results;
  }

  // Save discovered products with deduplication
  private async saveDiscoveredProducts(products: any[], source: string): Promise<number> {
    let savedCount = 0;

    for (const product of products) {
      // Check uniqueness before saving
      const isUnique = await this.assistant.validateUniqueness(product);
      
      if (isUnique) {
        const result = await this.assistant.saveProduct(product);
        if (result) {
          savedCount++;
          console.log(`Saved: ${product.name} (from ${source})`);
        }
      } else {
        console.log(`Skipped duplicate: ${product.name}`);
      }
    }

    return savedCount;
  }

  // Log discovery activity to database
  private async logDiscovery(logEntry: any) {
    this.discoveryLog.push(logEntry);
    
    // You could also save to a database table
    await supabase
      .from('discovery_logs')
      .insert({
        ...logEntry,
        service: 'automated_discovery'
      });
  }

  // Helper delay function
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get discovery statistics
  async getDiscoveryStats() {
    const { data: productCount } = await supabase
      .from('uses_products')
      .select('count', { count: 'exact' });

    const { data: recentProducts } = await supabase
      .from('uses_products')
      .select('id, name, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    return {
      totalProducts: productCount?.[0]?.count || 0,
      recentProducts: recentProducts || [],
      discoveryLog: this.discoveryLog,
      isRunning: this.isRunning
    };
  }

  // Schedule periodic discovery (to be called from a cron job or interval)
  async scheduleDiscovery(intervalHours: number = 24) {
    setInterval(async () => {
      if (!this.isRunning) {
        console.log('Starting scheduled discovery...');
        await this.runFullDiscovery();
      }
    }, intervalHours * 60 * 60 * 1000);
  }
}