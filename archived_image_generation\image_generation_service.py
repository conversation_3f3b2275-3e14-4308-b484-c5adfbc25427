#!/usr/bin/env python3
"""
Centralized Image Generation Service
All image generation goes through this service which uses the Edge Function API
"""

import asyncio
import json
import logging
import os
from typing import List, Dict, Optional, Any
from datetime import datetime
import aiohttp
from dataclasses import dataclass
from enum import Enum

from .supabase_client import get_supabase_client

logger = logging.getLogger(__name__)


class ImageProvider(Enum):
    """Available image generation providers"""
    PLACEHOLDER = "placeholder"
    STABLE_DIFFUSION = "stable-diffusion"
    DALL_E = "dall-e"
    IMAGEN_3 = "imagen-3"
    MIDJOURNEY = "midjourney"
    REPLICATE = "replicate"
    TOGETHER_AI = "together-ai"


@dataclass
class ImageGenerationRequest:
    """Request for image generation"""
    product_id: int
    prompt: str
    provider: ImageProvider = ImageProvider.PLACEHOLDER
    priority: int = 5
    metadata: Dict[str, Any] = None
    

@dataclass
class ImageGenerationResult:
    """Result of image generation"""
    product_id: int
    image_url: str
    provider: str
    cost: float
    duration_seconds: float
    success: bool
    error_message: Optional[str] = None


class ImageGenerationService:
    """
    Centralized service for all image generation
    Routes all requests through the Supabase Edge Function
    """
    
    def __init__(self, supabase_client=None):
        self.supabase = supabase_client or get_supabase_client()
        self.edge_function_url = f"{os.environ['SUPABASE_URL']}/functions/v1/hemp-image-generator"
        self.anon_key = os.environ['SUPABASE_ANON_KEY']
        
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """Generate a single image through the Edge Function"""
        # Queue the request
        queue_id = await self._queue_request(request)
        
        # Trigger Edge Function
        await self._trigger_edge_function()
        
        # Wait for completion
        result = await self._wait_for_completion(queue_id, request.product_id)
        
        return result
        
    async def generate_batch(self, requests: List[ImageGenerationRequest]) -> List[ImageGenerationResult]:
        """Generate multiple images in batch"""
        # Queue all requests
        queue_ids = []
        for request in requests:
            queue_id = await self._queue_request(request)
            queue_ids.append((queue_id, request.product_id))
            
        # Trigger Edge Function once for the batch
        await self._trigger_edge_function()
        
        # Wait for all completions
        results = []
        for queue_id, product_id in queue_ids:
            result = await self._wait_for_completion(queue_id, product_id)
            results.append(result)
            
        return results
        
    async def generate_for_products(self, product_ids: List[int], provider: ImageProvider = ImageProvider.PLACEHOLDER) -> List[ImageGenerationResult]:
        """Generate images for specific products"""
        # Get product details
        products_result = self.supabase.table('uses_products').select('*').in_('id', product_ids).execute()
        
        if not products_result.data:
            logger.warning(f"No products found for IDs: {product_ids}")
            return []
            
        # Create requests
        requests = []
        for product in products_result.data:
            prompt = self._generate_prompt(product)
            request = ImageGenerationRequest(
                product_id=product['id'],
                prompt=prompt,
                provider=provider,
                metadata={'product_name': product['name']}
            )
            requests.append(request)
            
        # Generate in batch
        return await self.generate_batch(requests)
        
    async def generate_all_missing(self, provider: ImageProvider = ImageProvider.PLACEHOLDER, limit: int = 100) -> List[ImageGenerationResult]:
        """Generate images for all products without images"""
        # Get products without images
        result = self.supabase.table('uses_products').select('id').is_('image_url', 'null').limit(limit).execute()
        
        if not result.data:
            logger.info("No products without images found")
            return []
            
        product_ids = [p['id'] for p in result.data]
        logger.info(f"Found {len(product_ids)} products without images")
        
        return await self.generate_for_products(product_ids, provider)
        
    async def regenerate_image(self, product_id: int, provider: Optional[ImageProvider] = None) -> ImageGenerationResult:
        """Regenerate image for a specific product"""
        # Get product details
        product_result = self.supabase.table('uses_products').select('*').eq('id', product_id).execute()
        
        if not product_result.data:
            raise ValueError(f"Product {product_id} not found")
            
        product = product_result.data[0]
        
        # Use specified provider or auto-select
        if not provider:
            provider = await self._select_best_provider()
            
        # Generate new prompt
        prompt = self._generate_prompt(product)
        
        request = ImageGenerationRequest(
            product_id=product_id,
            prompt=prompt,
            provider=provider,
            priority=3  # Higher priority for regeneration
        )
        
        return await self.generate_image(request)
        
    async def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status"""
        # Get queue stats
        pending_result = self.supabase.table('image_generation_queue').select('count').eq('status', 'pending').execute()
        processing_result = self.supabase.table('image_generation_queue').select('count').eq('status', 'processing').execute()
        completed_result = self.supabase.table('image_generation_queue').select('count').eq('status', 'completed').execute()
        failed_result = self.supabase.table('image_generation_queue').select('count').eq('status', 'failed').execute()
        
        # Get provider stats
        provider_stats_result = self.supabase.rpc('get_provider_stats').execute()
        
        return {
            'queue': {
                'pending': len(pending_result.data) if pending_result.data else 0,
                'processing': len(processing_result.data) if processing_result.data else 0,
                'completed': len(completed_result.data) if completed_result.data else 0,
                'failed': len(failed_result.data) if failed_result.data else 0
            },
            'providers': provider_stats_result.data if provider_stats_result.data else []
        }
        
    async def retry_failed(self, limit: int = 50) -> List[ImageGenerationResult]:
        """Retry failed image generations"""
        # Get failed items
        failed_result = self.supabase.table('image_generation_queue').select('*').eq('status', 'failed').lt('attempts', 3).limit(limit).execute()
        
        if not failed_result.data:
            logger.info("No failed items to retry")
            return []
            
        # Update status to retry
        failed_ids = [item['id'] for item in failed_result.data]
        self.supabase.table('image_generation_queue').update({'status': 'retry'}).in_('id', failed_ids).execute()
        
        # Trigger Edge Function
        await self._trigger_edge_function()
        
        # Wait for completions
        results = []
        for item in failed_result.data:
            result = await self._wait_for_completion(item['id'], item['product_id'])
            results.append(result)
            
        return results
        
    async def _queue_request(self, request: ImageGenerationRequest) -> int:
        """Queue an image generation request"""
        queue_data = {
            'product_id': request.product_id,
            'prompt': request.prompt,
            'provider': request.provider.value,
            'priority': request.priority,
            'status': 'pending',
            'metadata': request.metadata or {},
            'created_by': 'image_generation_service'
        }
        
        result = self.supabase.table('image_generation_queue').insert(queue_data).execute()
        
        if not result.data:
            raise Exception("Failed to queue image generation request")
            
        return result.data[0]['id']
        
    async def _trigger_edge_function(self):
        """Trigger the Edge Function to process queue"""
        headers = {
            'Authorization': f'Bearer {self.anon_key}',
            'Content-Type': 'application/json'
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    self.edge_function_url,
                    headers=headers,
                    json={'action': 'process_queue'}
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Edge Function error: {error_text}")
                    else:
                        result = await response.json()
                        logger.info(f"Edge Function triggered: {result}")
                        
            except Exception as e:
                logger.error(f"Failed to trigger Edge Function: {e}")
                
    async def _wait_for_completion(self, queue_id: int, product_id: int, timeout_seconds: int = 300) -> ImageGenerationResult:
        """Wait for image generation to complete"""
        start_time = datetime.now()
        
        while (datetime.now() - start_time).seconds < timeout_seconds:
            # Check queue item status
            queue_result = self.supabase.table('image_generation_queue').select('*').eq('id', queue_id).execute()
            
            if not queue_result.data:
                raise Exception(f"Queue item {queue_id} not found")
                
            item = queue_result.data[0]
            
            if item['status'] == 'completed':
                # Get product to verify image URL
                product_result = self.supabase.table('uses_products').select('image_url').eq('id', product_id).execute()
                
                if product_result.data and product_result.data[0]['image_url']:
                    return ImageGenerationResult(
                        product_id=product_id,
                        image_url=product_result.data[0]['image_url'],
                        provider=item['provider'],
                        cost=item.get('cost', 0),
                        duration_seconds=item.get('processing_duration_seconds', 0),
                        success=True
                    )
                    
            elif item['status'] == 'failed':
                return ImageGenerationResult(
                    product_id=product_id,
                    image_url='',
                    provider=item['provider'],
                    cost=0,
                    duration_seconds=0,
                    success=False,
                    error_message=item.get('error_message', 'Unknown error')
                )
                
            # Wait before checking again
            await asyncio.sleep(2)
            
        # Timeout
        return ImageGenerationResult(
            product_id=product_id,
            image_url='',
            provider='unknown',
            cost=0,
            duration_seconds=timeout_seconds,
            success=False,
            error_message='Generation timed out'
        )
        
    def _generate_prompt(self, product: Dict) -> str:
        """Generate optimized prompt for product"""
        name = product.get('name', 'hemp product')
        description = product.get('description', '')
        plant_part = product.get('plant_part', 'hemp')
        
        # Base prompt
        prompt = f"Professional product photography of {name}"
        
        # Add context based on plant part
        part_contexts = {
            'fiber': ", textile or fabric material, natural fibers, weave texture visible",
            'seeds': ", hemp seeds in a bowl or package, food product, nutritional",
            'oil': ", hemp oil in glass bottle, amber or green liquid, premium packaging",
            'flower': ", hemp flower buds, botanical product, trichomes visible",
            'hurds': ", hemp hurds or shives, building material, woody chips",
            'roots': ", hemp root system, medicinal product, natural remedy",
            'leaves': ", hemp leaves, green foliage, botanical specimen",
            'biomass': ", processed hemp biomass, industrial material"
        }
        
        if plant_part in part_contexts:
            prompt += part_contexts[plant_part]
            
        # Add description context if available
        if description and len(description) > 20:
            # Extract key terms from description
            key_terms = []
            for term in ['sustainable', 'eco-friendly', 'organic', 'premium', 'industrial']:
                if term in description.lower():
                    key_terms.append(term)
                    
            if key_terms:
                prompt += f", {', '.join(key_terms[:2])}"
                
        prompt += ", clean white background, commercial photography style, high quality, 4k"
        
        return prompt
        
    async def _select_best_provider(self) -> ImageProvider:
        """Select the best available provider based on config and availability"""
        # Check provider config
        config_result = self.supabase.table('ai_provider_config').select('*').eq('is_active', True).order('priority').execute()
        
        if config_result.data:
            for config in config_result.data:
                provider_name = config['provider_name'].lower().replace(' ', '-')
                try:
                    return ImageProvider(provider_name)
                except ValueError:
                    continue
                    
        # Default to placeholder
        return ImageProvider.PLACEHOLDER
        

# Convenience functions
async def generate_image(product_id: int, provider: str = None) -> ImageGenerationResult:
    """Quick function to generate image for a single product"""
    service = ImageGenerationService()
    
    if provider:
        provider_enum = ImageProvider(provider)
    else:
        provider_enum = await service._select_best_provider()
        
    product_result = service.supabase.table('uses_products').select('*').eq('id', product_id).execute()
    
    if not product_result.data:
        raise ValueError(f"Product {product_id} not found")
        
    product = product_result.data[0]
    prompt = service._generate_prompt(product)
    
    request = ImageGenerationRequest(
        product_id=product_id,
        prompt=prompt,
        provider=provider_enum
    )
    
    return await service.generate_image(request)
    

async def generate_all_missing_images(provider: str = "placeholder", limit: int = 100) -> List[ImageGenerationResult]:
    """Generate images for all products without images"""
    service = ImageGenerationService()
    provider_enum = ImageProvider(provider)
    return await service.generate_all_missing(provider_enum, limit)


async def get_image_generation_status() -> Dict[str, Any]:
    """Get current status of image generation queue"""
    service = ImageGenerationService()
    return await service.get_queue_status()