import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useClaude } from '@/hooks/use-claude';

export function SimpleChatDemo() {
  const {
    messages,
    isLoading,
    error,
    sendMessage,
    conversationId,
    initializeConversation,
  } = useClaude({
    agentId: 'product-discovery',
    autoInit: false, // Manual initialization for testing
  });

  const handleInitialize = async () => {
    try {
      console.log('[SimpleChatDemo] Manually initializing conversation...');
      await initializeConversation();
    } catch (err) {
      console.error('[SimpleChatDemo] Failed to initialize:', err);
    }
  };

  const handleSendMessage = async () => {
    try {
      await sendMessage('Tell me about hemp products');
    } catch (err) {
      console.error('[SimpleChatDemo] Failed to send message:', err);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Simple Chat Demo - Testing AI Integration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="p-4 bg-gray-100 rounded-lg">
          <p className="text-sm mb-2">
            <strong>Conversation ID:</strong> {conversationId || 'Not initialized'}
          </p>
          <p className="text-sm mb-2">
            <strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}
          </p>
          <p className="text-sm mb-2">
            <strong>Error:</strong> {error?.message || 'None'}
          </p>
          <p className="text-sm">
            <strong>Messages:</strong> {messages.length}
          </p>
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={handleInitialize} 
            disabled={!!conversationId}
            variant="outline"
          >
            Initialize Conversation
          </Button>
          <Button 
            onClick={handleSendMessage} 
            disabled={!conversationId || isLoading}
          >
            Send Test Message
          </Button>
        </div>

        {messages.length > 0 && (
          <div className="space-y-2 p-4 bg-gray-50 rounded-lg max-h-[300px] overflow-y-auto">
            {messages.map((msg, idx) => (
              <div 
                key={idx} 
                className={`p-2 rounded ${
                  msg.role === 'user' ? 'bg-blue-100' : 'bg-green-100'
                }`}
              >
                <strong>{msg.role}:</strong> {msg.content}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}