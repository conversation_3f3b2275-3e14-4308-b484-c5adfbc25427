#!/usr/bin/env python3
"""
Simple test to verify agent fixes - no external dependencies
"""

import os
import sys

# Manually load .env file
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value
    print("✓ Loaded .env file")

# Check environment
print("\n=== Environment Check ===")
print(f"SUPABASE_URL: {os.environ.get('SUPABASE_URL', 'Not set')[:50]}...")
print(f"SUPABASE_ANON_KEY: {os.environ.get('SUPABASE_ANON_KEY', 'Not set')[:20]}...")
print(f"OPENAI_API_KEY: {'Set' if os.environ.get('OPENAI_API_KEY') else 'Not set'}")

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test imports
print("\n=== Testing Imports ===")
try:
    from agents.research.research_agent import HempResearchAgent
    print("✓ Research agent imported successfully")
except Exception as e:
    print(f"✗ Failed to import research agent: {e}")

try:
    from supabase import create_client
    print("✓ Supabase client imported successfully")
except Exception as e:
    print(f"✗ Failed to import supabase: {e}")

# Test connection
print("\n=== Testing Supabase Connection ===")
try:
    from supabase import create_client
    url = os.environ.get('SUPABASE_URL')
    key = os.environ.get('SUPABASE_ANON_KEY')
    
    if url and key:
        supabase = create_client(url, key)
        # Test query
        result = supabase.table('plant_parts').select('name').limit(5).execute()
        print("✓ Connected to Supabase successfully")
        print(f"  Sample plant parts: {[p['name'] for p in result.data]}")
    else:
        print("✗ Missing Supabase credentials")
except Exception as e:
    print(f"✗ Supabase connection error: {e}")

print("\n✅ Basic test completed!")