#!/usr/bin/env python3
"""Final test of DeepSeek with proper env loading"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Load .env file FIRST
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Now import after env is loaded
from utils.ai_providers import DeepSeekProvider, MultiProviderAI
from utils.simple_ai_wrapper import SimpleAIWrapper


async def test_deepseek():
    """Test DeepSeek with proper env loading"""
    
    # Check API key
    api_key = os.getenv("DEEPSEEK_API_KEY")
    logger.info(f"DeepSeek API Key: {api_key[:10]}...{api_key[-4:] if api_key else 'NOT FOUND'}")
    
    # Test DeepSeek directly
    logger.info("\n=== Testing DeepSeek Direct ===")
    try:
        deepseek = DeepSeekProvider()
        result = await deepseek.generate("List 3 innovative hemp products in one line each")
        logger.info(f"✅ DeepSeek works! Result preview: {result[:100]}...")
    except Exception as e:
        logger.error(f"❌ DeepSeek failed: {e}")
        return
    
    # Test with MultiProviderAI
    logger.info("\n=== Testing MultiProviderAI ===")
    try:
        multi = MultiProviderAI(primary_provider="deepseek", fallback_providers=[])
        result, provider, cost = await multi.generate("List 2 hemp construction materials")
        logger.info(f"✅ MultiProviderAI works! Used: {provider}")
        logger.info(f"Cost: ${cost:.6f}")
        logger.info(f"Result preview: {result[:100]}...")
    except Exception as e:
        logger.error(f"❌ MultiProviderAI failed: {e}")
    
    # Test with SimpleAIWrapper
    logger.info("\n=== Testing SimpleAIWrapper ===")
    try:
        multi = MultiProviderAI(primary_provider="deepseek", fallback_providers=[])
        wrapped = SimpleAIWrapper(multi)
        result = await wrapped.generate("Name one hemp product")
        logger.info(f"✅ SimpleAIWrapper works!")
        logger.info(f"Result: {result}")
    except Exception as e:
        logger.error(f"❌ SimpleAIWrapper failed: {e}")


if __name__ == "__main__":
    asyncio.run(test_deepseek())