import { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleMarker, <PERSON><PERSON> } from 'react-leaflet';
import { HempCompany } from '@/hooks/use-companies';
import 'leaflet/dist/leaflet.css';
import '@/styles/leaflet-dark.css';

interface HempCompaniesLeafletMapProps {
  companies: HempCompany[];
  onCompanyClick?: (company: HempCompany) => void;
}

export function HempCompaniesLeafletMap({ companies, onCompanyClick }: HempCompaniesLeafletMapProps) {
  const companiesWithLocation = companies.filter(c => c.latitude && c.longitude);

  // Center map on average of all company locations
  const center = companiesWithLocation.length > 0
    ? {
        lat: companiesWithLocation.reduce((sum, c) => sum + c.latitude!, 0) / companiesWithLocation.length,
        lng: companiesWithLocation.reduce((sum, c) => sum + c.longitude!, 0) / companiesWithLocation.length,
      }
    : { lat: 40, lng: -95 }; // Default to center of US

  const getCompanyColor = (type?: string): string => {
    switch (type) {
      case 'manufacturer': return '#3B82F6';
      case 'distributor': return '#10B981';
      case 'retailer': return '#8B5CF6';
      case 'brand': return '#F97316';
      default: return '#FFD700';
    }
  };

  return (
    <div className="relative w-full">
      <MapContainer
        center={center}
        zoom={3}
        style={{ height: '600px', width: '100%' }}
        className="rounded-lg"
        maxBounds={[[-90, -180], [90, 180]]}
        maxBoundsViscosity={1.0}
        minZoom={2}
        maxZoom={18}
        worldCopyJump={false}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          className="grayscale opacity-70"
        />
        
        {companiesWithLocation.map((company) => (
          <CircleMarker
            key={company.id}
            center={[company.latitude!, company.longitude!]}
            radius={10}
            pathOptions={{
              fillColor: getCompanyColor(company.company_type),
              color: '#fff',
              weight: 2,
              opacity: 1,
              fillOpacity: 0.8,
            }}
            eventHandlers={{
              click: () => {
                if (onCompanyClick) {
                  onCompanyClick(company);
                }
              },
            }}
          >
            <Popup>
              <div className="text-sm">
                <h3 className="font-semibold">{company.name}</h3>
                {company.city && <p>{company.city}, {company.country}</p>}
                {company.company_type && <p className="text-xs mt-1 capitalize">{company.company_type}</p>}
                {company.product_count !== undefined && (
                  <p className="text-xs mt-1">{company.product_count} products</p>
                )}
              </div>
            </Popup>
          </CircleMarker>
        ))}
      </MapContainer>
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-black/80 backdrop-blur-sm rounded-lg p-4 border border-gray-700 z-[1000]">
        <h4 className="text-sm font-semibold text-gray-100 mb-2">Company Types</h4>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-xs text-gray-300">Manufacturer</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-xs text-gray-300">Distributor</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-purple-500"></div>
            <span className="text-xs text-gray-300">Retailer</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
            <span className="text-xs text-gray-300">Brand</span>
          </div>
        </div>
      </div>

      {/* Controls hint */}
      <div className="absolute top-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-700 z-[1000]">
        <p className="text-xs text-gray-400">
          Click and drag to pan • Scroll to zoom • Click markers for details
        </p>
      </div>
    </div>
  );
}