import os
import requests
import json
from datetime import datetime, timezone
from dotenv import load_dotenv
import time
from typing import Dict, List, Optional
from urllib.parse import urlparse, urljoin

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL", "https://ktoqznqmlnxrtvubewyz.supabase.co")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_KEY:
    print("Error: SUPABASE_SERVICE_ROLE_KEY not found")
    exit(1)

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

class EnhancedCompanyScraper:
    def __init__(self):
        self.sources = {
            "hemp_industry_daily": {
                "name": "Hemp Industry Daily",
                "directory_url": "https://hempindustrydaily.com/hemp-company-directory/",
                "search_patterns": ["logo", "company-logo", "brand-logo", "og:image"]
            },
            "vote_hemp": {
                "name": "Vote Hemp",
                "directory_url": "https://www.votehemp.com/hemp-businesses/",
                "search_patterns": ["logo", "company-image", "business-logo"]
            },
            "eiha": {
                "name": "European Industrial Hemp Association",
                "directory_url": "https://eiha.org/members/",
                "search_patterns": ["member-logo", "company-logo", "logo"]
            }
        }
        
        # Common hemp company websites for direct scraping
        self.known_companies = [
            {
                "name": "HempWood",
                "website": "https://hempwood.com",
                "description": "Manufacturer of hemp-based wood alternatives",
                "type": "manufacturer"
            },
            {
                "name": "Hemp Inc",
                "website": "https://hempinc.com",
                "description": "Industrial hemp products and solutions",
                "type": "manufacturer"
            },
            {
                "name": "GenCanna",
                "website": "https://gencanna.com",
                "description": "Global leader in hemp genetics and processing",
                "type": "processor"
            },
            {
                "name": "HempFlax",
                "website": "https://hempflax.com",
                "description": "European hemp cultivation and processing",
                "type": "processor"
            },
            {
                "name": "Manitoba Harvest",
                "website": "https://manitobaharvest.com",
                "description": "Hemp food products and nutrition",
                "type": "manufacturer"
            },
            {
                "name": "Bast Fibre Technologies",
                "website": "https://bastfibretech.com",
                "description": "Hemp fiber processing technology",
                "type": "technology"
            },
            {
                "name": "Hemp Fortex",
                "website": "https://hempfortex.com",
                "description": "Hemp textiles and fabrics",
                "type": "manufacturer"
            },
            {
                "name": "Sunstrand",
                "website": "https://sunstrand.com",
                "description": "Sustainable hemp materials",
                "type": "manufacturer"
            }
        ]
    
    def extract_logo_from_website(self, url: str) -> Optional[str]:
        """Extract logo URL from a company website"""
        try:
            # Add headers to appear as a browser
            request_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=request_headers, timeout=10)
            if response.status_code == 200:
                html = response.text
                
                # Look for common logo patterns
                logo_patterns = [
                    'class="logo"',
                    'id="logo"',
                    'logo.png',
                    'logo.jpg',
                    'logo.svg',
                    'brand-logo',
                    'site-logo',
                    'company-logo'
                ]
                
                # Try to find Open Graph image first (most reliable)
                og_start = html.find('property="og:image"')
                if og_start > -1:
                    content_start = html.find('content="', og_start)
                    if content_start > -1:
                        content_end = html.find('"', content_start + 9)
                        logo_url = html[content_start + 9:content_end]
                        if logo_url:
                            return urljoin(url, logo_url)
                
                # Look for logo in img tags
                for pattern in logo_patterns:
                    pattern_pos = html.find(pattern)
                    if pattern_pos > -1:
                        # Find nearest img src
                        img_start = html.rfind('<img', 0, pattern_pos + 200)
                        if img_start > -1:
                            src_start = html.find('src="', img_start)
                            if src_start > -1:
                                src_end = html.find('"', src_start + 5)
                                logo_url = html[src_start + 5:src_end]
                                if logo_url and ('logo' in logo_url.lower() or pattern in logo_url.lower()):
                                    return urljoin(url, logo_url)
                
        except Exception as e:
            print(f"Error extracting logo from {url}: {e}")
        
        return None
    
    def extract_company_details(self, company_data: Dict) -> Dict:
        """Extract detailed company information including logo"""
        enhanced_data = company_data.copy()
        
        # Try to get logo from website
        if company_data.get('website'):
            logo_url = self.extract_logo_from_website(company_data['website'])
            if logo_url:
                enhanced_data['logo_url'] = logo_url
        
        # Extract location info if available
        enhanced_data['location'] = {
            'country': company_data.get('country', 'United States'),
            'city': company_data.get('city'),
            'state': company_data.get('state'),
            'coordinates': self.get_approximate_coordinates(company_data)
        }
        
        # Add metadata
        enhanced_data['metadata'] = {
            'scraped_date': datetime.now(timezone.utc).isoformat(),
            'source': company_data.get('source', 'direct'),
            'verified': False
        }
        
        return enhanced_data
    
    def get_approximate_coordinates(self, company_data: Dict) -> Optional[Dict]:
        """Get approximate coordinates for company location"""
        # Simple mapping of known locations (expand as needed)
        location_coords = {
            "United States": {"lat": 39.8283, "lng": -98.5795},
            "Canada": {"lat": 56.1304, "lng": -106.3468},
            "Netherlands": {"lat": 52.1326, "lng": 5.2913},
            "Germany": {"lat": 51.1657, "lng": 10.4515},
            "France": {"lat": 46.2276, "lng": 2.2137},
            "United Kingdom": {"lat": 55.3781, "lng": -3.4360}
        }
        
        country = company_data.get('country', 'United States')
        if country in location_coords:
            return location_coords[country]
        
        return None
    
    def scrape_known_companies(self) -> List[Dict]:
        """Scrape information from known hemp companies"""
        companies = []
        
        for company in self.known_companies:
            print(f"Scraping: {company['name']}")
            enhanced_company = self.extract_company_details(company)
            companies.append(enhanced_company)
            time.sleep(1)  # Rate limiting
        
        return companies
    
    def save_to_database(self, companies: List[Dict]) -> int:
        """Save companies to Supabase"""
        saved_count = 0
        updated_count = 0
        
        for company in companies:
            # Check if company already exists
            check_url = f"{SUPABASE_URL}/rest/v1/hemp_companies"
            params = {
                "name": f"eq.{company['name']}",
                "select": "id,logo_url"
            }
            
            response = requests.get(check_url, headers=headers, params=params)
            existing = response.json()
            
            if not existing:
                # Prepare data for new company
                company_data = {
                    "name": company['name'],
                    "description": company.get('description', ''),
                    "company_type": company.get('type', 'manufacturer'),
                    "website": company.get('website'),
                    "logo_url": company.get('logo_url'),
                    "verified": company.get('verified', False),
                    "created_at": datetime.now(timezone.utc).isoformat()
                }
                
                # Add location data if available
                if company.get('location'):
                    location = company['location']
                    company_data.update({
                        "country": location.get('country'),
                        "city": location.get('city'),
                        "state_province": location.get('state')
                    })
                    
                    if location.get('coordinates'):
                        company_data.update({
                            "latitude": location['coordinates']['lat'],
                            "longitude": location['coordinates']['lng']
                        })
                
                # Insert into database
                insert_url = f"{SUPABASE_URL}/rest/v1/hemp_companies"
                response = requests.post(insert_url, headers=headers, json=company_data)
                
                if response.status_code == 201:
                    saved_count += 1
                    print(f"✅ Saved: {company['name']}")
                else:
                    print(f"❌ Failed to save: {company['name']}")
                    print(f"Error: {response.text}")
                    
            else:
                # Update existing company if we found a logo
                existing_company = existing[0]
                if company.get('logo_url') and not existing_company.get('logo_url'):
                    update_data = {
                        "logo_url": company['logo_url'],
                        "website": company.get('website'),
                        "updated_at": datetime.now(timezone.utc).isoformat()
                    }
                    
                    update_url = f"{SUPABASE_URL}/rest/v1/hemp_companies?id=eq.{existing_company['id']}"
                    response = requests.patch(update_url, headers=headers, json=update_data)
                    
                    if response.status_code in [200, 204]:
                        updated_count += 1
                        print(f"🔄 Updated logo for: {company['name']}")
                    else:
                        print(f"❌ Failed to update: {company['name']}")
                else:
                    print(f"⏭️  Already exists: {company['name']}")
        
        return saved_count, updated_count
    
    def update_existing_companies_logos(self):
        """Update logos for existing companies that don't have them"""
        # Get companies without logos
        check_url = f"{SUPABASE_URL}/rest/v1/hemp_companies"
        params = {
            "logo_url": "is.null",
            "website": "not.is.null",
            "select": "id,name,website"
        }
        
        response = requests.get(check_url, headers=headers, params=params)
        companies = response.json()
        
        print(f"\n🔍 Found {len(companies)} companies without logos")
        
        updated = 0
        for company in companies[:10]:  # Limit to 10 for now
            if company.get('website'):
                print(f"Checking logo for: {company['name']}")
                logo_url = self.extract_logo_from_website(company['website'])
                
                if logo_url:
                    update_data = {
                        "logo_url": logo_url,
                        "updated_at": datetime.now(timezone.utc).isoformat()
                    }
                    
                    update_url = f"{SUPABASE_URL}/rest/v1/hemp_companies?id=eq.{company['id']}"
                    response = requests.patch(update_url, headers=headers, json=update_data)
                    
                    if response.status_code in [200, 204]:
                        updated += 1
                        print(f"✅ Updated logo for: {company['name']}")
                
                time.sleep(1)  # Rate limiting
        
        return updated


def main():
    """Main function to run enhanced company scraping"""
    scraper = EnhancedCompanyScraper()
    
    print("🏢 Starting enhanced company scraping...")
    
    # Scrape known companies
    companies = scraper.scrape_known_companies()
    print(f"\n📊 Scraped {len(companies)} known companies")
    
    # Save to database
    if companies:
        saved, updated = scraper.save_to_database(companies)
        print(f"\n✅ Saved {saved} new companies")
        print(f"🔄 Updated {updated} existing companies with logos")
    
    # Update existing companies without logos
    print("\n🔍 Updating existing companies without logos...")
    logo_updates = scraper.update_existing_companies_logos()
    print(f"🔄 Updated {logo_updates} company logos")
    
    # Show current count
    count_url = f"{SUPABASE_URL}/rest/v1/hemp_companies"
    headers_count = headers.copy()
    headers_count["Prefer"] = "count=exact"
    
    response = requests.head(count_url, headers=headers_count)
    if 'content-range' in response.headers:
        total = response.headers['content-range'].split('/')[1]
        print(f"\n📊 Total companies in database: {total}")
        
    # Count companies with logos
    logo_count_url = f"{SUPABASE_URL}/rest/v1/hemp_companies?logo_url=not.is.null"
    response = requests.head(logo_count_url, headers=headers_count)
    if 'content-range' in response.headers:
        with_logos = response.headers['content-range'].split('/')[1]
        print(f"🖼️  Companies with logos: {with_logos}")


if __name__ == "__main__":
    main()