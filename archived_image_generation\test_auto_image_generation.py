#!/usr/bin/env python3
"""
Test automatic image generation - minimal dependencies
"""

import os
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Load .env from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
load_dotenv(env_path)

# Get Supabase credentials
SUPABASE_URL = os.getenv('VITE_SUPABASE_URL')
SUPABASE_KEY = os.getenv('VITE_SUPABASE_ANON_KEY')

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Missing Supabase credentials")
    exit(1)

# Create Supabase client
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

def create_image_prompt(product_name, product_description):
    """Create an optimized prompt for image generation"""
    prompt = f"Industrial hemp product: {product_name}"
    
    desc_lower = product_description.lower()
    
    # Add context-specific details
    if 'fiber' in desc_lower or 'textile' in desc_lower:
        prompt += ", showing textile fibers and fabric texture"
    elif 'plastic' in desc_lower or 'packaging' in desc_lower:
        prompt += ", modern industrial bioplastic material"
    elif 'construction' in desc_lower or 'hempcrete' in desc_lower:
        prompt += ", construction material blocks showing texture"
    elif 'food' in desc_lower or 'seed' in desc_lower or 'protein' in desc_lower:
        prompt += ", nutritious food product with hemp seeds"
    
    prompt += ", professional product photography, clean white background, high quality, commercial style"
    
    return prompt

def main():
    print("🚀 Testing Automatic Image Generation")
    print("=" * 60)
    
    # Test product
    test_product = {
        "name": "Hemp Fiber Car Dashboard",
        "description": "Sustainable automotive dashboard made from compressed hemp fibers, lighter and stronger than traditional plastics"
    }
    
    print(f"\n📦 Test Product: {test_product['name']}")
    print(f"📝 Description: {test_product['description']}")
    
    # Create image generation prompt
    prompt = create_image_prompt(test_product['name'], test_product['description'])
    print(f"\n🎨 Generated Prompt: {prompt}")
    
    # Check if product exists
    existing = supabase.table('uses_products').select('id, name').eq('name', test_product['name']).execute()
    
    if existing.data:
        product_id = existing.data[0]['id']
        print(f"\n✅ Product already exists (ID: {product_id})")
    else:
        # Create product
        new_product = {
            'name': test_product['name'],
            'description': test_product['description'],
            'plant_part_id': 1,  # Default to fiber
            'industry_sub_category_id': 1,  # Default
            'commercialization_stage': 'Pilot',
            'image_url': '/api/placeholder/400/300',
            'created_at': datetime.now().isoformat()
        }
        
        result = supabase.table('uses_products').insert(new_product).execute()
        
        if result.data:
            product_id = result.data[0]['id']
            print(f"\n✅ Created product (ID: {product_id})")
        else:
            print("\n❌ Failed to create product")
            return
    
    # Queue image generation
    queue_entry = {
        'reference_type': 'product',
        'reference_id': product_id,
        'prompt': prompt,
        'provider': 'imagen_3',
        'status': 'pending',
        'metadata': {
            'type': 'product',
            'product_id': product_id,
            'product_name': test_product['name'],
            'auto_generated': True,
            'test_script': True
        },
        'created_at': datetime.now().isoformat()
    }
    
    queue_result = supabase.table('image_generation_queue').insert(queue_entry).execute()
    
    if queue_result.data:
        queue_id = queue_result.data[0]['id']
        print(f"\n🖼️ Successfully queued image generation!")
        print(f"   Queue ID: {queue_id}")
        print(f"   Status: pending")
        print(f"   Provider: imagen_3")
        
        print("\n📊 Current Queue Status:")
        # Check queue status
        queue_stats = supabase.table('image_generation_queue').select('status').execute()
        if queue_stats.data:
            status_counts = {}
            for item in queue_stats.data:
                status = item['status']
                status_counts[status] = status_counts.get(status, 0) + 1
            
            for status, count in status_counts.items():
                print(f"   {status}: {count}")
    else:
        print("\n❌ Failed to queue image generation")
    
    print("\n✅ Test complete!")
    print("\n💡 Next Steps:")
    print("1. Check the image_generation_queue table in Supabase")
    print("2. Run your edge function to process the queue")
    print("3. The product image will be automatically updated when generation completes")

if __name__ == "__main__":
    main()