name: Monitoring and Reporting

on:
  schedule:
    # Hourly health checks
    - cron: '0 * * * *'
    # Daily summary at 8 AM UTC
    - cron: '0 8 * * *'
    # Weekly summary on Mondays at 8 AM UTC
    - cron: '0 8 * * 1'
    
  workflow_dispatch:
    inputs:
      report_type:
        description: 'Type of report to generate'
        required: false
        default: 'health'
        type: choice
        options:
          - health
          - daily
          - weekly
          - full
      alert_level:
        description: 'Minimum alert level to report'
        required: false
        default: 'warning'
        type: choice
        options:
          - info
          - warning
          - error
          - critical
      create_issues:
        description: 'Create GitHub issues for alerts'
        required: false
        default: true
        type: boolean

env:
  SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
  SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
  PYTHONPATH: ${{ github.workspace }}

jobs:
  health-check:
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'workflow_dispatch' ||
      github.event.schedule == '0 * * * *'
    outputs:
      has_alerts: ${{ steps.check-health.outputs.has_alerts }}
      alert_level: ${{ steps.check-health.outputs.alert_level }}
      
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        
    - name: Run health check
      id: check-health
      env:
        ALERT_SEVERITY: ${{ github.event.inputs.alert_level || 'warning' }},error,critical
        EXPORT_METRICS: true
        METRICS_FILE: health-metrics.json
      run: |
        echo "🏥 Running system health check"
        
        # Run monitoring script
        python scripts/monitor_and_alert.py || EXIT_CODE=$?
        
        # Check exit code
        if [[ $EXIT_CODE -eq 2 ]]; then
          echo "has_alerts=true" >> $GITHUB_OUTPUT
          echo "alert_level=critical" >> $GITHUB_OUTPUT
        elif [[ $EXIT_CODE -eq 1 ]]; then
          echo "has_alerts=true" >> $GITHUB_OUTPUT
          echo "alert_level=error" >> $GITHUB_OUTPUT
        else
          # Check for warnings in output
          if grep -q "WARNING:" health-metrics.json 2>/dev/null; then
            echo "has_alerts=true" >> $GITHUB_OUTPUT
            echo "alert_level=warning" >> $GITHUB_OUTPUT
          else
            echo "has_alerts=false" >> $GITHUB_OUTPUT
            echo "alert_level=none" >> $GITHUB_OUTPUT
          fi
        fi
        
    - name: Upload health metrics
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: health-metrics-${{ github.run_number }}
        path: health-metrics.json
        retention-days: 7

  daily-summary:
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'workflow_dispatch' && contains('daily,full', github.event.inputs.report_type)) ||
      (github.event_name == 'schedule' && github.event.schedule == '0 8 * * *')
      
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        
    - name: Make hemp executable
      run: chmod +x hemp
        
    - name: Generate daily summary
      run: |
        echo "📅 Generating daily summary report"
        
        # Create comprehensive daily report
        cat > daily-summary.md << 'EOF'
        # Daily Summary Report
        Generated: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        
        EOF
        
        # System overview
        echo "## System Overview" >> daily-summary.md
        python hemp monitor --format report >> daily-summary.md || echo "Monitoring data unavailable" >> daily-summary.md
        
        # Agent performance (last 24h)
        echo -e "\n## Agent Performance (Last 24 Hours)" >> daily-summary.md
        python hemp monitor --format json | jq -r '
          .agents.by_agent | to_entries[] | 
          "### \(.key)\n- Status: \(.value.active)\n- Success Rate: \(.value.success_rate)%\n- Tasks Completed: \(.value.tasks_completed)\n"
        ' >> daily-summary.md || echo "Agent performance data unavailable" >> daily-summary.md
        
        # Task queue analysis
        echo -e "\n## Task Queue Analysis" >> daily-summary.md
        python hemp monitor --format json | jq -r '
          .tasks | 
          "- Pending: \(.by_status.pending // 0)\n- Processing: \(.by_status.processing // 0)\n- Completed: \(.by_status.completed // 0)\n- Failed: \(.by_status.failed // 0)\n- Avg Wait Time: \(.avg_wait_time)s\n- Avg Processing Time: \(.avg_processing_time)s"
        ' >> daily-summary.md
        
        # Image generation summary
        echo -e "\n## Image Generation Summary" >> daily-summary.md
        python hemp images status >> daily-summary.md
        
        # Database growth
        echo -e "\n## Database Growth (24h)" >> daily-summary.md
        python hemp monitor --format json | jq -r '
          .database | 
          "- New Products: \(.growth_24h)\n- Total Records: \(.total_records)"
        ' >> daily-summary.md
        
        # Cost analysis
        echo -e "\n## Cost Analysis" >> daily-summary.md
        python hemp monitor --format json | jq -r '
          .images | 
          "- Image Generation Cost (Total): $\(.total_cost)\n- Providers Used: \(.providers | keys | join(", "))"
        ' >> daily-summary.md
        
    - name: Upload daily summary
      uses: actions/upload-artifact@v3
      with:
        name: daily-summary-${{ github.run_number }}
        path: daily-summary.md
        retention-days: 30

  weekly-report:
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'workflow_dispatch' && contains('weekly,full', github.event.inputs.report_type)) ||
      (github.event_name == 'schedule' && github.event.schedule == '0 8 * * 1')
      
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        
    - name: Generate weekly report
      run: |
        echo "📊 Generating weekly summary report"
        
        # Create weekly report header
        cat > weekly-report.md << 'EOF'
        # Weekly Summary Report
        Week Ending: $(date -u +"%Y-%m-%d")
        
        ## Executive Summary
        
        This report summarizes the HempQuarterz automation platform performance for the past week.
        
        EOF
        
        # Key metrics
        echo "## Key Metrics" >> weekly-report.md
        
        # Get current metrics
        METRICS=$(python hemp monitor --format json)
        
        echo "$METRICS" | jq -r '
          "- **System Uptime**: \(.system.uptime)%\n" +
          "- **Total Automation Runs**: \(.system.total_automation_runs)\n" +
          "- **Success Rate**: \(.system.automation_success_rate)%\n" +
          "- **Products Discovered**: \(.system.total_products_discovered)\n" +
          "- **Companies Found**: \(.system.total_companies_found)"
        ' >> weekly-report.md
        
        # Weekly trends (would need historical data)
        echo -e "\n## Weekly Trends" >> weekly-report.md
        echo "- Product Discovery Rate: [Calculating from historical data]" >> weekly-report.md
        echo "- Agent Efficiency: [Calculating from historical data]" >> weekly-report.md
        echo "- Cost per Product: [Calculating from historical data]" >> weekly-report.md
        
        # Top performing agents
        echo -e "\n## Top Performing Agents" >> weekly-report.md
        echo "$METRICS" | jq -r '
          .agents.by_agent | to_entries | 
          sort_by(-.value.success_rate) | 
          .[0:3][] | 
          "- **\(.key)**: \(.value.success_rate)% success rate, \(.value.tasks_completed) tasks"
        ' >> weekly-report.md
        
        # Recommendations
        echo -e "\n## Recommendations" >> weekly-report.md
        
        # Check for issues and make recommendations
        PENDING_TASKS=$(echo "$METRICS" | jq -r '.tasks.by_status.pending // 0')
        if [[ $PENDING_TASKS -gt 50 ]]; then
          echo "- ⚠️ High task backlog ($PENDING_TASKS pending) - consider increasing agent frequency" >> weekly-report.md
        fi
        
        FAILED_IMAGES=$(echo "$METRICS" | jq -r '.images.queue.failed // 0')
        if [[ $FAILED_IMAGES -gt 20 ]]; then
          echo "- ⚠️ High image generation failures ($FAILED_IMAGES) - review provider configuration" >> weekly-report.md
        fi
        
        # Action items
        echo -e "\n## Action Items for Next Week" >> weekly-report.md
        echo "- [ ] Review and address any critical alerts" >> weekly-report.md
        echo "- [ ] Optimize underperforming agents" >> weekly-report.md
        echo "- [ ] Plan for database scaling if needed" >> weekly-report.md
        
    - name: Upload weekly report
      uses: actions/upload-artifact@v3
      with:
        name: weekly-report-${{ github.run_number }}
        path: weekly-report.md
        retention-days: 90

  alert-management:
    needs: health-check
    runs-on: ubuntu-latest
    if: |
      needs.health-check.outputs.has_alerts == 'true' &&
      (github.event.inputs.create_issues != 'false' || github.event_name == 'schedule')
      
    steps:
    - uses: actions/checkout@v3
    
    - name: Download health metrics
      uses: actions/download-artifact@v3
      with:
        name: health-metrics-${{ github.run_number }}
        
    - name: Create issues for critical alerts
      if: needs.health-check.outputs.alert_level == 'critical' || needs.health-check.outputs.alert_level == 'error'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const metrics = JSON.parse(fs.readFileSync('health-metrics.json', 'utf8'));
          const alerts = metrics.alerts || [];
          
          // Group alerts by severity
          const criticalAlerts = alerts.filter(a => a.severity === 'critical');
          const errorAlerts = alerts.filter(a => a.severity === 'error');
          
          if (criticalAlerts.length > 0 || errorAlerts.length > 0) {
            const title = `🚨 System Alert - ${new Date().toISOString().split('T')[0]}`;
            
            let body = `## System Monitoring Alert\n\n`;
            body += `Alert Level: **${needs.health-check.outputs.alert_level}**\n\n`;
            
            if (criticalAlerts.length > 0) {
              body += `### 🔴 Critical Alerts\n`;
              criticalAlerts.forEach(alert => {
                body += `- **${alert.message}**\n`;
                body += `  - Current: ${alert.current_value}, Threshold: ${alert.threshold}\n`;
              });
              body += `\n`;
            }
            
            if (errorAlerts.length > 0) {
              body += `### ❌ Error Alerts\n`;
              errorAlerts.forEach(alert => {
                body += `- **${alert.message}**\n`;
                body += `  - Current: ${alert.current_value}, Threshold: ${alert.threshold}\n`;
              });
              body += `\n`;
            }
            
            body += `### Next Steps\n`;
            body += `1. Check the [monitoring dashboard](python hemp monitor --live)\n`;
            body += `2. Review [full metrics](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n`;
            body += `3. Take corrective action as needed\n\n`;
            body += `Run ID: ${{ github.run_id }}\n`;
            
            // Create issue
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['alert', 'monitoring', needs.health-check.outputs.alert_level]
            });
          }

  create-summary:
    needs: [health-check, daily-summary, weekly-report]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Create workflow summary
      run: |
        echo "## 📊 Monitoring & Reporting Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Health status
        if [[ "${{ needs.health-check.outputs.has_alerts }}" == "true" ]]; then
          echo "### ⚠️ System Health: Issues Detected" >> $GITHUB_STEP_SUMMARY
          echo "Alert Level: **${{ needs.health-check.outputs.alert_level }}**" >> $GITHUB_STEP_SUMMARY
        else
          echo "### ✅ System Health: All Good" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Report generation status
        echo "### Reports Generated" >> $GITHUB_STEP_SUMMARY
        echo "- Health Check: ✅" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          echo "- Report Type: ${{ github.event.inputs.report_type }}" >> $GITHUB_STEP_SUMMARY
        else
          SCHEDULE="${{ github.event.schedule }}"
          case "$SCHEDULE" in
            "0 8 * * *")
              echo "- Daily Summary: ✅" >> $GITHUB_STEP_SUMMARY
              ;;
            "0 8 * * 1")
              echo "- Weekly Report: ✅" >> $GITHUB_STEP_SUMMARY
              ;;
          esac
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "[View All Artifacts](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY