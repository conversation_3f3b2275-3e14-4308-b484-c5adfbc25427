# Research Tables Migration Guide

## Overview
The codebase currently has two research-related tables:
- `research_papers` (legacy, empty) 
- `research_entries` (active, has 5 records)

We need to consolidate to use only `research_entries` and remove `research_papers`.

## Current Status

### ✅ Already Using research_entries:
1. **Supabase API** (`/client/src/lib/supabase-api.ts`) - Correctly uses `research_entries`
2. **Shared Schema** (`/shared/schema.ts`) - Defines `researchEntries` 
3. **Storage DB** (`/server/storage-db.ts`) - Uses alias: `researchEntries as researchPapers`

### ❌ Still Using research_papers:
1. **Client API** (`/client/src/lib/api.ts`) - **FIXED!** Updated all 7 functions
2. **DB Init** (`/server/db-init.ts`) - Creates `research_papers` table (line 84)
3. **Migration File** (`/supabase/migrations/20250519123456_enhanced_schema/up.sql`) - Creates table and indexes

## Migration Steps

### 1. Run Migration Script (if research_papers exists in production)
```sql
-- Run the migration script to:
-- 1. Move any data from research_papers to research_entries
-- 2. Update junction tables
-- 3. Drop research_papers table
-- 4. Add missing indexes to research_entries

-- Execute in Supabase SQL Editor:
-- Copy contents of migrate_research_tables.sql
```

### 2. Update Remaining Code Files

#### Option A: If DB hasn't been deployed to production yet
1. Delete or comment out the `CREATE TABLE research_papers` section in:
   - `/server/db-init.ts` (lines 84-102)
   - `/supabase/migrations/20250519123456_enhanced_schema/up.sql` (lines 59-78)

2. Remove the indexes for research_papers in the migration file (lines 93-95, 105, 113)

#### Option B: If DB is already in production
1. Don't modify existing migration files
2. Create a new migration to handle the cleanup
3. Run the `migrate_research_tables.sql` script

### 3. Update Frontend Type Definitions (if needed)
Check if `ResearchPaper` type in TypeScript files needs to be updated to match `research_entries` schema:
- `publication_date` → `publication_or_filing_date`
- `authors` → `authors_or_assignees` 
- `doi` → `doi_or_patent_number`
- `journal` → `journal_or_office`
- Add `entry_type` field

## Verification Steps

1. **Check Supabase Dashboard**:
   - Confirm `research_papers` table is empty
   - Verify `research_entries` has all expected data
   - Check that frontend Research section still works

2. **Test Frontend**:
   - Navigate to Research section
   - Verify research entries display correctly
   - Test search functionality

3. **Test Agent Integration**:
   ```bash
   python test_save_research.py
   ```

## Benefits of Using research_entries

1. **More Flexible**: `entry_type` field allows Papers, Articles, Reports, Patents, etc.
2. **Generic Fields**: Works for different content types (authors_or_assignees, doi_or_patent_number)
3. **Already Integrated**: Frontend code in `supabase-api.ts` already uses it
4. **Future-Proof**: Can handle various research content types

## Summary

The `research_papers` table is legacy and should be removed. All code has been updated to use `research_entries` except for some initialization/migration files. Run the migration script to safely consolidate the tables.