name: Hemp Product Automation - Consolidated

on:
  # Manual trigger with agent selection
  workflow_dispatch:
    inputs:
      agent_type:
        description: 'Hemp agent type to run (consolidated)'
        required: true
        type: choice
        options:
          - all
          - seeds-food
          - seeds-nutrition
          - fiber-textiles
          - fiber-composites
          - oil-personal-care
          - oil-biofuel
          - flower-products
          - hurds-materials
          - roots-medicine
          - roots-biotech
          - leaves-feed
          - leaves-medicine
          - biomass-energy
          - whole-plant
  
  # Scheduled runs - optimized for consolidated agents
  schedule:
    # High-priority agents: Every 6 hours
    - cron: '0 0,6,12,18 * * *'  # seeds-food
    
    # Medium-priority agents: Every 12 hours
    - cron: '0 3,15 * * *'  # fiber-textiles, oil-personal-care
    
    # Daily agents: Once per day
    - cron: '0 9 * * *'  # flower-products, hurds-materials
    
    # Low-priority agents: Every 2 days
    - cron: '0 21 */2 * *'  # roots-medicine, roots-biotech, leaves-medicine
    - cron: '0 2 */2 * *'   # fiber-composites, oil-biofuel
    - cron: '0 14 */2 * *'  # biomass-energy, whole-plant, seeds-nutrition, leaves-feed

jobs:
  validate-environment:
    runs-on: ubuntu-latest
    outputs:
      is_valid: ${{ steps.check-env.outputs.is_valid }}
    steps:
      - name: Check required secrets
        id: check-env
        run: |
          MISSING_SECRETS=""
          
          if [ -z "${{ secrets.SUPABASE_URL }}" ]; then
            MISSING_SECRETS="${MISSING_SECRETS}SUPABASE_URL "
          fi
          
          if [ -z "${{ secrets.SUPABASE_ANON_KEY }}" ]; then
            MISSING_SECRETS="${MISSING_SECRETS}SUPABASE_ANON_KEY "
          fi
          
          if [ -z "${{ secrets.OPENAI_API_KEY }}" ]; then
            MISSING_SECRETS="${MISSING_SECRETS}OPENAI_API_KEY "
          fi
          
          if [ -n "$MISSING_SECRETS" ]; then
            echo "❌ Missing required secrets: $MISSING_SECRETS"
            echo "is_valid=false" >> $GITHUB_OUTPUT
            exit 1
          else
            echo "✅ All required secrets are configured"
            echo "is_valid=true" >> $GITHUB_OUTPUT
          fi

  determine-agents:
    needs: validate-environment
    if: needs.validate-environment.outputs.is_valid == 'true'
    runs-on: ubuntu-latest
    outputs:
      agents: ${{ steps.set-agents.outputs.agents }}
    steps:
      - name: Determine which agents to run
        id: set-agents
        run: |
          HOUR=$(date -u +%H)
          DAY=$(date -u +%j)
          
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            if [ "${{ github.event.inputs.agent_type }}" == "all" ]; then
              echo 'agents=["seeds-food", "seeds-nutrition", "fiber-textiles", "fiber-composites", "oil-personal-care", "oil-biofuel", "flower-products", "hurds-materials", "roots-medicine", "roots-biotech", "leaves-feed", "leaves-medicine", "biomass-energy", "whole-plant"]' >> $GITHUB_OUTPUT
            else
              echo 'agents=["${{ github.event.inputs.agent_type }}"]' >> $GITHUB_OUTPUT
            fi
          else
            # Schedule-based agent selection for consolidated agents
            case $HOUR in
              0|6|12|18)
                echo 'agents=["seeds-food"]' >> $GITHUB_OUTPUT
                ;;
              3|15)
                echo 'agents=["fiber-textiles", "oil-personal-care"]' >> $GITHUB_OUTPUT
                ;;
              9)
                echo 'agents=["flower-products", "hurds-materials"]' >> $GITHUB_OUTPUT
                ;;
              21)
                if [ $((DAY % 2)) -eq 0 ]; then
                  echo 'agents=["roots-medicine", "roots-biotech", "leaves-medicine"]' >> $GITHUB_OUTPUT
                else
                  echo 'agents=[]' >> $GITHUB_OUTPUT
                fi
                ;;
              2)
                if [ $((DAY % 2)) -eq 0 ]; then
                  echo 'agents=["fiber-composites", "oil-biofuel"]' >> $GITHUB_OUTPUT
                else
                  echo 'agents=[]' >> $GITHUB_OUTPUT
                fi
                ;;
              14)
                if [ $((DAY % 2)) -eq 0 ]; then
                  echo 'agents=["biomass-energy", "whole-plant", "seeds-nutrition", "leaves-feed"]' >> $GITHUB_OUTPUT
                else
                  echo 'agents=[]' >> $GITHUB_OUTPUT
                fi
                ;;
              *)
                echo 'agents=[]' >> $GITHUB_OUTPUT
                ;;
            esac
          fi

  run-hemp-automation:
    needs: determine-agents
    if: needs.determine-agents.outputs.agents != '[]'
    runs-on: ubuntu-latest
    timeout-minutes: 30  # Prevent runaway jobs
    strategy:
      matrix:
        agent: ${{ fromJson(needs.determine-agents.outputs.agents) }}
      max-parallel: 2  # Limit concurrent runs to reduce API load
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          cache: 'pip'
      
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      
      - name: Verify script exists
        run: |
          if [ ! -f "hemp_agent_enhanced.py" ]; then
            echo "❌ Error: hemp_agent_enhanced.py not found!"
            exit 1
          fi
          echo "✅ Script file exists"
      
      - name: Run Consolidated Hemp Agent - ${{ matrix.agent }}
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          PYTHONUNBUFFERED: "1"
        run: |
          echo "🌿 Starting Consolidated Hemp Agent: ${{ matrix.agent }}"
          echo "⏰ Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
          echo "📊 This is a CONSOLIDATED agent - handles multiple industries"
          
          # Run the enhanced agent with specific type
          python hemp_agent_enhanced.py --type "${{ matrix.agent }}" --limit 10
          
          # Check exit code
          if [ $? -eq 0 ]; then
            echo "✅ Agent completed successfully"
          else
            echo "❌ Agent failed with exit code $?"
            exit 1
          fi
      
      - name: Generate run summary
        if: always()
        run: |
          echo "## Consolidated Hemp Agent Run Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Agent**: ${{ matrix.agent }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Type**: Consolidated (handles multiple industries)" >> $GITHUB_STEP_SUMMARY
          echo "- **Status**: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Time**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
          echo "- **Run ID**: ${{ github.run_id }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Consolidation Info" >> $GITHUB_STEP_SUMMARY
          echo "This workflow uses consolidated agents (reduced from 19 to 9):" >> $GITHUB_STEP_SUMMARY
          echo "- **flower-products**: Combines pharma, CBD, and wellness" >> $GITHUB_STEP_SUMMARY
          echo "- **oil-personal-care**: Combines cosmetics and wellness" >> $GITHUB_STEP_SUMMARY
          echo "- **hurds-materials**: Combines construction, hempcrete, and bedding" >> $GITHUB_STEP_SUMMARY
      
      - name: Monitor agent results
        if: success()
        continue-on-error: true
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        run: |
          if [ -f "monitor_hemp.py" ]; then
            echo "📊 Checking agent results..."
            python monitor_hemp.py --agent "${{ matrix.agent }}" --recent 1
          else
            echo "⚠️ monitor_hemp.py not found, skipping monitoring"
          fi
      
      - name: Validate data integrity
        if: success()
        continue-on-error: true
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        run: |
          if [ -f "validate_hemp_data.py" ]; then
            echo "🔍 Validating data integrity..."
            python validate_hemp_data.py
          else
            echo "⚠️ validate_hemp_data.py not found, skipping validation"
          fi

  consolidation-report:
    needs: run-hemp-automation
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Generate consolidation report
        run: |
          echo "## 📊 Consolidation Performance Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Agent Consolidation Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Previous Agent Count**: 19" >> $GITHUB_STEP_SUMMARY
          echo "- **New Agent Count**: 9 (52% reduction)" >> $GITHUB_STEP_SUMMARY
          echo "- **Efficiency Gain**: Reduced API calls and runtime" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Benefits Achieved" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Simplified maintenance" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Better resource utilization" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Improved data consistency" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Reduced operational costs" >> $GITHUB_STEP_SUMMARY

  error-notification:
    needs: [validate-environment, run-hemp-automation]
    if: failure()
    runs-on: ubuntu-latest
    steps:
      - name: Create issue on failure
        uses: actions/github-script@v7
        with:
          script: |
            const date = new Date().toISOString().split('T')[0];
            const title = `Hemp Automation Failed - ${date}`;
            
            // Check if issue already exists
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              labels: ['automation-failure']
            });
            
            const existingIssue = issues.data.find(issue => issue.title === title);
            
            if (!existingIssue) {
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                body: `## Automation Failure Report\n\n- **Workflow Run**: [${context.runId}](${context.serverUrl}/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})\n- **Time**: ${new Date().toUTCString()}\n- **Event**: ${context.eventName}\n- **Type**: Consolidated Agents\n\nPlease check the workflow logs for details.`,
                labels: ['automation-failure', 'hemp-automation', 'consolidated']
              });
            }
