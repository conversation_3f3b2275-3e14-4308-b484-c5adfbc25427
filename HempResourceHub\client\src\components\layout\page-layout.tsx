import React from "react";
import { cn } from "@/lib/utils";
import { EnhancedBreadcrumbs } from "@/components/ui/enhanced-breadcrumbs";

interface PageLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  showBreadcrumbs?: boolean;
  breadcrumbItems?: any[];
  className?: string;
  containerClassName?: string;
  headerClassName?: string;
  contentClassName?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl' | 'full';
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '7xl': 'max-w-7xl',
  full: 'max-w-full',
};

const spacingClasses = {
  sm: 'py-6 md:py-8',
  md: 'py-8 md:py-12',
  lg: 'py-12 md:py-16',
  xl: 'py-16 md:py-20',
};

export function PageLayout({
  children,
  title,
  description,
  showBreadcrumbs = true,
  breadcrumbItems,
  className,
  containerClassName,
  headerClassName,
  contentClassName,
  maxWidth = '7xl',
  spacing = 'md'
}: PageLayoutProps) {
  return (
    <div className={cn("min-h-screen bg-gray-950", className)}>
      <div className={cn(
        maxWidthClasses[maxWidth],
        "mx-auto px-4 sm:px-6 lg:px-8",
        spacingClasses[spacing],
        containerClassName
      )}>
        {/* Breadcrumbs */}
        {showBreadcrumbs && (
          <div className="mb-6 md:mb-8">
            <EnhancedBreadcrumbs 
              items={breadcrumbItems}
              showHome={true}
              showContext={true}
            />
          </div>
        )}

        {/* Page Header */}
        {(title || description) && (
          <div className={cn("mb-6 md:mb-8 lg:mb-12", headerClassName)}>
            {title && (
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 md:mb-4">
                {title}
              </h1>
            )}
            {description && (
              <p className="text-base md:text-lg text-gray-300 leading-relaxed max-w-3xl">
                {description}
              </p>
            )}
          </div>
        )}

        {/* Page Content */}
        <div className={cn("space-y-6 md:space-y-8", contentClassName)}>
          {children}
        </div>
      </div>
    </div>
  );
}

// Specialized layout components for common page types
export function ProductPageLayout({ children, ...props }: Omit<PageLayoutProps, 'maxWidth'>) {
  return (
    <PageLayout maxWidth="7xl" spacing="md" {...props}>
      {children}
    </PageLayout>
  );
}

export function AdminPageLayout({ children, ...props }: Omit<PageLayoutProps, 'maxWidth' | 'spacing'>) {
  return (
    <PageLayout maxWidth="full" spacing="lg" {...props}>
      {children}
    </PageLayout>
  );
}

export function DetailPageLayout({ children, ...props }: Omit<PageLayoutProps, 'maxWidth'>) {
  return (
    <PageLayout maxWidth="7xl" spacing="md" {...props}>
      {children}
    </PageLayout>
  );
}

// Section component for consistent content sections
interface PageSectionProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  spacing?: 'sm' | 'md' | 'lg';
}

const sectionSpacingClasses = {
  sm: 'space-y-4',
  md: 'space-y-6',
  lg: 'space-y-8',
};

export function PageSection({
  children,
  title,
  description,
  className,
  headerClassName,
  contentClassName,
  spacing = 'md'
}: PageSectionProps) {
  return (
    <section className={cn(sectionSpacingClasses[spacing], className)}>
      {(title || description) && (
        <div className={cn("space-y-2 md:space-y-3", headerClassName)}>
          {title && (
            <h2 className="text-xl md:text-2xl lg:text-3xl font-semibold text-white">
              {title}
            </h2>
          )}
          {description && (
            <p className="text-sm md:text-base text-gray-400 leading-relaxed">
              {description}
            </p>
          )}
        </div>
      )}
      <div className={cn("space-y-4 md:space-y-6", contentClassName)}>
        {children}
      </div>
    </section>
  );
}

// Grid component for consistent card layouts
interface PageGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

const gridColumnClasses = {
  1: 'grid-cols-1',
  2: 'grid-cols-1 md:grid-cols-2',
  3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  5: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  6: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6',
};

const gridGapClasses = {
  sm: 'gap-3 md:gap-4',
  md: 'gap-4 md:gap-6',
  lg: 'gap-6 md:gap-8',
};

export function PageGrid({
  children,
  columns = 3,
  gap = 'md',
  className
}: PageGridProps) {
  return (
    <div className={cn(
      "grid",
      gridColumnClasses[columns],
      gridGapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
}

// Card wrapper for consistent card styling
interface PageCardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  padding?: 'sm' | 'md' | 'lg';
}

const cardPaddingClasses = {
  sm: 'p-4',
  md: 'p-4 md:p-6',
  lg: 'p-6 md:p-8',
};

export function PageCard({
  children,
  title,
  description,
  className,
  headerClassName,
  contentClassName,
  padding = 'md'
}: PageCardProps) {
  return (
    <div className={cn(
      "bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-xl",
      "hover:border-gray-700 transition-colors",
      cardPaddingClasses[padding],
      className
    )}>
      {(title || description) && (
        <div className={cn("mb-4 md:mb-6", headerClassName)}>
          {title && (
            <h3 className="text-lg md:text-xl font-semibold text-white mb-2">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm md:text-base text-gray-400">
              {description}
            </p>
          )}
        </div>
      )}
      <div className={contentClassName}>
        {children}
      </div>
    </div>
  );
}

export default PageLayout;
