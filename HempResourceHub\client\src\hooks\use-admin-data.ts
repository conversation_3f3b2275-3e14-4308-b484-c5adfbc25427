import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase-client";

interface AgentMetrics {
  activeAgents: number;
  tasksCompleted: number;
  successRate: number;
  avgResponseTime: number;
  performanceHistory: Array<{
    timestamp: string;
    [agent: string]: number | string;
  }>;
  taskDistribution: Array<{
    type: string;
    count: number;
  }>;
  recentActivities: Array<{
    agent: string;
    action: string;
    timestamp: string;
    status: 'success' | 'error' | 'warning';
  }>;
}

interface SystemHealth {
  agents: Array<{
    id: string;
    name: string;
    status: 'active' | 'idle' | 'error' | 'maintenance';
    lastActive: string;
    tasksToday: number;
    successRate: number;
  }>;
  systemLoad: number;
  errorRate: number;
  uptime: number;
}

export function useAgentMetrics(timeRange: string = '24h') {
  return useQuery({
    queryKey: ['agent-metrics', timeRange],
    queryFn: async () => {
      // Calculate date range
      const now = new Date();
      const startDate = new Date();
      
      switch(timeRange) {
        case '1h':
          startDate.setHours(now.getHours() - 1);
          break;
        case '24h':
          startDate.setDate(now.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(now.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(now.getDate() - 30);
          break;
      }
      
      // Fetch agent task queue data
      const { data: tasks, error: tasksError } = await supabase
        .from('agent_task_queue')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });
      
      if (tasksError) throw tasksError;
      
      // Fetch agent performance metrics
      const { data: metrics, error: metricsError } = await supabase
        .from('agent_performance_metrics')
        .select('*')
        .gte('metric_date', startDate.toISOString().split('T')[0]);
      
      if (metricsError) throw metricsError;
      
      // Calculate metrics
      const activeAgents = new Set(tasks?.map(t => t.assigned_agent)).size || 0;
      const completedTasks = tasks?.filter(t => t.status === 'completed').length || 0;
      const totalTasks = tasks?.length || 1;
      const successRate = (completedTasks / totalTasks) * 100;
      
      // Calculate average response time
      const responseTimes = tasks
        ?.filter(t => t.completed_at && t.started_at)
        .map(t => {
          const start = new Date(t.started_at).getTime();
          const end = new Date(t.completed_at).getTime();
          return end - start;
        }) || [];
      
      const avgResponseTime = responseTimes.length > 0
        ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
        : 0;
      
      // Group tasks by type for distribution
      const taskDistribution = tasks?.reduce((acc, task) => {
        const type = task.task_type || 'Unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};
      
      // Format recent activities
      const recentActivities = tasks
        ?.slice(0, 20)
        .map(task => ({
          agent: task.assigned_agent || 'Unknown',
          action: task.params?.description || 'Task executed',
          timestamp: task.created_at,
          status: task.status === 'completed' ? 'success' : 
                 task.status === 'failed' ? 'error' : 'warning' as const
        })) || [];
      
      return {
        activeAgents,
        tasksCompleted: completedTasks,
        successRate: Math.round(successRate * 10) / 10,
        avgResponseTime: Math.round(avgResponseTime),
        performanceHistory: [], // TODO: Implement time-series data
        taskDistribution: Object.entries(taskDistribution).map(([type, count]) => ({
          type,
          count
        })),
        recentActivities
      };
    },
    refetchInterval: 30000 // Refetch every 30 seconds
  });
}

export function useSystemHealth() {
  return useQuery({
    queryKey: ['system-health'],
    queryFn: async () => {
      // Fetch agent configurations
      const { data: agentConfigs, error: configError } = await supabase
        .from('agent_configurations')
        .select('*');
      
      if (configError) throw configError;
      
      // Fetch recent tasks for each agent
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      const { data: todayTasks, error: tasksError } = await supabase
        .from('agent_task_queue')
        .select('*')
        .gte('created_at', today.toISOString());
      
      if (tasksError) throw tasksError;
      
      // Calculate health metrics for each agent
      const agents = agentConfigs?.map(config => {
        const agentTasks = todayTasks?.filter(t => t.assigned_agent === config.agent_type) || [];
        const completedTasks = agentTasks.filter(t => t.status === 'completed');
        const failedTasks = agentTasks.filter(t => t.status === 'failed');
        
        // Determine status based on recent activity
        const lastTask = agentTasks[0];
        let status: 'active' | 'idle' | 'error' | 'maintenance' = 'idle';
        
        if (config.is_active === false) {
          status = 'maintenance';
        } else if (lastTask) {
          const lastActiveTime = new Date(lastTask.created_at).getTime();
          const timeSinceActive = now.getTime() - lastActiveTime;
          
          if (timeSinceActive < 5 * 60 * 1000) { // Active in last 5 minutes
            status = 'active';
          } else if (failedTasks.length > completedTasks.length) {
            status = 'error';
          }
        }
        
        return {
          id: config.agent_type,
          name: config.agent_type.split('_').map(w => 
            w.charAt(0).toUpperCase() + w.slice(1)
          ).join(' '),
          status,
          lastActive: lastTask ? formatTimeAgo(new Date(lastTask.created_at)) : 'Never',
          tasksToday: agentTasks.length,
          successRate: agentTasks.length > 0 
            ? Math.round((completedTasks.length / agentTasks.length) * 100)
            : 100
        };
      }) || [];
      
      // Calculate system-wide metrics
      const totalTasks = todayTasks?.length || 0;
      const failedTasks = todayTasks?.filter(t => t.status === 'failed').length || 0;
      const errorRate = totalTasks > 0 ? (failedTasks / totalTasks) * 100 : 0;
      
      return {
        agents,
        systemLoad: agents.filter(a => a.status === 'active').length / agents.length * 100,
        errorRate: Math.round(errorRate * 10) / 10,
        uptime: 99.9 // Mock for now
      };
    },
    refetchInterval: 10000 // Refetch every 10 seconds for real-time updates
  });
}

export function useRecentActivities(limit: number = 50) {
  return useQuery({
    queryKey: ['recent-activities', limit],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('agent_task_queue')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      return data?.map(task => ({
        id: task.task_id,
        agent: task.assigned_agent || 'Unknown',
        action: task.params?.description || 'Task executed',
        timestamp: task.created_at,
        status: task.status === 'completed' ? 'success' : 
               task.status === 'failed' ? 'error' : 'warning' as const,
        details: task.result || task.error_log?.[0] || null
      })) || [];
    },
    refetchInterval: 5000 // Refetch every 5 seconds for live updates
  });
}

// Helper function to format time ago
function formatTimeAgo(date: Date): string {
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (seconds < 60) return `${seconds} seconds ago`;
  if (seconds < 3600) return `${Math.floor(seconds / 60)} minutes ago`;
  if (seconds < 86400) return `${Math.floor(seconds / 3600)} hours ago`;
  return `${Math.floor(seconds / 86400)} days ago`;
}