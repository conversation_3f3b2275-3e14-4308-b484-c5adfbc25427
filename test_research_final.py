#!/usr/bin/env python3
"""
Final test for research agent - tests the complete fix.
"""

import os
import sys
import asyncio
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

async def test():
    """Test the research agent is working"""
    logger.info("Testing Research Agent Fix...\n")
    
    try:
        # Test 1: Import dependencies
        logger.info("1. Testing imports...")
        from agents.research.unified_research_agent import create_research_agent, ResearchFeatures
        logger.info("   ✅ Research agent imports successful")
        
        # Test 2: Check Supabase
        logger.info("\n2. Testing Supabase connection...")
        from lib.supabase_client import get_supabase_client
        try:
            supabase = get_supabase_client()
            logger.info("   ✅ Supabase client created")
        except Exception as e:
            logger.error(f"   ❌ Supabase error: {e}")
            logger.info("   Creating mock Supabase for testing...")
            
            class MockSupabase:
                def table(self, name): return self
                def select(self, *args): return self
                def eq(self, *args): return self
                def insert(self, *args): return self
                async def execute(self):
                    class Result:
                        data = []
                    return Result()
            
            supabase = MockSupabase()
        
        # Test 3: Create agent without AI
        logger.info("\n3. Creating research agent (web scraping mode)...")
        agent = create_research_agent(
            supabase,
            ai_provider=None,
            features=[ResearchFeatures.WEB_SCRAPING, ResearchFeatures.FEED_MONITORING]
        )
        logger.info("   ✅ Agent created successfully")
        
        # Test 4: Execute discovery
        logger.info("\n4. Testing product discovery...")
        logger.info("   Searching for 'hemp fiber products'...")
        
        task = {
            'action': 'discover_products',
            'params': {
                'query': 'hemp fiber products',
                'limit': 5
            }
        }
        
        result = await agent.execute(task)
        
        logger.info(f"\n   ✅ Discovery completed!")
        logger.info(f"   Status: {result['status']}")
        logger.info(f"   Products found: {result['products_found']}")
        logger.info(f"   Products saved: {result['products_saved']}")
        
        if result['products_found'] > 0:
            logger.info("\n   Sample product:")
            product = result['products'][0]
            logger.info(f"   - Name: {product.get('name', 'N/A')}")
            logger.info(f"   - Plant Part: {product.get('plant_part', 'N/A')}")
            logger.info(f"   - Source: {product.get('data_source', 'N/A')}")
        
        logger.info("\n✅ ALL TESTS PASSED! Research agent is working.")
        return True
        
    except Exception as e:
        logger.error(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test())
    
    if success:
        logger.info("\n" + "="*60)
        logger.info("SUCCESS! The research agent is fixed and working.")
        logger.info("="*60)
        logger.info("\nYou can now run:")
        logger.info("  python hemp_cli.py agent research 'your search query'")
        logger.info("\nOr use the direct runner:")
        logger.info("  python run_research_agent.py")
    else:
        logger.info("\nPlease check the errors above and ensure all dependencies are installed:")
        logger.info("  pip install supabase aiohttp feedparser beautifulsoup4 tenacity")