#!/usr/bin/env python3
"""
Test script to verify hemp agent setup without using OpenAI API
"""

import os
import sys
from datetime import datetime
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment():
    """Test environment setup"""
    print("🔍 Testing Environment Setup")
    print("=" * 50)
    
    # Check environment variables
    env_vars = {
        'SUPABASE_URL': os.environ.get('SUPABASE_URL'),
        'SUPABASE_ANON_KEY': os.environ.get('SUPABASE_ANON_KEY'),
        'OPENAI_API_KEY': os.environ.get('OPENAI_API_KEY')
    }
    
    all_good = True
    for var, value in env_vars.items():
        if value:
            masked_value = value[:10] + "..." if len(value) > 10 else value
            print(f"✅ {var}: {masked_value}")
        else:
            print(f"❌ {var}: Not set")
            all_good = False
    
    return all_good

def test_database_connection():
    """Test Supabase connection"""
    print("\n🔍 Testing Database Connection")
    print("=" * 50)
    
    try:
        supabase = create_client(
            os.environ['SUPABASE_URL'],
            os.environ['SUPABASE_ANON_KEY']
        )
        
        # Test each table
        tables = [
            'hemp_plant_archetypes',
            'plant_parts',
            'industries',
            'industry_sub_categories',
            'uses_products',
            'companies',
            'product_companies',
            'hemp_agent_runs',
            'hemp_automation_products',
            'hemp_automation_companies'
        ]
        
        for table in tables:
            try:
                result = supabase.table(table).select('id').limit(1).execute()
                print(f"✅ {table}: Connected")
            except Exception as e:
                print(f"❌ {table}: {str(e)[:50]}")
        
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_agent_runs():
    """Check recent agent runs"""
    print("\n🔍 Checking Recent Agent Runs")
    print("=" * 50)
    
    try:
        supabase = create_client(
            os.environ['SUPABASE_URL'],
            os.environ['SUPABASE_ANON_KEY']
        )
        
        # Get recent runs
        runs = supabase.table('hemp_agent_runs') \
            .select('*') \
            .order('timestamp', desc=True) \
            .limit(5) \
            .execute()
        
        if runs.data:
            print(f"Found {len(runs.data)} recent runs:")
            for run in runs.data:
                timestamp = run.get('timestamp', 'Unknown')
                agent = run.get('agent_name', 'Unknown')
                products = run.get('products_found', 0)
                status = run.get('status', 'Unknown')
                print(f"  - {timestamp}: {agent} - {products} products ({status})")
        else:
            print("No recent agent runs found")
        
        return True
    except Exception as e:
        print(f"❌ Error checking runs: {e}")
        return False

def check_openai_status():
    """Check OpenAI API status without making a real API call"""
    print("\n🔍 Checking OpenAI API Key Format")
    print("=" * 50)
    
    api_key = os.environ.get('OPENAI_API_KEY', '')
    
    if not api_key:
        print("❌ OPENAI_API_KEY not set")
        return False
    
    if api_key.startswith('sk-') and len(api_key) > 20:
        print("✅ API key format looks valid")
        print("⚠️  Note: Cannot verify quota without making an API call")
        print("   To check your quota, visit: https://platform.openai.com/account/billing")
        return True
    else:
        print("❌ API key format appears invalid")
        return False

def suggest_mock_mode():
    """Suggest running in mock mode"""
    print("\n💡 Suggestion: Run Agents in Mock Mode")
    print("=" * 50)
    print("Since you're out of OpenAI credits, you can:")
    print("1. Add credits at https://platform.openai.com/account/billing")
    print("2. Or create a mock version that uses sample data")
    print("\nWould you like me to create a mock agent for testing?")

def main():
    """Run all tests"""
    print("🌿 Hemp Agent System Test")
    print("=" * 50)
    print(f"Time: {datetime.now()}")
    print()
    
    # Run tests
    env_ok = test_environment()
    if env_ok:
        db_ok = test_database_connection()
        if db_ok:
            test_agent_runs()
    
    openai_ok = check_openai_status()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    if env_ok and db_ok:
        print("✅ Environment and database are properly configured")
        if not openai_ok or True:  # Always show this for now
            suggest_mock_mode()
    else:
        print("❌ Please fix the issues above before running agents")
    
    # Show consolidated agents
    print("\n📋 Available Consolidated Agents (9 total)")
    print("=" * 50)
    agents = {
        'seeds-food': 'Seeds for food & beverage',
        'seeds-nutrition': 'Seeds for nutritional supplements',
        'fiber-textiles': 'Fiber for textiles',
        'fiber-composites': 'Fiber for composites',
        'oil-personal-care': 'Oil for cosmetics, personal care & wellness',
        'oil-biofuel': 'Oil for biofuel',
        'flower-products': 'Flower for pharma, CBD, wellness & health',
        'hurds-materials': 'Hurds for construction, hempcrete & bedding',
        'roots-medicine': 'Roots for traditional medicine',
        'roots-biotech': 'Roots for biotech',
        'leaves-feed': 'Leaves for animal feed',
        'leaves-medicine': 'Leaves for medicine',
        'biomass-energy': 'Biomass for energy',
        'whole-plant': 'Whole plant for sustainability'
    }
    
    for name, desc in agents.items():
        print(f"  - {name:20} : {desc}")

if __name__ == "__main__":
    main()
