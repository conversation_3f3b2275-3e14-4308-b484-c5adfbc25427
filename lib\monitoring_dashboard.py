#!/usr/bin/env python3
"""
Terminal-based Monitoring Dashboard
Real-time monitoring display for HempQuarterz systems
"""

import asyncio
import os
import sys
from datetime import datetime
from typing import Dict, Any, List
import curses
from curses import wrapper
import signal

from .monitoring_service import MonitoringService, AlertSeverity


class MonitoringDashboard:
    """Terminal dashboard for real-time monitoring"""
    
    def __init__(self, stdscr):
        self.stdscr = stdscr
        self.monitoring_service = MonitoringService()
        self.running = True
        self.refresh_rate = 5  # seconds
        self.current_tab = 0
        self.tabs = ['Overview', 'Agents', 'Tasks', 'Images', 'Alerts']
        
        # Setup colors
        curses.start_color()
        curses.init_pair(1, curses.COLOR_GREEN, curses.COLOR_BLACK)
        curses.init_pair(2, curses.COLOR_YELLOW, curses.COLOR_BLACK)
        curses.init_pair(3, curses.COLOR_RED, curses.COLOR_BLACK)
        curses.init_pair(4, curses.COLOR_CYAN, curses.COLOR_BLACK)
        curses.init_pair(5, curses.COLOR_WHITE, curses.COLOR_BLACK)
        
        # Make cursor invisible
        curses.curs_set(0)
        
        # Non-blocking input
        self.stdscr.nodelay(True)
        
    async def run(self):
        """Main dashboard loop"""
        while self.running:
            try:
                # Get latest metrics
                metrics = await self.monitoring_service.collect_metrics()
                
                # Clear screen
                self.stdscr.clear()
                
                # Draw UI
                self._draw_header(metrics)
                self._draw_tabs()
                self._draw_content(metrics)
                self._draw_footer()
                
                # Refresh display
                self.stdscr.refresh()
                
                # Handle input
                key = self.stdscr.getch()
                if key == ord('q'):
                    self.running = False
                elif key == curses.KEY_LEFT and self.current_tab > 0:
                    self.current_tab -= 1
                elif key == curses.KEY_RIGHT and self.current_tab < len(self.tabs) - 1:
                    self.current_tab += 1
                elif key == ord('r'):
                    continue  # Force refresh
                    
                # Wait before next update
                await asyncio.sleep(self.refresh_rate)
                
            except KeyboardInterrupt:
                self.running = False
            except Exception as e:
                # Display error
                self.stdscr.addstr(10, 2, f"Error: {str(e)}", curses.color_pair(3))
                self.stdscr.refresh()
                await asyncio.sleep(5)
                
    def _draw_header(self, metrics: Dict[str, Any]):
        """Draw dashboard header"""
        height, width = self.stdscr.getmaxyx()
        
        # Title
        title = "🌿 HempQuarterz Monitoring Dashboard"
        self.stdscr.addstr(0, (width - len(title)) // 2, title, curses.A_BOLD | curses.color_pair(1))
        
        # Timestamp
        timestamp = f"Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        self.stdscr.addstr(1, width - len(timestamp) - 2, timestamp, curses.color_pair(5))
        
        # System health indicator
        health_color = curses.color_pair(1)  # Green
        if metrics.get('alerts'):
            severity_order = {AlertSeverity.CRITICAL: 3, AlertSeverity.ERROR: 3, 
                            AlertSeverity.WARNING: 2, AlertSeverity.INFO: 4}
            max_severity = max(severity_order.get(AlertSeverity(alert['severity']), 1) 
                             for alert in metrics['alerts'])
            health_color = curses.color_pair(max_severity)
            
        uptime = metrics['system']['uptime']
        health_text = f"System Health: {uptime:.1f}% | Alerts: {len(metrics.get('alerts', []))}"
        self.stdscr.addstr(1, 2, health_text, health_color)
        
        # Separator
        self.stdscr.hline(2, 0, curses.ACS_HLINE, width)
        
    def _draw_tabs(self):
        """Draw tab navigation"""
        height, width = self.stdscr.getmaxyx()
        y = 3
        x = 2
        
        for i, tab in enumerate(self.tabs):
            if i == self.current_tab:
                self.stdscr.addstr(y, x, f"[ {tab} ]", curses.A_BOLD | curses.color_pair(4))
            else:
                self.stdscr.addstr(y, x, f"  {tab}  ", curses.color_pair(5))
            x += len(tab) + 6
            
        self.stdscr.hline(4, 0, curses.ACS_HLINE, width)
        
    def _draw_content(self, metrics: Dict[str, Any]):
        """Draw main content based on current tab"""
        if self.current_tab == 0:
            self._draw_overview(metrics)
        elif self.current_tab == 1:
            self._draw_agents(metrics)
        elif self.current_tab == 2:
            self._draw_tasks(metrics)
        elif self.current_tab == 3:
            self._draw_images(metrics)
        elif self.current_tab == 4:
            self._draw_alerts(metrics)
            
    def _draw_overview(self, metrics: Dict[str, Any]):
        """Draw system overview"""
        y = 6
        
        # System stats
        self._draw_section_header(y, "System Overview")
        y += 2
        
        stats = [
            ("Total Automation Runs", metrics['system']['total_automation_runs']),
            ("Success Rate", f"{metrics['system']['automation_success_rate']:.1f}%"),
            ("Active Agents Today", metrics['system']['daily_active_agents']),
            ("Total Products", metrics['system']['total_products_discovered']),
            ("Total Companies", metrics['system']['total_companies_found']),
            ("Products Added (24h)", metrics['database']['growth_24h'])
        ]
        
        for label, value in stats:
            self.stdscr.addstr(y, 4, f"{label}:", curses.color_pair(5))
            self.stdscr.addstr(y, 30, str(value), curses.A_BOLD)
            y += 1
            
        # Quick status indicators
        y += 2
        self._draw_section_header(y, "Quick Status")
        y += 2
        
        # Agents
        active_agents = metrics['agents']['active']
        total_agents = metrics['agents']['total']
        agent_color = curses.color_pair(1) if active_agents == total_agents else curses.color_pair(2)
        self.stdscr.addstr(y, 4, f"Agents: {active_agents}/{total_agents} active", agent_color)
        y += 1
        
        # Tasks
        pending_tasks = metrics['tasks']['by_status'].get('pending', 0)
        task_color = curses.color_pair(1) if pending_tasks < 50 else curses.color_pair(2)
        if pending_tasks > 100:
            task_color = curses.color_pair(3)
        self.stdscr.addstr(y, 4, f"Task Queue: {pending_tasks} pending", task_color)
        y += 1
        
        # Images
        without_images = metrics['images']['products_without_images']
        image_color = curses.color_pair(1) if without_images < 100 else curses.color_pair(2)
        self.stdscr.addstr(y, 4, f"Products without images: {without_images}", image_color)
        
    def _draw_agents(self, metrics: Dict[str, Any]):
        """Draw agent details"""
        y = 6
        
        self._draw_section_header(y, "Agent Status")
        y += 2
        
        # Headers
        self.stdscr.addstr(y, 4, "Agent Name", curses.A_BOLD)
        self.stdscr.addstr(y, 25, "Status", curses.A_BOLD)
        self.stdscr.addstr(y, 35, "Success", curses.A_BOLD)
        self.stdscr.addstr(y, 45, "Tasks", curses.A_BOLD)
        self.stdscr.addstr(y, 55, "Last Run", curses.A_BOLD)
        y += 1
        
        # Agent rows
        for agent_name, agent_data in metrics['agents']['by_agent'].items():
            # Status indicator
            status = "●" if agent_data['active'] else "○"
            status_color = curses.color_pair(1) if agent_data['active'] else curses.color_pair(3)
            
            self.stdscr.addstr(y, 4, agent_name[:20], curses.color_pair(5))
            self.stdscr.addstr(y, 25, status, status_color)
            self.stdscr.addstr(y, 35, f"{agent_data['success_rate']:.0f}%", curses.color_pair(5))
            self.stdscr.addstr(y, 45, f"{agent_data['tasks_completed']}", curses.color_pair(5))
            
            if agent_data['last_run']:
                last_run_time = datetime.fromisoformat(agent_data['last_run'])
                time_ago = self._format_time_ago(last_run_time)
                self.stdscr.addstr(y, 55, time_ago, curses.color_pair(5))
            else:
                self.stdscr.addstr(y, 55, "Never", curses.color_pair(2))
                
            y += 1
            
    def _draw_tasks(self, metrics: Dict[str, Any]):
        """Draw task queue details"""
        y = 6
        
        self._draw_section_header(y, "Task Queue Status")
        y += 2
        
        # Queue stats
        queue_stats = [
            ("Pending", metrics['tasks']['by_status'].get('pending', 0), curses.color_pair(2)),
            ("Processing", metrics['tasks']['by_status'].get('processing', 0), curses.color_pair(4)),
            ("Completed", metrics['tasks']['by_status'].get('completed', 0), curses.color_pair(1)),
            ("Failed", metrics['tasks']['by_status'].get('failed', 0), curses.color_pair(3))
        ]
        
        for status, count, color in queue_stats:
            self.stdscr.addstr(y, 4, f"{status}:", curses.color_pair(5))
            self.stdscr.addstr(y, 20, str(count), color | curses.A_BOLD)
            
            # Bar graph
            max_width = 30
            bar_width = min(int(count / 10), max_width) if count > 0 else 0
            if bar_width > 0:
                self.stdscr.addstr(y, 30, "█" * bar_width, color)
                
            y += 1
            
        y += 2
        self._draw_section_header(y, "Performance Metrics")
        y += 2
        
        perf_stats = [
            ("Avg Wait Time", f"{metrics['tasks']['avg_wait_time']:.1f}s"),
            ("Avg Processing", f"{metrics['tasks']['avg_processing_time']:.1f}s"),
            ("Queue Depth", metrics['tasks']['queue_depth'])
        ]
        
        for label, value in perf_stats:
            self.stdscr.addstr(y, 4, f"{label}:", curses.color_pair(5))
            self.stdscr.addstr(y, 25, str(value), curses.A_BOLD)
            y += 1
            
    def _draw_images(self, metrics: Dict[str, Any]):
        """Draw image generation details"""
        y = 6
        
        self._draw_section_header(y, "Image Generation Status")
        y += 2
        
        # Overview stats
        overview_stats = [
            ("Products with images", metrics['images']['products_with_images'], curses.color_pair(1)),
            ("Products without images", metrics['images']['products_without_images'], curses.color_pair(2)),
            ("Total generated", metrics['images']['total_generated'], curses.color_pair(5)),
            ("Total cost", f"${metrics['images']['total_cost']:.2f}", curses.color_pair(5))
        ]
        
        for label, value, color in overview_stats:
            self.stdscr.addstr(y, 4, f"{label}:", curses.color_pair(5))
            self.stdscr.addstr(y, 30, str(value), color | curses.A_BOLD)
            y += 1
            
        y += 2
        self._draw_section_header(y, "Provider Performance")
        y += 2
        
        # Provider stats
        if metrics['images']['providers']:
            self.stdscr.addstr(y, 4, "Provider", curses.A_BOLD)
            self.stdscr.addstr(y, 20, "Total", curses.A_BOLD)
            self.stdscr.addstr(y, 30, "Success", curses.A_BOLD)
            self.stdscr.addstr(y, 40, "Avg Cost", curses.A_BOLD)
            y += 1
            
            for provider_name, provider_data in metrics['images']['providers'].items():
                self.stdscr.addstr(y, 4, provider_name[:15], curses.color_pair(5))
                self.stdscr.addstr(y, 20, str(provider_data['total']), curses.color_pair(5))
                self.stdscr.addstr(y, 30, f"{provider_data['success_rate']:.0f}%", curses.color_pair(5))
                self.stdscr.addstr(y, 40, f"${provider_data['avg_cost']:.4f}", curses.color_pair(5))
                y += 1
                
    def _draw_alerts(self, metrics: Dict[str, Any]):
        """Draw alerts and notifications"""
        y = 6
        
        alerts = metrics.get('alerts', [])
        
        if not alerts:
            self._draw_section_header(y, "✅ No Active Alerts")
            y += 2
            self.stdscr.addstr(y, 4, "All systems operating normally", curses.color_pair(1))
        else:
            self._draw_section_header(y, f"🚨 Active Alerts ({len(alerts)})")
            y += 2
            
            for alert in alerts:
                # Severity icon and color
                severity_icons = {
                    'critical': '🔴',
                    'error': '❌',
                    'warning': '⚠️',
                    'info': 'ℹ️'
                }
                severity_colors = {
                    'critical': curses.color_pair(3),
                    'error': curses.color_pair(3),
                    'warning': curses.color_pair(2),
                    'info': curses.color_pair(4)
                }
                
                icon = severity_icons.get(alert['severity'], '❓')
                color = severity_colors.get(alert['severity'], curses.color_pair(5))
                
                # Alert details
                self.stdscr.addstr(y, 4, icon + " " + alert['message'], color | curses.A_BOLD)
                y += 1
                
                details = f"Current: {alert['current_value']:.2f}, Threshold: {alert['threshold']}"
                self.stdscr.addstr(y, 6, details, curses.color_pair(5))
                y += 1
                
                triggered_time = datetime.fromisoformat(alert['triggered_at'])
                time_ago = self._format_time_ago(triggered_time)
                self.stdscr.addstr(y, 6, f"Triggered: {time_ago}", curses.color_pair(5))
                y += 2
                
    def _draw_footer(self):
        """Draw footer with controls"""
        height, width = self.stdscr.getmaxyx()
        y = height - 2
        
        # Separator
        self.stdscr.hline(y - 1, 0, curses.ACS_HLINE, width)
        
        # Controls
        controls = "← → Navigate | R Refresh | Q Quit"
        self.stdscr.addstr(y, 2, controls, curses.color_pair(5))
        
        # Refresh indicator
        refresh_text = f"Auto-refresh: {self.refresh_rate}s"
        self.stdscr.addstr(y, width - len(refresh_text) - 2, refresh_text, curses.color_pair(5))
        
    def _draw_section_header(self, y: int, title: str):
        """Draw a section header"""
        self.stdscr.addstr(y, 2, title, curses.A_BOLD | curses.color_pair(4))
        
    def _format_time_ago(self, dt: datetime) -> str:
        """Format datetime as time ago"""
        delta = datetime.now() - dt
        
        if delta.days > 0:
            return f"{delta.days}d ago"
        elif delta.seconds > 3600:
            return f"{delta.seconds // 3600}h ago"
        elif delta.seconds > 60:
            return f"{delta.seconds // 60}m ago"
        else:
            return "Just now"


def run_dashboard(stdscr):
    """Run the monitoring dashboard"""
    dashboard = MonitoringDashboard(stdscr)
    asyncio.run(dashboard.run())


def main():
    """Main entry point for dashboard"""
    try:
        wrapper(run_dashboard)
    except KeyboardInterrupt:
        print("\nDashboard closed")
    except Exception as e:
        print(f"Dashboard error: {e}")
        

if __name__ == "__main__":
    main()