#!/usr/bin/env python3
"""
Enhanced Research Agent with Automatic Image Generation
This agent discovers hemp products and automatically generates images for them
"""

import json
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any
import aiohttp
try:
    from .enhanced_research_agent import EnhancedResearchAgent
except ImportError:
    # If enhanced agent fails to import, create a base class
    from typing import List, Dict, Optional, Any
    
    class EnhancedResearchAgent:
        """Fallback base class if enhanced agent can't be imported"""
        def __init__(self, supabase_url: str, supabase_key: str, openai_api_key: Optional[str] = None):
            from supabase import create_client
            self.supabase = create_client(supabase_url, supabase_key)
            self.supabase_url = supabase_url
            self.supabase_key = supabase_key
            self.openai_api_key = openai_api_key
            
        async def discover_products(self, search_query: str, max_results: int = 10) -> List[Dict]:
            """Fallback discovery using hardcoded data"""
            return []
            
        def _extract_companies(self, product: Dict) -> List[str]:
            return product.get('companies', [])
            
        def _clean_product_name(self, name: str, companies: List[str]) -> str:
            return name
            
        async def _get_plant_part_id(self, plant_part: Optional[str]) -> Optional[int]:
            if not plant_part:
                return None
            result = await self.supabase.table('plant_parts').select('id').eq('name', plant_part).execute()
            return result.data[0]['id'] if result.data else None
            
        async def _get_industry_sub_category_id(self, industry: Optional[str], sub_industry: Optional[str]) -> Optional[int]:
            if not industry:
                return None
            result = await self.supabase.table('industries').select('id').eq('name', industry).execute()
            if result.data:
                industry_id = result.data[0]['id']
                sub_result = await self.supabase.table('industry_sub_categories').select('id').eq('industry_id', industry_id).execute()
                return sub_result.data[0]['id'] if sub_result.data else None
            return None
            
        async def _get_or_create_company(self, company_name: str) -> Optional[int]:
            result = await self.supabase.table('hemp_companies').select('id').eq('name', company_name).execute()
            if result.data:
                return result.data[0]['id']
            # Create new company
            new_company = {
                'name': company_name,
                'description': f'{company_name} - Hemp product manufacturer',
                'verified': False,
                'company_type': 'manufacturer'
            }
            result = await self.supabase.table('hemp_companies').insert(new_company).execute()
            return result.data[0]['id'] if result.data else None
            
        async def _link_product_to_companies(self, product_id: int, company_ids: List[int]):
            for company_id in company_ids:
                await self.supabase.table('hemp_company_products').insert({
                    'product_id': product_id,
                    'company_id': company_id
                }).execute()
                
        def _generate_keywords(self, product: Dict) -> List[str]:
            return []

logger = logging.getLogger(__name__)

class ResearchAgentWithImages(EnhancedResearchAgent):
    """Research agent that automatically generates images for discovered products"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.image_generation_enabled = True
        self.image_provider = 'imagen_3'  # Default to Google Imagen 3
        
    async def _queue_image_generation(self, product_id: int, product_name: str, product_description: str) -> Optional[int]:
        """Queue image generation for a product"""
        try:
            # Create enhanced prompt for better image generation
            prompt = self._create_image_prompt(product_name, product_description)
            
            # Add to image generation queue
            queue_entry = {
                'reference_type': 'product',
                'reference_id': product_id,
                'prompt': prompt,
                'provider': self.image_provider,
                'status': 'pending',
                'metadata': {
                    'type': 'product',
                    'product_id': product_id,
                    'product_name': product_name,
                    'auto_generated': True,
                    'generated_by': 'research_agent'
                },
                'created_at': datetime.now().isoformat()
            }
            
            result = await self.supabase.table('image_generation_queue').insert(queue_entry).execute()
            
            if result.data:
                queue_id = result.data[0]['id']
                logger.info(f"Queued image generation for product: {product_name} (Queue ID: {queue_id})")
                return queue_id
            
        except Exception as e:
            logger.error(f"Error queuing image generation: {e}")
            return None
    
    def _create_image_prompt(self, product_name: str, product_description: str) -> str:
        """Create an optimized prompt for image generation"""
        # Clean and enhance the prompt
        base_prompt = f"Industrial hemp product: {product_name}"
        
        # Add descriptive context
        if product_description:
            # Extract key features from description
            desc_lower = product_description.lower()
            
            # Add material/texture hints
            if any(word in desc_lower for word in ['fiber', 'textile', 'fabric']):
                base_prompt += ", showing textile fibers and fabric texture"
            elif any(word in desc_lower for word in ['plastic', 'bioplastic', 'composite']):
                base_prompt += ", modern industrial bioplastic material"
            elif any(word in desc_lower for word in ['oil', 'extract', 'cbd']):
                base_prompt += ", amber oil in glass container with hemp leaves"
            elif any(word in desc_lower for word in ['concrete', 'hempcrete', 'construction']):
                base_prompt += ", construction material blocks showing texture"
            elif any(word in desc_lower for word in ['food', 'seed', 'protein']):
                base_prompt += ", nutritious food product with hemp seeds"
            
        # Add style directives
        base_prompt += ", professional product photography, clean white background, high quality, commercial style, realistic, detailed"
        
        return base_prompt
    
    async def _trigger_batch_image_generation(self, queue_ids: List[int]):
        """Trigger image generation for a batch of queued items"""
        try:
            # Get Supabase edge function URL
            edge_function_url = f"{self.supabase_url}/functions/v1/generate-product-image"
            
            # Prepare headers with service role key for edge function
            headers = {
                'Authorization': f'Bearer {self.supabase_key}',
                'Content-Type': 'application/json'
            }
            
            # Process in small batches to avoid overwhelming the API
            batch_size = 5
            for i in range(0, len(queue_ids), batch_size):
                batch = queue_ids[i:i + batch_size]
                
                async with aiohttp.ClientSession() as session:
                    for queue_id in batch:
                        try:
                            payload = {'queueId': queue_id}
                            async with session.post(edge_function_url, json=payload, headers=headers) as response:
                                if response.status == 200:
                                    logger.info(f"Triggered image generation for queue ID: {queue_id}")
                                else:
                                    logger.error(f"Failed to trigger generation for queue ID {queue_id}: {response.status}")
                        except Exception as e:
                            logger.error(f"Error triggering generation for queue ID {queue_id}: {e}")
                
                # Brief delay between batches
                if i + batch_size < len(queue_ids):
                    await asyncio.sleep(2)
                    
        except Exception as e:
            logger.error(f"Error in batch image generation: {e}")
    
    async def save_discovered_products(self, products: List[Dict]) -> int:
        """Override to add image generation after saving products"""
        saved_count = 0
        queue_ids = []
        
        for product in products:
            try:
                # Extract company names from product
                companies = self._extract_companies(product)
                clean_name = self._clean_product_name(product['name'], companies)
                
                # Check if product already exists
                existing = await self.supabase.table('uses_products').select('id').eq('name', clean_name).execute()
                
                if not existing.data:
                    # Get IDs for foreign keys
                    plant_part_id = await self._get_plant_part_id(product.get('plant_part'))
                    industry_sub_category_id = await self._get_industry_sub_category_id(
                        product.get('industry'), 
                        product.get('sub_industry')
                    )
                    
                    # Determine primary company
                    primary_company_id = None
                    if companies:
                        primary_company_id = await self._get_or_create_company(companies[0])
                    
                    # Build database record
                    db_product = {
                        'name': clean_name,
                        'description': product['description'],
                        'plant_part_id': plant_part_id,
                        'industry_sub_category_id': industry_sub_category_id,
                        'primary_company_id': primary_company_id,
                        'benefits_advantages': product.get('benefits_advantages', []),
                        'sustainability_aspects': product.get('sustainability_aspects', []),
                        'technical_specifications': product.get('technical_specifications', {}),
                        'commercialization_stage': product.get('commercialization_stage', 'R&D'),
                        'keywords': self._generate_keywords(product),
                        'data_sources': {
                            'source': product.get('data_source', ''),
                            'url': product.get('source_url', ''),
                            'discovered_date': product.get('discovered_date', datetime.now().isoformat()),
                            'ai_extracted_companies': companies
                        },
                        # Start with placeholder image
                        'image_url': '/api/placeholder/400/300'
                    }
                    
                    # Insert product
                    result = await self.supabase.table('uses_products').insert(db_product).execute()
                    
                    if result.data:
                        saved_count += 1
                        product_id = result.data[0]['id']
                        logger.info(f"Saved new product: {clean_name}")
                        
                        # Queue image generation if enabled
                        if self.image_generation_enabled:
                            queue_id = await self._queue_image_generation(
                                product_id, 
                                clean_name, 
                                product['description']
                            )
                            if queue_id:
                                queue_ids.append(queue_id)
                        
                        # Create company relationships
                        if companies:
                            company_ids = []
                            for company_name in companies:
                                company_id = await self._get_or_create_company(company_name)
                                if company_id:
                                    company_ids.append(company_id)
                            
                            if company_ids:
                                await self._link_product_to_companies(product_id, company_ids)
                else:
                    logger.info(f"Product already exists: {clean_name}")
                    
                    # FIXED: Do NOT re-queue images for existing products
                    # This was causing the overpopulation issue
                    # if self.image_generation_enabled and existing.data:
                    #     # REMOVED: Re-queuing logic that was creating duplicates
                    #     pass
                    
                    # Still check if we need to add company relationships
                    if companies and existing.data:
                        product_id = existing.data[0]['id']
                        for company_name in companies:
                            company_id = await self._get_or_create_company(company_name)
                            if company_id:
                                await self._link_product_to_companies(product_id, [company_id])
                    
            except Exception as e:
                logger.error(f"Error saving product: {e}")
        
        # Trigger image generation for all queued items
        if queue_ids:
            logger.info(f"Triggering image generation for {len(queue_ids)} products...")
            await self._trigger_batch_image_generation(queue_ids)
        
        return saved_count
    
    async def update_product_with_generated_image(self, product_id: int, image_url: str):
        """Update product with generated image URL"""
        try:
            result = await self.supabase.table('uses_products').update({
                'image_url': image_url,
                'updated_at': datetime.now().isoformat()
            }).eq('id', product_id).execute()
            
            if result.data:
                logger.info(f"Updated product {product_id} with generated image")
                return True
        except Exception as e:
            logger.error(f"Error updating product with image: {e}")
        
        return False
    
    async def process_completed_images(self):
        """Process completed images from the queue and update products"""
        try:
            # Get completed images from queue
            completed = await self.supabase.table('image_generation_queue').select('*').eq('status', 'completed').eq('reference_type', 'product').execute()
            
            if completed.data:
                updated_count = 0
                for item in completed.data:
                    if item.get('generated_image_url') and item.get('reference_id'):
                        success = await self.update_product_with_generated_image(
                            item['reference_id'],
                            item['generated_image_url']
                        )
                        if success:
                            updated_count += 1
                            
                            # Mark queue item as applied
                            await self.supabase.table('image_generation_queue').update({
                                'status': 'applied',
                                'updated_at': datetime.now().isoformat()
                            }).eq('id', item['id']).execute()
                
                logger.info(f"Updated {updated_count} products with generated images")
                
        except Exception as e:
            logger.error(f"Error processing completed images: {e}")


async def main():
    """Example usage of research agent with automatic image generation"""
    import os
    from dotenv import load_dotenv
    load_dotenv()
    
    # Initialize agent
    agent = ResearchAgentWithImages(
        supabase_url=os.getenv('VITE_SUPABASE_URL'),
        supabase_key=os.getenv('VITE_SUPABASE_ANON_KEY'),
        openai_api_key=os.getenv('OPENAI_API_KEY')
    )
    
    print("🔬 Research Agent with Automatic Image Generation")
    print("=" * 60)
    
    # Example: Discover products in a specific category
    search_query = "hemp bioplastics automotive applications"
    print(f"🔍 Searching for: {search_query}")
    
    # Run discovery
    products = await agent.discover_products(search_query, max_results=10)
    
    if products:
        print(f"\n✅ Discovered {len(products)} products")
        
        # Save products (this will automatically queue image generation)
        saved = await agent.save_discovered_products(products)
        print(f"💾 Saved {saved} new products to database")
        print(f"🖼️ Queued image generation for all new products")
        
        # Wait a bit for images to generate
        print("\n⏳ Waiting for image generation to complete...")
        await asyncio.sleep(30)
        
        # Process completed images
        await agent.process_completed_images()
        print("✅ Updated products with generated images")
    else:
        print("❌ No products discovered")


if __name__ == "__main__":
    asyncio.run(main())