import React from 'react';
import { ExternalLink } from 'lucide-react';

interface Attribution {
  source_name?: string;
  source_url?: string;
  license?: string;
  attribution_required?: boolean;
  alt_text?: string;
}

interface AttributedImageProps {
  src?: string;
  alt: string;
  attribution?: Attribution;
  className?: string;
  fallbackSrc?: string;
  aspectRatio?: 'square' | '16:9' | '4:3' | 'auto';
}

export function AttributedImage({ 
  src, 
  alt, 
  attribution, 
  className = "", 
  fallbackSrc = "/images/placeholder-image.png",
  aspectRatio = 'auto'
}: AttributedImageProps) {
  const [imgSrc, setImgSrc] = React.useState(src || fallbackSrc);
  const [showAttribution, setShowAttribution] = React.useState(false);

  const aspectRatioClasses = {
    'square': 'aspect-square',
    '16:9': 'aspect-video',
    '4:3': 'aspect-[4/3]',
    'auto': ''
  };

  const handleError = () => {
    if (imgSrc !== fallbackSrc) {
      setImgSrc(fallbackSrc);
    }
  };

  if (!src && !fallbackSrc) {
    return null;
  }

  return (
    <figure 
      className="relative group overflow-hidden"
      onMouseEnter={() => setShowAttribution(true)}
      onMouseLeave={() => setShowAttribution(false)}
    >
      <div className={`relative overflow-hidden ${aspectRatioClasses[aspectRatio]}`}>
        <img 
          src={imgSrc} 
          alt={attribution?.alt_text || alt} 
          className={`w-full h-full object-cover ${className}`}
          onError={handleError}
          loading="lazy"
        />
        
        {/* Gradient overlay for better text readability */}
        {attribution?.attribution_required && (
          <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-black/70 to-transparent pointer-events-none" />
        )}
      </div>

      {/* Attribution overlay */}
      {attribution?.attribution_required && (
        <figcaption 
          className={`absolute bottom-0 left-0 right-0 bg-black/80 backdrop-blur-sm text-xs text-gray-200 px-3 py-2 transform transition-transform duration-200 ${
            showAttribution ? 'translate-y-0' : 'translate-y-full'
          }`}
        >
          <div className="flex items-center justify-between gap-2">
            <div className="flex-1 min-w-0">
              {attribution.source_url ? (
                <a 
                  href={attribution.source_url} 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="flex items-center gap-1 text-green-400 hover:text-green-300 transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  <span className="truncate">
                    {attribution.source_name || 'View Source'}
                  </span>
                  <ExternalLink className="w-3 h-3 flex-shrink-0" />
                </a>
              ) : (
                <span className="text-gray-300">
                  {attribution.source_name || 'Source'}
                </span>
              )}
            </div>
            {attribution.license && (
              <span className="text-gray-400 text-xs">
                {attribution.license}
              </span>
            )}
          </div>
        </figcaption>
      )}

      {/* Small attribution indicator when not hovering */}
      {attribution?.attribution_required && !showAttribution && (
        <div className="absolute bottom-2 right-2 bg-black/60 backdrop-blur-sm rounded-full p-1">
          <ExternalLink className="w-3 h-3 text-white/70" />
        </div>
      )}
    </figure>
  );
}