#!/usr/bin/env python3
"""
Direct runner for the research agent - bypasses CLI complexities.
This script demonstrates how to run the research agent with web scraping.
"""

import asyncio
import os
import logging
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def main():
    """Run the research agent directly"""
    logger.info("Starting Hemp Research Agent...")
    
    try:
        # Import required components
        logger.info("Loading dependencies...")
        from lib.supabase_client import get_supabase_client
        from agents.research.unified_research_agent import create_research_agent, ResearchFeatures
        
        # Get Supabase client
        logger.info("Connecting to Supabase...")
        supabase = get_supabase_client()
        
        # Create research agent without AI (web scraping only)
        logger.info("Creating research agent (web scraping mode)...")
        agent = create_research_agent(
            supabase,
            ai_provider=None,  # No AI provider - will use web scraping
            features=[
                ResearchFeatures.WEB_SCRAPING,
                ResearchFeatures.FEED_MONITORING,
                ResearchFeatures.COMPANY_EXTRACTION
            ]
        )
        
        # Run product discovery
        logger.info("\n" + "="*60)
        logger.info("DISCOVERING HEMP PRODUCTS")
        logger.info("="*60)
        logger.info("Query: 'industrial hemp products'")
        logger.info("Method: Web scraping from hemp industry sources")
        logger.info("Features: Web scraping, RSS feeds, Company extraction")
        logger.info("="*60 + "\n")
        
        # Execute discovery
        task = {
            'action': 'discover_products',
            'params': {
                'query': 'industrial hemp products',
                'limit': 10
            }
        }
        
        result = await agent.execute(task)
        
        # Display results
        logger.info("\n" + "="*60)
        logger.info("RESULTS")
        logger.info("="*60)
        logger.info(f"Status: {result['status']}")
        logger.info(f"Products found: {result['products_found']}")
        logger.info(f"Products saved to database: {result['products_saved']}")
        
        if result['products_found'] > 0:
            logger.info("\nSample products discovered:")
            for i, product in enumerate(result['products'][:5], 1):
                logger.info(f"\n{i}. {product.get('name', 'Unknown Product')}")
                logger.info(f"   Plant Part: {product.get('plant_part', 'N/A')}")
                logger.info(f"   Industry: {product.get('industry', 'N/A')}")
                logger.info(f"   Source: {product.get('data_source', 'N/A')}")
                if product.get('companies'):
                    logger.info(f"   Companies: {', '.join(product['companies'][:3])}")
        else:
            logger.info("\nNo products found. This could mean:")
            logger.info("- The web sources are temporarily unavailable")
            logger.info("- Network connectivity issues")
            logger.info("- The sources have changed their structure")
        
        logger.info("\n" + "="*60)
        logger.info("Research agent execution completed!")
        
    except Exception as e:
        logger.error(f"Error running research agent: {e}")
        logger.error("Stack trace:", exc_info=True)
        
        logger.info("\nTroubleshooting tips:")
        logger.info("1. Check your .env file has SUPABASE_URL and SUPABASE_ANON_KEY")
        logger.info("2. Ensure all dependencies are installed:")
        logger.info("   pip install supabase aiohttp feedparser beautifulsoup4 tenacity")
        logger.info("3. Check your internet connection")

if __name__ == "__main__":
    logger.info("Hemp Research Agent Runner v1.0")
    logger.info("-" * 60)
    asyncio.run(main())