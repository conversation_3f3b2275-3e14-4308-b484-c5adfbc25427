#!/usr/bin/env python3
"""
Simple test to check if the research agent is working
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Testing Research Agent...")

async def test():
    try:
        # Import after path is set
        from hemp_cli import HempCLI
        
        print("✅ Imports successful")
        
        # Create CLI instance
        cli = HempCLI()
        print("✅ CLI instance created")
        
        # Try to run agent with web scraping only
        print("\nRunning research agent with web scraping...")
        results = await cli.run_agent(
            'research',
            'hemp fiber products',
            features=['web'],
            max_results=3
        )
        
        print(f"\n✅ Agent completed! Found {len(results)} products")
        
        if results:
            print("\nFirst product:")
            print(f"  Name: {results[0].get('name', 'Unknown')}")
            print(f"  Plant Part: {results[0].get('plant_part', 'Unknown')}")
            print(f"  Industry: {results[0].get('industry', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test())
    print(f"\nTest {'PASSED' if success else 'FAILED'}!")