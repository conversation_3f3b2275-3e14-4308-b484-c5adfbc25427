// Test script for navigation flow
// Run with: node test-navigation-flow.js

const testUrls = [
  // Legacy URLs that should redirect
  { url: 'http://localhost:3000/all-products', expected: '/hemp-dex', description: 'All Products redirect' },
  { url: 'http://localhost:3000/products-by-category', expected: '/hemp-dex?tab=plant-parts', description: 'Products by Category redirect' },
  { url: 'http://localhost:3000/hemp-dex-old', expected: '/hemp-dex', description: 'Old HempDex redirect' },
  
  // Direct HempDex URLs
  { url: 'http://localhost:3000/hemp-dex', expected: '/hemp-dex', description: 'HempDex main page' },
  { url: 'http://localhost:3000/hemp-dex?tab=plant-parts', expected: '/hemp-dex?tab=plant-parts', description: 'HempDex Plant Parts tab' },
  { url: 'http://localhost:3000/hemp-dex?tab=industries', expected: '/hemp-dex?tab=industries', description: 'HempDex Industries tab' },
  { url: 'http://localhost:3000/hemp-dex?search=fiber', expected: '/hemp-dex?search=fiber', description: 'HempDex with search' },
];

console.log('Testing Navigation Flow...\n');

async function testNavigation() {
  for (const test of testUrls) {
    try {
      const response = await fetch(test.url, {
        redirect: 'manual', // Don't follow redirects automatically
        headers: {
          'User-Agent': 'Mozilla/5.0'
        }
      });
      
      if (response.status === 302 || response.status === 301) {
        const location = response.headers.get('location');
        console.log(`✅ ${test.description}: Redirects to ${location}`);
      } else if (response.status === 200) {
        console.log(`✅ ${test.description}: Direct access OK`);
      } else {
        console.log(`❌ ${test.description}: Unexpected status ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${test.description}: Error - ${error.message}`);
    }
  }
}

// Test search functionality
async function testSearch() {
  console.log('\nTesting Search Functionality...\n');
  
  const searchTerms = ['fiber', 'cbd', 'textile', 'construction'];
  
  for (const term of searchTerms) {
    const url = `http://localhost:3000/hemp-dex?search=${encodeURIComponent(term)}`;
    try {
      const response = await fetch(url);
      if (response.ok) {
        console.log(`✅ Search for "${term}": Page loads successfully`);
      } else {
        console.log(`❌ Search for "${term}": Failed with status ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Search for "${term}": Error - ${error.message}`);
    }
  }
}

// Run tests
testNavigation().then(() => testSearch());