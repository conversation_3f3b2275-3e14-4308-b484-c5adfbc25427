const { createClient } = require('../HempResourceHub/node_modules/@supabase/supabase-js');
require('../HempResourceHub/node_modules/dotenv').config({ path: '../.env' });

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkRelationships() {
  try {
    // Check industry sub-categories
    const { data: subCategories, error: subError } = await supabase
      .from('industry_sub_categories')
      .select('*')
      .limit(10);
    
    if (subError) {
      console.error('Error fetching sub-categories:', subError);
    } else {
      console.log('\n=== Industry Sub-Categories ===');
      console.log(`Found ${subCategories?.length || 0} sub-categories`);
      subCategories?.forEach(sub => {
        console.log(`- ${sub.name} (ID: ${sub.id}, Industry ID: ${sub.industry_id})`);
      });
    }
    
    // Check products with sub-category relationships
    const { data: productsWithSubs, error: prodError } = await supabase
      .from('uses_products')
      .select('id, name, industry_sub_category_id')
      .not('industry_sub_category_id', 'is', null)
      .limit(10);
    
    if (!prodError) {
      console.log('\n=== Products with Sub-Categories ===');
      console.log(`Found ${productsWithSubs?.length || 0} products with sub-categories`);
      productsWithSubs?.forEach(prod => {
        console.log(`- ${prod.name} (Sub-Category ID: ${prod.industry_sub_category_id})`);
      });
    }
    
    // Count products by plant part
    for (let i = 1; i <= 8; i++) {
      const { count } = await supabase
        .from('uses_products')
        .select('*', { count: 'exact', head: true })
        .eq('plant_part_id', i);
      
      console.log(`\nPlant Part ${i}: ${count} products`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkRelationships();