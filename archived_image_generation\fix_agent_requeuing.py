#!/usr/bin/env python3
"""
Fix script to prevent agents from re-queuing image generation for existing products
"""

import os
import sys

def fix_research_agent_with_images():
    """Fix the research_agent_with_images.py to prevent re-queuing"""
    
    file_path = 'agents/research/research_agent_with_images.py'
    
    if not os.path.exists(file_path):
        print(f"ERROR: File not found: {file_path}")
        return False
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find and comment out the problematic section
    old_code = """                    # Check if existing product needs image
                    if self.image_generation_enabled and existing.data:
                        product_id = existing.data[0]['id']
                        
                        # Check if product has only placeholder image
                        product_check = await self.supabase.table('uses_products').select('image_url').eq('id', product_id).execute()
                        if product_check.data and ('placeholder' in product_check.data[0].get('image_url', '') or not product_check.data[0].get('image_url')):
                            queue_id = await self._queue_image_generation(
                                product_id,
                                clean_name,
                                product['description']
                            )
                            if queue_id:
                                queue_ids.append(queue_id)"""
    
    new_code = """                    # FIXED: Do NOT re-queue images for existing products
                    # This was causing the overpopulation issue
                    # if self.image_generation_enabled and existing.data:
                    #     # REMOVED: Re-queuing logic that was creating duplicates
                    #     pass"""
    
    if old_code in content:
        content = content.replace(old_code, new_code)
        
        # Write the fixed content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"FIXED: {file_path}")
        print("   - Removed re-queuing logic for existing products")
        return True
    else:
        print(f"WARNING: Code pattern not found in {file_path}")
        print("   - File may have been already fixed or modified")
        return False

def add_image_queue_check():
    """Add a function to check if product already has queued image"""
    
    check_function = '''
async def has_pending_image(self, product_id: int) -> bool:
    """Check if product already has a pending or completed image in queue"""
    try:
        result = await self.supabase.table('image_generation_queue').select('id').eq('product_id', product_id).in_('status', ['pending', 'processing', 'completed']).execute()
        return bool(result.data)
    except:
        return False
'''
    
    print("\nAdd this function to your research agents to prevent duplicates:")
    print(check_function)
    
    print("\nThen use it before queuing:")
    print("""
    # Only queue if product doesn't already have an image queued
    if not await self.has_pending_image(product_id):
        queue_id = await self._queue_image_generation(product_id, name, description)
""")

def create_agent_deprecation_notice():
    """Create a notice file about deprecated agents"""
    
    notice = """# DEPRECATED AGENTS - DO NOT USE

## The Problem
Multiple research agents were re-queuing image generation for existing products,
causing the image_generation_queue to grow exponentially (1,893 entries for 219 products).

## Deprecated Files (DO NOT USE):
- run_agent_with_images.py
- run_simple_agent_with_images.py  
- run_enhanced_agent.py
- agents/research/research_agent_with_images.py (use unified agent instead)
- agents/research/enhanced_research_agent.py (use unified agent instead)

## Use Instead:
```bash
# Use the unified CLI
./hemp agent research "your query" --max-results 10

# Or use the unified research agent directly
from agents.research.unified_research_agent import UnifiedResearchAgent
```

## Key Rules:
1. NEVER re-queue images for products that already have an image_url (even placeholders)
2. Only queue images when creating NEW products
3. Check image_generation_queue before queuing to prevent duplicates
"""
    
    with open('DEPRECATED_AGENTS.md', 'w') as f:
        f.write(notice)
    
    print("\nCreated DEPRECATED_AGENTS.md")

def main():
    print("Fixing Agent Re-queuing Issue")
    print("=" * 50)
    
    # Fix the main culprit
    fix_research_agent_with_images()
    
    # Add helper function info
    add_image_queue_check()
    
    # Create deprecation notice
    create_agent_deprecation_notice()
    
    print("\nFix Complete!")
    print("\nIMPORTANT NEXT STEPS:")
    print("1. Stop using research_agent_with_images.py")
    print("2. Use only the unified_research_agent.py")
    print("3. Never re-queue images for existing products")
    print("4. Check DEPRECATED_AGENTS.md for list of files to avoid")

if __name__ == "__main__":
    main()