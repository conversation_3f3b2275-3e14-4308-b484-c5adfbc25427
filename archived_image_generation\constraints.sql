
-- Run this SQL in Supabase to prevent future duplicates:

-- 1. Unique constraint for active entries
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_product_active 
ON image_generation_queue(product_id) 
WHERE status IN ('pending', 'processing');

-- 2. Auto-cleanup trigger
CREATE OR REPLACE FUNCTION auto_cleanup_completed()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' THEN
        DELETE FROM image_generation_queue 
        WHERE product_id = NEW.product_id 
        AND id != NEW.id 
        AND status = 'completed';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cleanup_completed_entries
AFTER UPDATE ON image_generation_queue
FOR EACH ROW
WHEN (NEW.status = 'completed')
EXECUTE FUNCTION auto_cleanup_completed();
