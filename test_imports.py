#!/usr/bin/env python3
"""
Test basic imports to diagnose issues
"""

import sys
print(f"Python path: {sys.path[:3]}...")

try:
    from supabase import create_client
    print("✅ supabase import OK")
except ImportError as e:
    print(f"❌ supabase import failed: {e}")

try:
    from dotenv import load_dotenv
    print("✅ dotenv import OK")
except ImportError as e:
    print(f"❌ dotenv import failed: {e}")

try:
    from lib.supabase_client import get_supabase_client
    print("✅ lib.supabase_client import OK")
except ImportError as e:
    print(f"❌ lib.supabase_client import failed: {e}")

try:
    from utils.ai_providers import MultiProviderAI
    print("✅ utils.ai_providers import OK")
except ImportError as e:
    print(f"❌ utils.ai_providers import failed: {e}")

try:
    from hemp_cli import HempCLI
    print("✅ hemp_cli import OK")
except ImportError as e:
    print(f"❌ hemp_cli import failed: {e}")
    
try:
    from agents.research.unified_research_agent import create_research_agent
    print("✅ agents.research.unified_research_agent import OK")
except ImportError as e:
    print(f"❌ agents.research.unified_research_agent import failed: {e}")

print("\nTest complete!")