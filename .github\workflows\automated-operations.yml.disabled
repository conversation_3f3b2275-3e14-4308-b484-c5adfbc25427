# DISABLED - This workflow was causing frequent failures
# Renamed to .disabled on 2025-06-26 to stop resource waste
# Original schedule: Every 6 hours + daily
# Issue: Hemp CLI commands failing, missing infrastructure
# Replacement: manual-agent-operations.yml for controlled testing

name: Automated Operations (DISABLED)

on:
  # schedule:
  #   # High priority - Every 6 hours
  #   - cron: '0 */6 * * *'
  #   # Daily operations
  #   - cron: '0 8 * * *'
  
  workflow_dispatch:
    inputs:
      operation:
        description: 'Operation to run'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - agents
          - images
          - monitoring
      agent_type:
        description: 'Specific agent (if operation=agents)'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - research
          - content
          - seo
          - outreach
          - monetization
          - compliance

env:
  SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
  SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  PYTHONPATH: ${{ github.workspace }}

jobs:
  # Pre-flight checks
  validate-environment:
    runs-on: ubuntu-latest
    outputs:
      is_valid: ${{ steps.check.outputs.is_valid }}
      operation: ${{ steps.determine.outputs.operation }}
      
    steps:
    - name: Check environment
      id: check
      run: |
        MISSING=""
        [[ -z "${{ secrets.SUPABASE_URL }}" ]] && MISSING="$MISSING SUPABASE_URL"
        [[ -z "${{ secrets.SUPABASE_ANON_KEY }}" ]] && MISSING="$MISSING SUPABASE_ANON_KEY"
        
        if [[ -n "$MISSING" ]]; then
          echo "❌ Missing secrets:$MISSING"
          echo "is_valid=false" >> $GITHUB_OUTPUT
        else
          echo "✅ Environment validated"
          echo "is_valid=true" >> $GITHUB_OUTPUT
        fi
        
    - name: Determine operation
      id: determine
      run: |
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          echo "operation=${{ github.event.inputs.operation }}" >> $GITHUB_OUTPUT
        else
          # Schedule-based determination
          HOUR=$(date +%H)
          if [[ "${{ github.event.schedule }}" == "0 */6 * * *" ]]; then
            echo "operation=agents" >> $GITHUB_OUTPUT
          else
            echo "operation=monitoring" >> $GITHUB_OUTPUT
          fi
        fi

  # Agent Operations
  run-agents:
    needs: validate-environment
    if: |
      needs.validate-environment.outputs.is_valid == 'true' && 
      (needs.validate-environment.outputs.operation == 'all' || 
       needs.validate-environment.outputs.operation == 'agents')
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install dependencies
      run: pip install -r requirements.txt
        
    - name: Make hemp executable
      run: chmod +x hemp
        
    - name: Run agents
      run: |
        if [[ "${{ github.event.inputs.agent_type }}" == "all" ]] || [[ -z "${{ github.event.inputs.agent_type }}" ]]; then
          # Run priority agents based on schedule
          python hemp agent research "Discover emerging hemp products" --max-results 20
          python hemp agent content "Create content for recent products" --max-results 10
        else
          # Run specific agent
          python hemp agent ${{ github.event.inputs.agent_type }} "Process hemp data" --max-results 20
        fi

  # Image Generation - DISABLED to prevent over-generation
  # generate-images:
  #   needs: [validate-environment, run-agents]
  #   if: |
  #     always() && 
  #     needs.validate-environment.outputs.is_valid == 'true' && 
  #     (needs.validate-environment.outputs.operation == 'all' || 
  #      needs.validate-environment.outputs.operation == 'images')
  #   runs-on: ubuntu-latest
  #   
  #   steps:
  #   - uses: actions/checkout@v3
  #   
  #   - name: Set up Python
  #     uses: actions/setup-python@v4
  #     with:
  #       python-version: '3.10'
  #       
  #   - name: Install dependencies
  #     run: pip install -r requirements.txt
  #       
  #   - name: Check products without images
  #     id: check-images
  #     run: |
  #       echo "🔍 Image generation disabled - over-generation issue"

  # Monitoring and Reporting
  monitoring:
    needs: validate-environment
    if: |
      always() && 
      needs.validate-environment.outputs.is_valid == 'true' && 
      (needs.validate-environment.outputs.operation == 'all' || 
       needs.validate-environment.outputs.operation == 'monitoring')
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install dependencies
      run: pip install -r requirements.txt
        
    - name: Make hemp executable
      run: chmod +x hemp
        
    - name: Run monitoring
      run: |
        echo "📊 Running system monitoring..."
        
        # Health check
        python hemp monitor --format health || echo "Health check failed"
        
        # Database validation
        python hemp db validate || echo "Database validation failed"
        
        # Queue status
        echo -e "\n📋 Queue Status:"
        python -c "
        from supabase import create_client
        import os
        
        client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_ANON_KEY'))
        
        # Get queue stats
        result = client.rpc('get_queue_statistics').execute()
        if result.data:
            for stat in result.data:
                print(f'{stat['status']}: {stat['count']}')
        " || echo "Queue status check failed"
        
    - name: Generate report
      if: github.event.schedule == '0 8 * * *' || github.event.inputs.operation == 'monitoring'
      run: |
        python hemp monitor --format report > daily-report.md
        
    - name: Upload report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: monitoring-report-${{ github.run_number }}
        path: daily-report.md
        retention-days: 30
        if-no-files-found: ignore

  # Summary
  create-summary:
    needs: [run-agents, monitoring]  # Removed generate-images
    if: always()
    runs-on: ubuntu-latest
    
    steps:
    - name: Create summary
      run: |
        echo "## 🚀 Automated Operations Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Run ID**: ${{ github.run_id }}" >> $GITHUB_STEP_SUMMARY
        echo "**Triggered by**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        echo "### Operations Executed" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Environment Validation" >> $GITHUB_STEP_SUMMARY
        [[ "${{ needs.run-agents.result }}" == "success" ]] && echo "- ✅ Agent Operations" >> $GITHUB_STEP_SUMMARY
        [[ "${{ needs.monitoring.result }}" == "success" ]] && echo "- ✅ Monitoring" >> $GITHUB_STEP_SUMMARY
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "[View Full Logs](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY