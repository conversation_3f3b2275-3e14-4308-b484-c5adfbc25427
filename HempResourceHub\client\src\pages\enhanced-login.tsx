import { useState } from 'react';
import { Link, useLocation } from 'wouter';
import { Helmet } from 'react-helmet';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Loader2, Eye, EyeOff, Leaf, Shield, Chrome, Mail } from 'lucide-react';
import { useAuth } from '@/components/auth/enhanced-auth-provider';
import HempQuarterzLogo from '@/assets/circle-logo.png?url';

export const EnhancedLoginPage = () => {
  const [, setLocation] = useLocation();
  const { signIn, signInWithOAuth, loading } = useAuth();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mfaRequired, setMfaRequired] = useState(false);
  const [mfaCode, setMfaCode] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      const { error } = await signIn(formData.email, formData.password);
      
      if (error) {
        if (error.message.includes('MFA')) {
          setMfaRequired(true);
        } else {
          setError(error.message);
        }
      } else {
        setLocation('/admin');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setError(null);
    setIsSubmitting(true);

    try {
      const { error } = await signInWithOAuth('google');
      if (error) {
        setError(error.message);
      }
    } catch (err) {
      setError('Failed to sign in with Google. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const fillDemoCredentials = (type: 'admin' | 'user') => {
    if (type === 'admin') {
      setFormData({
        email: '<EMAIL>',
        password: 'admin123',
      });
    } else {
      setFormData({
        email: '<EMAIL>',
        password: 'user123',
      });
    }
  };

  return (
    <>
      <Helmet>
        <title>Sign In - HempQuarterz Database</title>
        <meta name="description" content="Sign in to access the HempQuarterz industrial hemp database and analytics platform." />
      </Helmet>

      <div className="min-h-screen bg-black flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-6">
          <Card className="bg-gray-900/60 backdrop-blur-sm border-green-500/30">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <img 
                  src={HempQuarterzLogo} 
                  alt="HempQuarterz Logo" 
                  className="h-16 w-16 rounded-full"
                />
              </div>
              <CardTitle className="text-2xl font-bold text-white hemp-brand-ultra">
                Welcome Back
              </CardTitle>
              <CardDescription className="text-gray-400">
                Sign in to your HempQuarterz account
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!mfaRequired ? (
                <>
                  {/* OAuth Buttons */}
                  <div className="space-y-3 mb-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleGoogleSignIn}
                      disabled={isSubmitting}
                      className="w-full bg-white hover:bg-gray-100 text-gray-900 border-gray-300"
                    >
                      <Chrome className="mr-2 h-4 w-4" />
                      Continue with Google
                    </Button>
                  </div>

                  <div className="relative mb-6">
                    <div className="absolute inset-0 flex items-center">
                      <Separator className="w-full bg-gray-700" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-gray-900 px-2 text-gray-400">Or continue with email</span>
                    </div>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    {error && (
                      <Alert className="border-red-500/50 bg-red-500/10">
                        <AlertDescription className="text-red-400">
                          {error}
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-gray-300">
                        Email Address
                      </Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        value={formData.email}
                        onChange={handleInputChange}
                        className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-green-400"
                        placeholder="Enter your email"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password" className="text-gray-300">
                        Password
                      </Label>
                      <div className="relative">
                        <Input
                          id="password"
                          name="password"
                          type={showPassword ? 'text' : 'password'}
                          autoComplete="current-password"
                          required
                          value={formData.password}
                          onChange={handleInputChange}
                          className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-green-400 pr-10"
                          placeholder="Enter your password"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-300"
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <Link href="/forgot-password">
                        <span className="text-sm text-green-400 hover:text-green-300 cursor-pointer">
                          Forgot your password?
                        </span>
                      </Link>
                    </div>

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-green-600 hover:bg-green-700 text-white"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Signing In...
                        </>
                      ) : (
                        'Sign In'
                      )}
                    </Button>
                  </form>
                </>
              ) : (
                <div className="space-y-4">
                  <div className="text-center">
                    <Shield className="mx-auto h-12 w-12 text-green-400 mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">Two-Factor Authentication</h3>
                    <p className="text-sm text-gray-400">
                      Enter the 6-digit code from your authenticator app
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="mfaCode" className="text-gray-300">
                      Authentication Code
                    </Label>
                    <Input
                      id="mfaCode"
                      name="mfaCode"
                      type="text"
                      maxLength={6}
                      value={mfaCode}
                      onChange={(e) => setMfaCode(e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-green-400 text-center text-lg tracking-widest"
                      placeholder="000000"
                    />
                  </div>

                  <Button
                    type="button"
                    disabled={isSubmitting || mfaCode.length !== 6}
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      'Verify Code'
                    )}
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setMfaRequired(false)}
                    className="w-full border-gray-700 text-gray-300 hover:bg-gray-800"
                  >
                    Back to Login
                  </Button>
                </div>
              )}

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-400">
                  Don't have an account?{' '}
                  <Link href="/enhanced-register">
                    <span className="text-green-400 hover:text-green-300 cursor-pointer font-medium">
                      Sign up here
                    </span>
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Demo Credentials */}
          <Card className="bg-gray-900/60 backdrop-blur-sm border-blue-500/30">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="text-sm font-medium text-blue-400 mb-3">Demo Credentials</h3>
                <div className="space-y-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => fillDemoCredentials('admin')}
                    className="w-full text-xs border-blue-500/50 text-blue-400 hover:bg-blue-500/10"
                  >
                    Fill Admin Demo (<EMAIL>)
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => fillDemoCredentials('user')}
                    className="w-full text-xs border-green-500/50 text-green-400 hover:bg-green-500/10"
                  >
                    Fill User Demo (<EMAIL>)
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default EnhancedLoginPage;
