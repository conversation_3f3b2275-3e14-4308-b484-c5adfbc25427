import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Product {
  id: string;
  name: string;
  icon: string;
  color: string;
  category: string;
}

interface SeedToProductAnimationProps {
  autoPlay?: boolean;
  duration?: number;
  onProductChange?: (product: Product) => void;
}

export const SeedToProductAnimation: React.FC<SeedToProductAnimationProps> = ({
  autoPlay = true,
  duration = 4000,
  onProductChange
}) => {
  const [currentProductIndex, setCurrentProductIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const products: Product[] = [
    { id: 'textile', name: 'Hemp Textiles', icon: '👕', color: '#3B82F6', category: 'Clothing' },
    { id: 'oil', name: 'Hemp Oil', icon: '🛢️', color: '#F59E0B', category: 'Wellness' },
    { id: 'paper', name: 'Hemp Paper', icon: '📄', color: '#10B981', category: 'Materials' },
    { id: 'rope', name: 'Hemp Rope', icon: '🪢', color: '#8B5CF6', category: 'Industrial' },
    { id: 'food', name: 'Hemp Seeds', icon: '🌰', color: '#EF4444', category: 'Nutrition' },
    { id: 'building', name: 'Hempcrete', icon: '🧱', color: '#6B7280', category: 'Construction' }
  ];

  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentProductIndex((prev) => (prev + 1) % products.length);
        setIsAnimating(false);
        onProductChange?.(products[(currentProductIndex + 1) % products.length]);
      }, 500);
    }, duration);

    return () => clearInterval(interval);
  }, [autoPlay, duration, currentProductIndex, onProductChange, products]);

  const currentProduct = products[currentProductIndex];

  const seedVariants = {
    initial: { scale: 1, rotate: 0 },
    growing: { 
      scale: [1, 1.2, 1.5, 1.2, 1],
      rotate: [0, 90, 180, 270, 360],
      transition: { duration: 2, ease: "easeInOut" }
    }
  };

  const transformationVariants = {
    initial: { opacity: 0, scale: 0, rotate: -180 },
    animate: { 
      opacity: 1, 
      scale: 1, 
      rotate: 0,
      transition: { 
        type: "spring", 
        stiffness: 200, 
        damping: 15,
        delay: 0.5
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0, 
      rotate: 180,
      transition: { duration: 0.3 }
    }
  };

  const particleVariants = {
    initial: { opacity: 0, scale: 0 },
    animate: { 
      opacity: [0, 1, 0], 
      scale: [0, 1, 0],
      x: [0, Math.random() * 100 - 50],
      y: [0, Math.random() * 100 - 50],
      transition: { duration: 1.5, ease: "easeOut" }
    }
  };

  const pathVariants = {
    initial: { pathLength: 0, opacity: 0 },
    animate: { 
      pathLength: 1, 
      opacity: 1,
      transition: { duration: 1.5, ease: "easeInOut" }
    }
  };

  return (
    <div className="relative w-full max-w-lg mx-auto h-80 flex items-center justify-center">
      {/* Background glow */}
      <div className="absolute inset-0 bg-gradient-radial from-green-500/10 via-transparent to-transparent rounded-full"></div>

      {/* Main animation container */}
      <div className="relative w-64 h-64">
        {/* Hemp seed */}
        <motion.div
          className="absolute left-8 top-1/2 transform -translate-y-1/2 z-10"
          variants={seedVariants}
          initial="initial"
          animate={isAnimating ? "growing" : "initial"}
        >
          <div className="w-12 h-12 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-2xl">🌰</span>
          </div>
        </motion.div>

        {/* Transformation path */}
        <svg
          className="absolute inset-0 w-full h-full"
          viewBox="0 0 256 256"
        >
          <motion.path
            d="M64 128 Q128 64 192 128"
            stroke="url(#pathGradient)"
            strokeWidth="3"
            fill="none"
            variants={pathVariants}
            initial="initial"
            animate={isAnimating ? "animate" : "initial"}
          />
          <defs>
            <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#22c55e" />
              <stop offset="50%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor={currentProduct.color} />
            </linearGradient>
          </defs>
        </svg>

        {/* Transformation particles */}
        {isAnimating && [...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute left-1/2 top-1/2 w-2 h-2 bg-green-400 rounded-full"
            variants={particleVariants}
            initial="initial"
            animate="animate"
            style={{
              left: `${50 + (Math.random() - 0.5) * 20}%`,
              top: `${50 + (Math.random() - 0.5) * 20}%`
            }}
          />
        ))}

        {/* Product result */}
        <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentProduct.id}
              className="relative"
              variants={transformationVariants}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <div 
                className="w-16 h-16 rounded-xl flex items-center justify-center shadow-lg border-2"
                style={{ 
                  backgroundColor: `${currentProduct.color}20`,
                  borderColor: currentProduct.color
                }}
              >
                <span className="text-3xl">{currentProduct.icon}</span>
              </div>
              
              {/* Product glow effect */}
              <div 
                className="absolute inset-0 rounded-xl blur-md opacity-30"
                style={{ backgroundColor: currentProduct.color }}
              ></div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Product info */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentProduct.id}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: 0.5 }}
          >
            <div className="bg-gray-900/80 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-700">
              <h3 className="text-white font-semibold text-sm">{currentProduct.name}</h3>
              <p className="text-gray-400 text-xs">{currentProduct.category}</p>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Progress indicators */}
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {products.map((_, index) => (
            <motion.div
              key={index}
              className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                index === currentProductIndex ? 'bg-green-400' : 'bg-gray-600'
              }`}
              animate={{
                scale: index === currentProductIndex ? 1.2 : 1
              }}
            />
          ))}
        </div>
      </div>

      {/* Control buttons */}
      <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 flex space-x-2">
        <button
          onClick={() => {
            setCurrentProductIndex((prev) => (prev - 1 + products.length) % products.length);
          }}
          className="px-3 py-1 bg-gray-800 hover:bg-gray-700 text-white rounded text-sm transition-colors"
        >
          ←
        </button>
        <button
          onClick={() => {
            setCurrentProductIndex((prev) => (prev + 1) % products.length);
          }}
          className="px-3 py-1 bg-gray-800 hover:bg-gray-700 text-white rounded text-sm transition-colors"
        >
          →
        </button>
      </div>
    </div>
  );
};

export default SeedToProductAnimation;
