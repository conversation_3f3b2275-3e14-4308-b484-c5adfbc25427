#!/usr/bin/env python3
"""
Direct test of web scraping functionality
"""

import asyncio
import aiohttp
from bs4 import BeautifulSoup
import feedparser
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_web_scraping():
    """Test direct web scraping from hemp sources"""
    
    sources = [
        {
            'name': 'Hemp Industry Daily Feed',
            'url': 'https://hempindustrydaily.com/feed/',
            'type': 'rss'
        },
        {
            'name': 'Vote Hemp',
            'url': 'https://votehemp.com',
            'type': 'website'
        }
    ]
    
    all_items = []
    
    for source in sources:
        logger.info(f"\nTesting {source['name']}...")
        
        if source['type'] == 'rss':
            try:
                # Parse RSS feed
                feed = feedparser.parse(source['url'])
                logger.info(f"Feed title: {feed.feed.get('title', 'Unknown')}")
                logger.info(f"Found {len(feed.entries)} entries")
                
                for entry in feed.entries[:5]:  # First 5 entries
                    item = {
                        'title': entry.get('title', ''),
                        'description': entry.get('summary', ''),
                        'url': entry.get('link', ''),
                        'source': source['name'],
                        'published': entry.get('published', '')
                    }
                    all_items.append(item)
                    logger.info(f"  - {item['title'][:60]}...")
                    
            except Exception as e:
                logger.error(f"RSS error: {e}")
                
        elif source['type'] == 'website':
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(source['url'], timeout=10) as response:
                        if response.status == 200:
                            html = await response.text()
                            soup = BeautifulSoup(html, 'html.parser')
                            
                            # Look for news/blog articles
                            articles = soup.find_all(['article', 'div'], class_=['post', 'news-item', 'blog-post'])[:5]
                            logger.info(f"Found {len(articles)} articles")
                            
                            for article in articles:
                                title_elem = article.find(['h1', 'h2', 'h3', 'a'])
                                desc_elem = article.find(['p', 'div'], class_=['excerpt', 'summary', 'description'])
                                
                                if title_elem:
                                    item = {
                                        'title': title_elem.get_text(strip=True),
                                        'description': desc_elem.get_text(strip=True) if desc_elem else '',
                                        'url': source['url'],
                                        'source': source['name']
                                    }
                                    all_items.append(item)
                                    logger.info(f"  - {item['title'][:60]}...")
                        else:
                            logger.warning(f"HTTP {response.status} from {source['url']}")
                            
            except Exception as e:
                logger.error(f"Web scraping error: {e}")
    
    logger.info(f"\nTotal items found: {len(all_items)}")
    
    # Process items into products
    products = []
    for item in all_items:
        # Simple product extraction from title/description
        text = f"{item['title']} {item['description']}".lower()
        
        # Check if it mentions a product
        product_keywords = ['product', 'launches', 'introduces', 'announces', 'develops', 'creates', 'hemp', 'fiber', 'oil', 'seed']
        if any(keyword in text for keyword in product_keywords):
            product = {
                'name': item['title'],
                'description': item['description'][:200],
                'source_url': item['url'],
                'data_source': item['source'],
                'plant_part': 'fiber' if 'fiber' in text else 'seeds' if 'seed' in text else 'biomass',
                'industry': 'Textiles' if 'textile' in text else 'Food & Beverage' if 'food' in text else 'Other'
            }
            products.append(product)
    
    logger.info(f"\nExtracted {len(products)} potential products")
    for i, product in enumerate(products[:3], 1):
        logger.info(f"\n{i}. {product['name']}")
        logger.info(f"   Plant part: {product['plant_part']}")
        logger.info(f"   Industry: {product['industry']}")

if __name__ == "__main__":
    logger.info("Testing Web Scraping Directly...")
    asyncio.run(test_web_scraping())