import React from "react";
import { cn } from "@/lib/utils";

// Typography component for consistent text styling across the app

interface TypographyProps {
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

// Heading Components
export function H1({ children, className, as = "h1" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-tight tracking-tight",
      className
    )}>
      {children}
    </Component>
  );
}

export function H2({ children, className, as = "h2" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-2xl md:text-3xl lg:text-4xl font-bold text-white leading-tight",
      className
    )}>
      {children}
    </Component>
  );
}

export function H3({ children, className, as = "h3" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-xl md:text-2xl lg:text-3xl font-semibold text-white leading-tight",
      className
    )}>
      {children}
    </Component>
  );
}

export function H4({ children, className, as = "h4" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-lg md:text-xl lg:text-2xl font-semibold text-white leading-tight",
      className
    )}>
      {children}
    </Component>
  );
}

export function H5({ children, className, as = "h5" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-base md:text-lg lg:text-xl font-semibold text-white leading-tight",
      className
    )}>
      {children}
    </Component>
  );
}

export function H6({ children, className, as = "h6" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-sm md:text-base lg:text-lg font-semibold text-white leading-tight",
      className
    )}>
      {children}
    </Component>
  );
}

// Body Text Components
export function BodyLarge({ children, className, as = "p" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-lg md:text-xl text-gray-300 leading-relaxed",
      className
    )}>
      {children}
    </Component>
  );
}

export function Body({ children, className, as = "p" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-base md:text-lg text-gray-300 leading-relaxed",
      className
    )}>
      {children}
    </Component>
  );
}

export function BodySmall({ children, className, as = "p" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-sm md:text-base text-gray-400 leading-relaxed",
      className
    )}>
      {children}
    </Component>
  );
}

export function Caption({ children, className, as = "span" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-xs md:text-sm text-gray-500 leading-normal",
      className
    )}>
      {children}
    </Component>
  );
}

// Specialized Text Components
export function Lead({ children, className, as = "p" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-lg md:text-xl lg:text-2xl text-gray-300 leading-relaxed font-light",
      className
    )}>
      {children}
    </Component>
  );
}

export function Muted({ children, className, as = "span" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-sm text-gray-500",
      className
    )}>
      {children}
    </Component>
  );
}

export function Code({ children, className, as = "code" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "relative rounded bg-gray-800 px-2 py-1 font-mono text-sm text-gray-300",
      className
    )}>
      {children}
    </Component>
  );
}

// Brand Text Components
export function BrandText({ children, className, as = "span" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent font-bold",
      className
    )}>
      {children}
    </Component>
  );
}

export function HighlightText({ children, className, as = "span" }: TypographyProps) {
  const Component = as;
  return (
    <Component className={cn(
      "text-green-400 font-medium",
      className
    )}>
      {children}
    </Component>
  );
}

// List Components
interface ListProps {
  children: React.ReactNode;
  className?: string;
}

export function List({ children, className }: ListProps) {
  return (
    <ul className={cn(
      "space-y-2 text-gray-300 leading-relaxed",
      className
    )}>
      {children}
    </ul>
  );
}

export function ListItem({ children, className }: ListProps) {
  return (
    <li className={cn(
      "flex items-start gap-2",
      className
    )}>
      <span className="text-green-400 mt-1.5 flex-shrink-0">•</span>
      <span className="text-sm md:text-base">{children}</span>
    </li>
  );
}

// Responsive Text Utilities
export const textSizes = {
  xs: "text-xs",
  sm: "text-sm",
  base: "text-base",
  lg: "text-lg",
  xl: "text-xl",
  "2xl": "text-2xl",
  "3xl": "text-3xl",
  "4xl": "text-4xl",
  "5xl": "text-5xl",
  "6xl": "text-6xl",
};

export const responsiveTextSizes = {
  xs: "text-xs",
  sm: "text-xs md:text-sm",
  base: "text-sm md:text-base",
  lg: "text-base md:text-lg",
  xl: "text-lg md:text-xl",
  "2xl": "text-xl md:text-2xl",
  "3xl": "text-2xl md:text-3xl",
  "4xl": "text-3xl md:text-4xl lg:text-5xl",
  "5xl": "text-4xl md:text-5xl lg:text-6xl",
  "6xl": "text-5xl md:text-6xl lg:text-7xl",
};

export const textColors = {
  primary: "text-white",
  secondary: "text-gray-300",
  muted: "text-gray-400",
  subtle: "text-gray-500",
  brand: "text-green-400",
  success: "text-green-400",
  warning: "text-yellow-400",
  error: "text-red-400",
  info: "text-blue-400",
};

// Utility function for creating consistent text styles
export function createTextStyle(
  size: keyof typeof responsiveTextSizes,
  color: keyof typeof textColors,
  weight?: "normal" | "medium" | "semibold" | "bold"
) {
  const weightClass = weight ? `font-${weight}` : "";
  return cn(responsiveTextSizes[size], textColors[color], weightClass);
}

export default {
  H1,
  H2,
  H3,
  H4,
  H5,
  H6,
  Body,
  BodyLarge,
  BodySmall,
  Caption,
  Lead,
  Muted,
  Code,
  BrandText,
  HighlightText,
  List,
  ListItem,
};
