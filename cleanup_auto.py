#!/usr/bin/env python3
"""
Automated cleanup script - runs without user confirmation
"""

import os
import sys
from datetime import datetime

# Add the v2 cleanup script's directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the cleanup functions
from cleanup_image_generation_tables_v2 import (
    cleanup_orphaned_history,
    cleanup_queue_duplicates,
    verify_cleanup,
    add_constraints_sql,
    logger
)

def main():
    """Run cleanup automatically"""
    logger.info("=== Automated Image Generation Cleanup ===")
    logger.info("Running without user confirmation")
    
    # Get initial stats
    initial_stats = verify_cleanup()
    logger.info(f"\nInitial state: {initial_stats['queue_entries']} queue entries for {initial_stats['total_products']} products")
    
    if initial_stats['queue_entries'] <= initial_stats['total_products']:
        logger.info("No cleanup needed - queue size is reasonable")
        return
    
    # Step 1: Clean history
    logger.info("\n--- Step 1: Cleaning History ---")
    history_deleted = cleanup_orphaned_history()
    
    # Step 2: Clean queue
    logger.info("\n--- Step 2: Cleaning Queue ---")
    queue_stats = cleanup_queue_duplicates()
    
    # Step 3: Verify results
    logger.info("\n--- Step 3: Verifying Results ---")
    final_stats = verify_cleanup()
    
    # Show improvement
    logger.info(f"\n=== Cleanup Summary ===")
    logger.info(f"Queue entries: {initial_stats['queue_entries']} → {final_stats['queue_entries']} "
               f"(reduced by {initial_stats['queue_entries'] - final_stats['queue_entries']})")
    logger.info(f"History entries: {initial_stats['history_entries']} → {final_stats['history_entries']} "
               f"(reduced by {initial_stats['history_entries'] - final_stats['history_entries']})")
    
    # Save SQL constraints to file
    logger.info("\n=== Saving Database Constraints ===")
    with open('apply_constraints.sql', 'w') as f:
        f.write(add_constraints_sql())
    logger.info("SQL constraints saved to apply_constraints.sql")
    
    logger.info("\n=== Cleanup Complete ===")

if __name__ == "__main__":
    main()