#!/usr/bin/env python3
"""
Test the image generation setup and show current status
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load .env from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
load_dotenv(env_path)

# Get Supabase credentials
SUPABASE_URL = os.getenv('VITE_SUPABASE_URL')
SUPABASE_KEY = os.getenv('VITE_SUPABASE_ANON_KEY')

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Missing Supabase credentials")
    exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def check_setup():
    """Check the current image generation setup"""
    print("🔍 Checking Image Generation Setup")
    print("=" * 60)
    
    # Check if tables exist
    try:
        # Check image generation queue
        queue = supabase.table('image_generation_queue').select('count').execute()
        print("✅ image_generation_queue table exists")
        
        # Get queue statistics
        statuses = supabase.table('image_generation_queue').select('status').execute()
        if statuses.data:
            status_counts = {}
            for item in statuses.data:
                status = item['status']
                status_counts[status] = status_counts.get(status, 0) + 1
            
            print("\n📊 Queue Status:")
            for status, count in status_counts.items():
                print(f"   {status}: {count}")
        else:
            print("   Queue is empty")
            
    except Exception as e:
        print(f"❌ image_generation_queue table error: {e}")
    
    # Check AI providers
    try:
        providers = supabase.table('ai_provider_config').select('*').execute()
        if providers.data:
            print("\n🤖 AI Providers Configured:")
            for provider in providers.data:
                status = "✅ Active" if provider.get('is_active', True) else "❌ Inactive"
                name = provider.get('display_name', provider.get('provider_name', 'Unknown'))
                cost = provider.get('cost_per_image', 0.02)
                print(f"   {name} - ${cost}/image - {status}")
    except Exception as e:
        print(f"❌ ai_provider_config table error: {e}")
    
    # Check products needing images
    try:
        # Count products with null image_url
        null_images = supabase.table('uses_products').select('count').is_('image_url', 'null').execute()
        null_count = null_images.data[0]['count'] if null_images.data else 0
        
        # Count products with placeholder images (using contains instead of like)
        all_products = supabase.table('uses_products').select('id, image_url').execute()
        placeholder_count = 0
        if all_products.data:
            for product in all_products.data:
                if product.get('image_url') and 'placeholder' in product['image_url']:
                    placeholder_count += 1
        
        total_needing_images = null_count + placeholder_count
        
        print(f"\n📦 Products Status:")
        print(f"   Products with no image: {null_count}")
        print(f"   Products with placeholder: {placeholder_count}")
        print(f"   Total needing images: {total_needing_images}")
        
        # Get total products
        total = supabase.table('uses_products').select('count').execute()
        if total.data:
            total_count = total.data[0]['count']
            print(f"   Total products: {total_count}")
            if total_count > 0:
                percent_with_images = ((total_count - total_needing_images) / total_count) * 100
                print(f"   Products with real images: {percent_with_images:.1f}%")
                
    except Exception as e:
        print(f"❌ Error checking products: {e}")
    
    print("\n✅ Setup verification complete!")
    
    # Show next steps
    if total_needing_images > 0:
        print(f"\n💡 You have {total_needing_images} products that need images.")
        print("   Run 'python run_agent_with_images.py' to discover new products with automatic image generation")
        print("   Or run 'python test-scripts/generate_all_images.js' to generate images for existing products")

if __name__ == "__main__":
    check_setup()