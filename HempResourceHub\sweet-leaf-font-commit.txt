feat: Improve Sweet Leaf font readability and accessibility

## Typography Overhaul by Augment Agent

### Major Font Styling Improvements
- **Removed visual effects**: Eliminated all text-shadow outlines, text-stroke properties, drop-shadow filters, and glow effects that were dimming the green text
- **Increased font sizes**: Enhanced readability with 15-50% larger text across all hemp brand classes
- **Standardized color**: Consistent #22c55e (green-500) for all Sweet Leaf font elements
- **Simplified CSS**: Removed complex visual effects for better performance and maintainability

### Font Size Increases
- **`.hemp-brand-ultra`**: 1.4em (40% larger) - most commonly used class
- **`.hemp-brand-bright`**: 1.45em (45% larger) - for hover effects  
- **`.font-hemp-heading`**: 1.5em (50% larger) - for headings
- **`.font-hemp-enhanced`**: 1.35em (35% larger)
- **`.hemp-brand-primary`**: 1.3em (30% larger)
- **All other hemp brand classes**: 1.15em-1.25em range

### Visual Effects Removed
- Text-shadow outlines (both black and white)
- Text-stroke properties and borders
- Drop-shadow filters and glow animations
- Gradient backgrounds and 3D pseudo-elements
- Complex CSS effects that impacted rendering performance

### Benefits Achieved
- **Better Readability**: Clean text without distracting outlines or shadows
- **Improved Mobile Experience**: Larger text sizes work better on all screen sizes
- **Consistent Branding**: Unified green color across all Sweet Leaf text elements
- **Better Performance**: Removed complex visual effects for faster rendering
- **Enhanced Accessibility**: Cleaner text is easier to read for all users
- **Simplified Maintenance**: Much cleaner CSS without complex shadow/stroke rules

### Files Modified
- `client/src/index.css` - Updated all hemp brand CSS classes
- `CHANGELOG_AUGMENT.md` - Added typography improvements documentation
- `README.md` - Updated recent enhancements section

### Impact
- 40-50% larger text sizes for better visibility
- Eliminated 12+ complex CSS visual effects
- Standardized color across all Sweet Leaf font classes
- Improved accessibility compliance and mobile experience
- Cleaner, more maintainable CSS codebase
