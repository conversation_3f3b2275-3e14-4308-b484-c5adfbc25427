#!/usr/bin/env python3
"""Test database operations with real Supabase connection."""

import os

url = os.environ.get('SUPABASE_URL')
key = os.environ.get('SUPABASE_ANON_KEY')

print(f"SUPABASE_URL: {'✅ Set' if url else '❌ Not set'}")
print(f"SUPABASE_ANON_KEY: {'✅ Set' if key else '❌ Not set'}")

if url and key:
    try:
        from supabase import create_client
        client = create_client(url, key)
        print("\n✅ Real Supabase client created")
        
        # Test table access
        tables = ['uses_products', 'hemp_companies', 'research_entries']
        print("\nTesting table access:")
        
        for table in tables:
            try:
                result = client.table(table).select('id').limit(1).execute()
                count = len(result.data) if hasattr(result, 'data') else 0
                print(f"✅ {table}: {count} records")
            except Exception as e:
                print(f"❌ {table}: {str(e)[:50]}...")
                
    except Exception as e:
        print(f"\n❌ Database connection failed: {e}")
else:
    print("\n⚠️ Using mock mode - no real database connection")
    print("To test real database, set GitHub Secrets:")
    print("- SUPABASE_URL")
    print("- SUPABASE_ANON_KEY")