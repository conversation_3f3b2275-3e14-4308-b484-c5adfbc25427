import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY environment variable is not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkProductImages() {
  try {
    // Get all products
    const { data: allProducts, error: allError } = await supabase
      .from('uses_products')
      .select('id, name, image_url');
    
    if (allError) {
      console.error('Error fetching products:', allError);
      return;
    }
    
    console.log(`Total products in database: ${allProducts.length}`);
    
    // Categorize products by image status
    const noImage = allProducts.filter(p => !p.image_url || p.image_url === '');
    const placeholderImage = allProducts.filter(p => p.image_url && p.image_url.includes('placeholder'));
    const augmentImage = allProducts.filter(p => p.image_url && p.image_url.includes('augment'));
    const otherImage = allProducts.filter(p => 
      p.image_url && 
      !p.image_url.includes('placeholder') && 
      !p.image_url.includes('augment')
    );
    
    console.log('\n=== Image Status Summary ===');
    console.log(`No image: ${noImage.length}`);
    console.log(`Placeholder images: ${placeholderImage.length}`);
    console.log(`Augment AI images: ${augmentImage.length}`);
    console.log(`Other images: ${otherImage.length}`);
    
    // Show sample of each category
    if (noImage.length > 0) {
      console.log('\nProducts without images (first 5):');
      noImage.slice(0, 5).forEach(p => console.log(`  - ${p.id}: ${p.name}`));
    }
    
    if (placeholderImage.length > 0) {
      console.log('\nProducts with placeholder images (first 5):');
      placeholderImage.slice(0, 5).forEach(p => console.log(`  - ${p.id}: ${p.name} => ${p.image_url}`));
    }
    
    if (augmentImage.length > 0) {
      console.log('\nProducts with Augment AI images (first 5):');
      augmentImage.slice(0, 5).forEach(p => console.log(`  - ${p.id}: ${p.name} => ${p.image_url}`));
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the check
checkProductImages();