import React, { useState, useEffect, useRef, useMemo } from "react";
import { useLocation } from "wouter";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Search, 
  Sparkles, 
  Clock, 
  TrendingUp, 
  X,
  ArrowRight,
  Lightbulb,
  Filter,
  Mic,
  Camera
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";

interface SearchSuggestion {
  id: string;
  type: 'query' | 'product' | 'category' | 'trending' | 'ai';
  text: string;
  description?: string;
  icon: React.ReactNode;
  confidence?: number;
  metadata?: any;
}

interface SmartSearchProps {
  placeholder?: string;
  onSearch?: (query: string, filters?: any) => void;
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  className?: string;
  showVoiceSearch?: boolean;
  showImageSearch?: boolean;
  showAISuggestions?: boolean;
}

export function SmartSearch({
  placeholder = "Search hemp products, applications, or ask a question...",
  onSearch,
  onSuggestionSelect,
  className,
  showVoiceSearch = true,
  showImageSearch = true,
  showAISuggestions = true
}: SmartSearchProps) {
  const [location, setLocation] = useLocation();
  const [query, setQuery] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { data: products } = useAllHempProducts();
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('hemp-recent-searches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // AI-powered search suggestions
  const aiSuggestions = useMemo<SearchSuggestion[]>(() => {
    if (!showAISuggestions || !query.trim()) return [];

    const queryLower = query.toLowerCase();
    const suggestions: SearchSuggestion[] = [];

    // Intent detection
    if (queryLower.includes('sustainable') || queryLower.includes('eco')) {
      suggestions.push({
        id: 'ai-sustainable',
        type: 'ai',
        text: 'Sustainable hemp applications',
        description: 'Find eco-friendly hemp products and their environmental benefits',
        icon: <Sparkles className="h-4 w-4 text-green-400" />,
        confidence: 0.9
      });
    }

    if (queryLower.includes('construction') || queryLower.includes('building')) {
      suggestions.push({
        id: 'ai-construction',
        type: 'ai',
        text: 'Hemp in construction materials',
        description: 'Hempcrete, insulation, and building applications',
        icon: <Lightbulb className="h-4 w-4 text-blue-400" />,
        confidence: 0.85
      });
    }

    if (queryLower.includes('food') || queryLower.includes('nutrition')) {
      suggestions.push({
        id: 'ai-food',
        type: 'ai',
        text: 'Hemp food products and nutrition',
        description: 'Seeds, oil, protein powder, and nutritional benefits',
        icon: <Lightbulb className="h-4 w-4 text-orange-400" />,
        confidence: 0.8
      });
    }

    // Question detection
    if (queryLower.startsWith('what') || queryLower.startsWith('how') || queryLower.includes('?')) {
      suggestions.push({
        id: 'ai-question',
        type: 'ai',
        text: `AI Answer: ${query}`,
        description: 'Get an AI-powered answer to your hemp question',
        icon: <Sparkles className="h-4 w-4 text-purple-400" />,
        confidence: 0.75
      });
    }

    return suggestions;
  }, [query, showAISuggestions]);

  // Product suggestions
  const productSuggestions = useMemo<SearchSuggestion[]>(() => {
    if (!products || !query.trim()) return [];

    return products
      .filter(product => 
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description?.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 3)
      .map(product => ({
        id: `product-${product.id}`,
        type: 'product' as const,
        text: product.name,
        description: product.description?.slice(0, 60) + '...',
        icon: <Search className="h-4 w-4 text-gray-400" />,
        metadata: { productId: product.id }
      }));
  }, [products, query]);

  // Category suggestions
  const categorySuggestions = useMemo<SearchSuggestion[]>(() => {
    if (!query.trim()) return [];

    const suggestions: SearchSuggestion[] = [];
    
    // Plant parts
    plantParts?.forEach(part => {
      if (part.name.toLowerCase().includes(query.toLowerCase())) {
        suggestions.push({
          id: `part-${part.id}`,
          type: 'category',
          text: `${part.name} products`,
          description: `All products made from ${part.name.toLowerCase()}`,
          icon: <Filter className="h-4 w-4 text-green-400" />,
          metadata: { plantPartId: part.id }
        });
      }
    });

    // Industries
    industries?.forEach(industry => {
      if (industry.name.toLowerCase().includes(query.toLowerCase())) {
        suggestions.push({
          id: `industry-${industry.id}`,
          type: 'category',
          text: `${industry.name} applications`,
          description: `Hemp products in ${industry.name.toLowerCase()}`,
          icon: <Filter className="h-4 w-4 text-blue-400" />,
          metadata: { industryId: industry.id }
        });
      }
    });

    return suggestions.slice(0, 3);
  }, [plantParts, industries, query]);

  // Trending suggestions
  const trendingSuggestions: SearchSuggestion[] = [
    {
      id: 'trending-1',
      type: 'trending',
      text: 'Hemp bioplastics',
      description: 'Biodegradable plastic alternatives',
      icon: <TrendingUp className="h-4 w-4 text-red-400" />
    },
    {
      id: 'trending-2',
      type: 'trending',
      text: 'CBD extraction methods',
      description: 'Latest extraction technologies',
      icon: <TrendingUp className="h-4 w-4 text-red-400" />
    },
    {
      id: 'trending-3',
      type: 'trending',
      text: 'Hemp concrete applications',
      description: 'Sustainable building materials',
      icon: <TrendingUp className="h-4 w-4 text-red-400" />
    }
  ];

  // Recent search suggestions
  const recentSuggestions: SearchSuggestion[] = recentSearches.map(search => ({
    id: `recent-${search}`,
    type: 'query',
    text: search,
    icon: <Clock className="h-4 w-4 text-gray-400" />
  }));

  // Combine all suggestions
  const allSuggestions = useMemo(() => {
    if (!query.trim()) {
      return [
        ...recentSuggestions.slice(0, 3),
        ...trendingSuggestions
      ];
    }

    return [
      ...aiSuggestions,
      ...productSuggestions,
      ...categorySuggestions
    ].slice(0, 8);
  }, [query, aiSuggestions, productSuggestions, categorySuggestions, recentSuggestions]);

  const handleSearch = (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;

    // Save to recent searches
    const newRecent = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 10);
    setRecentSearches(newRecent);
    localStorage.setItem('hemp-recent-searches', JSON.stringify(newRecent));

    // Perform search
    onSearch?.(searchQuery);
    setLocation(`/hemp-dex?search=${encodeURIComponent(searchQuery)}`);
    setQuery("");
    setIsFocused(false);
  };

  const handleSuggestionSelect = (suggestion: SearchSuggestion) => {
    onSuggestionSelect?.(suggestion);
    
    switch (suggestion.type) {
      case 'product':
        setLocation(`/product/${suggestion.metadata?.productId}`);
        break;
      case 'category':
        if (suggestion.metadata?.plantPartId) {
          setLocation(`/hemp-dex?parts=${suggestion.metadata.plantPartId}`);
        } else if (suggestion.metadata?.industryId) {
          setLocation(`/hemp-dex?industries=${suggestion.metadata.industryId}`);
        }
        break;
      case 'ai':
        // Handle AI suggestions
        if (suggestion.id === 'ai-question') {
          setLocation(`/hemp-dex?ai-query=${encodeURIComponent(query)}`);
        } else {
          handleSearch(suggestion.text);
        }
        break;
      default:
        handleSearch(suggestion.text);
    }
    
    setQuery("");
    setIsFocused(false);
  };

  const handleVoiceSearch = () => {
    if (!('webkitSpeechRecognition' in window)) {
      alert('Voice search not supported in this browser');
      return;
    }

    const recognition = new (window as any).webkitSpeechRecognition();
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    setIsListening(true);
    recognition.start();

    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      setQuery(transcript);
      setIsListening(false);
    };

    recognition.onerror = () => {
      setIsListening(false);
    };

    recognition.onend = () => {
      setIsListening(false);
    };
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => (prev + 1) % allSuggestions.length);
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev === 0 ? allSuggestions.length - 1 : prev - 1);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (allSuggestions[selectedIndex]) {
        handleSuggestionSelect(allSuggestions[selectedIndex]);
      } else {
        handleSearch();
      }
    } else if (e.key === 'Escape') {
      setIsFocused(false);
      inputRef.current?.blur();
    }
  };

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const showSuggestions = isFocused && allSuggestions.length > 0;

  return (
    <div ref={searchRef} className={cn("relative w-full max-w-2xl", className)}>
      <div className="relative">
        <Search className="absolute left-3 md:left-4 top-1/2 -translate-y-1/2 h-4 w-4 md:h-5 md:w-5 text-gray-400" />
        
        <Input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={cn(
            "w-full pl-10 md:pl-12 pr-16 md:pr-20 py-3 md:py-3 text-base md:text-base rounded-full",
            "bg-gray-900/80 border-gray-700",
            "focus:bg-gray-900 focus:border-green-400",
            "placeholder:text-gray-400",
            "transition-all duration-200",
            "min-h-[44px] touch-manipulation", // Enhanced touch targets for mobile
            "text-[16px] md:text-base" // Prevent zoom on iOS
          )}
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
          spellCheck="false"
        />

        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
          {query && (
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0 text-gray-400 hover:text-gray-300"
              onClick={() => setQuery("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
          
          {showVoiceSearch && (
            <Button
              size="sm"
              variant="ghost"
              className={cn(
                "h-8 w-8 md:h-6 md:w-6 p-0 text-gray-400 hover:text-gray-300 min-h-[44px] min-w-[44px] md:min-h-auto md:min-w-auto touch-manipulation",
                isListening && "text-red-400 animate-pulse"
              )}
              onClick={handleVoiceSearch}
              aria-label={isListening ? "Stop voice search" : "Start voice search"}
            >
              <Mic className="h-4 w-4" />
            </Button>
          )}

          {showImageSearch && (
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 md:h-6 md:w-6 p-0 text-gray-400 hover:text-gray-300 min-h-[44px] min-w-[44px] md:min-h-auto md:min-w-auto touch-manipulation"
              aria-label="Image search"
            >
              <Camera className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Suggestions Dropdown - Enhanced for mobile */}
      {showSuggestions && (
        <div className="absolute top-full mt-2 w-full bg-gray-900/95 backdrop-blur-sm rounded-xl shadow-2xl border border-green-500/30 overflow-hidden z-50 animate-in fade-in slide-in-from-top-2 duration-200">
          <ScrollArea className="max-h-80 md:max-h-96">
            <div className="p-2">
              {!query.trim() && recentSuggestions.length > 0 && (
                <div className="mb-4">
                  <div className="px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide">
                    Recent Searches
                  </div>
                  {recentSuggestions.slice(0, 3).map((suggestion, index) => (
                    <SuggestionItem
                      key={suggestion.id}
                      suggestion={suggestion}
                      isSelected={selectedIndex === index}
                      onClick={() => handleSuggestionSelect(suggestion)}
                    />
                  ))}
                </div>
              )}

              {!query.trim() && (
                <div className="mb-4">
                  <div className="px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide">
                    Trending Searches
                  </div>
                  {trendingSuggestions.map((suggestion, index) => (
                    <SuggestionItem
                      key={suggestion.id}
                      suggestion={suggestion}
                      isSelected={selectedIndex === (recentSuggestions.slice(0, 3).length + index)}
                      onClick={() => handleSuggestionSelect(suggestion)}
                    />
                  ))}
                </div>
              )}

              {query.trim() && (
                <div>
                  {aiSuggestions.length > 0 && (
                    <div className="mb-4">
                      <div className="px-3 py-2 text-xs font-medium text-purple-400 uppercase tracking-wide flex items-center gap-1">
                        <Sparkles className="h-3 w-3" />
                        AI Suggestions
                      </div>
                      {aiSuggestions.map((suggestion, index) => (
                        <SuggestionItem
                          key={suggestion.id}
                          suggestion={suggestion}
                          isSelected={selectedIndex === index}
                          onClick={() => handleSuggestionSelect(suggestion)}
                          showConfidence={true}
                        />
                      ))}
                    </div>
                  )}

                  {allSuggestions.filter(s => s.type !== 'ai').map((suggestion, index) => (
                    <SuggestionItem
                      key={suggestion.id}
                      suggestion={suggestion}
                      isSelected={selectedIndex === (aiSuggestions.length + index)}
                      onClick={() => handleSuggestionSelect(suggestion)}
                    />
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
}

interface SuggestionItemProps {
  suggestion: SearchSuggestion;
  isSelected: boolean;
  onClick: () => void;
  showConfidence?: boolean;
}

function SuggestionItem({ suggestion, isSelected, onClick, showConfidence }: SuggestionItemProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "w-full px-3 py-3 md:py-2 flex items-center gap-3 hover:bg-gray-800 transition-colors text-left rounded-lg min-h-[44px] touch-manipulation",
        isSelected && "bg-gray-800"
      )}
    >
      <div className="flex-shrink-0">{suggestion.icon}</div>
      <div className="flex-1 min-w-0">
        <div className="text-white font-medium truncate">{suggestion.text}</div>
        {suggestion.description && (
          <div className="text-gray-400 text-sm truncate">{suggestion.description}</div>
        )}
      </div>
      {showConfidence && suggestion.confidence && (
        <Badge variant="outline" className="text-xs">
          {Math.round(suggestion.confidence * 100)}%
        </Badge>
      )}
      <ArrowRight className="h-4 w-4 text-gray-500 flex-shrink-0" />
    </button>
  );
}
