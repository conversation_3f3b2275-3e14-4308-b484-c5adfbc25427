# Archived Agents

This directory contains deprecated agent files that were causing the image generation overpopulation issue.

## Why These Were Archived

These agents were re-queuing image generation for existing products that already had placeholder images, causing exponential growth in the image_generation_queue table (1,893 entries for only 219 products).

## What to Use Instead

Use the unified CLI and agents:
```bash
# Use the unified CLI
./hemp agent research "your query" --max-results 10

# Or use the unified research agent directly
from agents.research.unified_research_agent import UnifiedResearchAgent
```

## Archived Files

- **Runner Scripts**: Various scripts that ran agents individually
- **Test Files**: Test scripts for agents
- **Legacy Agents**: Old agent implementations that have been replaced
- **Enhanced Research Agent**: Replaced by unified research agent

## DO NOT USE THESE FILES

These files are kept for reference only. Using them will likely cause the image queue overpopulation issue to return.

Date Archived: January 24, 2025
Reason: Image generation queue overpopulation fix