import React, { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { 
  Filter, 
  X, 
  ChevronDown, 
  ChevronRight,
  Search,
  Sparkles,
  TrendingUp,
  Calendar,
  DollarSign
} from "lucide-react";
import { cn } from "@/lib/utils";

interface FilterOption {
  id: string | number;
  label: string;
  count?: number;
  color?: string;
}

interface FilterSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  type: 'checkbox' | 'radio' | 'range' | 'search' | 'toggle';
  options?: FilterOption[];
  value?: any;
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  isCollapsed?: boolean;
}

interface AdvancedFilterPanelProps {
  sections: FilterSection[];
  onFiltersChange: (filters: Record<string, any>) => void;
  onReset: () => void;
  className?: string;
  showActiveCount?: boolean;
}

export function AdvancedFilterPanel({
  sections,
  onFiltersChange,
  onReset,
  className,
  showActiveCount = true
}: AdvancedFilterPanelProps) {
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(new Set());
  const [searchTerms, setSearchTerms] = useState<Record<string, string>>({});

  // Initialize filters from sections
  useEffect(() => {
    const initialFilters: Record<string, any> = {};
    sections.forEach(section => {
      if (section.value !== undefined) {
        initialFilters[section.id] = section.value;
      } else if (section.type === 'checkbox') {
        initialFilters[section.id] = [];
      } else if (section.type === 'range') {
        initialFilters[section.id] = [section.min || 0, section.max || 100];
      } else if (section.type === 'toggle') {
        initialFilters[section.id] = false;
      }
    });
    setFilters(initialFilters);
  }, [sections]);

  // Notify parent of filter changes
  useEffect(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  const updateFilter = (sectionId: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [sectionId]: value
    }));
  };

  const toggleCollapse = (sectionId: string) => {
    setCollapsedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const getActiveFilterCount = () => {
    return Object.entries(filters).reduce((count, [key, value]) => {
      if (Array.isArray(value) && value.length > 0) return count + value.length;
      if (typeof value === 'boolean' && value) return count + 1;
      if (typeof value === 'string' && value.trim()) return count + 1;
      if (Array.isArray(value) && value.length === 2) {
        const section = sections.find(s => s.id === key);
        if (section && (value[0] !== section.min || value[1] !== section.max)) {
          return count + 1;
        }
      }
      return count;
    }, 0);
  };

  const handleReset = () => {
    const resetFilters: Record<string, any> = {};
    sections.forEach(section => {
      if (section.type === 'checkbox') {
        resetFilters[section.id] = [];
      } else if (section.type === 'range') {
        resetFilters[section.id] = [section.min || 0, section.max || 100];
      } else if (section.type === 'toggle') {
        resetFilters[section.id] = false;
      } else {
        resetFilters[section.id] = '';
      }
    });
    setFilters(resetFilters);
    setSearchTerms({});
    onReset();
  };

  const renderFilterSection = (section: FilterSection) => {
    const isCollapsed = collapsedSections.has(section.id) || section.isCollapsed;
    const sectionValue = filters[section.id];

    return (
      <div key={section.id} className="border-b border-gray-800 last:border-b-0">
        <Collapsible>
          <CollapsibleTrigger
            onClick={() => toggleCollapse(section.id)}
            className="flex items-center justify-between w-full p-4 hover:bg-gray-800/50 transition-colors"
          >
            <div className="flex items-center gap-3">
              <div className="text-green-400">{section.icon}</div>
              <span className="font-medium text-white">{section.title}</span>
              {section.type === 'checkbox' && sectionValue?.length > 0 && (
                <Badge variant="secondary" className="ml-2 bg-green-500/20 text-green-400">
                  {sectionValue.length}
                </Badge>
              )}
            </div>
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            )}
          </CollapsibleTrigger>
          
          <CollapsibleContent className={cn("px-4 pb-4", isCollapsed && "hidden")}>
            {section.type === 'search' && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder={section.placeholder || "Search..."}
                  value={searchTerms[section.id] || ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    setSearchTerms(prev => ({ ...prev, [section.id]: value }));
                    updateFilter(section.id, value);
                  }}
                  className="pl-10 bg-gray-900/50 border-gray-700 focus:border-green-400"
                />
              </div>
            )}

            {section.type === 'checkbox' && section.options && (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {section.options.map((option) => (
                  <label
                    key={option.id}
                    className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-800/50 cursor-pointer group"
                  >
                    <input
                      type="checkbox"
                      checked={sectionValue?.includes(option.id) || false}
                      onChange={(e) => {
                        const currentValues = sectionValue || [];
                        const newValues = e.target.checked
                          ? [...currentValues, option.id]
                          : currentValues.filter((v: any) => v !== option.id);
                        updateFilter(section.id, newValues);
                      }}
                      className="rounded border-gray-600 text-green-500 focus:ring-green-500 focus:ring-offset-0"
                    />
                    <span className="flex-1 text-gray-300 group-hover:text-white transition-colors">
                      {option.label}
                    </span>
                    {option.count !== undefined && (
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
                        {option.count}
                      </Badge>
                    )}
                  </label>
                ))}
              </div>
            )}

            {section.type === 'range' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between text-sm text-gray-400">
                  <span>{section.min}</span>
                  <span className="text-white font-medium">
                    {sectionValue?.[0]} - {sectionValue?.[1]}
                  </span>
                  <span>{section.max}</span>
                </div>
                <Slider
                  value={sectionValue || [section.min || 0, section.max || 100]}
                  onValueChange={(value) => updateFilter(section.id, value)}
                  min={section.min || 0}
                  max={section.max || 100}
                  step={section.step || 1}
                  className="w-full"
                />
              </div>
            )}

            {section.type === 'toggle' && (
              <div className="flex items-center justify-between">
                <Label htmlFor={section.id} className="text-gray-300">
                  Enable {section.title}
                </Label>
                <Switch
                  id={section.id}
                  checked={sectionValue || false}
                  onCheckedChange={(checked) => updateFilter(section.id, checked)}
                />
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </div>
    );
  };

  const activeCount = getActiveFilterCount();

  return (
    <div className={cn("bg-gray-900/95 backdrop-blur-sm border border-gray-800 rounded-lg", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-green-400" />
          <span className="font-semibold text-white">Filters</span>
          {showActiveCount && activeCount > 0 && (
            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
              {activeCount} active
            </Badge>
          )}
        </div>
        {activeCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="text-gray-400 hover:text-white"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
        )}
      </div>

      {/* Filter Sections */}
      <ScrollArea className="max-h-[600px]">
        <div className="divide-y divide-gray-800">
          {sections.map(renderFilterSection)}
        </div>
      </ScrollArea>

      {/* Quick Actions */}
      <div className="p-4 border-t border-gray-800 bg-gray-900/50">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 border-gray-700 text-gray-300 hover:bg-gray-800"
            onClick={() => updateFilter('trending', !filters.trending)}
          >
            <TrendingUp className="h-4 w-4 mr-1" />
            Trending
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex-1 border-gray-700 text-gray-300 hover:bg-gray-800"
            onClick={() => updateFilter('recent', !filters.recent)}
          >
            <Calendar className="h-4 w-4 mr-1" />
            Recent
          </Button>
        </div>
      </div>
    </div>
  );
}
