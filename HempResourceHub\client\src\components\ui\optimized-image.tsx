import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  placeholder?: string;
  fallbackSrc?: string;
  onError?: () => void;
  onLoad?: () => void;
  sizes?: string;
  quality?: number;
  blur?: boolean;
  zoom?: boolean;
  aspectRatio?: string;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  placeholder = '/images/unknown-hemp-image.png',
  fallbackSrc = '/images/unknown-hemp-image.png',
  onError,
  onLoad,
  sizes,
  quality = 75,
  blur = true,
  zoom = false,
  aspectRatio
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (priority || !window.IntersectionObserver) {
      setIsInView(true);
      return;
    }
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { 
        threshold: 0.1, 
        rootMargin: '50px' 
      }
    );
    
    if (containerRef.current) {
      observer.observe(containerRef.current);
    }
    
    return () => observer.disconnect();
  }, [priority]);
  
  // Generate srcset for responsive images
  const generateSrcSet = () => {
    // If the image is from a local source or external URL, we can't generate srcset
    if (!src || src.startsWith('http') || src.startsWith('//')) {
      return undefined;
    }
    
    const base = src.replace(/\.[^/.]+$/, '');
    const ext = src.match(/\.[^/.]+$/)?.[0] || '';
    
    // Check if we have different sizes available
    // This assumes your build process generates these sizes
    return `
      ${base}-400w${ext} 400w,
      ${base}-800w${ext} 800w,
      ${base}-1200w${ext} 1200w
    `;
  };
  
  const handleImageError = () => {
    if (!hasError && fallbackSrc && fallbackSrc !== src) {
      // Try fallback image first
      setHasError(true);
      return;
    }
    setHasError(true);
    onError?.();
  };

  const handleImageLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  // Use fallback or placeholder if image fails to load
  const imageSrc = hasError ? (fallbackSrc || placeholder) : src;
  
  return (
    <div 
      ref={containerRef}
      className={cn("relative overflow-hidden", className)} 
      style={{ width, height }}
    >
      {/* Placeholder/skeleton while loading */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 bg-gray-700 animate-pulse rounded" />
      )}
      
      {/* Blur placeholder for smooth transition */}
      {!isLoaded && !hasError && isInView && (
        <div
          className="absolute inset-0 bg-gray-600 blur-sm"
          style={{
            backgroundImage: `url(${placeholder})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        />
      )}
      
      {/* Actual image */}
      {isInView && (
        <img
          ref={imgRef}
          src={imageSrc}
          srcSet={!hasError ? generateSrcSet() : undefined}
          sizes={sizes || "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={cn(
            "w-full h-full object-cover",
            isLoaded ? 'opacity-100' : 'opacity-0',
            'transition-opacity duration-300'
          )}
        />
      )}
      
      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="mt-2 text-xs text-gray-500">Image unavailable</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;