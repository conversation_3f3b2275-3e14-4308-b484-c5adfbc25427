# HempQuarterz Unified CLI Guide

The unified CLI consolidates all hemp database operations into a single command-line interface.

## Installation

The CLI is already set up in your project. Simply use:
```bash
./hemp [command] [options]
```

Or with Python:
```bash
python3 hemp_cli.py [command] [options]
```

## Available Commands

### 1. Agent Operations

Run any AI agent with a specific task:

```bash
# Research agent with basic features
./hemp agent research "hemp construction materials"

# Research agent with all features
./hemp agent research "innovative hemp products" --features company image deep web feed trend

# Content agent
./hemp agent content "Write blog post about hemp textiles"

# SEO agent
./hemp agent seo "Optimize hemp product descriptions"

# Run the orchestrator
./hemp agent orchestrator "Find and analyze hemp innovations in automotive industry"
```

### 2. Image Generation

Generate images for products:

```bash
# Generate images for all products without images
./hemp images generate

# Generate for specific products
./hemp images generate --products 1 2 3 4 5

# Use a specific provider
./hemp images generate --provider stable-diffusion
```

### 3. Database Operations

#### Populate Database
```bash
# Populate all data
./hemp db populate --type all

# Only products
./hemp db populate --type products

# Advanced products
./hemp db populate --type advanced
```

#### Export Data
```bash
# Export as JSON
./hemp db export --format json --output ./exports

# Export as CSV
./hemp db export --format csv --output ./data-backup
```

#### Validate Data
```bash
# Check database integrity
./hemp db validate
```

#### Merge Companies
```bash
# Preview merges (dry run)
./hemp db merge-companies

# Execute merges
./hemp db merge-companies --execute
```

### 4. Monitoring

Monitor agent status and recent tasks:

```bash
./hemp monitor
```

## Examples

### Complete Workflow Example

```bash
# 1. Populate initial data
./hemp db populate --type all

# 2. Run research to find new products
./hemp agent research "sustainable hemp packaging" --features company image

# 3. Generate content for new products
./hemp agent content "Create descriptions for hemp packaging products"

# 4. Monitor progress
./hemp monitor

# 5. Generate missing images
./hemp images generate --provider stable-diffusion

# 6. Export data for backup
./hemp db export --format json
```

### Automated Discovery Pipeline

```bash
# Run comprehensive discovery with all features
./hemp agent research "hemp innovations 2024" \
  --features basic company image deep web feed trend \
  --max-results 50

# Check what was found
./hemp monitor

# Validate the data
./hemp db validate

# Merge any duplicate companies
./hemp db merge-companies --execute
```

## Features by Agent Type

### Research Agent Features
- `basic` - Basic AI-powered discovery
- `company` - Extract and save company information
- `image` - Auto-generate product images
- `deep` - Extended AI analysis
- `web` - Advanced web scraping
- `feed` - RSS/news feed monitoring
- `trend` - Market trend analysis

### Provider Options for Images
- `placeholder` - Fast placeholder images
- `stable-diffusion` - Realistic product images
- `dall-e` - OpenAI DALL-E generation
- `midjourney` - High-quality artistic images

## Environment Variables Required

Make sure these are set in your `.env` file:
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
OPENAI_API_KEY=your_openai_key  # For AI features
```

## Troubleshooting

### Command not found
```bash
# Make sure the script is executable
chmod +x hemp hemp_cli.py
```

### Module not found
```bash
# Install required dependencies
pip install -r requirements.txt
```

### Database connection issues
```bash
# Check your environment variables
python3 -c "import os; print('SUPABASE_URL' in os.environ)"
```

## Advanced Usage

### Running in Background
```bash
# Run long tasks in background
nohup ./hemp agent orchestrator "Complete hemp market analysis" &

# Check progress later
./hemp monitor
```

### Scheduling with Cron
```bash
# Add to crontab for daily discovery
0 2 * * * /path/to/hemp agent research "new hemp products" --features company image
```

### Batch Processing
```bash
# Create a batch script
#!/bin/bash
./hemp agent research "hemp textiles" --features company
./hemp agent research "hemp construction" --features company
./hemp agent research "hemp food products" --features company
./hemp db merge-companies --execute
./hemp images generate
```

## Integration with Other Tools

The CLI can be integrated with:
- GitHub Actions (see workflows)
- Scheduling systems (cron, systemd timers)
- CI/CD pipelines
- Monitoring systems
- Data pipelines

## Next Steps

1. The unified research agent is complete and integrated
2. The unified CLI provides single entry point for all operations
3. Next phases will focus on:
   - Centralizing image generation
   - Implementing monitoring dashboard
   - Optimizing GitHub Actions

Use `./hemp --help` for quick reference on any command.