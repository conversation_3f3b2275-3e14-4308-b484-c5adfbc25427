/* Interactive Hover States and Micro-animations */

/* Button hover effects */
button {
  @apply transition-all duration-300 ease-out;
  position: relative;
  overflow: hidden;
}

button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

button:hover::before {
  width: 300px;
  height: 300px;
}

/* Card hover animations */
.card-hover {
  @apply transition-all duration-300 ease-out;
  position: relative;
}

.card-hover::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to bottom right,
    transparent,
    rgba(34, 197, 94, 0.1)
  );
  opacity: 0;
  transition: opacity 0.3s ease-out;
  pointer-events: none;
}

.card-hover:hover::after {
  opacity: 1;
}

/* Link hover underline animation */
a.link-hover {
  position: relative;
  text-decoration: none;
}

a.link-hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: currentColor;
  transition: width 0.3s ease-out;
}

a.link-hover:hover::after {
  width: 100%;
}

/* Image hover zoom */
.image-hover {
  overflow: hidden;
}

.image-hover img {
  @apply transition-transform duration-500 ease-out;
}

.image-hover:hover img {
  @apply scale-110;
}

/* Pulse animation for important elements */
@keyframes pulse-subtle {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

.pulse-hover:hover {
  animation: pulse-subtle 1s ease-in-out infinite;
}

/* Shake animation for errors */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.shake {
  animation: shake 0.5s ease-in-out;
}

/* Slide in animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-up {
  animation: slideInUp 0.5s ease-out forwards;
}

.animate-slide-in-down {
  animation: slideInDown 0.5s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out forwards;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Bounce animation */
@keyframes bounce-subtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.bounce-hover:hover {
  animation: bounce-subtle 0.5s ease-in-out;
}

/* Rotate animation */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate-hover:hover {
  animation: rotate 0.5s ease-in-out;
}

/* Badge hover effect */
.badge-hover {
  @apply transition-all duration-200;
}

.badge-hover:hover {
  @apply scale-105 shadow-lg;
}

/* Input focus animations */
input:focus,
textarea:focus,
select:focus {
  @apply outline-none ring-2 ring-green-500 ring-offset-2 ring-offset-gray-900;
  @apply transition-all duration-200;
}

/* Skeleton loading animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Glow effect on hover */
.glow-hover {
  @apply transition-all duration-300;
}

.glow-hover:hover {
  filter: drop-shadow(0 0 15px rgba(34, 197, 94, 0.5));
}

/* Scale on click */
.scale-click:active {
  @apply scale-95;
  @apply transition-transform duration-100;
}

/* Smooth color transitions */
.color-transition {
  @apply transition-colors duration-300 ease-in-out;
}

/* Border animation */
@keyframes borderPulse {
  0%, 100% {
    border-color: rgba(34, 197, 94, 0.3);
  }
  50% {
    border-color: rgba(34, 197, 94, 0.8);
  }
}

.border-pulse {
  animation: borderPulse 2s ease-in-out infinite;
}

/* Transform origin utilities */
.origin-left {
  transform-origin: left center;
}

.origin-right {
  transform-origin: right center;
}

.origin-top {
  transform-origin: center top;
}

.origin-bottom {
  transform-origin: center bottom;
}