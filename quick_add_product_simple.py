import os
import json
import requests
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"Loaded .env from: {env_path}")
else:
    load_dotenv()
    print("Loaded .env from current directory")

# Get Supabase credentials
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL", "https://ktoqznqmlnxrtvubewyz.supabase.co")
SUPABASE_KEY = os.getenv("VITE_SUPABASE_ANON_KEY")

if not SUPABASE_KEY:
    print("Error: VITE_SUPABASE_ANON_KEY not found in environment variables")
    print("Please ensure .env file exists in HempResourceHub directory")
    exit(1)

# Headers for Supabase API
headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

def add_hemp_product(
    name: str,
    description: str,
    plant_part_ids: list,
    applications: list,
    benefits: list,
    companies: list = None,
    market_stage: str = "research",
    sustainability_score: int = None
):
    """Add a new hemp product to the database using REST API"""
    
    # Check if product already exists
    check_url = f"{SUPABASE_URL}/rest/v1/uses_products"
    params = {
        "name": f"ilike.%{name}%",
        "select": "id"
    }
    
    response = requests.get(check_url, headers=headers, params=params)
    existing = response.json()
    
    if existing:
        print(f"Product '{name}' already exists, skipping...")
        return None
    
    # Prepare product data
    product_data = {
        "name": name,
        "description": description,
        "plant_part_ids": plant_part_ids,
        "industry_sub_category_id": 1,
        "applications": applications,
        "benefits": benefits,
        "market_stage": market_stage,
        "image_url": "/images/unknown-hemp-image.png",
        "created_at": datetime.utcnow().isoformat(),
        "updated_at": datetime.utcnow().isoformat()
    }
    
    if sustainability_score:
        product_data["sustainability_score"] = sustainability_score
    
    # Insert product
    insert_url = f"{SUPABASE_URL}/rest/v1/uses_products"
    response = requests.post(insert_url, headers=headers, json=product_data)
    
    if response.status_code == 201:
        result = response.json()
        product_id = result[0]['id']
        print(f"✅ Added product: {name} (ID: {product_id})")
        
        # Add companies if provided
        if companies:
            for company_name in companies:
                add_company_relationship(product_id, company_name)
                
        return product_id
    else:
        print(f"❌ Failed to add product: {name}")
        print(f"Error: {response.text}")
        return None


def add_company_relationship(product_id: int, company_name: str):
    """Add company and create relationship with product"""
    
    # Check if company exists
    check_url = f"{SUPABASE_URL}/rest/v1/hemp_companies"
    params = {
        "name": f"eq.{company_name}",
        "select": "id"
    }
    
    response = requests.get(check_url, headers=headers, params=params)
    existing = response.json()
    
    if not existing:
        # Create company
        company_data = {
            "name": company_name,
            "company_type": "manufacturer",
            "verified": False,
            "description": f"Manufactures hemp products"
        }
        
        response = requests.post(f"{SUPABASE_URL}/rest/v1/hemp_companies", 
                               headers=headers, json=company_data)
        
        if response.status_code == 201:
            company_id = response.json()[0]['id']
            print(f"  Created company: {company_name}")
        else:
            print(f"  Failed to create company: {company_name}")
            return
    else:
        company_id = existing[0]['id']
    
    # Create relationship
    relationship_data = {
        "company_id": company_id,
        "product_id": product_id,
        "relationship_type": "primary"
    }
    
    requests.post(f"{SUPABASE_URL}/rest/v1/hemp_company_products", 
                 headers=headers, json=relationship_data)


# Example usage
if __name__ == "__main__":
    print("Adding sample hemp products...")
    print(f"Using Supabase URL: {SUPABASE_URL}")
    
    # Plant part IDs reference:
    # 1 = Cannabinoids, 2 = Hemp Bast (Fiber), 3 = Hemp Flowers
    # 4 = Hemp Leaves, 5 = Hemp Stalks, 6 = Hemp Seeds
    # 7 = Hemp Roots, 8 = Hemp Hurds, 9 = Whole Plant
    
    products = [
        {
            "name": "Hemp-Based Circuit Boards",
            "description": "Biodegradable PCB substrates made from hemp fiber composites, offering eco-friendly alternative to traditional fiberglass boards",
            "plant_part_ids": [2, 8],  # Fiber and hurds
            "applications": ["electronics", "circuit boards", "IoT devices"],
            "benefits": ["biodegradable", "non-toxic", "heat resistant", "lightweight"],
            "companies": ["GreenTech Electronics", "HempTech Industries"],
            "market_stage": "growing",
            "sustainability_score": 92
        },
        {
            "name": "Hemp Graphene Supercapacitors",
            "description": "High-performance energy storage devices using hemp-derived graphene nanosheets for rapid charging applications",
            "plant_part_ids": [2],  # Fiber
            "applications": ["energy storage", "electric vehicles", "power banks"],
            "benefits": ["fast charging", "long lifespan", "cost-effective", "sustainable"],
            "companies": ["NanoHemp Technologies"],
            "market_stage": "research",
            "sustainability_score": 95
        },
        {
            "name": "Hemp Acoustic Panels",
            "description": "Sound-absorbing panels made from compressed hemp fibers for architectural acoustics and noise reduction",
            "plant_part_ids": [2, 8],  # Fiber and hurds
            "applications": ["recording studios", "offices", "home theaters", "restaurants"],
            "benefits": ["excellent sound absorption", "fire resistant", "mold resistant", "VOC-free"],
            "companies": ["AcoustiHemp", "Sound Solutions Hemp"],
            "market_stage": "established",
            "sustainability_score": 88
        }
    ]
    
    for product in products:
        add_hemp_product(**product)
    
    print("\n✅ Product addition complete!")
    
    # Show total product count
    count_url = f"{SUPABASE_URL}/rest/v1/uses_products"
    params = {"select": "id", "limit": "1"}
    headers_count = headers.copy()
    headers_count["Prefer"] = "count=exact"
    
    response = requests.head(count_url, headers=headers_count)
    if 'content-range' in response.headers:
        total = response.headers['content-range'].split('/')[1]
        print(f"\nTotal products in database: {total}")