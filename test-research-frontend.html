<!DOCTYPE html>
<html>
<head>
    <title>Test Research Frontend</title>
</head>
<body>
    <h1>Research Data Test</h1>
    <div id="results"></div>
    
    <script type="module">
        // Test if research data loads from Supabase
        const SUPABASE_URL = 'https://ktoqznqmlnxrtvubewyz.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjI0MDMyMzAsImV4cCI6MjAzNzk3OTIzMH0.8_JAbiyrzl78hZYcR2hGFgR5Kmc0L3KrkGPBxuGAEYE';
        
        async function testResearch() {
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/research_entries?limit=5`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                const data = await response.json();
                
                document.getElementById('results').innerHTML = `
                    <h2>Research Entries Found: ${data.length}</h2>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        testResearch();
    </script>
</body>
</html>