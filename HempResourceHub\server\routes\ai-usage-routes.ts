import { Router } from 'express';
import { db } from '../../db';
import { aiUsage, aiUsageSummary, aiModelPricing } from '../../shared/schema-ai-usage';
import { desc, eq, gte, sql, and } from 'drizzle-orm';
import { authMiddleware } from '../middleware/auth';

const router = Router();

// Get usage statistics
router.get('/usage-stats', async (req, res) => {
  try {
    const { days = '30', agentId } = req.query;
    const daysNum = parseInt(days as string, 10);
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysNum);
    
    // Build query conditions
    const conditions = [gte(aiUsage.createdAt, startDate)];
    if (agentId) {
      conditions.push(eq(aiUsage.agentId, agentId as string));
    }
    
    // Fetch usage data
    const usageData = await db
      .select()
      .from(aiUsage)
      .where(and(...conditions))
      .orderBy(desc(aiUsage.createdAt));
    
    // Calculate aggregate stats
    const stats = {
      totalRequests: usageData.length,
      totalInputTokens: 0,
      totalOutputTokens: 0,
      totalCost: 0,
      averageResponseTime: 0,
      errorRate: 0,
      byAgent: {} as Record<string, any>,
      byModel: {} as Record<string, any>,
      dailyUsage: [] as any[],
    };
    
    // Process each record
    let totalResponseTime = 0;
    let errorCount = 0;
    
    usageData.forEach(record => {
      stats.totalInputTokens += record.inputTokens;
      stats.totalOutputTokens += record.outputTokens;
      stats.totalCost += parseFloat(record.totalCost);
      
      if (record.responseTimeMs) {
        totalResponseTime += record.responseTimeMs;
      }
      
      if (record.error) {
        errorCount++;
      }
      
      // Group by agent
      if (!stats.byAgent[record.agentId]) {
        stats.byAgent[record.agentId] = {
          requests: 0,
          cost: 0,
          tokens: 0,
          inputTokens: 0,
          outputTokens: 0,
        };
      }
      stats.byAgent[record.agentId].requests++;
      stats.byAgent[record.agentId].cost += parseFloat(record.totalCost);
      stats.byAgent[record.agentId].tokens += record.totalTokens;
      stats.byAgent[record.agentId].inputTokens += record.inputTokens;
      stats.byAgent[record.agentId].outputTokens += record.outputTokens;
      
      // Group by model
      if (!stats.byModel[record.model]) {
        stats.byModel[record.model] = {
          requests: 0,
          cost: 0,
        };
      }
      stats.byModel[record.model].requests++;
      stats.byModel[record.model].cost += parseFloat(record.totalCost);
    });
    
    // Calculate averages
    stats.averageResponseTime = usageData.length > 0 ? totalResponseTime / usageData.length : 0;
    stats.errorRate = usageData.length > 0 ? errorCount / usageData.length : 0;
    
    // Get daily usage for chart
    const dailyUsageQuery = await db
      .select({
        date: sql<string>`DATE(${aiUsage.createdAt})`.as('date'),
        cost: sql<number>`SUM(CAST(${aiUsage.totalCost} AS DECIMAL))`.as('cost'),
        requests: sql<number>`COUNT(*)`.as('requests'),
        tokens: sql<number>`SUM(${aiUsage.totalTokens})`.as('tokens'),
      })
      .from(aiUsage)
      .where(and(...conditions))
      .groupBy(sql`DATE(${aiUsage.createdAt})`)
      .orderBy(sql`DATE(${aiUsage.createdAt})`);
    
    stats.dailyUsage = dailyUsageQuery;
    
    res.json(stats);
  } catch (error) {
    console.error('Error fetching usage stats:', error);
    res.status(500).json({ error: 'Failed to fetch usage statistics' });
  }
});

// Get detailed usage logs
router.get('/usage-logs', authMiddleware, async (req, res) => {
  try {
    const { 
      limit = '100', 
      offset = '0', 
      agentId, 
      startDate, 
      endDate 
    } = req.query;
    
    const conditions = [];
    if (agentId) {
      conditions.push(eq(aiUsage.agentId, agentId as string));
    }
    if (startDate) {
      conditions.push(gte(aiUsage.createdAt, new Date(startDate as string)));
    }
    if (endDate) {
      conditions.push(sql`${aiUsage.createdAt} <= ${new Date(endDate as string)}`);
    }
    
    const logs = await db
      .select()
      .from(aiUsage)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(aiUsage.createdAt))
      .limit(parseInt(limit as string, 10))
      .offset(parseInt(offset as string, 10));
    
    res.json(logs);
  } catch (error) {
    console.error('Error fetching usage logs:', error);
    res.status(500).json({ error: 'Failed to fetch usage logs' });
  }
});

// Get model pricing information
router.get('/model-pricing', async (req, res) => {
  try {
    const pricing = await db
      .select()
      .from(aiModelPricing)
      .where(eq(aiModelPricing.isActive, true))
      .orderBy(aiModelPricing.model);
    
    res.json(pricing);
  } catch (error) {
    console.error('Error fetching model pricing:', error);
    res.status(500).json({ error: 'Failed to fetch model pricing' });
  }
});

// Update model pricing (admin only)
router.post('/model-pricing', authMiddleware, async (req, res) => {
  try {
    const { model, inputPricePerMillion, outputPricePerMillion, contextWindow } = req.body;
    
    // Check if model exists
    const existing = await db
      .select()
      .from(aiModelPricing)
      .where(eq(aiModelPricing.model, model))
      .limit(1);
    
    if (existing.length > 0) {
      // Update existing
      await db
        .update(aiModelPricing)
        .set({
          inputPricePerMillion: inputPricePerMillion.toString(),
          outputPricePerMillion: outputPricePerMillion.toString(),
          contextWindow,
          effectiveDate: new Date(),
        })
        .where(eq(aiModelPricing.model, model));
    } else {
      // Insert new
      await db.insert(aiModelPricing).values({
        model,
        inputPricePerMillion: inputPricePerMillion.toString(),
        outputPricePerMillion: outputPricePerMillion.toString(),
        contextWindow,
        isActive: true,
      });
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error updating model pricing:', error);
    res.status(500).json({ error: 'Failed to update model pricing' });
  }
});

// Get cost projections
router.get('/cost-projection', async (req, res) => {
  try {
    const { days = '7' } = req.query;
    const daysNum = parseInt(days as string, 10);
    
    // Get recent usage pattern
    const recentUsage = await db
      .select({
        avgDailyCost: sql<number>`AVG(daily_cost)`.as('avg_daily_cost'),
        avgDailyRequests: sql<number>`AVG(daily_requests)`.as('avg_daily_requests'),
      })
      .from(
        db
          .select({
            dailyCost: sql<number>`SUM(CAST(${aiUsage.totalCost} AS DECIMAL))`.as('daily_cost'),
            dailyRequests: sql<number>`COUNT(*)`.as('daily_requests'),
          })
          .from(aiUsage)
          .where(gte(aiUsage.createdAt, new Date(Date.now() - daysNum * 24 * 60 * 60 * 1000)))
          .groupBy(sql`DATE(${aiUsage.createdAt})`)
          .as('daily_stats')
      );
    
    const avgDailyCost = recentUsage[0]?.avgDailyCost || 0;
    const avgDailyRequests = recentUsage[0]?.avgDailyRequests || 0;
    
    res.json({
      projections: {
        daily: avgDailyCost,
        weekly: avgDailyCost * 7,
        monthly: avgDailyCost * 30,
        yearly: avgDailyCost * 365,
      },
      requestProjections: {
        daily: avgDailyRequests,
        weekly: avgDailyRequests * 7,
        monthly: avgDailyRequests * 30,
        yearly: avgDailyRequests * 365,
      },
      basedOnDays: daysNum,
    });
  } catch (error) {
    console.error('Error calculating cost projection:', error);
    res.status(500).json({ error: 'Failed to calculate cost projection' });
  }
});

export default router;