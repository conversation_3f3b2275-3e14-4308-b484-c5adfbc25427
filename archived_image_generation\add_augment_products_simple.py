#!/usr/bin/env python3
"""
Simple script to add Augment-discovered products directly to Supabase
"""

import os
import asyncio
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client, Client

# Load .env from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
load_dotenv(env_path)

# Products discovered by Augment Code
AUGMENT_PRODUCTS = [
    {
        "name": "Hemp-Based Ultracapacitors",
        "description": "High-performance energy storage devices using hemp biomass-derived activated carbon that provides superior energy density compared to traditional ultracapacitors. Developed by Florrent using regeneratively grown hemp.",
        "plant_part": "Fiber",
        "industry": "Energy",
        "sub_industry": "Energy Storage",
        "companies": ["Florrent"],
        "benefits": ["Higher energy density than conventional ultracapacitors", "Made from regeneratively grown hemp biomass", "Supports BIPOC farmers in supply chain"],
        "commercialization_stage": "Commercial",
        "technical_specs": "High energy density ultracapacitor technology for hybrid energy storage applications",
        "environmental_impact": "Carbon negative production using regeneratively grown hemp, supports sustainable agriculture",
        "market_size": "$15 billion ultracapacitor market by 2030"
    },
    {
        "name": "Hemp Molded Fiber Packaging",
        "description": "Plastic-free molded fiber packaging products made primarily from industrial hemp, offering sustainable alternative to traditional plastic packaging for food and non-food applications.",
        "plant_part": "Fiber",
        "industry": "Packaging",
        "sub_industry": "Sustainable Packaging",
        "companies": ["RENW", "element6 Dynamics", "PAPACKS"],
        "benefits": ["100% plastic-free packaging solution", "Fully circular and compostable", "Made from agricultural waste hemp"],
        "commercialization_stage": "Commercial",
        "environmental_impact": "Eliminates plastic waste, uses agricultural hemp waste, fully compostable"
    },
    {
        "name": "Hemp Protein Isolate (90%+ Purity)",
        "description": "World's first commercial soluble hemp protein isolate with greater than 90% protein content, developed through innovative processing technology for food and beverage applications.",
        "plant_part": "Seeds",
        "industry": "Food & Beverage",
        "sub_industry": "Protein Ingredients",
        "companies": ["Burcon NutraScience", "HPS Food and Ingredients"],
        "benefits": ["Greater than 90% protein content", "Highly soluble and functional", "Complete amino acid profile"],
        "commercialization_stage": "Commercial",
        "technical_specs": "Protein isolate with >90% protein content, high solubility, neutral taste"
    },
    {
        "name": "Hemp-Reinforced 3D Printing Filaments",
        "description": "Advanced 3D printing filaments incorporating hemp fibers with polypropylene and PLA matrices, offering improved mechanical properties and sustainability for additive manufacturing.",
        "plant_part": "Fiber",
        "industry": "Technology",
        "sub_industry": "3D Printing Materials",
        "companies": ["Various research institutions"],
        "benefits": ["Enhanced mechanical properties vs pure polymer", "Sustainable bio-based reinforcement", "Reduced material costs"],
        "commercialization_stage": "Pilot",
        "technical_specs": "Hemp fiber reinforced PP and PLA composites with fiber ratios of 10-30%"
    },
    {
        "name": "HempWool Thermal Insulation Batts",
        "description": "High-performance thermal insulation batts made from 90% natural hemp fiber, offering superior insulation properties with health and environmental benefits for construction applications.",
        "plant_part": "Fiber",
        "industry": "Construction",
        "sub_industry": "Insulation Materials",
        "companies": ["Hempitecture"],
        "benefits": ["90% natural hemp fiber content", "Superior thermal performance", "Non-toxic and safe for installers"],
        "commercialization_stage": "Commercial",
        "technical_specs": "R-values ranging from R-13 to R-30, available in various thicknesses"
    },
    {
        "name": "Hemp-Based Automotive Interior Panels",
        "description": "Sustainable automotive interior components including side panels and seat frames made from hemp fiber composites, developed for next-generation vehicle interiors.",
        "plant_part": "Fiber",
        "industry": "Automotive",
        "sub_industry": "Interior Components",
        "companies": ["Volkswagen", "Revoltech GmbH"],
        "benefits": ["Lightweight compared to traditional materials", "Sustainable alternative to petroleum-based plastics"],
        "commercialization_stage": "R&D",
        "technical_specs": "Hemp fiber reinforced composites suitable for automotive interior applications"
    },
    {
        "name": "Hemp-Based Sustainable Aviation Fuel (SAF)",
        "description": "Advanced biofuel produced from hemp biomass for aviation applications, offering carbon-neutral alternative to conventional jet fuel with potential for commercial aviation use.",
        "plant_part": "Seeds",
        "industry": "Energy",
        "sub_industry": "Aviation Fuels",
        "companies": ["SGP BioEnergy", "Honeywell", "Atlantic Biomass", "Bionoid"],
        "benefits": ["Carbon neutral fuel production", "Uses non-food crop feedstock", "Reduces aviation industry carbon footprint"],
        "commercialization_stage": "Pilot",
        "environmental_impact": "Carbon neutral production, reduces aviation emissions, uses agricultural waste"
    },
    {
        "name": "Hemp Fiber Circuit Board Substrates",
        "description": "Bio-based printed circuit board substrates using hemp fibers as reinforcement, offering sustainable alternative to traditional fiberglass PCBs with improved recyclability.",
        "plant_part": "Fiber",
        "industry": "Technology",
        "sub_industry": "Electronics Components",
        "companies": ["Jiva Materials"],
        "benefits": ["Sustainable alternative to fiberglass PCBs", "Improved recyclability of electronic components"],
        "commercialization_stage": "R&D",
        "environmental_impact": "Reduces e-waste toxicity, uses renewable hemp fibers"
    },
    {
        "name": "Hemp Prefabricated Building Panels",
        "description": "Prefabricated construction panels incorporating hemp fibers and hempcrete for rapid, sustainable building construction with superior thermal and structural properties.",
        "plant_part": "Fiber",
        "industry": "Construction",
        "sub_industry": "Prefab Building Systems",
        "companies": ["Lower Sioux Community"],
        "benefits": ["Rapid construction assembly", "Superior thermal insulation properties", "Carbon sequestering building material"],
        "commercialization_stage": "Commercial",
        "environmental_impact": "Carbon negative building material, sustainable construction methods"
    },
    {
        "name": "Hemp-Based Graphene Supercapacitors",
        "description": "Next-generation supercapacitors using hemp-derived graphene nanosheets that demonstrate superior performance characteristics compared to traditional graphene at significantly lower cost.",
        "plant_part": "Fiber",
        "industry": "Energy",
        "sub_industry": "Advanced Energy Storage",
        "companies": ["Premier Graphene", "HydroGraph"],
        "benefits": ["Superior performance vs traditional graphene", "1/1000th the cost of conventional graphene"],
        "commercialization_stage": "Pilot",
        "technical_specs": "Hemp-derived graphene electrodes for high-performance supercapacitors"
    }
]

# Mapping of plant part names to IDs (from database)
PLANT_PART_IDS = {
    "Fiber": 1,
    "Seeds": 2,
    "Hurd/Shiv": 3,
    "Flowers": 4,
    "Leaves": 5,
    "Roots": 6
}

# Mapping of industry names to IDs
INDUSTRY_IDS = {
    "Construction": 1,
    "Textiles": 2,
    "Food & Nutrition": 3,
    "Food & Beverage": 3,  # Same as Food & Nutrition
    "Health & Beauty": 4,
    "Automotive": 5,
    "Energy": 6,
    "Packaging": 7,
    "Agriculture": 8,
    "Technology": 9,
    "Other": 10
}

async def get_or_create_company(supabase: Client, company_name: str) -> int:
    """Get company ID or create new company"""
    # Check if company exists
    result = supabase.table('hemp_companies').select('id').eq('name', company_name).execute()
    
    if result.data:
        return result.data[0]['id']
    
    # Create new company
    new_company = {
        'name': company_name,
        'description': f'{company_name} - Industrial hemp products manufacturer',
        'ai_discovered': True,
        'created_at': datetime.utcnow().isoformat()
    }
    
    result = supabase.table('hemp_companies').insert(new_company).execute()
    return result.data[0]['id']

async def get_sub_industry_id(supabase: Client, industry_name: str, sub_industry_name: str) -> Optional[int]:
    """Get sub-industry ID"""
    industry_id = INDUSTRY_IDS.get(industry_name)
    if not industry_id:
        return None
    
    # Try to find sub-industry
    result = supabase.table('industry_sub_categories').select('id').eq('industry_id', industry_id).eq('name', sub_industry_name).execute()
    
    if result.data:
        return result.data[0]['id']
    
    # Create new sub-industry if not found
    new_sub = {
        'industry_id': industry_id,
        'name': sub_industry_name,
        'description': f'{sub_industry_name} in {industry_name} industry'
    }
    
    result = supabase.table('industry_sub_categories').insert(new_sub).execute()
    return result.data[0]['id']

def generate_image_prompt(product: dict) -> str:
    """Generate an optimized image prompt for the product"""
    name = product['name']
    description = product.get('description', '')
    
    # Base prompt structure
    prompt = f"Professional product photography of {name}. "
    
    # Add material/texture hints based on product type
    if 'ultracapacitor' in name.lower() or 'supercapacitor' in name.lower():
        prompt += "High-tech energy storage device with sleek design, visible electrodes and connections. "
    elif 'packaging' in name.lower():
        prompt += "Eco-friendly molded packaging containers and trays, natural fiber texture visible. "
    elif 'protein' in name.lower():
        prompt += "White protein powder in a modern container with hemp leaves decoration. "
    elif 'filament' in name.lower():
        prompt += "3D printer filament spool with natural fiber-reinforced material, hemp fibers visible. "
    elif 'insulation' in name.lower():
        prompt += "Thermal insulation batts showing fluffy natural fiber texture, construction material. "
    elif 'panel' in name.lower() and 'automotive' in name.lower():
        prompt += "Car interior door panel made from sustainable composite material, modern automotive design. "
    elif 'fuel' in name.lower():
        prompt += "Aviation fuel in industrial containers with aircraft in background, sustainable energy. "
    elif 'circuit' in name.lower():
        prompt += "Green printed circuit board with natural fiber substrate, electronic components visible. "
    elif 'building panel' in name.lower():
        prompt += "Prefabricated construction panels stacked at building site, hemp fiber texture visible. "
    elif 'graphene' in name.lower():
        prompt += "Advanced nanomaterial structure visualization, hexagonal patterns, high-tech laboratory setting. "
    
    # Add general styling
    prompt += "Professional studio lighting, clean white background, commercial product photography style. High quality, detailed, 4K."
    
    return prompt

async def main():
    """Add products to database"""
    
    # Check environment variables
    supabase_url = os.getenv('VITE_SUPABASE_URL')
    supabase_key = os.getenv('VITE_SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing VITE_SUPABASE_URL or VITE_SUPABASE_ANON_KEY in .env file")
        return
    
    # Create Supabase client
    supabase: Client = create_client(supabase_url, supabase_key)
    
    print("🚀 Adding Augment-Discovered Hemp Products")
    print("=" * 60)
    print(f"📊 Products to add: {len(AUGMENT_PRODUCTS)}")
    print()
    
    total_saved = 0
    
    for i, product in enumerate(AUGMENT_PRODUCTS, 1):
        print(f"\n[{i}/{len(AUGMENT_PRODUCTS)}] Processing: {product['name']}")
        
        try:
            # Check if product already exists
            existing = supabase.table('uses_products').select('id').eq('name', product['name']).execute()
            if existing.data:
                print(f"   ⚠️ Product already exists, skipping")
                continue
            
            # Get IDs for foreign keys
            plant_part_id = PLANT_PART_IDS.get(product['plant_part'])
            sub_industry_id = await get_sub_industry_id(supabase, product['industry'], product.get('sub_industry', ''))
            
            # Create product record
            new_product = {
                'name': product['name'],
                'description': product['description'],
                'plant_part_id': plant_part_id,
                'industry_sub_category_id': sub_industry_id,
                'benefits_advantages': product.get('benefits', []),
                'technical_specifications': product.get('technical_specs', ''),
                'environmental_impact': product.get('environmental_impact', ''),
                'market_size': product.get('market_size', ''),
                'commercialization_stage': product.get('commercialization_stage', 'R&D'),
                'ai_discovered': True,
                'image_url': '/api/placeholder/400/300',  # Placeholder
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            }
            
            # Insert product
            result = supabase.table('uses_products').insert(new_product).execute()
            product_id = result.data[0]['id']
            total_saved += 1
            print(f"   ✅ Product saved with ID: {product_id}")
            
            # Add companies
            if product.get('companies'):
                for company_name in product['companies']:
                    if company_name and 'Various' not in company_name:
                        company_id = await get_or_create_company(supabase, company_name)
                        
                        # Link company to product
                        company_product = {
                            'company_id': company_id,
                            'product_id': product_id,
                            'relationship_type': 'manufacturer',
                            'is_primary': True
                        }
                        supabase.table('hemp_company_products').insert(company_product).execute()
                        print(f"   🏢 Linked company: {company_name}")
            
            # Queue image generation
            image_prompt = generate_image_prompt(product)
            image_queue = {
                'product_id': product_id,
                'prompt': image_prompt,
                'status': 'pending',
                'priority': 1,
                'created_at': datetime.utcnow().isoformat()
            }
            supabase.table('image_generation_queue').insert(image_queue).execute()
            print(f"   🖼️ Image generation queued")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 Summary:")
    print(f"   📦 Products processed: {len(AUGMENT_PRODUCTS)}")
    print(f"   💾 New products saved: {total_saved}")
    print(f"   🖼️ Images queued: {total_saved}")
    
    print("\n📝 Next Steps:")
    print("1. View products at http://localhost:3000/all-products")
    print("2. Run 'node trigger_image_generation.js' to start image generation")
    print("3. Run 'python merge_agent_companies.py --auto' to consolidate companies")

if __name__ == "__main__":
    # Python 3.7+ required for asyncio.run()
    import sys
    if sys.version_info >= (3, 7):
        asyncio.run(main())
    else:
        # Fallback for older Python versions
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())