# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Current Status (Jan 27, 2025)

### 🖼️ Image Scraping & Attribution System Complete!
- **Company Logos**: 20/136 companies have logos (66% success rate)
- **Research Images**: 15/19 entries have images (91% success rate)
- **Attribution Component**: Legal compliance with hover-to-reveal source
- **Database Stats**: 222 products, 136 companies, 19 research entries

### 🚀 Key Systems Implemented
1. **Unified CLI** (`./hemp`): Single entry point for all operations
2. **Enhanced UI Components**: Smart Search, Interactive Cards, Analytics Dashboard
3. **Automated Scrapers**: Research papers, company data, images with attribution
4. **AI Agent System**: 6 active agents for content, research, and automation
5. **Modern Background**: Framer Motion particles (replaced heavy Three.js)

### 🎯 Latest Updates (Jan 27, 2025)
- **GitHub Actions**: Modernized all workflows with latest action versions
- **Security**: Fixed all npm vulnerabilities (prismjs, esbuild) with overrides
- **UI Enhancements**: Added alphabet filtering, improved plant parts navigation
- **Performance**: Optimized GitHub Actions with caching and modular scripts
- **Documentation**: Created workflow improvements guide

## Quick Start Commands

```bash
# Development
cd HempResourceHub
npm install
npm run dev                    # Starts Express + Vite

# Database
npm run db:push               # Push schema changes

# Image Scraping
python run_simple_scrapers.py              # Without attribution
python run_image_scrapers_fixed.py         # With attribution

# Unified CLI
./hemp agent research "query" --features company image
./hemp images generate --provider stable-diffusion
./hemp monitor --live
./hemp db export --format json

# Enhanced Scrapers
python enhanced_research_scraper.py        # PubMed integration
python enhanced_company_scraper.py         # Logo extraction
```

## Project Architecture

### Tech Stack
- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: Express.js + Drizzle ORM
- **Database**: PostgreSQL via Supabase
- **UI**: shadcn/ui components + custom animations
- **State**: React Query (TanStack Query)

### Database Schema
```
hemp_plant_archetypes → plant_parts → uses_products
                                    ↘ industry_sub_categories
                                    ↘ hemp_companies
                                    ↘ research_entries
```

### Key Tables (Actual Names)
- `hemp_plant_archetypes` (NOT plant_types)
- `uses_products` (NOT hemp_products)
- `industry_sub_categories` (NOT sub_industries)
- `research_entries` (NOT research_papers)
- `plant_parts` with `plant_type_id` FK

## Recent Features & Fixes

### UI/UX Enhancements (Augment Code)
- **Navigation**: Simplified to single `/products` route
- **Admin Panel**: 9→5 tabs with dropdown selectors
- **Visual**: Modern cards, gradients, hemp growing loader
- **Performance**: 53% navigation complexity reduction
- **Animations**: 4 new components (hemp-growing-loader, etc.)

### Data & Backend (Claude)
- **Product Discovery**: Manual + Python scripts
- **Company System**: Extraction, relationships, deduplication
- **Image Generation**: Integrated with research agent
- **Attribution**: Legal compliance for scraped content

### Fixed Issues
1. ✅ Database table name mismatches
2. ✅ Image display with proper fallbacks
3. ✅ Duplicate image generation loop
4. ✅ Research frontend column mapping
5. ✅ SSL/authentication for development
6. ✅ Content Security Policy for fonts
7. ✅ All npm vulnerabilities (prismjs, esbuild)
8. ✅ GitHub Actions updated to latest versions

## Environment Variables

```bash
# Required
VITE_SUPABASE_URL=https://ktoqznqmlnxrtvubewyz.supabase.co
VITE_SUPABASE_ANON_KEY=[from Supabase dashboard]
DATABASE_URL=postgresql://postgres:[password]@db.ktoqznqmlnxrtvubewyz.supabase.co:5432/postgres

# Optional
SUPABASE_SERVICE_ROLE_KEY=[for admin operations]
OPENAI_API_KEY=[for AI features]
```

## Development Workflow

1. **Frontend Changes**: Edit components in `/client/src/`, hot-reload via Vite
2. **Database Changes**: Update `/shared/schema.ts`, run `npm run db:push`
3. **API Changes**: Update `/server/routes.ts` and `/server/storage-db.ts`
4. **Python Scripts**: Use for data population and scraping
5. **Testing**: `npm run test` for API tests

## Key Components & Files

### Frontend
- `/client/src/components/ui/` - Reusable UI components
- `/client/src/components/animations/` - Hemp-themed animations
- `/client/src/pages/` - Route page components
- `/client/src/hooks/` - React Query data hooks

### Backend
- `/server/index.ts` - Express server setup
- `/server/routes.ts` - API endpoints
- `/server/storage-db.ts` - Database queries
- `/shared/schema.ts` - Shared type definitions

### Python Scripts
- `enhanced_research_scraper.py` - PubMed/article scraping
- `enhanced_company_scraper.py` - Logo extraction
- `run_simple_scrapers.py` - Pipeline runner
- `quick_add_product.py` - Manual product entry

### UI Components Created
- `attributed-image.tsx` - Image with source attribution
- `hemp-growing-loader.tsx` - Plant growth animation
- `smart-search.tsx` - AI-powered search
- `interactive-product-card.tsx` - Enhanced cards
- `data-visualization-dashboard.tsx` - Analytics
- `alphabet-filter.tsx` - A-Z filtering component

## Common Tasks

### Adding Products
```python
# Manual with companies
python quick_add_product.py

# Bulk import from CSV
python bulk_import_products.py

# Via research agent
python run_agent_with_images.py
```

### Running Scrapers
```python
# Company logos
python simple_logo_scraper.py

# Research images
python hemp_industry_daily_scraper.py

# Full pipeline
python run_simple_scrapers.py
```

### Troubleshooting

#### Puppeteer in WSL
```bash
sudo apt-get update
sudo apt-get install -y chromium-browser
sudo apt-get install -y libnss3 libnspr4 libatk1.0-0 libatk-bridge2.0-0
```

#### Database Connection
- Check URL encoding for special characters in password
- Use `sslmode=disable` for local development
- Ensure all environment variables are set

#### Image Issues
- Verify `/client/public/images/` directory exists
- Check for proper `?url` suffix in Vite imports
- Use fallback images for missing content

## Important Notes

- Always use `plant_type_id` not `archetype_id` (DB uses the former)
- Products table is `uses_products` not `hemp_products`
- Research table is `research_entries` not `research_papers`
- Use SERVICE_ROLE_KEY for admin operations (bypasses RLS)
- Keep git commits under 50MB (no node_modules)

## Support & Feedback

- **Help**: Use `/help` command
- **Issues**: Report at https://github.com/anthropics/claude-code/issues
- **Docs**: https://docs.anthropic.com/en/docs/claude-code