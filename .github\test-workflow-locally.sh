#!/bin/bash

# Test GitHub Actions Workflows Locally
# This script helps test workflow scripts without running them in GitHub

echo "🧪 GitHub Actions Local Testing Script"
echo "====================================="
echo ""

# Check if act is installed (GitHub Actions local runner)
if ! command -v act &> /dev/null; then
    echo "ℹ️  'act' is not installed. Install it for full local testing:"
    echo "   brew install act  # macOS"
    echo "   curl https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash  # Linux"
    echo ""
fi

# Set up test environment
export PYTHONPATH=$(pwd)
export MOCK_MODE=true
export TEST_MODE=true

# Load .env if it exists
if [ -f .env ]; then
    echo "📋 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Create test directories
echo "📁 Creating test directory structure..."
mkdir -p lib agents data logs
touch lib/__init__.py agents/__init__.py

# Create minimal requirements.txt if missing
if [ ! -f requirements.txt ]; then
    echo "📄 Creating minimal requirements.txt..."
    cat > requirements.txt << 'EOF'
supabase>=2.0.0
requests>=2.28.0
beautifulsoup4>=4.11.0
python-dotenv>=0.20.0
openai>=0.27.0
EOF
fi

# Create mock supabase_client if needed
if [ ! -f lib/supabase_client.py ]; then
    echo "📄 Creating mock supabase_client.py..."
    cat > lib/supabase_client.py << 'EOF'
import os
from typing import Any

class MockSupabaseClient:
    """Mock Supabase client for testing"""
    def table(self, name: str):
        return self
    
    def select(self, *args, **kwargs):
        return self
    
    def eq(self, column: str, value: Any):
        return self
    
    def gte(self, column: str, value: Any):
        return self
    
    def limit(self, count: int):
        return self
    
    def insert(self, data: dict):
        return self
    
    def execute(self):
        # Return mock data
        return type('obj', (object,), {
            'data': [],
            'count': 0
        })()

def get_supabase_client():
    """Get mock Supabase client for testing"""
    if os.environ.get('MOCK_MODE', 'false').lower() == 'true':
        print("🧪 Using mock Supabase client")
        return MockSupabaseClient()
    
    # Try real client
    try:
        from supabase import create_client
        url = os.environ.get('SUPABASE_URL')
        key = os.environ.get('SUPABASE_ANON_KEY')
        
        if url and key:
            return create_client(url, key)
    except:
        pass
    
    # Fallback to mock
    return MockSupabaseClient()
EOF
fi

# Function to test a specific workflow script
test_workflow_script() {
    local script_name=$1
    local description=$2
    
    echo ""
    echo "🧪 Testing: $description"
    echo "-----------------------------------"
    
    if [ -f "$script_name" ]; then
        python "$script_name"
        if [ $? -eq 0 ]; then
            echo "✅ $description: PASSED"
        else
            echo "❌ $description: FAILED"
        fi
    else
        echo "⏭️  $description: Script not found"
    fi
}

# Menu for testing
echo ""
echo "Choose what to test:"
echo "1) Test environment setup"
echo "2) Test hourly discovery script"
echo "3) Test weekly research script"
echo "4) Test all imports"
echo "5) Run diagnostic checks"
echo "6) Exit"
echo ""
read -p "Enter choice (1-6): " choice

case $choice in
    1)
        echo ""
        echo "🔍 Testing environment setup..."
        echo "Python version: $(python --version 2>&1)"
        echo "Working directory: $(pwd)"
        echo "PYTHONPATH: $PYTHONPATH"
        echo ""
        echo "Environment variables:"
        echo "- SUPABASE_URL: $([ -n "$SUPABASE_URL" ] && echo '✅ Set' || echo '❌ Not set')"
        echo "- SUPABASE_ANON_KEY: $([ -n "$SUPABASE_ANON_KEY" ] && echo '✅ Set' || echo '❌ Not set')"
        echo "- OPENAI_API_KEY: $([ -n "$OPENAI_API_KEY" ] && echo '✅ Set' || echo '❌ Not set')"
        echo "- MOCK_MODE: $MOCK_MODE"
        ;;
        
    2)
        # Create and test hourly discovery script
        cat > test_hourly_discovery.py << 'EOF'
import os
os.environ['MOCK_MODE'] = 'true'
os.environ['MAX_PRODUCTS'] = '3'
os.environ['FOCUS_CATEGORY'] = 'food-beverage'

print("🧪 Testing hourly discovery in mock mode...")

# Import and run the discovery function
try:
    exec(open('simplified_discovery.py').read()) if os.path.exists('simplified_discovery.py') else None
    print("✅ Hourly discovery test completed")
except Exception as e:
    print(f"❌ Hourly discovery test failed: {e}")
EOF
        test_workflow_script "test_hourly_discovery.py" "Hourly Discovery"
        ;;
        
    3)
        # Create and test weekly research script
        cat > test_weekly_research.py << 'EOF'
import os
os.environ['MOCK_MODE'] = 'true'
os.environ['AGENT_FOCUS'] = 'balanced'
os.environ['MAX_PRODUCTS_PER_AGENT'] = '3'

print("🧪 Testing weekly research in mock mode...")

try:
    # Test basic functionality
    from lib.supabase_client import get_supabase_client
    client = get_supabase_client()
    print("✅ Supabase client created")
    
    # Test mock query
    result = client.table('uses_products').select('id').limit(1).execute()
    print("✅ Mock query executed")
    
    print("✅ Weekly research test completed")
except Exception as e:
    print(f"❌ Weekly research test failed: {e}")
EOF
        test_workflow_script "test_weekly_research.py" "Weekly Research"
        ;;
        
    4)
        echo ""
        echo "🔍 Testing all imports..."
        python -c "
import sys
print('Python path:', sys.path[:3])

modules = [
    ('os', 'Standard library'),
    ('json', 'Standard library'),
    ('datetime', 'Standard library'),
    ('lib', 'Local lib module'),
    ('agents', 'Local agents module'),
]

for module, desc in modules:
    try:
        __import__(module)
        print(f'✅ {desc} ({module})')
    except Exception as e:
        print(f'❌ {desc} ({module}): {e}')

# Test Supabase client
try:
    from lib.supabase_client import get_supabase_client
    client = get_supabase_client()
    print('✅ Supabase client imported and created')
except Exception as e:
    print(f'❌ Supabase client: {e}')
"
        ;;
        
    5)
        echo ""
        echo "🔍 Running diagnostic checks..."
        
        # Check directory structure
        echo ""
        echo "Directory structure:"
        [ -d "lib" ] && echo "✅ lib/" || echo "❌ lib/ missing"
        [ -d "agents" ] && echo "✅ agents/" || echo "❌ agents/ missing"
        [ -f "requirements.txt" ] && echo "✅ requirements.txt" || echo "❌ requirements.txt missing"
        [ -f ".env" ] && echo "✅ .env" || echo "⚠️  .env missing (using environment)"
        
        # Check Python packages
        echo ""
        echo "Python packages:"
        python -c "
import importlib
packages = ['supabase', 'requests', 'bs4', 'dotenv', 'openai']
for pkg in packages:
    try:
        importlib.import_module(pkg)
        print(f'✅ {pkg}')
    except:
        print(f'❌ {pkg} - run: pip install {pkg}')
"
        ;;
        
    6)
        echo "👋 Exiting..."
        exit 0
        ;;
        
    *)
        echo "❌ Invalid choice"
        ;;
esac

echo ""
echo "-----------------------------------"
echo "💡 Tips:"
echo "- Set MOCK_MODE=true to test without real API calls"
echo "- Create a .env file with your credentials for real testing"
echo "- Check .github/workflows/README.md for more info"