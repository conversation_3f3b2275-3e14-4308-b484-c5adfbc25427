#!/usr/bin/env python3
"""
Add products discovered by Augment Code to the database
"""

import asyncio
import os
import sys
import json
from dotenv import load_dotenv

# Add the project root to sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.research.research_agent_with_images import ResearchAgentWithImages

# Load .env from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
load_dotenv(env_path)

# Products discovered by Augment Code
AUGMENT_PRODUCTS = {
  "products": [
    {
      "name": "Hemp-Based Ultracapacitors",
      "description": "High-performance energy storage devices using hemp biomass-derived activated carbon that provides superior energy density compared to traditional ultracapacitors. Developed by Florrent using regeneratively grown hemp.",
      "plant_part": "Fiber",
      "industry": "Energy",
      "sub_industry": "Energy Storage",
      "companies": ["Florrent"],
      "benefits": [
        "Higher energy density than conventional ultracapacitors",
        "Made from regeneratively grown hemp biomass",
        "Supports BIPOC farmers in supply chain",
        "Carbon sequestering production process",
        "Rapid charge/discharge capabilities"
      ],
      "commercialization_stage": "Commercial",
      "technical_specs": "High energy density ultracapacitor technology for hybrid energy storage applications",
      "environmental_impact": "Carbon negative production using regeneratively grown hemp, supports sustainable agriculture",
      "market_size": "$15 billion ultracapacitor market by 2030",
      "source_url": "https://www.renewableenergyworld.com/energy-storage/battery/startup-spotlight-florrents-hemp-based-ultracapacitors/"
    },
    {
      "name": "Hemp Molded Fiber Packaging",
      "description": "Plastic-free molded fiber packaging products made primarily from industrial hemp, offering sustainable alternative to traditional plastic packaging for food and non-food applications.",
      "plant_part": "Fiber",
      "industry": "Packaging",
      "sub_industry": "Sustainable Packaging",
      "companies": ["RENW", "element6 Dynamics", "PAPACKS"],
      "benefits": [
        "100% plastic-free packaging solution",
        "Fully circular and compostable",
        "Made from agricultural waste hemp",
        "Suitable for food and non-food applications",
        "Scalable manufacturing process"
      ],
      "commercialization_stage": "Commercial",
      "technical_specs": "Molded fiber technology using industrial hemp as primary raw material",
      "environmental_impact": "Eliminates plastic waste, uses agricultural hemp waste, fully compostable",
      "market_size": "Growing sustainable packaging market",
      "source_url": "https://www.packagingdive.com/news/renw-element6-dynamics-papacks-industrial-hemp-molded-fiber/727951/"
    },
    {
      "name": "Hemp Protein Isolate (90%+ Purity)",
      "description": "World's first commercial soluble hemp protein isolate with greater than 90% protein content, developed through innovative processing technology for food and beverage applications.",
      "plant_part": "Seeds",
      "industry": "Food & Beverage",
      "sub_industry": "Protein Ingredients",
      "companies": ["Burcon NutraScience", "HPS Food and Ingredients"],
      "benefits": [
        "Greater than 90% protein content",
        "Highly soluble and functional",
        "Complete amino acid profile",
        "Allergen-friendly alternative protein",
        "Sustainable protein source"
      ],
      "commercialization_stage": "Commercial",
      "technical_specs": "Protein isolate with >90% protein content, high solubility, neutral taste",
      "environmental_impact": "Lower environmental footprint than animal proteins, sustainable hemp cultivation",
      "market_size": "Growing plant-based protein market valued at billions",
      "source_url": "https://burcon.ca/2024/04/hps-and-burcon-achieve-first-commercial-sales-of-worlds-first-hemp-protein-isolate/"
    },
    {
      "name": "Hemp-Reinforced 3D Printing Filaments",
      "description": "Advanced 3D printing filaments incorporating hemp fibers with polypropylene and PLA matrices, offering improved mechanical properties and sustainability for additive manufacturing.",
      "plant_part": "Fiber",
      "industry": "Technology",
      "sub_industry": "3D Printing Materials",
      "companies": ["Various research institutions", "Commercial filament manufacturers"],
      "benefits": [
        "Enhanced mechanical properties vs pure polymer",
        "Sustainable bio-based reinforcement",
        "Reduced material costs",
        "Improved printability",
        "Recyclable composite materials"
      ],
      "commercialization_stage": "Pilot",
      "technical_specs": "Hemp fiber reinforced PP and PLA composites with fiber ratios of 10-30%",
      "environmental_impact": "Reduces petroleum-based polymer content, uses agricultural hemp waste",
      "market_size": "Growing 3D printing materials market",
      "source_url": "https://www.sciencedirect.com/science/article/pii/S2405844024026483"
    },
    {
      "name": "HempWool Thermal Insulation Batts",
      "description": "High-performance thermal insulation batts made from 90% natural hemp fiber, offering superior insulation properties with health and environmental benefits for construction applications.",
      "plant_part": "Fiber",
      "industry": "Construction",
      "sub_industry": "Insulation Materials",
      "companies": ["Hempitecture"],
      "benefits": [
        "90% natural hemp fiber content",
        "Superior thermal performance",
        "Non-toxic and safe for installers",
        "Moisture regulating properties",
        "Carbon sequestering building material"
      ],
      "commercialization_stage": "Commercial",
      "technical_specs": "R-values ranging from R-13 to R-30, available in various thicknesses",
      "environmental_impact": "Carbon negative material, non-toxic production, sustainable hemp cultivation",
      "market_size": "Growing green building materials market",
      "source_url": "https://www.hempitecture.com/hempwool/"
    },
    {
      "name": "Hemp-Based Automotive Interior Panels",
      "description": "Sustainable automotive interior components including side panels and seat frames made from hemp fiber composites, developed for next-generation vehicle interiors.",
      "plant_part": "Fiber",
      "industry": "Automotive",
      "sub_industry": "Interior Components",
      "companies": ["Volkswagen", "Revoltech GmbH"],
      "benefits": [
        "Lightweight compared to traditional materials",
        "Sustainable alternative to petroleum-based plastics",
        "Good mechanical properties for automotive use",
        "Reduced vehicle carbon footprint",
        "Innovative surface material applications"
      ],
      "commercialization_stage": "R&D",
      "technical_specs": "Hemp fiber reinforced composites suitable for automotive interior applications",
      "environmental_impact": "Reduces petroleum-based materials in vehicles, sustainable hemp cultivation",
      "market_size": "Multi-billion automotive interior materials market",
      "source_url": "https://www.volkswagen-newsroom.com/en/press-releases/imitation-leather-from-industrial-hemp-innovative-and-sustainable-material-for-future-car-interiors-18665"
    },
    {
      "name": "Hemp-Based Sustainable Aviation Fuel (SAF)",
      "description": "Advanced biofuel produced from hemp biomass for aviation applications, offering carbon-neutral alternative to conventional jet fuel with potential for commercial aviation use.",
      "plant_part": "Seeds/Biomass",
      "industry": "Energy",
      "sub_industry": "Aviation Fuels",
      "companies": ["SGP BioEnergy", "Honeywell", "Atlantic Biomass", "Bionoid"],
      "benefits": [
        "Carbon neutral fuel production",
        "Uses non-food crop feedstock",
        "Reduces aviation industry carbon footprint",
        "Compatible with existing aircraft engines",
        "Utilizes hemp agricultural waste"
      ],
      "commercialization_stage": "Pilot",
      "technical_specs": "Sustainable aviation fuel meeting industry specifications for commercial aircraft",
      "environmental_impact": "Carbon neutral production, reduces aviation emissions, uses agricultural waste",
      "market_size": "Growing SAF market driven by aviation decarbonization goals",
      "source_url": "https://ess.honeywell.com/us/en/about-ess/newsroom/press-release/2024/10/honeywell-and-sgp-bioenergy-to-develop-plant-based-biochemicals-reducing-industry-reliance-on-fossil-fuels"
    },
    {
      "name": "Hemp Fiber Circuit Board Substrates",
      "description": "Bio-based printed circuit board substrates using hemp fibers as reinforcement, offering sustainable alternative to traditional fiberglass PCBs with improved recyclability.",
      "plant_part": "Fiber",
      "industry": "Technology",
      "sub_industry": "Electronics Components",
      "companies": ["Jiva Materials", "UK research institutions"],
      "benefits": [
        "Sustainable alternative to fiberglass PCBs",
        "Improved recyclability of electronic components",
        "Reduced e-waste environmental impact",
        "Bio-based reinforcement material",
        "Maintains electrical performance requirements"
      ],
      "commercialization_stage": "R&D",
      "technical_specs": "Hemp fiber reinforced PCB laminates meeting electronics industry standards",
      "environmental_impact": "Reduces e-waste toxicity, uses renewable hemp fibers, improved end-of-life recycling",
      "market_size": "Multi-billion PCB market with growing sustainability focus",
      "source_url": "https://hempgazette.com/news/hemp-circuit-board-hg2131/"
    },
    {
      "name": "Hemp Prefabricated Building Panels",
      "description": "Prefabricated construction panels incorporating hemp fibers and hempcrete for rapid, sustainable building construction with superior thermal and structural properties.",
      "plant_part": "Fiber/Hurd",
      "industry": "Construction",
      "sub_industry": "Prefab Building Systems",
      "companies": ["Lower Sioux Community", "Various hemp construction companies"],
      "benefits": [
        "Rapid construction assembly",
        "Superior thermal insulation properties",
        "Carbon sequestering building material",
        "Moisture regulating properties",
        "Reduced construction waste"
      ],
      "commercialization_stage": "Commercial",
      "technical_specs": "Prefabricated panels combining hemp fiber and hempcrete for structural applications",
      "environmental_impact": "Carbon negative building material, sustainable construction methods",
      "market_size": "Growing prefab construction market",
      "source_url": "https://lowersioux.com/hemp-program-and-housing-project/"
    },
    {
      "name": "Hemp-Based Graphene Supercapacitors",
      "description": "Next-generation supercapacitors using hemp-derived graphene nanosheets that demonstrate superior performance characteristics compared to traditional graphene at significantly lower cost.",
      "plant_part": "Fiber",
      "industry": "Energy",
      "sub_industry": "Advanced Energy Storage",
      "companies": ["Premier Graphene", "HydroGraph"],
      "benefits": [
        "Superior performance vs traditional graphene",
        "1/1000th the cost of conventional graphene",
        "Sustainable production from agricultural waste",
        "High energy and power density",
        "Rapid charge/discharge capabilities"
      ],
      "commercialization_stage": "Pilot",
      "technical_specs": "Hemp-derived graphene electrodes for high-performance supercapacitors",
      "environmental_impact": "Uses agricultural hemp waste, sustainable graphene production process",
      "market_size": "Multi-billion energy storage market",
      "source_url": "https://businessofcannabis.com/from-waste-to-wonder-material-hemp-graphene-could-be-the-future-of-energy-storage/"
    }
  ]
}

async def main():
    """Add Augment-discovered products to the database"""
    
    # Check for required environment variables
    required_vars = ['VITE_SUPABASE_URL', 'VITE_SUPABASE_ANON_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please add them to your .env file")
        return
    
    # Initialize agent
    agent = ResearchAgentWithImages(
        supabase_url=os.getenv('VITE_SUPABASE_URL'),
        supabase_key=os.getenv('VITE_SUPABASE_ANON_KEY'),
        openai_api_key=os.getenv('OPENAI_API_KEY')  # Optional
    )
    
    print("🚀 Adding Augment-Discovered Hemp Products")
    print("=" * 60)
    print(f"📊 Products to add: {len(AUGMENT_PRODUCTS['products'])}")
    print()
    
    total_saved = 0
    
    # Convert Augment format to agent format
    for i, product in enumerate(AUGMENT_PRODUCTS['products'], 1):
        print(f"\n[{i}/{len(AUGMENT_PRODUCTS['products'])}] Processing: {product['name']}")
        
        try:
            # Convert to agent's expected format
            agent_product = {
                'name': product['name'],
                'description': product['description'],
                'plant_part': product['plant_part'],
                'industry': product['industry'],
                'sub_industry': product.get('sub_industry', ''),
                'companies': product.get('companies', []),
                'benefits': product.get('benefits', []),
                'technical_specs': product.get('technical_specs', ''),
                'environmental_impact': product.get('environmental_impact', ''),
                'market_size': product.get('market_size', ''),
                'commercialization_stage': product.get('commercialization_stage', 'R&D'),
                'source': product.get('source_url', '')
            }
            
            # Save the product (this will also queue image generation)
            saved = await agent.save_discovered_products([agent_product])
            
            if saved > 0:
                total_saved += saved
                print(f"   ✅ Saved successfully")
                print(f"   🖼️ Image generation queued")
                
                # Extract and save companies
                if product.get('companies'):
                    print(f"   🏢 Companies: {', '.join(product['companies'])}")
            else:
                print(f"   ⚠️ Product may already exist in database")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Summary:")
    print(f"   📦 Products processed: {len(AUGMENT_PRODUCTS['products'])}")
    print(f"   💾 New products saved: {total_saved}")
    print(f"   🖼️ Images queued: {total_saved}")
    
    if total_saved > 0:
        print("\n⏳ Waiting 30 seconds for initial image generation...")
        await asyncio.sleep(30)
        
        # Process any completed images
        print("🔄 Processing completed images...")
        await agent.process_completed_images()
        
        print("\n✅ Products added successfully!")
        print("💡 Images will continue generating in the background")
    
    print("\n📝 Next Steps:")
    print("1. View products at http://localhost:3000/all-products")
    print("2. Check Supabase dashboard for image generation queue")
    print("3. Run 'python merge_agent_companies.py --auto' to consolidate companies")

if __name__ == "__main__":
    asyncio.run(main())