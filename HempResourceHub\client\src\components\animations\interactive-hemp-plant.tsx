import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PlantPart {
  id: string;
  name: string;
  description: string;
  color: string;
  position: { x: number; y: number };
}

interface InteractiveHempPlantProps {
  onPartSelect?: (part: PlantPart) => void;
  selectedPart?: string | null;
}

export const InteractiveHempPlant: React.FC<InteractiveHempPlantProps> = ({
  onPartSelect,
  selectedPart
}) => {
  const [hoveredPart, setHoveredPart] = useState<string | null>(null);

  const plantParts: PlantPart[] = [
    {
      id: 'flower',
      name: 'Flower/Bud',
      description: 'Used for CBD extraction and medicinal products',
      color: '#8B5CF6',
      position: { x: 50, y: 15 }
    },
    {
      id: 'leaves',
      name: 'Leaves',
      description: 'Used for teas, extracts, and animal feed',
      color: '#22C55E',
      position: { x: 35, y: 30 }
    },
    {
      id: 'stem',
      name: 'Stem/Stalk',
      description: 'Source of bast fibers for textiles and construction',
      color: '#16A34A',
      position: { x: 50, y: 50 }
    },
    {
      id: 'seeds',
      name: 'Seeds',
      description: 'Rich in protein and omega fatty acids',
      color: '#F59E0B',
      position: { x: 65, y: 25 }
    },
    {
      id: 'roots',
      name: 'Roots',
      description: 'Soil remediation and biomass production',
      color: '#92400E',
      position: { x: 50, y: 85 }
    }
  ];

  const handlePartClick = (part: PlantPart) => {
    onPartSelect?.(part);
  };

  const getPartOpacity = (partId: string) => {
    if (!hoveredPart && !selectedPart) return 1;
    if (hoveredPart === partId || selectedPart === partId) return 1;
    return 0.4;
  };

  const containerVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 1,
        staggerChildren: 0.2
      }
    }
  };

  const partVariants = {
    initial: { scale: 0, opacity: 0 },
    animate: { 
      scale: 1, 
      opacity: 1,
      transition: { type: "spring", stiffness: 200, damping: 15 }
    }
  };

  return (
    <div className="relative w-full max-w-md mx-auto">
      <motion.svg
        width="100%"
        height="400"
        viewBox="0 0 100 100"
        className="overflow-visible"
        variants={containerVariants}
        initial="initial"
        animate="animate"
      >
        {/* Background glow */}
        <defs>
          <radialGradient id="plantGlow" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#22c55e" stopOpacity="0.1" />
            <stop offset="100%" stopColor="#22c55e" stopOpacity="0" />
          </radialGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        <circle cx="50" cy="50" r="45" fill="url(#plantGlow)" />

        {/* Root system */}
        <motion.g variants={partVariants}>
          <path
            d="M50 85 Q40 90 30 95 M50 85 Q45 88 35 92 M50 85 Q55 88 65 92 M50 85 Q60 90 70 95"
            stroke="#92400E"
            strokeWidth="1.5"
            fill="none"
            opacity={getPartOpacity('roots')}
            className="cursor-pointer transition-all duration-300"
            onClick={() => handlePartClick(plantParts.find(p => p.id === 'roots')!)}
            onMouseEnter={() => setHoveredPart('roots')}
            onMouseLeave={() => setHoveredPart(null)}
            filter={hoveredPart === 'roots' || selectedPart === 'roots' ? 'url(#glow)' : ''}
          />
        </motion.g>

        {/* Main stem */}
        <motion.g variants={partVariants}>
          <rect
            x="49"
            y="30"
            width="2"
            height="55"
            fill="#16A34A"
            rx="1"
            opacity={getPartOpacity('stem')}
            className="cursor-pointer transition-all duration-300"
            onClick={() => handlePartClick(plantParts.find(p => p.id === 'stem')!)}
            onMouseEnter={() => setHoveredPart('stem')}
            onMouseLeave={() => setHoveredPart(null)}
            filter={hoveredPart === 'stem' || selectedPart === 'stem' ? 'url(#glow)' : ''}
          />
        </motion.g>

        {/* Leaves */}
        <motion.g variants={partVariants}>
          {/* Left leaves */}
          <path
            d="M35 35 Q25 30 20 35 Q25 40 35 35"
            fill="#22C55E"
            opacity={getPartOpacity('leaves')}
            className="cursor-pointer transition-all duration-300"
            onClick={() => handlePartClick(plantParts.find(p => p.id === 'leaves')!)}
            onMouseEnter={() => setHoveredPart('leaves')}
            onMouseLeave={() => setHoveredPart(null)}
            filter={hoveredPart === 'leaves' || selectedPart === 'leaves' ? 'url(#glow)' : ''}
          />
          <path
            d="M40 45 Q30 40 25 45 Q30 50 40 45"
            fill="#22C55E"
            opacity={getPartOpacity('leaves')}
            className="cursor-pointer transition-all duration-300"
            onClick={() => handlePartClick(plantParts.find(p => p.id === 'leaves')!)}
            onMouseEnter={() => setHoveredPart('leaves')}
            onMouseLeave={() => setHoveredPart(null)}
          />
          
          {/* Right leaves */}
          <path
            d="M65 35 Q75 30 80 35 Q75 40 65 35"
            fill="#22C55E"
            opacity={getPartOpacity('leaves')}
            className="cursor-pointer transition-all duration-300"
            onClick={() => handlePartClick(plantParts.find(p => p.id === 'leaves')!)}
            onMouseEnter={() => setHoveredPart('leaves')}
            onMouseLeave={() => setHoveredPart(null)}
          />
          <path
            d="M60 45 Q70 40 75 45 Q70 50 60 45"
            fill="#22C55E"
            opacity={getPartOpacity('leaves')}
            className="cursor-pointer transition-all duration-300"
            onClick={() => handlePartClick(plantParts.find(p => p.id === 'leaves')!)}
            onMouseEnter={() => setHoveredPart('leaves')}
            onMouseLeave={() => setHoveredPart(null)}
          />
        </motion.g>

        {/* Seeds/Buds */}
        <motion.g variants={partVariants}>
          <circle
            cx="65"
            cy="25"
            r="3"
            fill="#F59E0B"
            opacity={getPartOpacity('seeds')}
            className="cursor-pointer transition-all duration-300"
            onClick={() => handlePartClick(plantParts.find(p => p.id === 'seeds')!)}
            onMouseEnter={() => setHoveredPart('seeds')}
            onMouseLeave={() => setHoveredPart(null)}
            filter={hoveredPart === 'seeds' || selectedPart === 'seeds' ? 'url(#glow)' : ''}
          />
          <circle cx="58" cy="28" r="2" fill="#F59E0B" opacity={getPartOpacity('seeds')} />
          <circle cx="42" cy="28" r="2" fill="#F59E0B" opacity={getPartOpacity('seeds')} />
        </motion.g>

        {/* Flower/Top bud */}
        <motion.g variants={partVariants}>
          <ellipse
            cx="50"
            cy="15"
            rx="4"
            ry="6"
            fill="#8B5CF6"
            opacity={getPartOpacity('flower')}
            className="cursor-pointer transition-all duration-300"
            onClick={() => handlePartClick(plantParts.find(p => p.id === 'flower')!)}
            onMouseEnter={() => setHoveredPart('flower')}
            onMouseLeave={() => setHoveredPart(null)}
            filter={hoveredPart === 'flower' || selectedPart === 'flower' ? 'url(#glow)' : ''}
          />
        </motion.g>

        {/* Interactive dots for each part */}
        {plantParts.map((part) => (
          <motion.circle
            key={part.id}
            cx={part.position.x}
            cy={part.position.y}
            r="2"
            fill={part.color}
            className="cursor-pointer"
            variants={partVariants}
            whileHover={{ scale: 1.5, r: 3 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => handlePartClick(part)}
            onMouseEnter={() => setHoveredPart(part.id)}
            onMouseLeave={() => setHoveredPart(null)}
            opacity={selectedPart === part.id ? 1 : 0.7}
          />
        ))}
      </motion.svg>

      {/* Tooltip */}
      <AnimatePresence>
        {hoveredPart && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-gray-900/90 backdrop-blur-sm text-white p-3 rounded-lg shadow-lg border border-gray-700 max-w-xs"
          >
            <h4 className="font-semibold text-green-400 mb-1">
              {plantParts.find(p => p.id === hoveredPart)?.name}
            </h4>
            <p className="text-sm text-gray-300">
              {plantParts.find(p => p.id === hoveredPart)?.description}
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InteractiveHempPlant;
