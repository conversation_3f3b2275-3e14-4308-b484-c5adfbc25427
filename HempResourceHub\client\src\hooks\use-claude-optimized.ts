import { useState, useEffect, useCallback, useRef } from 'react';
import { getClaudeAPI } from '@/lib/claude-api';
import { Message } from '@/lib/claude-api';

interface UseClaudeOptions {
  agentId: string;
  enableCache?: boolean;
  cacheTTL?: number; // seconds
  maxRetries?: number;
  onError?: (error: Error) => void;
  onCostUpdate?: (cost: number) => void;
}

interface UseClaudeReturn {
  messages: Message[];
  isLoading: boolean;
  error: Error | null;
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;
  conversationId: string | null;
  estimatedCost: number;
  isCached: boolean;
}

// Simple in-memory cache
const responseCache = new Map<string, { response: string; timestamp: number }>();

// Generate cache key from agent and message
function getCacheKey(agentId: string, message: string): string {
  return `${agentId}:${message.toLowerCase().trim()}`;
}

// Check if cached response is still valid
function getCachedResponse(key: string, ttl: number): string | null {
  const cached = responseCache.get(key);
  if (!cached) return null;
  
  const age = Date.now() - cached.timestamp;
  if (age > ttl * 1000) {
    responseCache.delete(key);
    return null;
  }
  
  return cached.response;
}

export function useClaudeOptimized({
  agentId,
  enableCache = true,
  cacheTTL = 3600, // 1 hour default
  maxRetries = 3,
  onError,
  onCostUpdate,
}: UseClaudeOptions): UseClaudeReturn {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [estimatedCost, setEstimatedCost] = useState(0);
  const [isCached, setIsCached] = useState(false);
  
  const claudeAPI = useRef(getClaudeAPI());
  const retryCount = useRef(0);
  const conversationCreating = useRef(false);

  // Initialize conversation
  useEffect(() => {
    if (!agentId || conversationId || conversationCreating.current) return;
    
    const initConversation = async () => {
      conversationCreating.current = true;
      let attempts = 0;
      
      while (attempts < maxRetries && !conversationId) {
        try {
          const id = await claudeAPI.current.createConversation(agentId);
          setConversationId(id);
          setError(null);
          console.log(`[Optimized] Conversation created: ${id}`);
          break;
        } catch (err) {
          attempts++;
          console.error(`[Optimized] Attempt ${attempts} failed:`, err);
          
          if (attempts >= maxRetries) {
            const error = err instanceof Error ? err : new Error('Failed to create conversation');
            setError(error);
            onError?.(error);
          } else {
            // Exponential backoff
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempts) * 1000));
          }
        }
      }
      
      conversationCreating.current = false;
    };
    
    initConversation();
  }, [agentId, conversationId, maxRetries, onError]);

  // Estimate cost based on message length and agent config
  const estimateMessageCost = useCallback((message: string, agentId: string): number => {
    // Rough estimation: 1 token ≈ 4 characters
    const inputTokens = Math.ceil(message.length / 4);
    const outputTokens = 500; // Conservative estimate
    
    // Haiku pricing
    const inputCost = (inputTokens / 1_000_000) * 0.25;
    const outputCost = (outputTokens / 1_000_000) * 1.25;
    
    return inputCost + outputCost;
  }, []);

  const sendMessage = useCallback(async (message: string) => {
    if (!conversationId || !message.trim()) {
      console.warn('[Optimized] Cannot send message: no conversation or empty message');
      return;
    }

    // Check cache first
    const cacheKey = getCacheKey(agentId, message);
    if (enableCache) {
      const cachedResponse = getCachedResponse(cacheKey, cacheTTL);
      if (cachedResponse) {
        console.log('[Optimized] Cache hit!');
        setMessages(prev => [
          ...prev,
          { role: 'user', content: message },
          { role: 'assistant', content: cachedResponse }
        ]);
        setIsCached(true);
        onCostUpdate?.(0); // No cost for cached response
        return;
      }
    }

    setIsLoading(true);
    setError(null);
    setIsCached(false);
    
    // Estimate cost
    const estCost = estimateMessageCost(message, agentId);
    setEstimatedCost(prev => prev + estCost);
    
    // Add user message immediately for better UX
    setMessages(prev => [...prev, { role: 'user', content: message }]);
    
    let attempts = 0;
    let response: string | null = null;
    
    while (attempts < maxRetries && !response) {
      try {
        response = await claudeAPI.current.sendMessage(conversationId, message);
        
        if (response) {
          setMessages(prev => [...prev, { role: 'assistant', content: response }]);
          
          // Cache the response
          if (enableCache) {
            responseCache.set(cacheKey, {
              response,
              timestamp: Date.now()
            });
            
            // Limit cache size
            if (responseCache.size > 100) {
              const oldestKey = Array.from(responseCache.entries())
                .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
              responseCache.delete(oldestKey);
            }
          }
          
          onCostUpdate?.(estCost);
          setError(null);
        }
      } catch (err) {
        attempts++;
        console.error(`[Optimized] Send attempt ${attempts} failed:`, err);
        
        if (attempts >= maxRetries) {
          const error = err instanceof Error ? err : new Error('Failed to send message');
          setError(error);
          onError?.(error);
          
          // Remove the user message on final failure
          setMessages(prev => prev.slice(0, -1));
        } else {
          // Exponential backoff with jitter
          const delay = Math.pow(2, attempts) * 1000 + Math.random() * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    setIsLoading(false);
  }, [conversationId, agentId, enableCache, cacheTTL, maxRetries, onError, onCostUpdate, estimateMessageCost]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setEstimatedCost(0);
    setIsCached(false);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (conversationId) {
        claudeAPI.current.deleteConversation(conversationId).catch(console.error);
      }
    };
  }, [conversationId]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    conversationId,
    estimatedCost,
    isCached,
  };
}