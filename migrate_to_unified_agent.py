#!/usr/bin/env python3
"""
Migration script to update existing code to use the unified research agent
"""

import os
import re
import shutil
from datetime import datetime


def backup_file(filepath):
    """Create a backup of the file before modifying"""
    backup_path = f"{filepath}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(filepath, backup_path)
    print(f"Backed up: {filepath} -> {backup_path}")
    return backup_path


def update_imports(content):
    """Update import statements to use unified agent"""
    # Pattern to match old imports
    patterns = [
        (r'from agents\.research\.research_agent import HempResearchAgent',
         'from agents.research.unified_research_agent import UnifiedResearchAgent, create_research_agent'),
        (r'from agents\.research\.enhanced_research_agent import EnhancedHempResearchAgent',
         'from agents.research.unified_research_agent import UnifiedResearchAgent, create_research_agent'),
        (r'from agents\.research\.research_agent_with_images import ResearchAgentWithImages',
         'from agents.research.unified_research_agent import UnifiedResearchAgent, create_research_agent'),
        (r'import agents\.research\.research_agent',
         'import agents.research.unified_research_agent'),
    ]
    
    for old_pattern, new_import in patterns:
        content = re.sub(old_pattern, new_import, content)
    
    return content


def update_class_instantiation(content):
    """Update class instantiation to use unified agent"""
    # Pattern to match old class instantiations
    patterns = [
        # Basic research agent
        (r'HempResearchAgent\((.*?)\)',
         lambda m: f"create_research_agent({m.group(1)}, features=['basic'])"),
        
        # Enhanced research agent
        (r'EnhancedHempResearchAgent\((.*?)\)',
         lambda m: f"create_research_agent({m.group(1)}, features=['company'])"),
        
        # Research agent with images
        (r'ResearchAgentWithImages\((.*?)\)',
         lambda m: f"create_research_agent({m.group(1)}, features=['company', 'image'])"),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    return content


def update_type_hints(content):
    """Update type hints to use unified agent"""
    patterns = [
        (r': HempResearchAgent', ': UnifiedResearchAgent'),
        (r': EnhancedHempResearchAgent', ': UnifiedResearchAgent'),
        (r': ResearchAgentWithImages', ': UnifiedResearchAgent'),
        (r'\[HempResearchAgent\]', '[UnifiedResearchAgent]'),
        (r'\[EnhancedHempResearchAgent\]', '[UnifiedResearchAgent]'),
        (r'\[ResearchAgentWithImages\]', '[UnifiedResearchAgent]'),
    ]
    
    for old_pattern, new_pattern in patterns:
        content = re.sub(old_pattern, new_pattern, content)
    
    return content


def migrate_file(filepath):
    """Migrate a single file to use unified agent"""
    print(f"\nProcessing: {filepath}")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply migrations
        content = update_imports(content)
        content = update_class_instantiation(content)
        content = update_type_hints(content)
        
        if content != original_content:
            # Backup original file
            backup_file(filepath)
            
            # Write updated content
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✓ Updated: {filepath}")
            return True
        else:
            print(f"- No changes needed: {filepath}")
            return False
            
    except Exception as e:
        print(f"✗ Error processing {filepath}: {e}")
        return False


def find_files_to_migrate(root_dir):
    """Find all Python files that might need migration"""
    files_to_migrate = []
    
    # Patterns to search for
    search_patterns = [
        'HempResearchAgent',
        'EnhancedHempResearchAgent', 
        'ResearchAgentWithImages',
        'research_agent.py',
        'enhanced_research_agent.py',
        'research_agent_with_images.py'
    ]
    
    for root, dirs, files in os.walk(root_dir):
        # Skip backup files and __pycache__
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py') and not file.endswith('.backup'):
                filepath = os.path.join(root, file)
                
                # Skip the unified agent itself
                if 'unified_research_agent.py' in filepath:
                    continue
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check if file contains any of the patterns
                    if any(pattern in content for pattern in search_patterns):
                        files_to_migrate.append(filepath)
                        
                except Exception as e:
                    print(f"Error reading {filepath}: {e}")
    
    return files_to_migrate


def create_migration_report(migrated_files, failed_files):
    """Create a report of the migration"""
    report_path = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_path, 'w') as f:
        f.write("=== Research Agent Migration Report ===\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write(f"Successfully migrated: {len(migrated_files)}\n")
        for file in migrated_files:
            f.write(f"  ✓ {file}\n")
        
        f.write(f"\nFailed: {len(failed_files)}\n")
        for file in failed_files:
            f.write(f"  ✗ {file}\n")
        
        f.write("\n=== Migration Notes ===\n")
        f.write("1. Old agent classes have been replaced with UnifiedResearchAgent\n")
        f.write("2. Use create_research_agent() helper function with features parameter\n")
        f.write("3. Features: ['basic', 'company', 'image', 'deep', 'web', 'feed', 'trend']\n")
        f.write("4. Backup files created with .backup_timestamp extension\n")
        f.write("\nTo restore: rename .backup files to original names\n")
    
    print(f"\nMigration report saved: {report_path}")
    return report_path


def main():
    """Main migration function"""
    print("=== Research Agent Migration Tool ===")
    print("This will update all code to use the unified research agent\n")
    
    # Get the project root
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Find files that need migration
    print("Scanning for files to migrate...")
    files_to_migrate = find_files_to_migrate(script_dir)
    
    if not files_to_migrate:
        print("No files found that need migration!")
        return
    
    print(f"\nFound {len(files_to_migrate)} files to migrate:")
    for file in files_to_migrate:
        print(f"  - {file}")
    
    # Ask for confirmation
    response = input("\nProceed with migration? (y/n): ")
    if response.lower() != 'y':
        print("Migration cancelled.")
        return
    
    # Perform migration
    migrated_files = []
    failed_files = []
    
    for filepath in files_to_migrate:
        if migrate_file(filepath):
            migrated_files.append(filepath)
        else:
            failed_files.append(filepath)
    
    # Create report
    report_path = create_migration_report(migrated_files, failed_files)
    
    print(f"\n=== Migration Complete ===")
    print(f"Successfully migrated: {len(migrated_files)}")
    print(f"Failed: {len(failed_files)}")
    print(f"Report: {report_path}")
    
    # Update runner examples
    print("\n=== Creating Example Runners ===")
    create_example_runners()
    
    print("\nMigration complete! Old agent files have been preserved.")
    print("You can now delete them manually if desired:")
    print("  - agents/research/research_agent.py")
    print("  - agents/research/enhanced_research_agent.py") 
    print("  - agents/research/research_agent_with_images.py")


def create_example_runners():
    """Create example runner scripts for the unified agent"""
    examples = [
        ("run_unified_basic.py", """#!/usr/bin/env python3
'''Example: Run unified agent with basic features'''
import asyncio
from agents.research.unified_research_agent import create_research_agent
from lib.supabase_client import get_supabase_client

async def main():
    supabase = get_supabase_client()
    
    # Create agent with basic features only
    agent = create_research_agent(supabase, features=['basic'])
    
    # Discover products
    products = await agent.discover_products("hemp construction materials", max_results=5)
    
    print(f"Found {len(products)} products")
    for product in products:
        print(f"- {product['name']}")

if __name__ == "__main__":
    asyncio.run(main())
"""),
        
        ("run_unified_full.py", """#!/usr/bin/env python3
'''Example: Run unified agent with all features'''
import asyncio
from agents.research.unified_research_agent import create_research_agent
from lib.supabase_client import get_supabase_client

async def main():
    supabase = get_supabase_client()
    
    # Create agent with all features
    agent = create_research_agent(
        supabase, 
        features=['basic', 'company', 'image', 'deep', 'web', 'feed', 'trend']
    )
    
    # Discover products with all features enabled
    products = await agent.discover_products("innovative hemp products", max_results=10)
    
    print(f"Found {len(products)} products with full analysis")
    for product in products:
        print(f"\\n- {product['name']}")
        if product.get('companies'):
            print(f"  Companies: {', '.join(product['companies'])}")
        if product.get('trend_analysis'):
            print(f"  Market potential: {product['trend_analysis'].get('market_growth_potential', 'N/A')}")

if __name__ == "__main__":
    asyncio.run(main())
"""),
    ]
    
    for filename, content in examples:
        filepath = os.path.join(os.path.dirname(__file__), filename)
        with open(filepath, 'w') as f:
            f.write(content)
        os.chmod(filepath, 0o755)
        print(f"Created example: {filename}")


if __name__ == "__main__":
    main()