@echo off
echo ========================================
echo Hemp Database Management Script
echo ========================================
echo.

REM Check if virtual environment exists
if exist .venv\Scripts\activate.bat (
    echo Activating virtual environment...
    call .venv\Scripts\activate.bat
) else (
    echo No virtual environment found. Using system Python.
)

echo.
echo Select an option:
echo 1. Add sample products to database
echo 2. Generate images for products without them
echo 3. Run research agent to find new products
echo 4. Check database statistics
echo 5. Install/Update dependencies
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo Running product population script...
    python populate_hemp_products_advanced.py
) else if "%choice%"=="2" (
    echo Generating images for products...
    python image_generation\hemp_image_generator.py --batch-size 10
) else if "%choice%"=="3" (
    echo Running research agent...
    python hemp_cli.py agent research "innovative hemp products" --features basic company
) else if "%choice%"=="4" (
    echo Checking database statistics...
    python hemp_cli.py db stats
) else if "%choice%"=="5" (
    echo Installing dependencies...
    pip install -r requirements.txt
) else (
    echo Invalid choice!
)

echo.
pause