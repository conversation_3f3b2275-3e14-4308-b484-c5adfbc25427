# HQz-Ai-DB-MCP-3

HempQuarterz Database - A comprehensive industrial hemp database system with AI-powered automation and monitoring.

## 🌿 Project Overview

This project implements a comprehensive database and automation platform for the industrial hemp industry, featuring:

### Core Database
- Hemp plant types and parts classification
- Products and applications catalog
- Companies and organizations directory
- Research papers and institutions
- Market data and analysis
- Regulatory information tracking
- Historical industry context

### AI Automation Platform (NEW!)
- **Unified CLI** - Single command interface for all operations
- **6 Specialized AI Agents** - Research, Content, SEO, Outreach, Monetization, Compliance
- **Automated Image Generation** - Multi-provider support with Edge Functions
- **Comprehensive Monitoring** - Real-time dashboards and alerts
- **GitHub Actions Integration** - Scheduled automation workflows

## 🎯 Recent Major Update: Platform Consolidation

We've successfully consolidated 32+ workflows into ~15 streamlined systems:
- **53% reduction** in system complexity
- **Unified interfaces** for all operations
- **Enhanced monitoring** and observability
- **Improved performance** and maintainability

See [CONSOLIDATION_SUMMARY.md](CONSOLIDATION_SUMMARY.md) for details.

## 📁 Project Structure

```
HQz-Ai-DB-MCP-3/
├── hemp_cli.py                        # Unified CLI for all operations
├── hemp                               # CLI wrapper (executable)
├── lib/                               # Core libraries
│   ├── image_generation_service.py    # Centralized image service
│   ├── monitoring_service.py          # Comprehensive monitoring
│   └── monitoring_dashboard.py        # Interactive terminal dashboard
├── agents/                            # AI Agents
│   ├── core/                          # Core agent infrastructure
│   ├── research/                      # Research agent (unified)
│   ├── content/                       # Content generation
│   ├── seo/                          # SEO optimization
│   ├── outreach/                     # Partnership outreach
│   ├── monetization/                 # Revenue opportunities
│   └── compliance/                   # Regulatory compliance
├── .github/workflows/                 # GitHub Actions
│   ├── automated-operations.yml       # All agent operations
│   └── monitoring-and-reporting.yml   # All monitoring tasks
├── supabase/functions/                # Edge Functions
│   └── hemp-image-generator/          # Serverless image generation
├── HempResourceHub/                   # Frontend application
│   ├── client/                        # React frontend
│   └── server/                        # Express backend
├── scripts/                           # Utility scripts
├── docs/                              # Documentation
└── schema.sql                         # Main database schema
```

## 🚀 Quick Start

### Prerequisites
- Python 3.10+
- Node.js 18+
- Supabase account
- GitHub account (for Actions)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3.git
   cd HQz-Ai-DB-MCP-3
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   cd HempResourceHub && npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your credentials:
   # - SUPABASE_URL
   # - SUPABASE_ANON_KEY
   # - OPENAI_API_KEY (for AI features)
   ```

4. **Initialize database**
   ```bash
   # Apply schema
   psql $DATABASE_URL < schema.sql
   
   # Populate initial data
   ./hemp db populate --type all
   ```

## 🎮 Using the Unified CLI

The `hemp` CLI provides access to all platform features:

### Agent Operations
```bash
# Run research agent
./hemp agent research "hemp construction materials" --features company image

# Run content generation
./hemp agent content "Create blog posts for new products"

# Run the orchestrator
./hemp agent orchestrator "Complete market analysis"
```

### Image Generation
```bash
# Generate images for products
./hemp images generate --provider stable-diffusion

# Check status
./hemp images status

# Retry failed generations
./hemp images retry --limit 50
```

### Database Operations
```bash
# Populate database
./hemp db populate --type all

# Export data
./hemp db export --format json --output ./backups

# Validate integrity
./hemp db validate

# Merge duplicate companies
./hemp db merge-companies --execute
```

### Monitoring
```bash
# Quick health check
./hemp monitor

# Interactive dashboard
./hemp monitor --live

# Generate report
./hemp monitor --format report

# Export metrics
./hemp monitor --format json --export metrics.json
```

## 📊 Monitoring Dashboard

Launch the real-time monitoring dashboard:
```bash
./hemp monitor --live
```

Features:
- System overview with health metrics
- Agent performance tracking
- Task queue monitoring
- Image generation analytics
- Active alerts display

Navigation:
- `←` `→` - Switch tabs
- `R` - Refresh
- `Q` - Quit

## 🤖 AI Agents

### Available Agents

1. **Research Agent** - Product discovery and market analysis
   - Features: basic, company, image, deep, web, feed, trend
   
2. **Content Agent** - Blog posts, descriptions, social media
   
3. **SEO Agent** - Keyword optimization, meta descriptions
   
4. **Outreach Agent** - Partnership opportunities, contact discovery
   
5. **Monetization Agent** - Revenue analysis, pricing strategies
   
6. **Compliance Agent** - Regulatory checking, policy updates

### Running Agents

```bash
# Individual agent
./hemp agent [type] "task description" [options]

# With orchestrator
./hemp agent orchestrator "complex multi-agent task"
```

## 🔄 GitHub Actions Automation

Two main workflows handle all automation:

### 1. Automated Operations
- Schedules: 6h, 12h, daily, 2-day intervals
- Runs agents based on priority
- Handles image generation
- Creates consolidated reports

### 2. Monitoring & Reporting
- Hourly health checks
- Daily summaries
- Weekly reports
- Automatic alert issues

Manual trigger:
```bash
gh workflow run automated-operations.yml
```

## 📚 Documentation

- [UNIFIED_CLI_GUIDE.md](UNIFIED_CLI_GUIDE.md) - Complete CLI reference
- [MONITORING_GUIDE.md](MONITORING_GUIDE.md) - Monitoring system guide
- [IMAGE_GENERATION_MIGRATION.md](IMAGE_GENERATION_MIGRATION.md) - Image service details
- [GITHUB_ACTIONS_MIGRATION.md](GITHUB_ACTIONS_MIGRATION.md) - Workflow documentation
- [AI_AGENT_BLUEPRINT.md](AI_AGENT_BLUEPRINT.md) - Agent architecture
- [CONSOLIDATION_SUMMARY.md](CONSOLIDATION_SUMMARY.md) - Recent consolidation details

## 🔧 Configuration

### Environment Variables
```bash
# Required
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
OPENAI_API_KEY=your-openai-key

# Optional
DATABASE_URL=postgresql://...
GITHUB_TOKEN=your-github-token
```

### Alert Configuration
Edit thresholds in `lib/monitoring_service.py`:
- Task failure rate threshold
- Agent inactivity period
- Queue backlog limits
- Image generation timeouts

## 🚨 Troubleshooting

### Common Issues

1. **CLI not found**
   ```bash
   chmod +x hemp hemp_cli.py
   ```

2. **Module import errors**
   ```bash
   pip install -r requirements.txt
   ```

3. **Database connection failed**
   - Check SUPABASE_URL and SUPABASE_ANON_KEY
   - Verify network connectivity

4. **Agent failures**
   - Check API keys (OpenAI, etc.)
   - Review agent logs: `./hemp monitor`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `./hemp db validate`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- Supabase for database infrastructure
- OpenAI for AI capabilities
- The hemp industry community

## 📞 Support

- Issues: [GitHub Issues](https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3/issues)
- Documentation: [Wiki](https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3/wiki)

---

Built with 💚 by HempQuarterz - Advancing the hemp industry through technology