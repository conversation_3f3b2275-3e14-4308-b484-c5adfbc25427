#!/usr/bin/env python3
"""
Add hemp products with separate company tracking
Allows multiple brands to offer the same type of product
"""

import os
import sys
from datetime import datetime
from supabase import create_client

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

# Generic products with multiple brand examples
HEMP_PRODUCTS_GENERIC = [
    # Hemp Seeds - Food Products
    {
        "name": "Hemp Hearts (Shelled Hemp Seeds)",
        "description": "Raw, hulled hemp seeds that are ready to eat. Rich in protein, omega fatty acids, and minerals. Versatile superfood for smoothies, yogurt, salads, and baking.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["10-15g plant protein per serving", "Perfect Omega-3 to Omega-6 ratio", "Rich in magnesium and iron", "Gluten-free and vegan"],
        "stage": "Established",
        "brands": ["Manitoba Harvest", "Navitas Organics", "Bob's Red Mill", "365 Whole Foods", "Nutiva"]
    },
    {
        "name": "Cold-Pressed Hemp Seed Oil",
        "description": "Unrefined oil extracted from hemp seeds using cold-press methods. Nutty flavor ideal for dressings, smoothies, and low-heat cooking. Rich in essential fatty acids.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["High in Omega-3 ALA", "Supports heart health", "Anti-inflammatory properties", "Versatile culinary oil"],
        "stage": "Established",
        "brands": ["Nutiva", "Manitoba Harvest", "Good Hemp", "Fresh Hemp Foods", "Hemp Foods Australia"]
    },
    {
        "name": "Hemp Protein Powder",
        "description": "Plant-based protein powder made from ground hemp seeds after oil extraction. Contains all essential amino acids and is easily digestible.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["50-70% protein content", "Complete amino acid profile", "High in fiber", "No artificial additives"],
        "stage": "Growing",
        "brands": ["Victory Hemp Foods", "Manitoba Harvest", "Sunwarrior", "Garden of Life", "NOW Sports"]
    },
    {
        "name": "Hemp Milk",
        "description": "Plant-based milk alternative made from blended hemp seeds and water. Creamy texture with nutty flavor, fortified with vitamins and minerals.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["Dairy-free alternative", "Naturally contains omega-3s", "No common allergens", "Sustainable crop source"],
        "stage": "Growing",
        "brands": ["Good Hemp", "Pacific Foods", "Tempt", "Living Harvest", "Ecomil"]
    },
    {
        "name": "Hemp Seed Butter",
        "description": "Creamy spread made from ground hemp seeds, similar to peanut or almond butter. Rich, nutty flavor with smooth texture.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["High protein spread", "Nut-free alternative", "Source of minerals", "No added sugars option"],
        "stage": "Niche",
        "brands": ["Dastony", "Hemp Hearts", "Rejuvenative Foods", "Artisana Organics"]
    },
    
    # Hemp Fiber - Textile Products
    {
        "name": "Hemp T-Shirt",
        "description": "Basic t-shirt made from hemp fiber or hemp-cotton blend. Naturally antimicrobial, breathable, and becomes softer with each wash.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["Antimicrobial properties", "UV resistant", "Durable and long-lasting", "Temperature regulating"],
        "stage": "Growing",
        "brands": ["Jungmaven", "Patagonia", "prAna", "tentree", "WAMA Underwear"]
    },
    {
        "name": "Hemp Denim Jeans",
        "description": "Durable denim jeans made with hemp fiber blend. Stronger than cotton denim with natural stretch and breathability.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["3x stronger than cotton", "Natural stretch", "Fade resistant", "Sustainable production"],
        "stage": "Growing",
        "brands": ["Levi's Wellthread", "Outerknown", "Nudie Jeans", "Citizen Wolf", "Hemp Tailor"]
    },
    {
        "name": "Hemp Canvas Tote Bag",
        "description": "Heavy-duty tote bag made from hemp canvas fabric. Extremely durable and suitable for shopping, travel, or everyday use.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["Exceptional durability", "Water resistant", "Biodegradable", "Machine washable"],
        "stage": "Established",
        "brands": ["EcoBags", "ChicoBag", "Rawganique", "Hemp Basics", "Dime Bags"]
    },
    {
        "name": "Hemp Rope",
        "description": "Strong natural rope made from twisted hemp fibers. Used for marine applications, climbing, decoration, and general purpose.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["Superior tensile strength", "Salt water resistant", "Natural and biodegradable", "UV resistant"],
        "stage": "Established",
        "brands": ["Ravenox", "SGT KNOTS", "Hemp Traders", "Nutscene", "Vivifying"]
    },
    {
        "name": "Hemp Fabric by the Yard",
        "description": "Raw hemp fabric sold for sewing and manufacturing. Available in various weights from lightweight muslin to heavy canvas.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["Versatile weights available", "Natural or dyed options", "Breathable fabric", "Gets softer over time"],
        "stage": "Growing",
        "brands": ["Hemp Traders", "Hemp Fortex", "Rawganique", "Organic Cotton Plus", "Hemp Basics"]
    },
    
    # Hemp Hurds - Construction Products
    {
        "name": "Hempcrete Building Blocks",
        "description": "Pre-cast blocks made from hemp hurds mixed with lime binder. Used for wall construction in sustainable building projects.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["Carbon negative material", "Excellent insulation", "Fire and pest resistant", "Regulates humidity"],
        "stage": "Growing",
        "brands": ["Just BioFiber", "IsoHemp", "Hemp Block USA", "American Hemp LLC", "Sunstrand"]
    },
    {
        "name": "Hemp Fiber Insulation Batts",
        "description": "Thermal and acoustic insulation made from hemp fibers. Safe to handle, non-toxic, and provides excellent R-value performance.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["No protective gear needed", "Mold and pest resistant", "Sound absorption", "Carbon sequestering"],
        "stage": "Growing",
        "brands": ["Hempitecture", "NatureFibres", "Ekolution", "CAVAC Biomatériaux", "Thermo-Hanf"]
    },
    {
        "name": "Hemp Particle Board",
        "description": "Engineered wood product made from compressed hemp hurds. Alternative to traditional particle board without formaldehyde.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["Formaldehyde-free", "Lightweight yet strong", "Good screw retention", "Fire resistant"],
        "stage": "Pilot",
        "brands": ["HempWood", "Canobiote", "Zellform", "Hemp Technologies", "BioComposites Group"]
    },
    {
        "name": "Hemp-Lime Plaster",
        "description": "Natural plaster system combining hemp shiv with lime for interior and exterior wall finishes. Breathable and mold resistant.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["Humidity regulation", "Natural antimicrobial", "Crack resistant", "Historical building compatible"],
        "stage": "Niche",
        "brands": ["Lhoist", "BCB Tradical", "Hemp-LimeConstruct", "Schönthaler", "HempStone"]
    },
    {
        "name": "Hemp Biocomposite Panels",
        "description": "Structural panels made from hemp fibers and bio-based resins. Used for walls, floors, and furniture manufacturing.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["High strength-to-weight ratio", "Renewable materials", "Custom densities available", "CNC machinable"],
        "stage": "Pilot",
        "brands": ["Lingrove", "Hemp Inc", "Hempearth", "CannaGreen", "Technological Abrasion"]
    },
    
    # Hemp Flower/Leaf Products
    {
        "name": "Hemp Tea",
        "description": "Herbal tea made from dried hemp leaves and flowers. Caffeine-free with naturally occurring compounds for relaxation.",
        "plant_part": "Hemp Leaves",
        "industry": "Food and Beverage",
        "benefits": ["Caffeine-free", "Calming properties", "Rich in antioxidants", "Organic options available"],
        "stage": "Growing",
        "brands": ["Buddha Teas", "The Tea Spot", "Hemp Tea Company", "Traditional Medicinals", "Mary's Whole Pet"]
    },
    {
        "name": "Hemp Essential Oil",
        "description": "Steam-distilled essential oil from hemp flowers and leaves. Used in aromatherapy and natural cosmetic formulations.",
        "plant_part": "Hemp Flowers",
        "industry": "Cosmetics",
        "benefits": ["Natural terpene profile", "Aromatherapy uses", "Skin conditioning", "Stress relief properties"],
        "stage": "Niche",
        "brands": ["Edens Garden", "Plant Therapy", "Hemp Garden", "Mountain Rose Herbs", "Floracopeia"]
    },
    {
        "name": "Hemp Flower Extract",
        "description": "Concentrated extract from hemp flowers containing beneficial compounds. Used in wellness products and supplements.",
        "plant_part": "Hemp Flowers",
        "industry": "Medicine",
        "benefits": ["Full spectrum compounds", "Entourage effect", "Lab tested purity", "Various potencies available"],
        "stage": "Growing",
        "brands": ["Charlotte's Web", "Lazarus Naturals", "NuLeaf Naturals", "Joy Organics", "CBDistillery"]
    }
]

def get_or_create_company(supabase, company_name):
    """Get existing company or create new one"""
    # Check if company exists
    result = supabase.table('hemp_companies').select('id').eq('name', company_name).execute()
    
    if result.data:
        return result.data[0]['id']
    
    # Create new company
    new_company = {
        'name': company_name,
        'description': f'{company_name} is a hemp product manufacturer and distributor.',
        'website': f'www.{company_name.lower().replace(" ", "").replace("&", "and")}.com',
        'verified': True
    }
    
    result = supabase.table('hemp_companies').insert(new_company).execute()
    if result.data:
        return result.data[0]['id']
    return None

def get_plant_part_id(supabase, plant_part_name):
    """Get plant part ID from name"""
    result = supabase.table('plant_parts').select('id').eq('name', plant_part_name).execute()
    if result.data:
        return result.data[0]['id']
    return None

def get_industry_subcategory_id(supabase, industry_name):
    """Get industry subcategory ID"""
    # First try to get the industry
    industry_result = supabase.table('industries').select('id').eq('name', industry_name).execute()
    if industry_result.data:
        industry_id = industry_result.data[0]['id']
        # Get first subcategory for this industry
        subcat_result = supabase.table('industry_sub_categories')\
            .select('id')\
            .eq('industry_id', industry_id)\
            .limit(1)\
            .execute()
        if subcat_result.data:
            return subcat_result.data[0]['id']
    return None

def add_product_company_relationship(supabase, product_id, company_id):
    """Add relationship between product and company"""
    # Check if relationship exists
    existing = supabase.table('hemp_company_products')\
        .select('id')\
        .eq('product_id', product_id)\
        .eq('company_id', company_id)\
        .execute()
    
    if not existing.data:
        # Create relationship
        relationship = {
            'product_id': product_id,
            'company_id': company_id,
            'is_primary': True,
            'verified': True
        }
        supabase.table('hemp_company_products').insert(relationship).execute()

def add_generic_products():
    """Add generic products with multiple brand associations"""
    # Initialize Supabase
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials!")
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("🌿 Adding Generic Hemp Products with Multiple Brands")
    print("=" * 50)
    
    products_added = 0
    products_skipped = 0
    companies_added = 0
    relationships_added = 0
    
    for product in HEMP_PRODUCTS_GENERIC:
        try:
            # Check if product exists
            existing = supabase.table('uses_products')\
                .select('id')\
                .eq('name', product['name'])\
                .execute()
            
            if existing.data:
                product_id = existing.data[0]['id']
                print(f"⏭️  Product exists: {product['name']}")
                products_skipped += 1
            else:
                # Get IDs
                plant_part_id = get_plant_part_id(supabase, product['plant_part'])
                industry_id = get_industry_subcategory_id(supabase, product['industry'])
                
                if not plant_part_id:
                    print(f"❌ Unknown plant part: {product['plant_part']}")
                    continue
                
                # Create product
                db_product = {
                    'name': product['name'],
                    'description': product['description'],
                    'plant_part_id': plant_part_id,
                    'industry_sub_category_id': industry_id,
                    'benefits_advantages': product['benefits'],
                    'commercialization_stage': product['stage'],
                    'keywords': [
                        'hemp',
                        product['plant_part'].lower().replace(' ', '-'),
                        product['industry'].lower().replace(' ', '-'),
                        'sustainable',
                        'eco-friendly',
                        'commercial',
                        'retail'
                    ],
                    'sustainability_aspects': [
                        'Renewable agricultural resource',
                        'Minimal water requirements',
                        'No pesticides needed',
                        'Carbon negative crop',
                        'Biodegradable end product'
                    ]
                }
                
                result = supabase.table('uses_products').insert(db_product).execute()
                if result.data:
                    product_id = result.data[0]['id']
                    print(f"✅ Added product: {product['name']}")
                    products_added += 1
                else:
                    continue
            
            # Add brand relationships
            print(f"   Adding brands for {product['name']}:")
            for brand in product['brands']:
                company_id = get_or_create_company(supabase, brand)
                if company_id:
                    add_product_company_relationship(supabase, product_id, company_id)
                    print(f"     + {brand}")
                    relationships_added += 1
                    
        except Exception as e:
            print(f"❌ Error with {product['name']}: {str(e)}")
    
    # Count unique companies
    companies_result = supabase.table('hemp_companies').select('count', count='exact').execute()
    
    print(f"\n📊 Summary:")
    print(f"   - New products added: {products_added}")
    print(f"   - Existing products: {products_skipped}")
    print(f"   - Total companies: {companies_result.count}")
    print(f"   - Brand relationships: {relationships_added}")
    
    # Show current total
    total = supabase.table('uses_products').select('count', count='exact').execute()
    print(f"   - Total products in database: {total.count}")
    
    # Show some sample products with their brands
    print(f"\n📦 Sample Products with Multiple Brands:")
    sample_products = supabase.table('uses_products')\
        .select('id, name')\
        .limit(5)\
        .execute()
    
    for product in sample_products.data:
        # Get companies for this product
        companies = supabase.table('hemp_company_products')\
            .select('hemp_companies(name)')\
            .eq('product_id', product['id'])\
            .execute()
        
        if companies.data:
            brand_names = [c['hemp_companies']['name'] for c in companies.data if c.get('hemp_companies')]
            if brand_names:
                print(f"   - {product['name']}")
                print(f"     Brands: {', '.join(brand_names)}")

if __name__ == "__main__":
    add_generic_products()