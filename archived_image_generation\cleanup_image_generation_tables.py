#!/usr/bin/env python3
"""
Cleanup script for image generation table overpopulation
Removes duplicate entries and prevents future duplicates
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv
from datetime import datetime
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Supabase client
supabase_url = os.getenv('SUPABASE_URL')
supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY') or os.getenv('SUPABASE_ANON_KEY')
supabase: Client = create_client(supabase_url, supabase_key)

def analyze_duplicates():
    """Analyze duplicate entries in the queue"""
    logger.info("Analyzing duplicate entries...")
    
    # Get all queue entries
    response = supabase.table('image_generation_queue').select('product_id, status, created_at').execute()
    all_entries = response.data if response.data else []
    
    # Group by product_id and count
    product_stats = {}
    for entry in all_entries:
        product_id = entry['product_id']
        if product_id not in product_stats:
            product_stats[product_id] = {
                'product_id': product_id,
                'duplicate_count': 0,
                'completed_count': 0,
                'pending_count': 0,
                'failed_count': 0,
                'processing_count': 0,
                'first_created': entry['created_at'],
                'last_created': entry['created_at']
            }
        
        stats = product_stats[product_id]
        stats['duplicate_count'] += 1
        
        # Count by status
        if entry['status'] == 'completed':
            stats['completed_count'] += 1
        elif entry['status'] == 'pending':
            stats['pending_count'] += 1
        elif entry['status'] == 'failed':
            stats['failed_count'] += 1
        elif entry['status'] == 'processing':
            stats['processing_count'] += 1
        
        # Update dates
        if entry['created_at'] < stats['first_created']:
            stats['first_created'] = entry['created_at']
        if entry['created_at'] > stats['last_created']:
            stats['last_created'] = entry['created_at']
    
    # Filter to only duplicates and sort
    duplicates = [stats for stats in product_stats.values() if stats['duplicate_count'] > 1]
    duplicates.sort(key=lambda x: x['duplicate_count'], reverse=True)
    
    logger.info(f"Found {len(duplicates)} products with duplicate entries")
    
    total_duplicates = sum(d['duplicate_count'] - 1 for d in duplicates)
    logger.info(f"Total duplicate entries to remove: {total_duplicates}")
    
    # Show top duplicates
    if duplicates:
        logger.info("\nTop 5 products with most duplicates:")
        for i, dup in enumerate(duplicates[:5]):
            logger.info(f"  Product {dup['product_id']}: {dup['duplicate_count']} entries "
                       f"(completed: {dup['completed_count']}, pending: {dup['pending_count']}, "
                       f"failed: {dup['failed_count']})")
    
    return duplicates

def cleanup_duplicates(dry_run=True):
    """Remove duplicate queue entries, keeping only the most recent completed one"""
    logger.info(f"Starting cleanup (dry_run={dry_run})...")
    
    # Get all queue entries grouped by product
    response = supabase.table('image_generation_queue').select('*').order('created_at', desc=True).execute()
    all_entries = response.data if response.data else []
    
    # Group by product_id
    products = {}
    for entry in all_entries:
        product_id = entry['product_id']
        if product_id not in products:
            products[product_id] = []
        products[product_id].append(entry)
    
    entries_to_delete = []
    stats = {
        'products_processed': 0,
        'entries_kept': 0,
        'entries_to_delete': 0,
        'completed_kept': 0,
        'pending_kept': 0
    }
    
    for product_id, entries in products.items():
        if len(entries) <= 1:
            stats['entries_kept'] += len(entries)
            continue
            
        stats['products_processed'] += 1
        
        # Sort by status priority: completed > pending > failed > processing
        def status_priority(entry):
            priorities = {'completed': 0, 'pending': 1, 'processing': 2, 'failed': 3}
            return priorities.get(entry['status'], 99)
        
        entries.sort(key=lambda e: (status_priority(e), e['created_at']), reverse=False)
        
        # Keep the best entry (first in sorted list)
        keep_entry = entries[0]
        stats['entries_kept'] += 1
        if keep_entry['status'] == 'completed':
            stats['completed_kept'] += 1
        elif keep_entry['status'] == 'pending':
            stats['pending_kept'] += 1
        
        # Mark others for deletion
        for entry in entries[1:]:
            entries_to_delete.append(entry['id'])
            stats['entries_to_delete'] += 1
    
    logger.info(f"Cleanup statistics:")
    logger.info(f"  - Products with duplicates: {stats['products_processed']}")
    logger.info(f"  - Entries to keep: {stats['entries_kept']}")
    logger.info(f"  - Entries to delete: {stats['entries_to_delete']}")
    logger.info(f"  - Completed entries kept: {stats['completed_kept']}")
    logger.info(f"  - Pending entries kept: {stats['pending_kept']}")
    
    if not dry_run and entries_to_delete:
        # Delete in batches
        batch_size = 100
        for i in range(0, len(entries_to_delete), batch_size):
            batch = entries_to_delete[i:i+batch_size]
            try:
                supabase.table('image_generation_queue').delete().in_('id', batch).execute()
                logger.info(f"Deleted batch {i//batch_size + 1} ({len(batch)} entries)")
            except Exception as e:
                logger.error(f"Error deleting batch: {e}")
    
    return stats

def fix_products_with_placeholder_images():
    """Ensure products with placeholder images aren't re-queued"""
    logger.info("Checking products with placeholder images...")
    
    # Get products with placeholder URLs
    response = supabase.table('uses_products').select('id, name, image_url').like('image_url', '%placeholder.com%').execute()
    products = response.data if response.data else []
    
    logger.info(f"Found {len(products)} products with placeholder images")
    
    # Check if they're in the queue
    for product in products:
        queue_response = supabase.table('image_generation_queue').select('id, status').eq('product_id', product['id']).execute()
        queue_entries = queue_response.data if queue_response.data else []
        
        if queue_entries:
            logger.warning(f"Product {product['id']} ({product['name']}) has placeholder but is still in queue: {len(queue_entries)} entries")

def add_unique_constraint(dry_run=True):
    """Add unique constraint to prevent future duplicates"""
    logger.info("Adding unique constraint recommendation...")
    
    constraint_sql = """
    -- Add unique constraint on (product_id, status) where status != 'completed'
    -- This allows multiple completed entries but only one pending/processing per product
    CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_product_active_status 
    ON image_generation_queue(product_id, status) 
    WHERE status IN ('pending', 'processing');
    
    -- Add trigger to prevent re-queuing products with images
    CREATE OR REPLACE FUNCTION prevent_requeue_with_image()
    RETURNS TRIGGER AS $$
    BEGIN
        -- Check if product already has an image
        IF EXISTS (
            SELECT 1 FROM uses_products 
            WHERE id = NEW.product_id 
            AND image_url IS NOT NULL 
            AND image_url != ''
        ) THEN
            -- Check if it's not a retry for a failed generation
            IF NEW.status = 'pending' AND NOT EXISTS (
                SELECT 1 FROM image_generation_queue 
                WHERE product_id = NEW.product_id 
                AND status = 'failed'
                AND created_at > NOW() - INTERVAL '1 hour'
            ) THEN
                RAISE EXCEPTION 'Product % already has an image', NEW.product_id;
            END IF;
        END IF;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    
    CREATE TRIGGER check_before_queue
    BEFORE INSERT ON image_generation_queue
    FOR EACH ROW
    EXECUTE FUNCTION prevent_requeue_with_image();
    """
    
    if not dry_run:
        logger.info("To apply these constraints, run the following SQL in Supabase:")
        print(constraint_sql)
    else:
        logger.info("Database constraints to prevent future duplicates (not applied in dry run)")

def main():
    """Main cleanup process"""
    logger.info("=== Image Generation Cleanup Script ===")
    
    # Analyze current state
    analyze_duplicates()
    
    # Fix products with placeholders
    fix_products_with_placeholder_images()
    
    # Cleanup duplicates (dry run first)
    logger.info("\n--- DRY RUN ---")
    dry_run_stats = cleanup_duplicates(dry_run=True)
    
    # Ask for confirmation
    if dry_run_stats['entries_to_delete'] > 0:
        response = input(f"\nProceed with deleting {dry_run_stats['entries_to_delete']} duplicate entries? (yes/no): ")
        if response.lower() == 'yes':
            logger.info("\n--- ACTUAL RUN ---")
            cleanup_duplicates(dry_run=False)
            logger.info("Cleanup completed!")
        else:
            logger.info("Cleanup cancelled")
    else:
        logger.info("No duplicates to clean up!")
    
    # Show constraint recommendations
    add_unique_constraint(dry_run=True)
    
    logger.info("\n=== Cleanup Complete ===")

if __name__ == "__main__":
    main()