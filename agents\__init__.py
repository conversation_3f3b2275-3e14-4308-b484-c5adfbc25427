"""
HempQuarterz AI Agents
Complete autonomous agent system for hemp business automation
"""

from .core.base_agent import BaseAgent, rate_limited, track_performance

# Import orchestrator only if available
try:
    from .core.orchestrator import Orchestrator
except ImportError:
    Orchestrator = None

# Import other agents
try:
    from .research.research_agent import ResearchAgent
except ImportError:
    ResearchAgent = None
    
try:
    from .content.content_agent import ContentAgent
except ImportError:
    ContentAgent = None
    
try:
    from .seo.seo_agent import SEOAgent
except ImportError:
    SEOAgent = None
    
try:
    from .outreach.outreach_agent import OutreachAgent
except ImportError:
    OutreachAgent = None
    
try:
    from .monetization.monetization_agent import MonetizationAgent
except ImportError:
    MonetizationAgent = None
    
try:
    from .compliance.compliance_agent import ComplianceAgent
except ImportError:
    ComplianceAgent = None

__version__ = "1.0.0"

__all__ = [
    "BaseAgent",
    "Orchestrator",
    "ResearchAgent", 
    "ContentAgent",
    "SEOAgent",
    "OutreachAgent",
    "MonetizationAgent",
    "ComplianceAgent",
    "rate_limited",
    "track_performance"
]

# Agent registry for dynamic loading
AGENT_REGISTRY = {}

# Only add available agents to registry
if Orchestrator:
    AGENT_REGISTRY["orchestrator"] = Orchestrator
if ResearchAgent:
    AGENT_REGISTRY["research"] = ResearchAgent
if ContentAgent:
    AGENT_REGISTRY["content"] = ContentAgent
if SEOAgent:
    AGENT_REGISTRY["seo"] = SEOAgent
if OutreachAgent:
    AGENT_REGISTRY["outreach"] = OutreachAgent
if MonetizationAgent:
    AGENT_REGISTRY["monetization"] = MonetizationAgent
if ComplianceAgent:
    AGENT_REGISTRY["compliance"] = ComplianceAgent

def get_agent(agent_name: str, config: dict):
    """
    Factory function to get an agent instance by name
    
    Args:
        agent_name: Name of the agent to instantiate
        config: Configuration dictionary for the agent
        
    Returns:
        Agent instance
        
    Raises:
        ValueError: If agent_name is not found in registry
    """
    agent_class = AGENT_REGISTRY.get(agent_name.lower())
    if not agent_class:
        raise ValueError(f"Unknown agent: {agent_name}. Available agents: {list(AGENT_REGISTRY.keys())}")
    
    return agent_class(config)