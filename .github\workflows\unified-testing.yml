name: Unified Testing & Diagnostics

on:
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of test to run'
        required: true
        type: choice
        options:
          - quick-check      # Basic connectivity test
          - diagnostic       # Full diagnostic check
          - environment      # Environment and imports
          - database         # Database operations
          - agent-dry-run    # Test agent functionality
          - comprehensive    # Run all tests
        default: 'quick-check'
      
      debug_mode:
        description: 'Enable debug output'
        required: false
        default: false
        type: boolean
        
  # Optional: Run basic tests on pull requests
  pull_request:
    branches: [ main ]
    paths:
      - '**.py'
      - 'requirements.txt'
      - '.github/workflows/*.yml'

permissions:
  contents: read
  actions: write
  pull-requests: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PYTHONPATH: ${{ github.workspace }}
  MOCK_MODE: true
  TEST_MODE: true

jobs:
  unified-test:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
        cache-dependency-path: |
          requirements.txt
          **/requirements*.txt
        
    # Quick Check - Always runs
    - name: Quick Environment Check
      if: always()
      run: |
        echo "🚀 Quick Environment Check"
        echo "========================="
        echo "Timestamp: $(date)"
        echo "Python: $(python --version)"
        echo "Runner OS: ${{ runner.os }}"
        echo "Workflow: ${{ github.workflow }}"
        echo "Test Type: ${{ github.event.inputs.test_type || 'automated' }}"
        echo ""
        echo "Secret Status:"
        echo "- SUPABASE_URL: ${{ secrets.SUPABASE_URL && '✅ Set' || '❌ Not set' }}"
        echo "- SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY && '✅ Set' || '❌ Not set' }}"
        echo "- OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY && '✅ Set' || '⚠️ Not set (optional)' }}"
        
    # Diagnostic Check
    - name: Diagnostic Check
      if: github.event.inputs.test_type == 'diagnostic' || github.event.inputs.test_type == 'comprehensive'
      run: |
        echo "🔍 Running Diagnostic Check"
        echo "=========================="
        
        # Check directory structure
        echo ""
        echo "📁 Directory Structure:"
        for dir in lib agents data logs HempResourceHub; do
          if [ -d "$dir" ]; then
            echo "✅ $dir/ exists ($(find $dir -type f -name "*.py" | wc -l) Python files)"
          else
            echo "❌ $dir/ not found"
          fi
        done
        
        # Check key files
        echo ""
        echo "📄 Key Files:"
        for file in requirements.txt .env README.md hemp setup.py; do
          [ -f "$file" ] && echo "✅ $file" || echo "❌ $file not found"
        done
        
        # Check Git status
        echo ""
        echo "📊 Git Status:"
        echo "Branch: $(git branch --show-current)"
        echo "Uncommitted files: $(git status --porcelain | wc -l)"
        
        # Create diagnostic report
        cat > diagnostic-report.json << EOF
        {
          "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
          "python_version": "$(python --version 2>&1)",
          "directories": {
            "lib": $([ -d lib ] && echo true || echo false),
            "agents": $([ -d agents ] && echo true || echo false),
            "data": $([ -d data ] && echo true || echo false)
          },
          "files": {
            "requirements": $([ -f requirements.txt ] && echo true || echo false),
            "env": $([ -f .env ] && echo true || echo false)
          },
          "secrets": {
            "supabase_url": ${{ secrets.SUPABASE_URL && 'true' || 'false' }},
            "supabase_key": ${{ secrets.SUPABASE_ANON_KEY && 'true' || 'false' }}
          }
        }
        EOF
        
    # Environment Test
    - name: Environment & Import Test
      if: github.event.inputs.test_type == 'environment' || github.event.inputs.test_type == 'comprehensive'
      run: |
        echo "📦 Testing Environment & Imports"
        echo "==============================="
        
        # Create test structure if missing
        mkdir -p lib agents
        touch lib/__init__.py agents/__init__.py
        
        # Install minimal requirements
        if [ -f requirements.txt ]; then
          echo "Installing requirements..."
          pip install -r requirements.txt 2>&1 | tail -20
        else
          echo "Installing minimal dependencies..."
          pip install supabase requests python-dotenv
        fi
        
        # Test imports
        python .github/scripts/test_environment.py
        
    # Database Test
    - name: Database Connection Test
      if: github.event.inputs.test_type == 'database' || github.event.inputs.test_type == 'comprehensive'
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
      run: |
        echo "🗄️ Testing Database Operations"
        echo "============================="
        
        python .github/scripts/test_database_operations.py
        
    # Agent Dry Run Test
    - name: Agent Functionality Test
      if: github.event.inputs.test_type == 'agent-dry-run' || github.event.inputs.test_type == 'comprehensive'
      run: |
        echo "🤖 Testing Agent Functionality"
        echo "============================"
        
        # Test hemp CLI
        if [ -f "hemp" ]; then
          chmod +x hemp
          echo "Testing hemp CLI..."
          python hemp --help || echo "⚠️ Hemp CLI not functional"
        else
          echo "⚠️ Hemp CLI not found"
        fi
        
        # Test agent structure
        python .github/scripts/test_agent_functionality.py
        
    # Generate Summary Report
    - name: Generate Test Summary
      if: always()
      run: |
        cat > test-summary.md << 'EOF'
# Test Summary Report

**Date**: $(date)
**Test Type**: ${{ github.event.inputs.test_type || 'automated' }}
**Triggered By**: ${{ github.event_name }}

## Results Overview

### Environment
- **Python**: $(python --version 2>&1)
- **OS**: ${{ runner.os }}
- **Status**: ${{ job.status }}

### Configuration
- **SUPABASE_URL**: ${{ secrets.SUPABASE_URL && '✅ Configured' || '❌ Missing' }}
- **SUPABASE_ANON_KEY**: ${{ secrets.SUPABASE_ANON_KEY && '✅ Configured' || '❌ Missing' }}
- **OPENAI_API_KEY**: ${{ secrets.OPENAI_API_KEY && '✅ Configured' || '⚠️ Not set (optional)' }}

### Test Results
EOF

        if [ -f diagnostic-report.json ]; then
          echo "- ✅ Diagnostic check completed" >> test-summary.md
        fi
        
        echo "" >> test-summary.md
        echo "## Recommendations" >> test-summary.md
        
        if [ -z "${{ secrets.SUPABASE_URL }}" ]; then
          echo "1. Set SUPABASE_URL in GitHub Secrets" >> test-summary.md
        fi
        
        if [ -z "${{ secrets.SUPABASE_ANON_KEY }}" ]; then
          echo "2. Set SUPABASE_ANON_KEY in GitHub Secrets" >> test-summary.md
        fi
        
        echo "" >> test-summary.md
        echo "---" >> test-summary.md
        echo "*Generated by Unified Testing workflow*" >> test-summary.md
        
    # Upload artifacts
    - name: Upload Test Results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: test-results-${{ github.event.inputs.test_type || 'auto' }}-${{ github.run_number }}
        path: |
          test-summary.md
          diagnostic-report.json
        retention-days: 7
        if-no-files-found: warn
        
    # GitHub Summary
    - name: Create GitHub Summary
      if: always()
      run: |
        echo "# 🧪 Test Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Test Type**: ${{ github.event.inputs.test_type || 'automated' }}" >> $GITHUB_STEP_SUMMARY
        echo "**Status**: ${{ job.status == 'success' && '✅ Passed' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        echo "## Quick Status" >> $GITHUB_STEP_SUMMARY
        echo "| Component | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Python Environment | ✅ |" >> $GITHUB_STEP_SUMMARY
        echo "| Directory Structure | $([ -d lib ] && echo '✅' || echo '❌') |" >> $GITHUB_STEP_SUMMARY
        echo "| Supabase Credentials | ${{ secrets.SUPABASE_URL && secrets.SUPABASE_ANON_KEY && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Import Tests | $([ -f lib/__init__.py ] && echo '✅' || echo '⚠️') |" >> $GITHUB_STEP_SUMMARY
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## Next Steps" >> $GITHUB_STEP_SUMMARY
        
        if [ -z "${{ secrets.SUPABASE_URL }}" ] || [ -z "${{ secrets.SUPABASE_ANON_KEY }}" ]; then
          echo "- 🔐 Configure missing GitHub Secrets" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "- 📊 Review test artifacts for detailed results" >> $GITHUB_STEP_SUMMARY
        echo "- 🚀 Run hourly discovery workflow once tests pass" >> $GITHUB_STEP_SUMMARY