#!/usr/bin/env python3
"""
Test DeepSeek with actual product creation using service role
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Use service role for database operations
from supabase import create_client, Client
supabase_url = os.getenv("SUPABASE_URL")
service_role_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
supabase: Client = create_client(supabase_url, service_role_key)

from utils.ai_providers import DeepSeekProvider


async def test_deepseek_product_creation():
    """Test creating products with DeepSeek AI"""
    logger.info("Testing DeepSeek for product creation...")
    
    # Create DeepSeek provider
    deepseek = DeepSeekProvider()
    
    # Generate products with DeepSeek
    prompt = """Create a JSON array of 5 innovative hemp construction products. For each product include:
    - name: product name (short, specific)
    - description: 2-3 sentence description
    - plant_part: one of [fiber, seeds, oil, hurds, flower]
    - benefits: array of 3 key benefits
    
    Format as valid JSON array."""
    
    try:
        logger.info("Generating products with DeepSeek...")
        response = await deepseek.generate(prompt, temperature=0.7)
        logger.info(f"DeepSeek response received, parsing...")
        
        # Extract JSON from response
        import json
        import re
        
        # Find JSON array in response
        json_match = re.search(r'\[[\s\S]*\]', response)
        if json_match:
            products_data = json.loads(json_match.group())
            logger.info(f"Parsed {len(products_data)} products from DeepSeek")
        else:
            logger.error("No JSON array found in response")
            return
            
    except Exception as e:
        logger.error(f"DeepSeek generation failed: {e}")
        return
    
    # Map plant parts to database IDs
    plant_part_map = {
        'fiber': 2,  # Hemp Bast (Fiber)
        'seeds': 7,  # Hemp Seed
        'oil': 1,    # Cannabinoids (closest)
        'hurds': 4,  # Hemp Hurd (Shivs)
        'flower': 3  # Hemp Flowers
    }
    
    # Get first industry sub-category
    sub_cat = supabase.table('industry_sub_categories').select('id').limit(1).execute()
    sub_cat_id = sub_cat.data[0]['id'] if sub_cat.data else 1
    
    # Save products to database
    saved_count = 0
    for product in products_data:
        try:
            # Map plant part
            plant_part = product.get('plant_part', 'fiber').lower()
            plant_part_id = plant_part_map.get(plant_part, 2)
            
            # Create product record
            product_record = {
                'name': product['name'],
                'description': product.get('description', ''),
                'plant_part_id': plant_part_id,
                'industry_sub_category_id': sub_cat_id,
                'benefits_advantages': product.get('benefits', []),
                'commercialization_stage': 'Research',
                'data_sources': ['DeepSeek AI Generated'],
                'image_url': f"https://placeholder.com/{product['name'].lower().replace(' ', '-')}.jpg"
            }
            
            result = supabase.table('uses_products').insert(product_record).execute()
            if result.data:
                saved_count += 1
                logger.info(f"✅ Saved: {product['name']} (ID: {result.data[0]['id']})")
            
        except Exception as e:
            logger.error(f"Failed to save product: {e}")
    
    logger.info(f"\n🎉 Successfully saved {saved_count} products using DeepSeek!")
    
    # Show total products
    total = supabase.table('uses_products').select('count', count='exact').execute()
    logger.info(f"Total products in database: {total.count}")


async def test_deepseek_search():
    """Test using DeepSeek to search and analyze products"""
    logger.info("\n📊 Testing DeepSeek for product analysis...")
    
    # Get some existing products
    products = supabase.table('uses_products').select('*').order('created_at', desc=True).limit(5).execute()
    
    if products.data:
        deepseek = DeepSeekProvider()
        
        # Analyze products with DeepSeek
        product_names = [p['name'] for p in products.data]
        prompt = f"""Analyze these hemp products and suggest 3 related innovative products:
        
        Existing products:
        {', '.join(product_names)}
        
        Suggest 3 new innovative hemp products that would complement these. Format as a simple numbered list."""
        
        try:
            suggestions = await deepseek.generate(prompt)
            logger.info(f"\nDeepSeek Product Suggestions:\n{suggestions}")
        except Exception as e:
            logger.error(f"Analysis failed: {e}")


async def main():
    """Run all tests"""
    await test_deepseek_product_creation()
    await test_deepseek_search()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()