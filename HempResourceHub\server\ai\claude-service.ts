import Anthropic from '@anthropic-ai/sdk';
import { v4 as uuidv4 } from 'uuid';
import { retryWithBackoff } from '../utils/retry';

export interface ClaudeAgent {
  id: string;
  name: string;
  systemPrompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

export interface Message {
  role: 'user' | 'assistant';
  content: string;
}

export interface ConversationContext {
  agentId: string;
  conversationId: string;
  messages: Message[];
}

export class ClaudeService {
  private anthropic: Anthropic;
  private agents: Map<string, ClaudeAgent> = new Map();
  private conversations: Map<string, ConversationContext> = new Map();

  constructor(apiKey: string) {
    this.anthropic = new Anthropic({
      apiKey: apiKey,
    });

    // Initialize default agents
    this.initializeDefaultAgents();
  }

  private initializeDefaultAgents() {
    // Product Discovery Agent
    this.registerAgent({
      id: 'product-discovery',
      name: 'Product Discovery Assistant',
      systemPrompt: `You are an expert industrial hemp product discovery assistant. Your role is to:
- Search for new industrial hemp products
- Extract structured product information
- Identify plant parts and categorize by industry
- Find manufacturing companies
- Assess market stage and sustainability
- Provide detailed analysis with sources

Always respond with structured data including:
- Product name and description
- Plant parts used
- Industry categories
- Company information
- Market analysis
- Sustainability metrics`,
      model: 'claude-3-haiku-20240307',
      temperature: 0.7,
      maxTokens: 2000,
    });

    // Code Generator Agent
    this.registerAgent({
      id: 'code-generator',
      name: 'Code Generator',
      systemPrompt: `You are an expert TypeScript and React developer specializing in hemp industry applications. 
Generate clean, well-documented code following these patterns:
- Use TypeScript with proper typing
- Follow React best practices with hooks
- Use Tailwind CSS for styling
- Include error handling
- Add helpful comments
- Follow the existing codebase conventions`,
      model: 'claude-3-haiku-20240307',
      temperature: 0.3,
      maxTokens: 3000,
    });

    // Data Analyst Agent
    this.registerAgent({
      id: 'data-analyst',
      name: 'Data Analyst',
      systemPrompt: `You are a data analyst specializing in the industrial hemp market. Analyze trends, patterns, and insights from the hemp industry database.
Focus on:
- Market trends and growth patterns
- Industry segmentation analysis
- Product innovation tracking
- Company performance metrics
- Research impact assessment
Provide data-driven insights with visualizations when applicable.`,
      model: 'claude-3-haiku-20240307',
      temperature: 0.5,
      maxTokens: 2000,
    });

    // Content Writer Agent
    this.registerAgent({
      id: 'content-writer',
      name: 'Content Writer',
      systemPrompt: `You are an SEO-optimized content writer for the hemp industry. Create engaging, informative content that:
- Educates about industrial hemp applications
- Highlights sustainability benefits
- Uses industry-appropriate terminology
- Includes relevant keywords naturally
- Follows E-A-T principles
- Cites credible sources`,
      model: 'claude-3-haiku-20240307',
      temperature: 0.8,
      maxTokens: 2500,
    });
  }

  registerAgent(agent: ClaudeAgent) {
    this.agents.set(agent.id, agent);
  }

  getAgent(agentId: string): ClaudeAgent | undefined {
    return this.agents.get(agentId);
  }

  getAllAgents(): ClaudeAgent[] {
    return Array.from(this.agents.values());
  }

  async createConversation(agentId: string): Promise<string> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    const conversationId = uuidv4();
    this.conversations.set(conversationId, {
      agentId,
      conversationId,
      messages: [],
    });

    return conversationId;
  }

  async sendMessage(
    conversationId: string,
    message: string,
    stream: boolean = false
  ): Promise<any> {
    const context = this.conversations.get(conversationId);
    if (!context) {
      throw new Error(`Conversation ${conversationId} not found`);
    }

    const agent = this.agents.get(context.agentId);
    if (!agent) {
      throw new Error(`Agent ${context.agentId} not found`);
    }

    // Add user message to context
    context.messages.push({ role: 'user', content: message });

    try {
      if (stream) {
        // Return a stream for real-time responses
        const stream = await retryWithBackoff(async () => 
          this.anthropic.messages.create({
            model: agent.model,
            max_tokens: agent.maxTokens,
            temperature: agent.temperature,
            system: agent.systemPrompt,
            messages: context.messages,
            stream: true,
          })
        );

        return stream;
      } else {
        // Return complete response with retry
        const response = await retryWithBackoff(async () =>
          this.anthropic.messages.create({
            model: agent.model,
            max_tokens: agent.maxTokens,
            temperature: agent.temperature,
            system: agent.systemPrompt,
            messages: context.messages,
          })
        );

        // Add assistant response to context
        const assistantMessage = response.content[0].type === 'text' 
          ? response.content[0].text 
          : '';
        context.messages.push({ role: 'assistant', content: assistantMessage });

        return response;
      }
    } catch (error) {
      console.error('Claude API error:', error);
      
      // Handle specific Anthropic errors
      if (error.status === 429) {
        throw new Error('Rate limit exceeded. Please wait a moment before trying again.');
      } else if (error.status === 401) {
        throw new Error('Invalid API key. Please check your Anthropic API key.');
      } else if (error.status === 400) {
        throw new Error('Invalid request. Please check your input.');
      }
      
      throw error;
    }
  }

  getConversation(conversationId: string): ConversationContext | undefined {
    return this.conversations.get(conversationId);
  }

  clearConversation(conversationId: string) {
    const context = this.conversations.get(conversationId);
    if (context) {
      context.messages = [];
    }
  }

  deleteConversation(conversationId: string) {
    this.conversations.delete(conversationId);
  }

  // Utility method for one-off queries without conversation context
  async query(agentId: string, prompt: string): Promise<string> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    const response = await this.anthropic.messages.create({
      model: agent.model,
      max_tokens: agent.maxTokens,
      temperature: agent.temperature,
      system: agent.systemPrompt,
      messages: [{ role: 'user', content: prompt }],
    });

    return response.content[0].type === 'text' ? response.content[0].text : '';
  }
}

// Singleton instance
let claudeServiceInstance: ClaudeService | null = null;

export function getClaudeService(): ClaudeService {
  if (!claudeServiceInstance) {
    const apiKey = process.env.ANTHROPIC_API_KEY || process.env.CLAUDE_API_KEY;
    if (!apiKey) {
      console.error('ANTHROPIC_API_KEY or CLAUDE_API_KEY environment variable is required');
      throw new Error('ANTHROPIC_API_KEY or CLAUDE_API_KEY environment variable is required');
    }
    console.log('Initializing Claude service with API key:', apiKey.substring(0, 10) + '...');
    claudeServiceInstance = new ClaudeService(apiKey);
  }
  return claudeServiceInstance;
}