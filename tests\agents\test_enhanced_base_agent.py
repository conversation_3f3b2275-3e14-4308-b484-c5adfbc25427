# tests/agents/test_enhanced_base_agent.py
"""
Comprehensive tests for enhanced base agent
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from agents.core.enhanced_base_agent import EnhancedBaseAgent


class TestEnhancedBaseAgent:
    """Comprehensive tests for enhanced base agent"""
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client"""
        client = Mock()
        client.table = Mock(return_value=Mock(
            insert=Mock(return_value=Mock(
                execute=AsyncMock(return_value=Mock(data=[{"id": 1}]))
            ))
        ))
        return client
    
    @pytest.fixture
    def agent(self, mock_supabase):
        """Create test agent instance"""
        return EnhancedBaseAgent(mock_supabase, "test_agent")
    
    @pytest.mark.asyncio
    async def test_retry_logic_success(self, agent):
        """Test successful execution after retry"""
        agent.execute = AsyncMock(
            side_effect=[Exception("First fail"), {"status": "success"}]
        )
        
        result = await agent.execute_with_retry({"task_id": "123"})
        
        assert result["status"] == "success"
        assert agent.execute.call_count == 2
    
    @pytest.mark.asyncio
    async def test_retry_logic_max_attempts(self, agent):
        """Test failure after max attempts"""
        agent.execute = AsyncMock(side_effect=Exception("Always fails"))
        agent._handle_critical_failure = AsyncMock()
        
        with pytest.raises(Exception):
            await agent.execute_with_retry({"task_id": "123"})
        
        assert agent.execute.call_count == 3
        agent._handle_critical_failure.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_notification_on_critical_failure(self, agent):
        """Test notification sent on critical failure"""
        with patch('agents.core.enhanced_base_agent.NotificationService') as mock_notif:
            mock_notif.return_value.send_email_notification = AsyncMock()
            
            await agent._handle_critical_failure(
                {"task_id": "123", "type": "research"},
                Exception("Critical error")
            )
            
            mock_notif.return_value.send_email_notification.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_exponential_backoff(self, agent):
        """Test exponential backoff timing"""
        agent.execute = AsyncMock(side_effect=Exception("Fail"))
        
        with patch('asyncio.sleep') as mock_sleep:
            mock_sleep.return_value = None
            
            try:
                await agent.execute_with_retry({"task_id": "123"})
            except:
                pass
            
            # Check backoff times: 1s, 2s, 4s...
            sleep_calls = [call[0][0] for call in mock_sleep.call_args_list]
            assert sleep_calls == [1, 2]  # 2 retries after initial attempt