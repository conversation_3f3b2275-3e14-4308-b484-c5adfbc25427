import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function analyzeImageGeneration() {
  console.log('🔍 Analyzing Image Generation vs Products');
  console.log('='.repeat(80));
  
  // 1. Count total products
  const { count: productCount } = await supabase
    .from('uses_products')
    .select('*', { count: 'exact', head: true });
    
  console.log(`📦 Total products in database: ${productCount}`);
  
  // 2. Count total queue entries
  const { count: queueCount } = await supabase
    .from('image_generation_queue')
    .select('*', { count: 'exact', head: true });
    
  console.log(`🖼️ Total image generation queue entries: ${queueCount}`);
  
  // 3. Analyze queue by status
  const { data: queueStats } = await supabase
    .from('image_generation_queue')
    .select('status')
    .then(result => {
      const stats = {};
      result.data?.forEach(item => {
        stats[item.status] = (stats[item.status] || 0) + 1;
      });
      return { data: stats };
    });
    
  console.log('\n📊 Queue Status Breakdown:');
  Object.entries(queueStats || {}).forEach(([status, count]) => {
    console.log(`   ${status}: ${count}`);
  });
  
  // 4. Find duplicate queue entries
  const { data: duplicates } = await supabase.rpc('get_duplicate_queue_entries', {});
  
  if (!duplicates) {
    // If RPC doesn't exist, do it manually
    const { data: allQueue } = await supabase
      .from('image_generation_queue')
      .select('product_id, status')
      .order('product_id');
      
    const productQueueCount = {};
    allQueue?.forEach(item => {
      productQueueCount[item.product_id] = (productQueueCount[item.product_id] || 0) + 1;
    });
    
    const duplicateProducts = Object.entries(productQueueCount)
      .filter(([_, count]) => count > 1)
      .map(([productId, count]) => ({ product_id: productId, count }));
      
    console.log(`\n⚠️ Products with multiple queue entries: ${duplicateProducts.length}`);
    if (duplicateProducts.length > 0) {
      console.log('Top 10 duplicates:');
      duplicateProducts.slice(0, 10).forEach(dup => {
        console.log(`   Product ${dup.product_id}: ${dup.count} entries`);
      });
    }
  }
  
  // 5. Find orphaned queue entries (no matching product)
  const { data: orphanedQueue } = await supabase
    .from('image_generation_queue')
    .select('id, product_id')
    .then(async (result) => {
      if (!result.data) return { data: [] };
      
      const productIds = [...new Set(result.data.map(q => q.product_id))];
      const { data: existingProducts } = await supabase
        .from('uses_products')
        .select('id')
        .in('id', productIds);
        
      const existingIds = new Set(existingProducts?.map(p => p.id) || []);
      const orphaned = result.data.filter(q => !existingIds.has(q.product_id));
      
      return { data: orphaned };
    });
    
  console.log(`\n❌ Orphaned queue entries (no product): ${orphanedQueue?.length || 0}`);
  if (orphanedQueue && orphanedQueue.length > 0) {
    console.log('Sample orphaned entries:');
    orphanedQueue.slice(0, 5).forEach(orphan => {
      console.log(`   Queue ID ${orphan.id} for non-existent product ${orphan.product_id}`);
    });
  }
  
  // 6. Check product_images table
  const { count: imageCount } = await supabase
    .from('product_images')
    .select('*', { count: 'exact', head: true });
    
  console.log(`\n📸 Total entries in product_images table: ${imageCount}`);
  
  // 7. Find products without images
  const { data: productsWithoutImages } = await supabase
    .from('uses_products')
    .select('id, name')
    .is('image_url', null)
    .limit(10);
    
  console.log(`\n🚫 Products without images: ${productsWithoutImages?.length || 0}`);
  if (productsWithoutImages && productsWithoutImages.length > 0) {
    productsWithoutImages.forEach(p => {
      console.log(`   [${p.id}] ${p.name}`);
    });
  }
  
  // 8. Analyze image generation costs
  const { data: costSummary } = await supabase
    .from('ai_generation_costs')
    .select('provider_name, success, cost')
    .then(result => {
      const summary = {};
      result.data?.forEach(item => {
        const key = `${item.provider_name}_${item.success ? 'success' : 'failed'}`;
        if (!summary[key]) {
          summary[key] = { count: 0, totalCost: 0 };
        }
        summary[key].count++;
        summary[key].totalCost += item.cost || 0;
      });
      return { data: summary };
    });
    
  console.log('\n💰 Generation Cost Summary:');
  Object.entries(costSummary || {}).forEach(([key, stats]) => {
    console.log(`   ${key}: ${stats.count} attempts, $${stats.totalCost.toFixed(4)} total`);
  });
  
  // 9. Recent generation activity
  const { data: recentActivity } = await supabase
    .from('image_generation_queue')
    .select('created_at, status')
    .order('created_at', { ascending: false })
    .limit(100)
    .then(result => {
      const byDate = {};
      result.data?.forEach(item => {
        const date = new Date(item.created_at).toLocaleDateString();
        byDate[date] = (byDate[date] || 0) + 1;
      });
      return { data: byDate };
    });
    
  console.log('\n📅 Recent Generation Activity:');
  Object.entries(recentActivity || {}).slice(0, 5).forEach(([date, count]) => {
    console.log(`   ${date}: ${count} queue entries created`);
  });
  
  // Summary and recommendations
  console.log('\n' + '='.repeat(80));
  console.log('📋 SUMMARY & RECOMMENDATIONS:');
  console.log(`   - Ratio: ${(queueCount / productCount).toFixed(2)} queue entries per product`);
  
  if (queueCount > productCount * 1.5) {
    console.log('   ⚠️ WARNING: Excessive queue entries detected!');
    console.log('   - There are significantly more queue entries than products');
    console.log('   - This indicates duplicate generation requests');
  }
  
  console.log('\n💡 Recommended Actions:');
  console.log('1. Remove duplicate queue entries for the same product');
  console.log('2. Delete orphaned queue entries (no matching product)');
  console.log('3. Add unique constraint on (product_id, status) in queue table');
  console.log('4. Modify scripts to check for existing queue entries before adding');
}

analyzeImageGeneration().catch(console.error);