// Updated sanitizePrompt function with more comprehensive keyword handling
const SENSITIVE_KEYWORDS = {
  'underwear': 'textile garment',
  'CBD': 'wellness product', 
  'extract': 'essence',
  'capsules': 'supplement form',
  'tincture': 'liquid supplement',
  'THC': 'compound',
  'cannabis': 'hemp plant',
  'marijuana': 'hemp plant',
  // Add more sensitive terms
  'lingerie': 'textile garment',
  'intimates': 'textile apparel',
  'bra': 'textile support garment',
  'panties': 'textile garment',
  'boxers': 'textile garment',
  'briefs': 'textile garment',
  'undergarment': 'textile garment',
  'underpants': 'textile garment'
};

// Enhanced sanitize function with case-insensitive word boundary matching
function sanitizePrompt(prompt: string): string {
  let sanitized = prompt;
  for (const [sensitive, safe] of Object.entries(SENSITIVE_KEYWORDS)) {
    // Use word boundaries to avoid partial matches
    const regex = new RegExp(`\\b${sensitive}\\b`, 'gi');
    sanitized = sanitized.replace(regex, safe);
  }
  
  // Log sanitization for debugging
  if (sanitized !== prompt) {
    console.log(`Sanitized prompt: "${prompt}" -> "${sanitized}"`);
  }
  
  return sanitized;
}