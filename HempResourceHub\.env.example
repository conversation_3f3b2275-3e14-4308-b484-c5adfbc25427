# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key

# Database Connection (for server)
DATABASE_URL=postgresql://user:password@host:port/database?sslmode=require
DB_PASSWORD=your_database_password
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Claude AI Configuration (Required for AI features)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
# Alternative: CLAUDE_API_KEY=your_claude_api_key_here

# Optional: API Security for AI endpoints
AI_API_KEY=your_optional_api_key_for_ai_endpoints
ADMIN_API_KEY=your_optional_admin_api_key
REQUIRE_AUTH=false # Set to true to require authentication in development

# Port Configuration (optional)
PORT=5173

# Node Environment
NODE_ENV=development