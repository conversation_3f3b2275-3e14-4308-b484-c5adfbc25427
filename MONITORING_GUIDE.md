# HempQuarterz Monitoring Guide

This guide explains the comprehensive monitoring system for tracking all autonomous workflows, agents, and system health.

## Overview

The monitoring system provides:
- Real-time system health checks
- Agent performance tracking
- Task queue monitoring
- Image generation analytics
- Automated alerting
- Interactive dashboard
- Historical metrics tracking

## Quick Start

### 1. Basic Health Check
```bash
./hemp monitor
```

Shows a quick overview of system health, active alerts, and key metrics.

### 2. Interactive Dashboard
```bash
./hemp monitor --live
```

Launches a real-time terminal dashboard with:
- System overview
- Agent status
- Task queue
- Image generation progress
- Active alerts

**Dashboard Controls:**
- `←` `→` - Navigate tabs
- `R` - Force refresh
- `Q` - Quit

### 3. Generate Reports
```bash
# Full monitoring report
./hemp monitor --format report

# Export as JSON
./hemp monitor --format json --export metrics.json

# Prometheus format (for external monitoring)
./hemp monitor --format prometheus
```

## Monitoring Components

### 1. Agent Monitoring
Tracks for each agent:
- Active/Inactive status
- Last run time
- Success rate
- Task completion count
- Average execution time

### 2. Task Queue Monitoring
Monitors:
- Pending tasks
- Processing tasks
- Completed/Failed counts
- Average wait time
- Average processing time
- Queue depth trends

### 3. Image Generation Monitoring
Tracks:
- Products with/without images
- Provider performance
- Generation costs
- Success rates by provider
- Queue status

### 4. Database Monitoring
Monitors:
- Total records by table
- 24-hour growth rate
- Error rates
- Data integrity

### 5. Workflow Monitoring
Tracks:
- GitHub Actions status
- Edge Function invocations
- Automation run history

## Alert System

### Default Alerts

1. **High Task Failure Rate** (Warning)
   - Triggers when failure rate > 20%
   
2. **Agent Inactive** (Warning)
   - Triggers when agent hasn't run in 24 hours
   
3. **Queue Backlog** (Error)
   - Triggers when pending tasks > 100
   
4. **Image Generation Stalled** (Warning)
   - Triggers when processing time > 5 minutes
   
5. **Database Error Rate** (Error)
   - Triggers when error rate > 5%
   
6. **Low Product Discovery** (Info)
   - Triggers when < 10 products discovered in 24h

### Alert Severities
- 🔴 **Critical**: System failure requiring immediate attention
- ❌ **Error**: Significant issue affecting functionality
- ⚠️ **Warning**: Potential issue that may need attention
- ℹ️ **Info**: Informational alert for awareness

## Automated Monitoring

### GitHub Actions Integration

The monitoring system runs automatically via GitHub Actions:

1. **Hourly Health Checks**
   - Runs every hour
   - Checks all alerts
   - Creates GitHub issues for critical alerts

2. **Daily Summary**
   - Runs at 8 AM UTC
   - Comprehensive system report
   - Historical metrics tracking
   - 90-day retention

### Manual Trigger
```bash
# From GitHub UI
Actions → System Monitoring → Run workflow

# With custom parameters
- alert_severity: "error,critical"
- export_metrics: true
```

## Metrics Export

### JSON Format
```bash
./hemp monitor --format json --export metrics.json
```

Includes all raw metrics data for:
- Custom analysis
- Historical tracking
- Integration with other tools

### Prometheus Format
```bash
./hemp monitor --format prometheus
```

Exports metrics in Prometheus format for integration with:
- Grafana
- Prometheus server
- Other monitoring tools

Example metrics:
```
# HELP hemp_agents_total Total number of agents
# TYPE hemp_agents_total gauge
hemp_agents_total 6

# HELP hemp_tasks_pending Number of pending tasks
# TYPE hemp_tasks_pending gauge
hemp_tasks_pending 15
```

## Performance Optimization

### Monitor Performance Issues

1. **High Task Wait Times**
   ```bash
   # Check task performance
   ./hemp monitor --format report | grep "Avg Wait Time"
   ```

2. **Agent Bottlenecks**
   ```bash
   # Check specific agent
   ./hemp monitor --format json | jq '.agents.by_agent.research'
   ```

3. **Image Generation Issues**
   ```bash
   # Check image queue
   ./hemp images status
   ```

### Troubleshooting

1. **No Metrics Available**
   - Check Supabase connection
   - Verify agent_status table exists
   - Ensure agents have run at least once

2. **Dashboard Not Updating**
   - Press `R` to force refresh
   - Check terminal size (minimum 80x24)
   - Verify async operations

3. **Alerts Not Triggering**
   - Check alert thresholds
   - Verify metrics collection
   - Review alert conditions

## Integration Examples

### Slack Webhook (Future)
```python
# In monitor_and_alert.py
async def send_slack_alert(alert, webhook_url):
    payload = {
        "text": f"{alert['severity']}: {alert['message']}",
        "color": "danger" if alert['severity'] == 'critical' else "warning"
    }
    # POST to webhook
```

### Custom Alerts
```python
# Add to MonitoringService._init_default_alerts()
Alert(
    name="custom_metric",
    condition="custom_value > threshold",
    message="Custom alert triggered",
    severity=AlertSeverity.WARNING,
    threshold=100
)
```

### External Monitoring
```bash
# Export for DataDog, New Relic, etc.
./hemp monitor --format json | curl -X POST https://api.monitoring.com/metrics
```

## Best Practices

1. **Regular Monitoring**
   - Check dashboard daily
   - Review weekly reports
   - Act on alerts promptly

2. **Alert Management**
   - Don't ignore warnings
   - Investigate root causes
   - Adjust thresholds as needed

3. **Performance Tracking**
   - Monitor trends over time
   - Identify bottlenecks early
   - Optimize based on data

4. **Historical Analysis**
   - Keep metrics exports
   - Track improvements
   - Plan capacity needs

## Next Steps

1. Set up automated monitoring in GitHub Actions
2. Configure alert thresholds for your needs
3. Create custom dashboards with exported metrics
4. Integrate with your existing monitoring tools

The monitoring system provides complete visibility into the HempQuarterz automation platform, enabling proactive management and optimization of all workflows.