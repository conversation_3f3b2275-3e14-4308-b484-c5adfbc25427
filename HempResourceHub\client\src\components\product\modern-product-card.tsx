import { Link } from "wouter";
import { HempProduct } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";

interface ModernProductCardProps {
  product: HempProduct;
  industryName?: string;
  subIndustryName?: string;
  plantPartName?: string;
}

const ModernProductCard = ({
  product,
  industryName,
  subIndustryName,
  plantPartName,
}: ModernProductCardProps) => {
  return (
    <Link href={`/product/${product.id}`}>
      <div className="group bg-gray-900 rounded-xl overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-xl hover:shadow-green-500/10 hover:-translate-y-1">
        {/* Image */}
        <div className="aspect-[4/3] relative overflow-hidden bg-gray-800">
          <img
            src={product.image_url || '/images/unknown-hemp-image.png'}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={(e) => {
              e.currentTarget.src = '/images/unknown-hemp-image.png';
            }}
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          
          {/* Category badge */}
          {industryName && (
            <div className="absolute top-3 left-3">
              <Badge className="bg-black/50 backdrop-blur-sm text-white border-0">
                {industryName}
              </Badge>
            </div>
          )}
        </div>
        
        {/* Content */}
        <div className="p-5">
          <h3 className="hemp-brand-ultra font-semibold text-lg mb-4">
            {product.name}
          </h3>
          
          {/* Tags */}
          <div className="flex items-center justify-between">
            <div className="flex gap-2 flex-wrap">
              {plantPartName && (
                <Badge variant="outline" className="text-xs border-gray-700 text-gray-300">
                  {plantPartName}
                </Badge>
              )}
              {subIndustryName && (
                <Badge variant="outline" className="text-xs border-gray-700 text-gray-300">
                  {subIndustryName}
                </Badge>
              )}
            </div>
            
            {/* Arrow icon */}
            <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-green-400 transition-colors" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ModernProductCard;