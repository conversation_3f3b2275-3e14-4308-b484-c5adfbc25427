#!/bin/bash

# Script to generate AI images for all remaining products with unknown-hemp placeholders

echo "=== Generating AI Images for All Remaining Hemp Products ==="
echo ""

ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs"
URL="https://ktoqznqmlnxrtvubewyz.supabase.co/functions/v1/hemp-image-generator"

# Function to queue products
queue_products() {
    echo "Queuing next batch of products..."
    psql "$DATABASE_URL" -c "
    INSERT INTO image_generation_queue (
        product_id,
        prompt,
        status,
        priority,
        retry_with_provider,
        created_at,
        updated_at
    )
    SELECT 
        up.id as product_id,
        CONCAT(
            'Professional product photography of ', 
            up.name,
            CASE 
                WHEN pp.name IS NOT NULL THEN CONCAT(', made from ', pp.name)
                ELSE ', hemp-based product'
            END,
            CASE 
                WHEN i.name = 'Food and Beverage' THEN ', appetizing presentation, natural lighting'
                WHEN i.name = 'Cosmetics and Personal Care' THEN ', elegant packaging, soft lighting, premium feel'
                WHEN i.name = 'Textiles' THEN ', fabric texture visible, natural draping'
                WHEN i.name = 'Construction' THEN ', industrial setting, durability focus'
                WHEN i.name = 'Automotive' THEN ', sleek design, technical components'
                WHEN i.name = 'Medical' THEN ', clean clinical setting, professional'
                ELSE ', commercial quality, clean background'
            END,
            ', high resolution, professional lighting, no text, no watermarks'
        ) as prompt,
        'pending' as status,
        5 as priority,
        'replicate' as retry_with_provider,
        NOW() as created_at,
        NOW() as updated_at
    FROM uses_products up
    LEFT JOIN plant_parts pp ON up.plant_part_id = pp.id
    LEFT JOIN industry_sub_categories isc ON up.industry_sub_category_id = isc.id
    LEFT JOIN industries i ON isc.industry_id = i.id
    WHERE up.image_url LIKE '%unknown-hemp%'
    AND up.id NOT IN (
        SELECT product_id FROM image_generation_queue WHERE status IN ('pending', 'processing')
    )
    LIMIT 20;"
}

# Process in batches
BATCH_SIZE=10
TOTAL_REMAINING=103

echo "Starting generation of $TOTAL_REMAINING images..."
echo "This will cost approximately: $$(echo "scale=4; $TOTAL_REMAINING * 0.0004" | bc)"
echo ""

# Process in batches
for i in $(seq 1 11); do
    echo "Processing batch $i of 11..."
    
    # Queue products if needed
    if [ $i -gt 1 ]; then
        queue_products
        sleep 2
    fi
    
    # Generate images
    curl -X POST $URL \
      -H "Authorization: Bearer $ANON_KEY" \
      -H "Content-Type: application/json" \
      -d "{\"batchSize\": $BATCH_SIZE, \"forceProvider\": \"replicate\"}" \
      | python3 -m json.tool
    
    echo ""
    echo "Batch $i complete. Waiting 5 seconds before next batch..."
    sleep 5
done

echo ""
echo "✅ All image generation complete!"
echo ""
echo "To check the results:"
echo "SELECT COUNT(*) FROM uses_products WHERE image_url LIKE '%unknown-hemp%';"