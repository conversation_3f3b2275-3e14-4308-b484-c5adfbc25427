#!/bin/bash

# Test script for new AI image providers
# Make sure to update the Edge Function and add API keys first!

echo "=== Hemp Image Generator Provider Test ==="
echo ""
echo "Prerequisites:"
echo "1. Add to Supabase Edge Function Secrets:"
echo "   - REPLICATE_API_KEY: [YOUR_REPLICATE_API_KEY]"
echo "   - TOGETHER_API_KEY: [YOUR_TOGETHER_API_KEY]"
echo ""
echo "2. Update Edge Function code with new providers"
echo "3. Deploy the updated function"
echo ""
echo "Press Enter to continue with tests..."
read

ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs"
URL="https://ktoqznqmlnxrtvubewyz.supabase.co/functions/v1/hemp-image-generator"

echo "Testing Replicate provider..."
curl -X POST $URL \
  -H "Authorization: Bearer $ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 1, "forceProvider": "replicate"}' \
  | python3 -m json.tool

echo ""
echo "Testing Together AI provider..."
curl -X POST $URL \
  -H "Authorization: Bearer $ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 1, "forceProvider": "together_ai"}' \
  | python3 -m json.tool