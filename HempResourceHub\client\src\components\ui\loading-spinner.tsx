import { cn } from "@/lib/utils";
import { HempGrowingLoader } from "@/components/animations/hemp-growing-loader";

interface LoadingSpinnerProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  useHempAnimation?: boolean;
  message?: string;
}

export function LoadingSpinner({
  className,
  size = "md",
  useHempAnimation = true,
  message
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8",
    lg: "h-12 w-12"
  };

  // Use hemp growing animation by default
  if (useHempAnimation) {
    return (
      <div className={cn("flex items-center justify-center", className)}>
        <HempGrowingLoader size={size} message={message} />
      </div>
    );
  }

  // Fallback to simple spinner
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <div
        className={cn(
          "animate-spin rounded-full border-2 border-gray-300 border-t-green-500",
          sizeClasses[size]
        )}
      />
    </div>
  );
}

export function LoadingState({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <LoadingSpinner size="lg" />
      <p className="mt-4 text-gray-400">{message}</p>
    </div>
  );
}

export default LoadingSpinner;