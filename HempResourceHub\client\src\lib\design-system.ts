// Design System Constants for Hemp Database
// Ensures consistent spacing, typography, and styling across the application

export const designSystem = {
  // Spacing Scale (based on 4px grid)
  spacing: {
    xs: '0.25rem',    // 4px
    sm: '0.5rem',     // 8px
    md: '0.75rem',    // 12px
    lg: '1rem',       // 16px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '2rem',    // 32px
    '4xl': '2.5rem',  // 40px
    '5xl': '3rem',    // 48px
    '6xl': '4rem',    // 64px
    '7xl': '5rem',    // 80px
    '8xl': '6rem',    // 96px
  },

  // Typography Scale
  typography: {
    // Font Sizes
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
      '6xl': '3.75rem', // 60px
    },
    
    // Line Heights
    lineHeight: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75',
    },
    
    // Font Weights
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },

  // Color Palette
  colors: {
    // Hemp Brand Colors
    primary: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',  // Main green
      500: '#22c55e',  // Primary green
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
    },
    
    // Gray Scale
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712',
    },
    
    // Status Colors
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },

  // Component Spacing
  components: {
    // Card spacing
    card: {
      padding: {
        sm: '1rem',      // 16px
        md: '1.5rem',    // 24px
        lg: '2rem',      // 32px
      },
      gap: '1.5rem',     // 24px between cards
    },
    
    // Section spacing
    section: {
      paddingY: {
        sm: '2rem',      // 32px
        md: '3rem',      // 48px
        lg: '4rem',      // 64px
        xl: '5rem',      // 80px
      },
      marginBottom: {
        sm: '1.5rem',    // 24px
        md: '2rem',      // 32px
        lg: '3rem',      // 48px
      },
    },
    
    // Button spacing
    button: {
      padding: {
        sm: '0.5rem 1rem',     // 8px 16px
        md: '0.75rem 1.5rem',  // 12px 24px
        lg: '1rem 2rem',       // 16px 32px
      },
      gap: '0.5rem',           // 8px between button elements
    },
    
    // Form spacing
    form: {
      fieldGap: '1rem',        // 16px between form fields
      labelMargin: '0.5rem',   // 8px below labels
      groupGap: '1.5rem',      // 24px between form groups
    },
    
    // Navigation spacing
    nav: {
      itemGap: '1.5rem',       // 24px between nav items
      padding: '1rem 1.5rem',  // 16px 24px nav item padding
    },
  },

  // Responsive Breakpoints
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // Animation & Transitions
  animation: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easing: {
      default: 'cubic-bezier(0.4, 0, 0.2, 1)',
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      out: 'cubic-bezier(0, 0, 0.2, 1)',
      inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },

  // Border Radius
  borderRadius: {
    sm: '0.25rem',   // 4px
    md: '0.375rem',  // 6px
    lg: '0.5rem',    // 8px
    xl: '0.75rem',   // 12px
    '2xl': '1rem',   // 16px
    full: '9999px',
  },

  // Shadows
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
};

// Utility functions for consistent styling
export const getSpacing = (size: keyof typeof designSystem.spacing) => designSystem.spacing[size];
export const getFontSize = (size: keyof typeof designSystem.typography.fontSize) => designSystem.typography.fontSize[size];
export const getColor = (color: string) => designSystem.colors[color as keyof typeof designSystem.colors];

// CSS-in-JS helper for consistent component styling
export const createComponentStyles = {
  // Standard card styling
  card: `
    background: rgba(17, 24, 39, 0.4);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: ${designSystem.borderRadius.xl};
    padding: ${designSystem.components.card.padding.md};
    transition: all ${designSystem.animation.duration.normal} ${designSystem.animation.easing.default};
  `,
  
  // Standard button styling
  button: `
    padding: ${designSystem.components.button.padding.md};
    border-radius: ${designSystem.borderRadius.lg};
    font-weight: ${designSystem.typography.fontWeight.medium};
    transition: all ${designSystem.animation.duration.fast} ${designSystem.animation.easing.default};
  `,
  
  // Standard section spacing
  section: `
    padding-top: ${designSystem.components.section.paddingY.md};
    padding-bottom: ${designSystem.components.section.paddingY.md};
    margin-bottom: ${designSystem.components.section.marginBottom.md};
  `,
};

// Tailwind CSS class generators for consistent styling
export const tw = {
  // Spacing utilities
  spacing: {
    section: 'py-12 md:py-16 lg:py-20',
    container: 'px-4 sm:px-6 lg:px-8',
    cardGap: 'gap-6',
    elementGap: 'space-y-4',
  },
  
  // Typography utilities
  typography: {
    heading1: 'text-3xl md:text-4xl lg:text-5xl font-bold',
    heading2: 'text-2xl md:text-3xl lg:text-4xl font-bold',
    heading3: 'text-xl md:text-2xl lg:text-3xl font-semibold',
    heading4: 'text-lg md:text-xl lg:text-2xl font-semibold',
    body: 'text-base leading-relaxed',
    caption: 'text-sm text-gray-400',
  },
  
  // Component utilities
  components: {
    card: 'bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-xl p-6',
    button: 'px-6 py-3 rounded-lg font-medium transition-all duration-200',
    input: 'px-4 py-2 rounded-lg border border-gray-700 bg-gray-900/50 focus:border-green-400',
    badge: 'px-2 py-1 text-xs rounded-full',
  },
  
  // Layout utilities
  layout: {
    grid: {
      responsive: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
      cards: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6',
      metrics: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    },
    flex: {
      center: 'flex items-center justify-center',
      between: 'flex items-center justify-between',
      start: 'flex items-center justify-start',
    },
  },
};

export default designSystem;
