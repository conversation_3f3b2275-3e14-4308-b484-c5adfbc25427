# GitHub Actions Fix Guide - Next Steps

## ✅ What We've Done So Far

1. **Merged PR #13** - Fixed the main workflow file with:
   - Added `PYTHONPATH` environment variable
   - Fixed permission errors with `chmod +x hemp`
   - Changed to `python hemp` for better compatibility
   - Added error handling to prevent cascade failures
   - Added `test_setup.py` to verify your setup

## 📋 What You Need to Do Now

### Step 1: Add GitHub Secrets (REQUIRED)

1. Go to your repository: https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3
2. Click **Settings** (in the repository, not your profile)
3. In the left sidebar, click **Secrets and variables** → **Actions**
4. Click **New repository secret** button
5. Add these three secrets:

#### Secret 1: SUPABASE_URL
- **Name**: `SUPABASE_URL`
- **Value**: `https://ktoqznqmlnxrtvubewyz.supabase.co`
- Click "Add secret"

#### Secret 2: SUPABASE_ANON_KEY
- **Name**: `SUP<PERSON>ASE_ANON_KEY`
- **Value**: Copy from your `.env` file:
  ```
  eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTE3NzYsImV4cCI6MjA2NDA2Nzc3Nn0.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs
  ```
- Click "Add secret"

#### Secret 3: OPENAI_API_KEY
- **Name**: `OPENAI_API_KEY`
- **Value**: Copy from your `.env` file (starts with `sk-proj-`)
- Click "Add secret"

### Step 2: Test Your Setup Locally

Run the test script to verify everything is configured correctly:

```bash
cd /mnt/c/Users/<USER>/OneDrive/Desktop/HQz-Ai-DB-MCP-3
python test_setup.py
```

This will check:
- ✓ Environment variables are set
- ✓ Dependencies are installed
- ✓ Modules can be imported
- ✓ CLI works properly
- ✓ Database connection is successful

### Step 3: Test the GitHub Action

1. Go to your repository's **Actions** tab
2. Click on **"Automated Operations"** workflow
3. Click **"Run workflow"** button
4. Select options:
   - Agent type: `research` (start with one agent)
   - Task priority: `medium`
   - Include images: `true`
5. Click **"Run workflow"** (green button)

### Step 4: Monitor the Workflow

1. Click on the running workflow to see progress
2. Click on each job to see detailed logs
3. Look for any red error messages

## 🔧 Remaining Workflows to Fix

After the main workflow is working, we need to fix these workflows with similar changes:
- `hemp-automation.yml`
- `monitoring-and-reporting.yml`
- `weekly-summary.yml`
- `image-generation.yml`
- `monitoring.yml`
- `status-check.yml`

## 🚨 Common Issues & Solutions

### Issue: "Module not found" errors
**Solution**: Already fixed with `PYTHONPATH: ${{ github.workspace }}`

### Issue: "Permission denied"
**Solution**: Already fixed with `chmod +x hemp` and `python hemp`

### Issue: "Authentication failed"
**Solution**: Add the GitHub secrets (Step 1 above)

### Issue: Workflow not running
**Solution**: Make sure you clicked "Run workflow" and selected options

## 📝 Test Commands

You can test individual components locally:

```bash
# Test the CLI
python hemp --help

# Test monitoring
python hemp monitor --format health

# Test agent listing
python hemp agent list

# Test database connection
python hemp db validate
```

## 🎯 Success Criteria

You'll know it's working when:
1. The workflow shows green checkmarks ✅
2. No red error messages in logs
3. Artifacts are uploaded successfully
4. The summary shows agent statistics

## Need Help?

Check the detailed logs in the Actions tab. The error messages will tell you exactly what's wrong.