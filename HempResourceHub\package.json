{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development NODE_OPTIONS='--dns-result-order=ipv4first' tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "dotenv drizzle-kit push --verbose", "test": "tsx --test server/tests/**/*.test.js", "setup:demo-users": "tsx scripts/setup-demo-users.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@react-three/drei": "^9.88.11", "@react-three/fiber": "^8.14.5", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.60.5", "@tremor/react": "^3.18.7", "@types/leaflet": "^1.9.18", "@types/pg": "^8.15.2", "@types/react-helmet": "^6.1.11", "@types/react-syntax-highlighter": "^15.5.13", "@types/three": "^0.176.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compression": "^1.8.0", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "framer-motion": "^11.18.2", "globe.gl": "^2.41.6", "helmet": "^8.1.0", "input-otp": "^1.4.2", "leaflet": "^1.9.4", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "next-themes": "^0.4.6", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.16.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-leaflet": "^4.2.1", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "tw-animate-css": "^1.2.5", "uuid": "^10.0.0", "vaul": "^1.1.2", "web-vitals": "^5.0.3", "wouter": "^3.3.5", "ws": "^8.18.2", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.1.2", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/compression": "^1.8.1", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.19", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "buffer": "^6.0.3", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "process": "^0.11.10", "supertest": "^7.1.1", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "util": "^0.12.5", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}, "overrides": {"prismjs": "^1.30.0", "esbuild": "^0.25.0"}}