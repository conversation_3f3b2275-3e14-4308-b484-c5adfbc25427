"""
Supabase client initialization module
Provides a centralized way to get Supabase client instance
"""

import os
try:
    from supabase import create_client, Client
except ImportError:
    # For WSL/Linux environments, might need full import path
    from supabase.client import Client, create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def get_supabase_client() -> Client:
    """Get initialized Supabase client"""
    # Check for environment variables
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_ANON_KEY")
    
    # Fallback to default URL if not set
    if not supabase_url:
        supabase_url = "https://ktoqznqmlnxrtvubewyz.supabase.co"
        
    if not supabase_key:
        raise ValueError(
            "SUPABASE_ANON_KEY not found in environment variables. "
            "Please add it to your .env file or set it as an environment variable."
        )
    
    # Create and return client
    return create_client(supabase_url, supabase_key)


# Convenience function for backward compatibility
def get_client() -> Client:
    """Alias for get_supabase_client"""
    return get_supabase_client()