#!/usr/bin/env python3
"""Test the fixed research agent with DeepSeek"""

import asyncio
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from lib.supabase_client import get_supabase_client
from utils.simple_ai_wrapper import get_simple_ai_provider
from agents.research.unified_research_agent import create_research_agent


async def test_research_agent():
    """Test research agent with DeepSeek"""
    
    logger.info("Setting up DeepSeek provider...")
    ai_provider = get_simple_ai_provider("deepseek")
    
    logger.info("Creating research agent...")
    supabase = get_supabase_client()
    
    # Test without company extraction to avoid the AI call
    agent = create_research_agent(
        supabase, 
        ai_provider=ai_provider, 
        features=['basic']  # Only basic features, no company extraction
    )
    
    # Verify configuration
    logger.info(f"Agent config:")
    logger.info(f"  - Company extraction: {agent.config.company_extraction}")
    logger.info(f"  - Use AI analysis: {agent.config.use_ai_analysis}")
    logger.info(f"  - Auto generate images: {agent.config.auto_generate_images}")
    logger.info(f"  - Enabled features: {agent.config.enabled_features}")
    
    # Test discovery
    logger.info("\nDiscovering hemp products...")
    try:
        products = await agent.discover_products("hemp building materials", max_results=2)
        
        logger.info(f"\nFound {len(products)} products:")
        for i, product in enumerate(products, 1):
            logger.info(f"\n{i}. {product.get('name', 'Unknown')}")
            logger.info(f"   Description: {product.get('description', 'N/A')[:100]}...")
            logger.info(f"   Plant part: {product.get('plant_part', 'N/A')}")
            logger.info(f"   Industry: {product.get('industry', 'N/A')}")
            logger.info(f"   Companies: {product.get('companies', [])}")
            
        return True
    except Exception as e:
        logger.error(f"Discovery failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_with_company_extraction():
    """Test with company extraction enabled"""
    
    logger.info("\n\n=== Testing WITH company extraction ===")
    ai_provider = get_simple_ai_provider("deepseek")
    supabase = get_supabase_client()
    
    # Create agent with company extraction
    agent = create_research_agent(
        supabase, 
        ai_provider=ai_provider, 
        features=['basic', 'company']  # Enable company extraction
    )
    
    logger.info(f"Company extraction enabled: {agent.config.company_extraction}")
    
    # Test discovery
    try:
        products = await agent.discover_products("HempWood flooring", max_results=1)
        
        if products:
            product = products[0]
            logger.info(f"\nProduct: {product.get('name')}")
            logger.info(f"Companies: {product.get('companies', [])}")
            
        return True
    except Exception as e:
        logger.error(f"Failed: {e}")
        return False


async def main():
    """Run all tests"""
    
    # Test 1: Without company extraction
    success1 = await test_research_agent()
    
    # Test 2: With company extraction
    success2 = await test_with_company_extraction()
    
    if success1 and success2:
        logger.info("\n✅ All tests passed!")
    else:
        logger.info("\n❌ Some tests failed")


if __name__ == "__main__":
    asyncio.run(main())