import { useState, useCallback, useRef, useEffect } from 'react';
import { getClaudeAPI, type Message } from '@/lib/claude-api';

export interface UseClaudeOptions {
  agentId: string;
  onStreamUpdate?: (chunk: string) => void;
  onComplete?: (fullResponse: string) => void;
  onError?: (error: Error) => void;
  autoInit?: boolean; // Add option to control auto-initialization
}

export function useClaude({ agentId, onStreamUpdate, onComplete, onError, autoInit = true }: UseClaudeOptions) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const claudeAPI = useRef(getClaudeAPI());
  const abortControllerRef = useRef<AbortController | null>(null);
  const initializationRef = useRef<boolean>(false);

  // Initialize conversation
  useEffect(() => {
    if (!autoInit) return;
    
    let isMounted = true;
    let currentConversationId: string | null = null;
    let initTimeout: NodeJS.Timeout;

    const initConversation = async () => {
      try {
        // Check if we already have a conversation ID or are initializing
        if (conversationId || initializationRef.current) {
          return;
        }

        // Mark as initializing
        initializationRef.current = true;

        const id = await claudeAPI.current.createConversation(agentId);
        if (isMounted) {
          currentConversationId = id;
          setConversationId(id);
        } else {
          // Component unmounted before conversation was created
          claudeAPI.current.deleteConversation(id).catch(console.error);
        }
      } catch (err) {
        if (isMounted) {
          const error = err instanceof Error ? err : new Error('Failed to initialize conversation');
          setError(error);
          onError?.(error);
        }
      } finally {
        initializationRef.current = false;
      }
    };

    // Delay initialization to avoid double-mount issues in development
    // Use a longer delay in development to handle React's double-mounting
    const delay = import.meta.env.DEV ? 500 : 100;
    initTimeout = setTimeout(() => {
      if (isMounted && !conversationId && !initializationRef.current) {
        initConversation();
      }
    }, delay);

    // Cleanup conversation on unmount
    return () => {
      isMounted = false;
      clearTimeout(initTimeout);
      if (currentConversationId) {
        claudeAPI.current.deleteConversation(currentConversationId).catch(console.error);
      }
    };
  }, [agentId, autoInit]); // Remove onError from dependencies

  const initializeConversation = useCallback(async () => {
    try {
      const id = await claudeAPI.current.createConversation(agentId);
      setConversationId(id);
      setError(null);
      return id;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to initialize conversation');
      setError(error);
      onError?.(error);
      throw error;
    }
  }, [agentId, onError]);

  const sendMessage = useCallback(
    async (content: string, stream = true) => {
      let convId = conversationId;
      
      // Auto-create conversation if needed
      if (!convId) {
        try {
          convId = await initializeConversation();
        } catch (err) {
          return;
        }
      }

      setIsLoading(true);
      setError(null);

      // Abort any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      // Add user message
      const userMessage: Message = { role: 'user', content };
      setMessages((prev) => [...prev, userMessage]);

      try {
        let assistantMessage = '';
        const assistantMessageId = Date.now().toString();

        // Add empty assistant message that will be updated
        setMessages((prev) => [...prev, { role: 'assistant', content: '' }]);

        const response = await claudeAPI.current.sendMessage(
          convId,
          content,
          stream
            ? (chunk) => {
                assistantMessage += chunk;
                setMessages((prev) => {
                  const newMessages = [...prev];
                  const lastMessage = newMessages[newMessages.length - 1];
                  if (lastMessage && lastMessage.role === 'assistant') {
                    lastMessage.content = assistantMessage;
                  }
                  return newMessages;
                });
                onStreamUpdate?.(chunk);
              }
            : undefined
        );

        if (!stream) {
          assistantMessage = response;
          setMessages((prev) => {
            const newMessages = [...prev];
            const lastMessage = newMessages[newMessages.length - 1];
            if (lastMessage && lastMessage.role === 'assistant') {
              lastMessage.content = assistantMessage;
            }
            return newMessages;
          });
        }

        onComplete?.(assistantMessage);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Failed to send message');
        setError(error);
        onError?.(error);

        // Remove the empty assistant message on error
        setMessages((prev) => {
          const newMessages = [...prev];
          if (newMessages[newMessages.length - 1]?.role === 'assistant' && 
              newMessages[newMessages.length - 1]?.content === '') {
            newMessages.pop();
          }
          return newMessages;
        });
      } finally {
        setIsLoading(false);
        abortControllerRef.current = null;
      }
    },
    [conversationId, onStreamUpdate, onComplete, onError, initializeConversation]
  );

  const clearMessages = useCallback(() => {
    setMessages([]);
    // Don't recreate conversation here - let the component remount handle it if needed
  }, []);

  const abort = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsLoading(false);
    }
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    abort,
    conversationId,
    initializeConversation,
  };
}