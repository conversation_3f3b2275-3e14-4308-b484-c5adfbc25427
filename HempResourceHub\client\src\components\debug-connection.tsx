import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase-client';
import { useAllHempProducts } from '@/hooks/use-product-data';

export function DebugConnection() {
  const [connectionStatus, setConnectionStatus] = useState<string>('Checking...');
  const [productCount, setProductCount] = useState<number | null>(null);
  const { data: products, isLoading, error } = useAllHempProducts();
  
  useEffect(() => {
    checkConnection();
  }, []);
  
  async function checkConnection() {
    try {
      // Test basic connection
      const { data, error } = await supabase
        .from('uses_products')
        .select('count', { count: 'exact', head: true });
      
      if (error) {
        setConnectionStatus(`Error: ${error.message}`);
        console.error('Supabase connection error:', error);
      } else {
        setConnectionStatus('Connected to Supabase');
        setProductCount(data);
      }
      
      // Also check the actual data
      const { data: sampleProducts, error: productsError } = await supabase
        .from('uses_products')
        .select('*')
        .limit(5);
      
      console.log('Sample products:', sampleProducts);
      if (productsError) {
        console.error('Products fetch error:', productsError);
      }
    } catch (err) {
      setConnectionStatus(`Exception: ${err}`);
      console.error('Connection check failed:', err);
    }
  }
  
  return (
    <div className="fixed bottom-4 left-4 bg-gray-900 p-4 rounded-lg shadow-lg text-white max-w-md">
      <h3 className="font-bold mb-2">Debug Info</h3>
      <p className="text-sm">Connection: {connectionStatus}</p>
      <p className="text-sm">Product Count: {productCount ?? 'Unknown'}</p>
      <p className="text-sm">Hook Status: {isLoading ? 'Loading...' : error ? `Error: ${error}` : `${products?.length || 0} products loaded`}</p>
      <button 
        onClick={checkConnection}
        className="mt-2 px-3 py-1 bg-blue-600 rounded text-xs"
      >
        Recheck
      </button>
    </div>
  );
}