name: Diagnostic Check

on:
  workflow_dispatch:

permissions:
  contents: read
  actions: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PYTHONPATH: ${{ github.workspace }}

jobs:
  diagnose:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
        cache-dependency-path: |
          requirements.txt
          **/requirements*.txt
        
    - name: Check Directory Structure
      run: |
        echo "📁 Repository structure:"
        ls -la
        echo ""
        echo "📁 Key directories:"
        ls -la lib/ || echo "❌ lib/ directory not found"
        ls -la agents/ || echo "❌ agents/ directory not found"
        echo ""
        echo "📄 Requirements file:"
        ls -la requirements.txt || echo "❌ requirements.txt not found"
        
    - name: Check Python Path
      run: |
        echo "🐍 Python configuration:"
        python --version
        echo "PYTHONPATH: $PYTHONPATH"
        echo "sys.path:"
        python -c "import sys; print('\n'.join(sys.path[:5]))"
        
    - name: Test Requirements Installation
      run: |
        echo "📦 Testing requirements installation:"
        if [ -f requirements.txt ]; then
          echo "Found requirements.txt, attempting install..."
          python -m pip install --upgrade pip
          pip install -r requirements.txt 2>&1 | tee install.log
          echo ""
          echo "Exit code: ${PIPESTATUS[0]}"
          if [ ${PIPESTATUS[0]} -ne 0 ]; then
            echo "❌ Requirements installation failed"
            echo "Checking for problematic packages..."
            grep -E "(error|ERROR|Failed)" install.log | head -20
          else
            echo "✅ Requirements installed successfully"
          fi
        else
          echo "❌ requirements.txt not found"
        fi
        
    - name: Test Core Imports
      run: |
        echo "📦 Testing core imports:"
        python .github/scripts/test_imports.py
        
    - name: Check Environment Variables
      run: |
        echo "🔐 Environment variables check:"
        echo "SUPABASE_URL: ${{ secrets.SUPABASE_URL && '✅ Set' || '❌ Not set' }}"
        echo "SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY && '✅ Set' || '❌ Not set' }}"
        echo "OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY && '✅ Set' || '❌ Not set' }}"
        echo "PYTHONPATH: ${{ env.PYTHONPATH }}"
        
    - name: Test Database Connection
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
      run: |
        echo "🗄️ Testing database connection:"
        python .github/scripts/test_database.py
        
    - name: List Available Agents
      run: |
        echo "🤖 Checking available agents:"
        if [ -d "agents" ]; then
          echo "Agents directory contents:"
          find agents -name "*.py" -type f | head -20
        else
          echo "❌ agents directory not found"
        fi
        
    - name: Generate Diagnostic Report
      if: always()
      run: |
        cat > diagnostic-report.md << 'EOF'
# GitHub Actions Diagnostic Report

**Date**: $(date)
**Status**: ${{ job.status }}

## Environment Check
- **Python**: $(python --version 2>&1)
- **Working Directory**: $(pwd)
- **Repository Structure**: ${{ steps.checkout.outcome }}

## Dependency Status
- **requirements.txt**: $([ -f requirements.txt ] && echo "✅ Found" || echo "❌ Not found")
- **lib directory**: $([ -d lib ] && echo "✅ Found" || echo "❌ Not found")
- **agents directory**: $([ -d agents ] && echo "✅ Found" || echo "❌ Not found")

## Secrets Configuration
- **SUPABASE_URL**: ${{ secrets.SUPABASE_URL && '✅ Set' || '❌ Not set' }}
- **SUPABASE_ANON_KEY**: ${{ secrets.SUPABASE_ANON_KEY && '✅ Set' || '❌ Not set' }}
- **OPENAI_API_KEY**: ${{ secrets.OPENAI_API_KEY && '✅ Set' || '❌ Not set' }}

## Common Issues Found
$([ ! -f requirements.txt ] && echo "- ❌ Missing requirements.txt file" || echo "- ✅ requirements.txt exists")
$([ ! -d lib ] && echo "- ❌ Missing lib directory" || echo "- ✅ lib directory exists")
$([ ! -d agents ] && echo "- ❌ Missing agents directory" || echo "- ✅ agents directory exists")

## Recommendations
1. Ensure all required files are committed to the repository
2. Set up GitHub Secrets for Supabase credentials
3. Verify Python dependencies are correctly specified
4. Check that all import paths are correct

---
*Generated by Diagnostic Check workflow*
EOF

    - name: Upload Diagnostic Report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: diagnostic-report-${{ github.run_number }}
        path: |
          diagnostic-report.md
          install.log
        retention-days: 7
        if-no-files-found: warn
        
    - name: Summary
      if: always()
      run: |
        echo "## 🔍 Diagnostic Check Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Quick Status" >> $GITHUB_STEP_SUMMARY
        echo "- **Python**: ✅ $(python --version 2>&1)" >> $GITHUB_STEP_SUMMARY
        echo "- **Repository**: ✅ Checked out" >> $GITHUB_STEP_SUMMARY
        echo "- **requirements.txt**: $([ -f requirements.txt ] && echo '✅ Found' || echo '❌ Not found')" >> $GITHUB_STEP_SUMMARY
        echo "- **lib directory**: $([ -d lib ] && echo '✅ Found' || echo '❌ Not found')" >> $GITHUB_STEP_SUMMARY
        echo "- **agents directory**: $([ -d agents ] && echo '✅ Found' || echo '❌ Not found')" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Secrets Status" >> $GITHUB_STEP_SUMMARY
        echo "- **SUPABASE_URL**: ${{ secrets.SUPABASE_URL && '✅ Set' || '❌ Not set - Required!' }}" >> $GITHUB_STEP_SUMMARY
        echo "- **SUPABASE_ANON_KEY**: ${{ secrets.SUPABASE_ANON_KEY && '✅ Set' || '❌ Not set - Required!' }}" >> $GITHUB_STEP_SUMMARY
        echo "- **OPENAI_API_KEY**: ${{ secrets.OPENAI_API_KEY && '✅ Set' || '⚠️ Not set - Optional' }}" >> $GITHUB_STEP_SUMMARY