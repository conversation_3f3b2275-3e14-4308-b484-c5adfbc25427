import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, Shield, ShieldCheck, ShieldX, Smartphone, Copy, CheckCircle, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/components/auth/enhanced-auth-provider';

interface MFASetupProps {
  onComplete?: () => void;
}

export const MFASetup = ({ onComplete }: MFASetupProps) => {
  const { enableMFA, verifyMFA, disableMFA, hasMFA } = useAuth();
  
  const [step, setStep] = useState<'status' | 'setup' | 'verify'>('status');
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleEnableMFA = async () => {
    setError(null);
    setIsLoading(true);

    try {
      const { error, qrCode } = await enableMFA();
      
      if (error) {
        setError(error.message);
      } else if (qrCode) {
        setQrCode(qrCode);
        setStep('verify');
      }
    } catch (err) {
      setError('Failed to enable MFA. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyMFA = async () => {
    if (verificationCode.length !== 6) {
      setError('Please enter a 6-digit code');
      return;
    }

    setError(null);
    setIsLoading(true);

    try {
      const { error } = await verifyMFA(verificationCode);
      
      if (error) {
        setError(error.message);
      } else {
        setSuccess('Two-factor authentication has been successfully enabled!');
        setStep('status');
        onComplete?.();
      }
    } catch (err) {
      setError('Failed to verify code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisableMFA = async () => {
    setError(null);
    setIsLoading(true);

    try {
      const { error } = await disableMFA();
      
      if (error) {
        setError(error.message);
      } else {
        setSuccess('Two-factor authentication has been disabled.');
        setStep('status');
        onComplete?.();
      }
    } catch (err) {
      setError('Failed to disable MFA. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const getSecretFromQR = (qrCode: string): string => {
    try {
      const url = new URL(qrCode.replace('otpauth://totp/', 'https://example.com/'));
      return url.searchParams.get('secret') || '';
    } catch {
      return '';
    }
  };

  return (
    <Card className="bg-gray-900/60 backdrop-blur-sm border-green-500/30">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-green-400" />
          <CardTitle className="text-white">Two-Factor Authentication</CardTitle>
        </div>
        <CardDescription className="text-gray-400">
          Add an extra layer of security to your account
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert className="border-red-500/50 bg-red-500/10">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-red-400">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-500/50 bg-green-500/10">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="text-green-400">
              {success}
            </AlertDescription>
          </Alert>
        )}

        {step === 'status' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                {hasMFA ? (
                  <ShieldCheck className="h-5 w-5 text-green-400" />
                ) : (
                  <ShieldX className="h-5 w-5 text-red-400" />
                )}
                <div>
                  <p className="text-white font-medium">
                    Two-Factor Authentication
                  </p>
                  <p className="text-sm text-gray-400">
                    {hasMFA ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
              </div>
              <Badge variant={hasMFA ? 'default' : 'destructive'}>
                {hasMFA ? 'Active' : 'Inactive'}
              </Badge>
            </div>

            <div className="space-y-2">
              {!hasMFA ? (
                <Button
                  onClick={handleEnableMFA}
                  disabled={isLoading}
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Setting up...
                    </>
                  ) : (
                    <>
                      <Shield className="mr-2 h-4 w-4" />
                      Enable Two-Factor Authentication
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleDisableMFA}
                  disabled={isLoading}
                  variant="destructive"
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Disabling...
                    </>
                  ) : (
                    <>
                      <ShieldX className="mr-2 h-4 w-4" />
                      Disable Two-Factor Authentication
                    </>
                  )}
                </Button>
              )}
            </div>

            {!hasMFA && (
              <div className="p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                <h4 className="text-blue-400 font-medium mb-2">Why enable 2FA?</h4>
                <ul className="text-sm text-gray-300 space-y-1">
                  <li>• Protects your account even if your password is compromised</li>
                  <li>• Required for admin access to sensitive features</li>
                  <li>• Uses industry-standard TOTP authentication</li>
                  <li>• Works with Google Authenticator, Authy, and other apps</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {step === 'verify' && qrCode && (
          <div className="space-y-4">
            <div className="text-center">
              <h3 className="text-lg font-medium text-white mb-2">
                Scan QR Code
              </h3>
              <p className="text-sm text-gray-400 mb-4">
                Use your authenticator app to scan this QR code
              </p>
              
              <div className="bg-white p-4 rounded-lg inline-block mb-4">
                <img 
                  src={qrCode} 
                  alt="QR Code for 2FA setup" 
                  className="w-48 h-48"
                />
              </div>

              <div className="text-left">
                <Label className="text-gray-300 text-sm">
                  Or enter this secret manually:
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={getSecretFromQR(qrCode)}
                    readOnly
                    className="bg-gray-800 border-gray-700 text-white text-sm font-mono"
                  />
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(getSecretFromQR(qrCode))}
                    className="border-gray-700 text-gray-300 hover:bg-gray-800"
                  >
                    {copied ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="verificationCode" className="text-gray-300">
                Enter the 6-digit code from your app
              </Label>
              <Input
                id="verificationCode"
                type="text"
                maxLength={6}
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-green-400 text-center text-lg tracking-widest"
                placeholder="000000"
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={() => setStep('status')}
                variant="outline"
                className="flex-1 border-gray-700 text-gray-300 hover:bg-gray-800"
              >
                Cancel
              </Button>
              <Button
                onClick={handleVerifyMFA}
                disabled={isLoading || verificationCode.length !== 6}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  'Verify & Enable'
                )}
              </Button>
            </div>

            <div className="p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <p className="text-yellow-400 text-sm">
                <strong>Important:</strong> Save your backup codes in a secure location. 
                You'll need them to access your account if you lose your authenticator device.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MFASetup;
