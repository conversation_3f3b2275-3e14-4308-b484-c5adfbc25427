#!/usr/bin/env python3
"""
Fix research entries to move image URLs from metadata to proper image_url field
"""
import os
import json
from dotenv import load_dotenv
from supabase import create_client, Client
from datetime import datetime, timezone

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Initialize Supabase client
url = os.getenv("VITE_SUPABASE_URL")
key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key)

def fix_research_images():
    """Move image URLs from metadata to image_url field"""
    print("🔍 Fetching research entries with metadata...")
    
    # Get all research entries
    response = supabase.table("research_entries").select("*").execute()
    entries = response.data
    
    fixed_count = 0
    
    for entry in entries:
        if entry.get('metadata') and not entry.get('image_url'):
            metadata = entry['metadata']
            
            # Check if metadata has image_url
            if isinstance(metadata, dict) and metadata.get('image_url'):
                image_url = metadata['image_url']
                
                # Update the entry
                update_data = {
                    'image_url': image_url,
                    'updated_at': datetime.now(timezone.utc).isoformat()
                }
                
                result = supabase.table("research_entries").update(update_data).eq('id', entry['id']).execute()
                
                if result.data:
                    fixed_count += 1
                    print(f"✅ Fixed image for: {entry['title'][:50]}...")
    
    print(f"\n📊 Fixed {fixed_count} research entries")
    
    # Show current stats
    response = supabase.table("research_entries").select("id").not_.is_("image_url", "null").execute()
    with_images = len(response.data) if response.data else 0
    
    print(f"📸 Research entries with images: {with_images}/{len(entries)}")

if __name__ == "__main__":
    fix_research_images()