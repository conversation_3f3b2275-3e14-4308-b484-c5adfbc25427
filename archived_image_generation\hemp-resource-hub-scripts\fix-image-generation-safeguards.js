import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function addImageGenerationSafeguards() {
  console.log('🛡️ Adding Image Generation Safeguards');
  console.log('='.repeat(80));
  
  // 1. Create a function to safely queue images for products
  const createSafeQueueFunction = `
    CREATE OR REPLACE FUNCTION safe_queue_product_image(
      p_product_id INTEGER,
      p_prompt TEXT,
      p_priority INTEGER DEFAULT 1
    )
    RETURNS BOOLEAN AS $$
    DECLARE
      v_existing_count INTEGER;
    BEGIN
      -- Check if product already has a successful image
      SELECT COUNT(*) INTO v_existing_count
      FROM image_generation_queue
      WHERE product_id = p_product_id
      AND status IN ('completed', 'pending', 'processing');
      
      -- If entry already exists, don't create duplicate
      IF v_existing_count > 0 THEN
        RETURN FALSE;
      END IF;
      
      -- Insert new queue entry
      INSERT INTO image_generation_queue (
        product_id,
        prompt,
        priority,
        status,
        created_at
      ) VALUES (
        p_product_id,
        p_prompt,
        p_priority,
        'pending',
        NOW()
      );
      
      RETURN TRUE;
    END;
    $$ LANGUAGE plpgsql;
  `;
  
  // 2. Create a trigger to auto-queue images for new products
  const createAutoQueueTrigger = `
    CREATE OR REPLACE FUNCTION auto_queue_product_image()
    RETURNS TRIGGER AS $$
    BEGIN
      -- Only queue if product doesn't have a real image
      IF NEW.image_url IS NULL OR NEW.image_url LIKE '%placeholder%' THEN
        PERFORM safe_queue_product_image(
          NEW.id,
          'Professional product photography of ' || NEW.name || ', ' || 
          COALESCE(NEW.description, '') || ', high quality, commercial product shot',
          1
        );
      END IF;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    
    -- Drop existing trigger if it exists
    DROP TRIGGER IF EXISTS auto_queue_product_image_trigger ON uses_products;
    
    -- Create trigger
    CREATE TRIGGER auto_queue_product_image_trigger
    AFTER INSERT ON uses_products
    FOR EACH ROW
    EXECUTE FUNCTION auto_queue_product_image();
  `;
  
  // 3. Create a view to show products needing images
  const createNeedsImageView = `
    CREATE OR REPLACE VIEW products_needing_images AS
    SELECT 
      p.id,
      p.name,
      p.description,
      p.image_url,
      pp.name as plant_part,
      isc.name as sub_industry,
      CASE 
        WHEN q.id IS NOT NULL THEN q.status
        ELSE 'not_queued'
      END as queue_status
    FROM uses_products p
    LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
    LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
    LEFT JOIN LATERAL (
      SELECT id, status
      FROM image_generation_queue
      WHERE product_id = p.id
      ORDER BY created_at DESC
      LIMIT 1
    ) q ON true
    WHERE p.image_url IS NULL 
    OR p.image_url LIKE '%placeholder%'
    OR p.image_url = '';
  `;
  
  // 4. Create a function to queue all products needing images
  const createBatchQueueFunction = `
    CREATE OR REPLACE FUNCTION queue_all_products_needing_images()
    RETURNS TABLE(products_queued INTEGER, products_skipped INTEGER) AS $$
    DECLARE
      v_queued INTEGER := 0;
      v_skipped INTEGER := 0;
      v_product RECORD;
    BEGIN
      FOR v_product IN 
        SELECT id, name, plant_part, sub_industry
        FROM products_needing_images
        WHERE queue_status = 'not_queued'
      LOOP
        IF safe_queue_product_image(
          v_product.id,
          'Professional product photography of ' || v_product.name || 
          CASE 
            WHEN v_product.plant_part IS NOT NULL 
            THEN ' made from hemp ' || v_product.plant_part 
            ELSE '' 
          END ||
          CASE 
            WHEN v_product.sub_industry IS NOT NULL 
            THEN ', ' || v_product.sub_industry || ' industry'
            ELSE ''
          END ||
          ', high quality commercial product photography, clean background',
          1
        ) THEN
          v_queued := v_queued + 1;
        ELSE
          v_skipped := v_skipped + 1;
        END IF;
      END LOOP;
      
      RETURN QUERY SELECT v_queued, v_skipped;
    END;
    $$ LANGUAGE plpgsql;
  `;
  
  console.log('📝 Creating database functions and triggers...\n');
  
  // Execute SQL commands
  try {
    // Note: Supabase client doesn't support raw SQL execution directly
    // These would need to be run in the Supabase SQL editor or via migration
    console.log('⚠️ Please run the following SQL commands in your Supabase SQL editor:\n');
    
    console.log('-- 1. Safe Queue Function');
    console.log(createSafeQueueFunction);
    console.log('\n-- 2. Auto Queue Trigger');
    console.log(createAutoQueueTrigger);
    console.log('\n-- 3. Products Needing Images View');
    console.log(createNeedsImageView);
    console.log('\n-- 4. Batch Queue Function');
    console.log(createBatchQueueFunction);
    
    console.log('\n' + '='.repeat(80));
    console.log('💡 ADDITIONAL RECOMMENDATIONS:\n');
    
    console.log('1. Add unique constraint to prevent duplicates:');
    console.log(`   ALTER TABLE image_generation_queue 
   ADD CONSTRAINT unique_product_status 
   UNIQUE (product_id, status) 
   WHERE status IN ('pending', 'processing', 'completed');\n`);
    
    console.log('2. Add index for better performance:');
    console.log(`   CREATE INDEX idx_queue_product_status 
   ON image_generation_queue(product_id, status);\n`);
    
    console.log('3. Update your application code to use safe_queue_product_image():');
    console.log(`   const { data, error } = await supabase
   .rpc('safe_queue_product_image', {
     p_product_id: productId,
     p_prompt: prompt,
     p_priority: 1
   });\n`);
    
    console.log('4. To queue all products needing images:');
    console.log(`   const { data, error } = await supabase
   .rpc('queue_all_products_needing_images');\n`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Create a JS function to safely add to queue
export async function safeQueueProductImage(productId, prompt, priority = 1) {
  // Check if already queued
  const { data: existing } = await supabase
    .from('image_generation_queue')
    .select('id, status')
    .eq('product_id', productId)
    .in('status', ['pending', 'processing', 'completed'])
    .single();
    
  if (existing) {
    console.log(`⚠️ Product ${productId} already has ${existing.status} image queue entry`);
    return false;
  }
  
  // Add to queue
  const { error } = await supabase
    .from('image_generation_queue')
    .insert({
      product_id: productId,
      prompt: prompt,
      priority: priority,
      status: 'pending'
    });
    
  if (error) {
    console.error(`❌ Error queuing image for product ${productId}:`, error);
    return false;
  }
  
  console.log(`✅ Queued image generation for product ${productId}`);
  return true;
}

// Create a function to queue only products that need images
export async function queueProductsNeedingImages() {
  console.log('🔍 Finding products that need images...');
  
  // Get products with placeholder or no images
  const { data: products } = await supabase
    .from('uses_products')
    .select(`
      id, 
      name, 
      description,
      image_url,
      plant_parts (name),
      industry_sub_categories (name)
    `)
    .or('image_url.is.null,image_url.like.%placeholder%');
    
  if (!products || products.length === 0) {
    console.log('✨ All products have images!');
    return;
  }
  
  console.log(`Found ${products.length} products needing images`);
  
  let queued = 0;
  let skipped = 0;
  
  for (const product of products) {
    const prompt = `Professional product photography of ${product.name}${
      product.plant_parts?.name ? ` made from hemp ${product.plant_parts.name}` : ''
    }${
      product.industry_sub_categories?.name ? `, ${product.industry_sub_categories.name} industry` : ''
    }, high quality commercial photography, clean background`;
    
    const success = await safeQueueProductImage(product.id, prompt);
    if (success) {
      queued++;
    } else {
      skipped++;
    }
  }
  
  console.log(`\n📊 Results: ${queued} queued, ${skipped} skipped`);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addImageGenerationSafeguards().catch(console.error);
}