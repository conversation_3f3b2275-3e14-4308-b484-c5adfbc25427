# Claude AI Integration for Windows

This document explains how the Claude AI integration works in the Hemp Resource Hub, specifically designed to work on Windows through server-side API calls.

## Architecture Overview

The integration uses a **server-side approach** to avoid Windows compatibility issues with the Claude Code SDK:

```
┌─────────────┐     HTTP/SSE      ┌─────────────┐     SDK      ┌─────────────┐
│   Frontend  │ ←───────────────→ │   Express   │ ←──────────→ │ Anthropic   │
│  React App  │                   │  API Server │               │   Claude    │
└─────────────┘                   └─────────────┘               └─────────────┘
```

## Key Components

### Backend (Server-Side)

1. **Claude Service** (`/server/ai/claude-service.ts`)
   - Manages Claude SDK integration
   - Handles conversations and agent instances
   - Supports streaming responses

2. **Agent Manager** (`/server/ai/agent-manager.ts`)
   - Provides high-level agent operations
   - Manages multiple agent types:
     - Product Discovery Assistant
     - Code Generator
     - Data Analyst
     - Content Writer

3. **AI Routes** (`/server/routes/ai-routes.ts`)
   - RESTful API endpoints
   - Server-Sent Events (SSE) for streaming
   - Rate limiting for API protection

### Frontend (Client-Side)

1. **Claude API Client** (`/client/src/lib/claude-api.ts`)
   - HTTP client for backend API
   - Supports streaming responses
   - Error handling and retries

2. **React Hook** (`/client/src/hooks/use-claude.ts`)
   - Manages conversation state
   - Handles message streaming
   - Provides loading/error states

3. **UI Components**
   - Product Assistant (`/client/src/components/ai/product-assistant.tsx`)
   - Code Generator (`/client/src/components/ai/code-generator.tsx`)
   - AI Dashboard (`/client/src/components/ai/ai-dashboard.tsx`)

## Setup Instructions

### 1. Install Dependencies

```bash
cd HempResourceHub
npm install
```

### 2. Configure Environment Variables

Add to your `.env` file:

```env
# Required for AI features
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional security
AI_API_KEY=your_api_key_for_ai_endpoints
REQUIRE_AUTH=false
```

### 3. Start the Application

```bash
npm run dev
```

The AI features will be available at:
- Product Assistant: `/admin` → AI Assistant tab
- AI Dashboard: Navigate to AI section in admin panel

## API Endpoints

### Conversation Management
- `GET /api/ai/agents` - List available agents
- `POST /api/ai/conversations` - Create new conversation
- `POST /api/ai/conversations/:id/messages` - Send message (supports streaming)
- `GET /api/ai/conversations/:id` - Get conversation history
- `DELETE /api/ai/conversations/:id` - Delete conversation

### Agent-Specific Endpoints
- `POST /api/ai/discover-products` - Product discovery
- `POST /api/ai/generate-code` - Code generation
- `POST /api/ai/analyze-data` - Data analysis
- `POST /api/ai/generate-content` - Content generation
- `POST /api/ai/multi-agent-task` - Run multiple agents

## Usage Examples

### Using the React Hook

```typescript
import { useClaude } from '@/hooks/use-claude';

function MyComponent() {
  const { messages, sendMessage, isLoading } = useClaude({
    agentId: 'product-discovery',
  });

  const handleAsk = () => {
    sendMessage('Find hemp products for construction');
  };

  return (
    <div>
      {messages.map((msg, i) => (
        <div key={i}>{msg.content}</div>
      ))}
      <button onClick={handleAsk} disabled={isLoading}>
        Ask Claude
      </button>
    </div>
  );
}
```

### Direct API Usage

```typescript
import { getClaudeAPI } from '@/lib/claude-api';

const api = getClaudeAPI();

// Discover products
const products = await api.discoverProducts('hemp insulation');

// Generate code
const code = await api.generateCode(
  'Create a chart component for hemp statistics',
  'TypeScript/React'
);

// Run multiple agents
const results = await api.runMultiAgentTask([
  { agentId: 'product-discovery', prompt: 'Find hemp textiles' },
  { agentId: 'data-analyst', prompt: 'Analyze market trends' }
]);
```

## Multi-Agent System

The system supports running multiple Claude instances concurrently:

1. **Product Discovery** - Searches and analyzes hemp products
2. **Code Generator** - Creates TypeScript/React code
3. **Data Analyst** - Analyzes trends and patterns
4. **Content Writer** - Generates SEO-optimized content

Each agent has its own:
- System prompt
- Temperature settings
- Token limits
- Conversation context

## Troubleshooting

### Common Issues

1. **"ANTHROPIC_API_KEY not found"**
   - Ensure the API key is set in your `.env` file
   - Restart the server after adding the key

2. **Rate Limiting Errors**
   - Default: 30 AI requests per 15 minutes
   - Adjust in `/server/routes/ai-routes.ts` if needed

3. **Streaming Not Working**
   - Check if your proxy/firewall supports SSE
   - Try disabling streaming: `sendMessage(text, false)`

### Windows-Specific Notes

- This implementation avoids direct Claude Code SDK usage
- All AI processing happens server-side
- No platform-specific dependencies required
- Works in WSL, native Windows, or any environment

## Security Considerations

1. **API Keys**: Never expose Anthropic API keys to the frontend
2. **Rate Limiting**: Implemented to prevent abuse
3. **Optional Auth**: Enable `REQUIRE_AUTH=true` for production
4. **Input Validation**: All inputs are validated before processing

## Future Enhancements

- [ ] Persistent conversation storage
- [ ] User-specific agent configurations
- [ ] WebSocket support for real-time updates
- [ ] Agent collaboration features
- [ ] Custom agent creation UI