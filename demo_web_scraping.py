#!/usr/bin/env python3
"""
Demo: Hemp Product Discovery via Web Scraping
Shows how the research agent finds products without AI
"""

import asyncio
import aiohttp
import feedparser
from bs4 import BeautifulSoup
from datetime import datetime
import logging

# Setup logging  
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

class HempProductScraper:
    """Demonstrates web scraping for hemp products"""
    
    def __init__(self):
        self.sources = [
            {
                'name': 'Hemp Industry Daily',
                'feed': 'https://hempindustrydaily.com/feed/',
                'base_url': 'https://hempindustrydaily.com'
            },
            {
                'name': 'Vote Hemp',
                'url': 'https://votehemp.com',
                'selectors': {
                    'articles': 'article, .post, .news-item',
                    'title': 'h1, h2, h3, .entry-title',
                    'content': 'p, .entry-content, .excerpt'
                }
            }
        ]
        
    async def discover_products(self, query: str = "hemp products", limit: int = 10):
        """Discover hemp products from web sources"""
        logger.info(f"\n🔍 Searching for: '{query}'")
        logger.info("=" * 60)
        
        all_products = []
        
        # Try RSS feeds first
        for source in self.sources:
            if 'feed' in source:
                products = await self._scrape_rss(source, query)
                all_products.extend(products)
        
        # Then try web scraping
        for source in self.sources:
            if 'url' in source and len(all_products) < limit:
                products = await self._scrape_website(source, query)
                all_products.extend(products)
        
        # Process and structure products
        final_products = []
        for raw_product in all_products[:limit]:
            product = self._structure_product(raw_product)
            if product:
                final_products.append(product)
        
        return final_products
    
    async def _scrape_rss(self, source, query):
        """Scrape RSS feed for products"""
        products = []
        logger.info(f"\n📡 Checking RSS feed: {source['name']}")
        
        try:
            feed = feedparser.parse(source['feed'])
            
            if feed.entries:
                logger.info(f"   Found {len(feed.entries)} recent articles")
                
                for entry in feed.entries[:20]:  # Check recent 20
                    # Check if entry is product-related
                    text = f"{entry.get('title', '')} {entry.get('summary', '')}".lower()
                    
                    if self._is_product_related(text, query):
                        products.append({
                            'title': entry.get('title', ''),
                            'description': entry.get('summary', ''),
                            'url': entry.get('link', ''),
                            'source': source['name'],
                            'published': entry.get('published', ''),
                            'raw_text': text
                        })
                        logger.info(f"   ✓ Found: {entry.get('title', '')[:50]}...")
                        
        except Exception as e:
            logger.error(f"   ✗ RSS Error: {e}")
            
        return products
    
    async def _scrape_website(self, source, query):
        """Scrape website for products"""
        products = []
        logger.info(f"\n🌐 Checking website: {source['name']}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(source['url'], timeout=10) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        # Find articles
                        selectors = source.get('selectors', {})
                        articles = soup.select(selectors.get('articles', 'article'))[:10]
                        
                        logger.info(f"   Found {len(articles)} articles to check")
                        
                        for article in articles:
                            title_elem = article.select_one(selectors.get('title', 'h2'))
                            content_elem = article.select_one(selectors.get('content', 'p'))
                            
                            if title_elem:
                                text = f"{title_elem.get_text()} {content_elem.get_text() if content_elem else ''}".lower()
                                
                                if self._is_product_related(text, query):
                                    products.append({
                                        'title': title_elem.get_text(strip=True),
                                        'description': content_elem.get_text(strip=True) if content_elem else '',
                                        'url': source['url'],
                                        'source': source['name'],
                                        'raw_text': text
                                    })
                                    logger.info(f"   ✓ Found: {title_elem.get_text(strip=True)[:50]}...")
                    else:
                        logger.warning(f"   ✗ HTTP {response.status}")
                        
        except Exception as e:
            logger.error(f"   ✗ Web Error: {type(e).__name__}")
            
        return products
    
    def _is_product_related(self, text, query):
        """Check if text is about hemp products"""
        # Product indicators
        product_words = [
            'product', 'launches', 'introduces', 'develops', 'announces',
            'innovation', 'solution', 'application', 'material', 'technology'
        ]
        
        # Hemp-specific terms
        hemp_words = [
            'hemp', 'fiber', 'fibre', 'textile', 'seed', 'oil', 'hurd',
            'biomass', 'cbd', 'cannabinoid', 'industrial hemp'
        ]
        
        # Check if text contains hemp terms and product indicators
        has_hemp = any(word in text for word in hemp_words)
        has_product = any(word in text for word in product_words)
        
        # Also check if query terms are present
        query_terms = query.lower().split()
        has_query = any(term in text for term in query_terms)
        
        return has_hemp and (has_product or has_query)
    
    def _structure_product(self, raw_product):
        """Structure raw data into product format"""
        text = raw_product['raw_text']
        
        # Extract plant part
        plant_part = 'biomass'  # default
        if 'fiber' in text or 'textile' in text:
            plant_part = 'fiber'
        elif 'seed' in text or 'grain' in text:
            plant_part = 'seeds'
        elif 'oil' in text:
            plant_part = 'oil'
        elif 'flower' in text or 'cbd' in text:
            plant_part = 'flower'
        elif 'hurd' in text or 'shiv' in text:
            plant_part = 'hurds'
            
        # Extract industry
        industry = 'Other'  # default
        if any(word in text for word in ['food', 'nutrition', 'protein', 'beverage']):
            industry = 'Food & Beverage'
        elif any(word in text for word in ['textile', 'fabric', 'clothing', 'fashion']):
            industry = 'Textiles'
        elif any(word in text for word in ['construction', 'building', 'insulation']):
            industry = 'Construction'
        elif any(word in text for word in ['plastic', 'composite', 'material']):
            industry = 'Materials'
        elif any(word in text for word in ['cosmetic', 'beauty', 'skin care']):
            industry = 'Personal Care'
            
        # Extract companies (simple pattern matching)
        companies = []
        # Look for company patterns like "X Inc", "X Company", "X Ltd"
        import re
        company_patterns = [
            r'([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\s+(?:Inc|Corp|Company|Co|Ltd|LLC)',
            r'([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\s+(?:launches|introduces|develops)'
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, raw_product['title'] + ' ' + raw_product['description'])
            companies.extend(matches)
        
        # Clean up product name
        name = raw_product['title']
        # Remove common prefixes
        for prefix in ['Breaking:', 'News:', 'Update:', 'PRESS RELEASE:']:
            if name.startswith(prefix):
                name = name[len(prefix):].strip()
        
        return {
            'name': name[:100],  # Limit length
            'description': raw_product['description'][:500],
            'plant_part': plant_part,
            'industry': industry,
            'companies': list(set(companies))[:3],  # Unique companies, max 3
            'source_url': raw_product['url'],
            'data_source': raw_product['source'],
            'discovered_date': datetime.now().isoformat(),
            'commercialization_stage': 'Growing'  # Default for news items
        }

async def main():
    """Run the demo"""
    logger.info("\n🌿 HEMP PRODUCT DISCOVERY DEMO 🌿")
    logger.info("Demonstrating web scraping without AI")
    
    scraper = HempProductScraper()
    
    # Search for products
    products = await scraper.discover_products("hemp fiber textile", limit=10)
    
    # Display results
    logger.info(f"\n\n📊 RESULTS: Found {len(products)} products")
    logger.info("=" * 60)
    
    if products:
        for i, product in enumerate(products, 1):
            logger.info(f"\n{i}. {product['name']}")
            logger.info(f"   📍 Plant Part: {product['plant_part']}")
            logger.info(f"   🏭 Industry: {product['industry']}")
            logger.info(f"   📰 Source: {product['data_source']}")
            logger.info(f"   🔗 URL: {product['source_url'][:50]}...")
            
            if product['companies']:
                logger.info(f"   🏢 Companies: {', '.join(product['companies'])}")
                
            if product['description']:
                desc = product['description']
                if len(desc) > 150:
                    desc = desc[:147] + "..."
                logger.info(f"   📝 {desc}")
    else:
        logger.info("\n❌ No products found. This could be due to:")
        logger.info("   - Network connectivity issues")
        logger.info("   - Source websites have changed")
        logger.info("   - No recent product news")
        
    logger.info("\n" + "=" * 60)
    logger.info("✅ Demo complete!")
    logger.info("\nIn the full implementation, these products would be:")
    logger.info("- Saved to Supabase database")
    logger.info("- Enhanced with AI analysis (if available)")
    logger.info("- Queued for image generation")
    logger.info("- Linked to company records")

if __name__ == "__main__":
    asyncio.run(main())