#!/usr/bin/env python3
"""Check Python path and module availability"""

import sys
import os

print(f"Current working directory: {os.getcwd()}")
print(f"Script directory: {os.path.dirname(os.path.abspath(__file__))}")
print(f"\nPython path (first 5):")
for i, path in enumerate(sys.path[:5]):
    print(f"  {i}: {path}")

print(f"\nChecking if 'lib' directory exists:")
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'lib')
print(f"  lib path: {lib_path}")
print(f"  exists: {os.path.exists(lib_path)}")
print(f"  is directory: {os.path.isdir(lib_path)}")

if os.path.exists(lib_path):
    print(f"\n  Contents of lib directory:")
    for item in os.listdir(lib_path)[:10]:
        print(f"    - {item}")

# Try direct import
print("\nTrying direct import of lib.supabase_client...")
try:
    # Add current directory to path if not there
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
        print(f"  Added {current_dir} to Python path")
    
    from lib.supabase_client import get_supabase_client
    print("  ✅ Import successful!")
except ImportError as e:
    print(f"  ❌ Import failed: {e}")