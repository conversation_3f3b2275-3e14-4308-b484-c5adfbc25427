#!/usr/bin/env python3
"""
Test saving hemp articles to research_entries table - Fixed version
"""

import os
from datetime import datetime
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_supabase() -> Client:
    """Get Supabase client"""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_ANON_KEY")
    
    if not url or not key:
        raise ValueError("Missing SUPABASE_URL or SUPABASE_ANON_KEY in .env file")
    
    return create_client(url, key)

def save_sample_research_entries():
    """Save sample hemp research entries"""
    print("Saving Hemp Research Entries to Supabase")
    print("=" * 50)
    
    # Get Supabase client
    supabase = get_supabase()
    print("✅ Connected to Supabase")
    
    # Check current research entries
    current_entries = supabase.table('research_entries').select('title').execute()
    print(f"\nCurrent research entries: {len(current_entries.data)}")
    
    # Sample research entries with CORRECT column names
    sample_entries = [
        {
            'title': 'HempWood Launches New Sustainable Flooring Line',
            'authors_or_assignees': ['HempWood Inc.', 'Industry Report'],
            'abstract_summary': 'HempWood Inc announces their latest hemp-based hardwood alternative flooring, offering 20% harder surface than oak with rapid renewability.',
            'publication_or_filing_date': '2024-01-15',
            'journal_or_office': 'Hemp Industry Daily',
            'full_text_url': 'https://hempindustrydaily.com/hempwood-flooring',
            'entry_type': 'Article',
            'keywords': ['hemp', 'flooring', 'construction', 'sustainable', 'hempwood']
        },
        {
            'title': 'Ford Expands Use of Hemp Biocomposites in Vehicle Production',
            'authors_or_assignees': ['Ford Motor Company', 'Automotive News'],
            'abstract_summary': 'Ford Motor Company increases hemp fiber composite usage in door panels and dashboard components, reducing vehicle weight by 10%.',
            'publication_or_filing_date': '2024-01-10',
            'journal_or_office': 'Automotive Hemp Report',
            'full_text_url': 'https://example.com/ford-hemp-biocomposites',
            'entry_type': 'Report',
            'keywords': ['hemp', 'automotive', 'biocomposite', 'ford', 'sustainable']
        },
        {
            'title': 'Hemp Protein Outperforms Soy in Digestibility Study',
            'authors_or_assignees': ['Dr. Sarah Johnson', 'University of Manitoba'],
            'abstract_summary': 'New research from the University of Manitoba shows hemp protein demonstrates superior digestibility compared to soy protein, with 91% digestibility rate.',
            'publication_or_filing_date': '2024-01-05',
            'journal_or_office': 'Journal of Agricultural and Food Chemistry',
            'doi_or_patent_number': '10.1021/acs.jafc.2024.12345',
            'full_text_url': 'https://pubs.acs.org/doi/10.1021/acs.jafc.2024.12345',
            'entry_type': 'Paper',
            'keywords': ['hemp', 'protein', 'nutrition', 'digestibility', 'research']
        },
        {
            'title': 'Hempcrete Building Blocks Receive Green Certification',
            'authors_or_assignees': ['Green Building Council'],
            'abstract_summary': 'Hempcrete blocks certified for carbon-negative construction, offering superior insulation and moisture regulation for sustainable building.',
            'publication_or_filing_date': '2024-01-20',
            'journal_or_office': 'Sustainable Construction Weekly',
            'full_text_url': 'https://greenbuilding.org/hempcrete-certification',
            'entry_type': 'Article',
            'keywords': ['hempcrete', 'construction', 'building', 'sustainable', 'carbon-negative']
        }
    ]
    
    # Get IDs for foreign keys
    print("\nLooking up reference data...")
    
    # Get plant type
    plant_types = supabase.table('hemp_plant_archetypes').select('id, name').execute()
    plant_type_id = plant_types.data[0]['id'] if plant_types.data else None
    print(f"Using plant type: {plant_types.data[0]['name'] if plant_types.data else 'None'}")
    
    # Get plant parts
    plant_parts = supabase.table('plant_parts').select('id, name').execute()
    plant_part_map = {part['name']: part['id'] for part in plant_parts.data}
    
    # Get industries
    industries = supabase.table('industries').select('id, name').execute()
    industry_map = {ind['name']: ind['id'] for ind in industries.data}
    
    # Add reference IDs to entries
    for entry in sample_entries:
        entry['plant_type_id'] = plant_type_id
        
        # Map based on keywords
        if 'construction' in entry['keywords'] or 'building' in entry['keywords']:
            entry['plant_part_id'] = plant_part_map.get('Hurds')
            entry['industry_id'] = industry_map.get('Construction')
        elif 'automotive' in entry['keywords']:
            entry['plant_part_id'] = plant_part_map.get('Fiber')
            entry['industry_id'] = industry_map.get('Automotive')
        elif 'protein' in entry['keywords'] or 'nutrition' in entry['keywords']:
            entry['plant_part_id'] = plant_part_map.get('Seeds')
            entry['industry_id'] = industry_map.get('Food & Beverage')
    
    # Save entries
    print("\nSaving new research entries...")
    saved_count = 0
    
    for entry in sample_entries:
        try:
            # Check if already exists
            existing = supabase.table('research_entries').select('id').eq('title', entry['title']).execute()
            
            if not existing.data:
                # Add timestamps
                entry['created_at'] = datetime.now().isoformat()
                entry['updated_at'] = datetime.now().isoformat()
                
                # Save to database
                result = supabase.table('research_entries').insert(entry).execute()
                
                if result.data:
                    saved_count += 1
                    print(f"✅ Saved: {entry['title'][:50]}...")
                else:
                    print(f"❌ Failed to save: {entry['title'][:50]}...")
            else:
                print(f"⏭️  Already exists: {entry['title'][:50]}...")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            print(f"   Entry: {entry['title']}")
    
    # Show results
    print(f"\n{saved_count} new research entries saved")
    
    # Display all entries
    print("\nAll research entries in database:")
    all_entries = supabase.table('research_entries').select('id, title, entry_type, publication_or_filing_date').order('publication_or_filing_date', desc=True).execute()
    
    if all_entries.data:
        for entry in all_entries.data:
            print(f"- [{entry['entry_type']}] {entry['title'][:60]}... ({entry['publication_or_filing_date']})")
    
    print(f"\nTotal entries: {len(all_entries.data)}")

if __name__ == "__main__":
    try:
        save_sample_research_entries()
    except Exception as e:
        print(f"Error: {e}")
        print("\nMake sure your .env file contains:")
        print("SUPABASE_URL=your-project-url")
        print("SUPABASE_ANON_KEY=your-anon-key")