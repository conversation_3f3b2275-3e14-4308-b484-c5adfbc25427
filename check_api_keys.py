#!/usr/bin/env python3
"""Check API keys are loaded correctly"""

import os
from dotenv import load_dotenv

# Load .env file
load_dotenv()

print("=== API Keys Status ===")

# Check DeepSeek
deepseek_key = os.getenv("DEEPSEEK_API_KEY")
if deepseek_key:
    print(f"[OK] DEEPSEEK_API_KEY: {deepseek_key[:10]}...{deepseek_key[-4:]}")
else:
    print("[X] DEEPSEEK_API_KEY: Not found")

# Check OpenAI
openai_key = os.getenv("OPENAI_API_KEY")
if openai_key:
    print(f"[OK] OPENAI_API_KEY: {openai_key[:10]}...{openai_key[-4:]}")
else:
    print("[X] OPENAI_API_KEY: Not found")

# Check Supabase
supabase_url = os.getenv("SUPABASE_URL")
if supabase_url:
    print(f"[OK] SUPABASE_URL: {supabase_url}")
else:
    print("[X] SUPABASE_URL: Not found")

print("\n=== Testing DeepSeek Connection ===")
try:
    from openai import OpenAI
    client = OpenAI(
        api_key=deepseek_key,
        base_url="https://api.deepseek.com/v1"
    )
    
    # Try a simple completion
    response = client.chat.completions.create(
        model="deepseek-chat",
        messages=[{"role": "user", "content": "Say hello"}],
        max_tokens=10
    )
    print(f"[OK] DeepSeek connection successful!")
    print(f"  Response: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"[X] DeepSeek connection failed: {e}")