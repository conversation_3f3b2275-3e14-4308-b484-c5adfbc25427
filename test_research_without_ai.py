#!/usr/bin/env python3
"""
Test research agent without AI providers - uses only web scraping and feed monitoring
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from lib.supabase_client import get_supabase_client
from agents.research.unified_research_agent import UnifiedResearchAgent, ResearchConfig, ResearchFeatures

async def test_research_without_ai():
    """Test research agent using only non-AI features"""
    logger.info("Testing research agent without AI providers...")
    
    # Get Supabase client
    supabase = get_supabase_client()
    logger.info("✅ Supabase client initialized")
    
    # Create config with only non-AI features
    config = ResearchConfig(
        enabled_features={
            ResearchFeatures.WEB_SCRAPING,
            ResearchFeatures.FEED_MONITORING,
            ResearchFeatures.COMPANY_EXTRACTION
        },
        company_extraction=True,
        use_ai_analysis=False,
        auto_generate_images=False,
        max_results=10
    )
    
    # Create agent without AI provider
    agent = UnifiedResearchAgent(supabase, ai_provider=None, config=config)
    logger.info("✅ Research agent created with web scraping and feed monitoring")
    
    # Test feed discovery
    logger.info("\n🔍 Testing feed discovery...")
    feed_products = await agent._feed_discovery("hemp innovation", 5)
    logger.info(f"Found {len(feed_products)} products from feeds")
    
    for i, product in enumerate(feed_products[:3], 1):
        logger.info(f"\n{i}. {product.get('title', 'No title')}")
        logger.info(f"   Source: {product.get('source', 'Unknown')}")
        logger.info(f"   URL: {product.get('url', 'No URL')}")
        if product.get('description'):
            desc = product['description'][:100] + '...' if len(product['description']) > 100 else product['description']
            logger.info(f"   Description: {desc}")
    
    # Test web scraping (if any results)
    logger.info("\n🌐 Testing web scraping...")
    web_products = await agent._web_scraping_discovery("hemp products", 5)
    logger.info(f"Found {len(web_products)} products from web scraping")
    
    # Test complete discovery
    logger.info("\n🚀 Running complete discovery...")
    all_products = await agent.discover_products("hemp innovation products", max_results=10)
    logger.info(f"Total products discovered: {len(all_products)}")
    
    # Display results
    if all_products:
        logger.info("\n=== Discovered Products ===")
        for i, product in enumerate(all_products[:5], 1):
            logger.info(f"\n{i}. {product.get('name', 'Unknown Product')}")
            logger.info(f"   Plant Part: {product.get('plant_part', 'Unknown')}")
            logger.info(f"   Industry: {product.get('industry', 'Unknown')}")
            logger.info(f"   Source: {product.get('data_source', 'Unknown')}")
            if product.get('companies'):
                logger.info(f"   Companies: {', '.join(product['companies'])}")
    else:
        logger.warning("No products discovered. This might be due to:")
        logger.warning("- RSS feeds being empty or down")
        logger.warning("- Web scraping being blocked")
        logger.warning("- Network connectivity issues")
    
    # Check database for existing products
    logger.info("\n📊 Checking existing products in database...")
    result = supabase.table('uses_products').select('count', count='exact').execute()
    logger.info(f"Total products in database: {result.count}")
    
    # Get some sample products
    sample = supabase.table('uses_products').select('*').limit(5).execute()
    if sample.data:
        logger.info("\nSample products from database:")
        for i, product in enumerate(sample.data, 1):
            logger.info(f"{i}. {product['name']} (ID: {product['id']})")

if __name__ == "__main__":
    try:
        asyncio.run(test_research_without_ai())
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()