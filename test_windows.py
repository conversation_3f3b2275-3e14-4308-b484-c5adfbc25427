#!/usr/bin/env python3
"""
Test script specifically for Windows environment.
Tests the research agent fixes without complex imports.
"""

import os
import sys
import asyncio
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

async def test_research_agent():
    """Test the research agent directly"""
    try:
        # Test imports individually
        logger.info("Testing imports...")
        
        try:
            from supabase import create_client
            logger.info("✅ Supabase import successful")
        except ImportError as e:
            logger.warning(f"⚠️  Supabase import issue: {e}")
            logger.info("   Trying alternative import...")
            try:
                import supabase
                logger.info(f"✅ Supabase module found: {supabase}")
            except ImportError:
                logger.error("❌ Supabase not installed. Run: pip install supabase")
                return False
        
        # Test research agent components
        logger.info("\nTesting research agent components...")
        
        # Import the unified research agent
        from agents.research.unified_research_agent import create_research_agent, ResearchFeatures
        logger.info("✅ Research agent imports successful")
        
        # Test web scraping tools
        try:
            import aiohttp
            import feedparser
            from bs4 import BeautifulSoup
            logger.info("✅ Web scraping dependencies available")
        except ImportError as e:
            logger.error(f"❌ Missing dependency: {e}")
            return False
        
        # Create a mock Supabase client for testing
        logger.info("\nCreating mock environment...")
        
        class MockSupabase:
            """Mock Supabase client for testing"""
            def table(self, name):
                return self
            
            def select(self, *args):
                return self
            
            def eq(self, *args):
                return self
            
            def insert(self, *args):
                return self
            
            async def execute(self):
                class Result:
                    data = []
                return Result()
        
        # Create research agent without AI
        mock_supabase = MockSupabase()
        agent = create_research_agent(
            mock_supabase, 
            ai_provider=None,
            features=[ResearchFeatures.WEB_SCRAPING, ResearchFeatures.FEED_MONITORING]
        )
        logger.info("✅ Research agent created successfully")
        
        # Test basic discovery
        logger.info("\nTesting product discovery...")
        logger.info("  - Using web scraping (no AI required)")
        logger.info("  - This may take a few seconds...")
        
        try:
            # Simple test - just ensure it runs without errors
            products = await agent.discover_products("hemp fiber", max_results=5)
            logger.info(f"✅ Discovery completed! Found {len(products)} products")
            
            if products:
                logger.info("\nSample product:")
                product = products[0]
                logger.info(f"  Name: {product.get('name', 'N/A')}")
                logger.info(f"  Source: {product.get('data_source', 'N/A')}")
                logger.info(f"  Plant Part: {product.get('plant_part', 'N/A')}")
        except Exception as e:
            logger.error(f"❌ Discovery failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        logger.info("\n✅ All tests passed! Research agent is working.")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test runner"""
    logger.info("=== Testing Hemp Research Agent Fixes ===\n")
    
    # Check Python version
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Platform: {sys.platform}\n")
    
    # Run async test
    success = asyncio.run(test_research_agent())
    
    if success:
        logger.info("\n🎉 SUCCESS! The research agent is now working properly.")
        logger.info("\nNext steps:")
        logger.info("1. Run the full hemp CLI: python hemp_cli.py agent research 'hemp products'")
        logger.info("2. The agent will use web scraping to find products")
        logger.info("3. If you add an AI API key, it will also use AI features")
    else:
        logger.info("\n❌ Tests failed. Please check the errors above.")
        logger.info("\nCommon fixes:")
        logger.info("1. Install missing dependencies: pip install supabase aiohttp feedparser beautifulsoup4")
        logger.info("2. Check your .env file has SUPABASE_URL and SUPABASE_ANON_KEY")

if __name__ == "__main__":
    main()