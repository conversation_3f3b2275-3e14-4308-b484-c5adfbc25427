import { lazy, Suspense, useState } from "react";
import { <PERSON><PERSON><PERSON> } from "react-helmet";
import AddPlantTypeForm from "@/components/admin/add-plant-type";
import AddPlantPartForm from "@/components/admin/add-plant-part";
import AddIndustryForm from "@/components/admin/add-industry";
import AddSubIndustryForm from "@/components/admin/add-sub-industry";
import ImageGenerationDashboard from "@/components/admin/image-generation-dashboard";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AgentStatusCards } from "@/components/admin/agent-status-cards";
import { DataVisualizationDashboard } from "@/components/ui/data-visualization-dashboard";
import { SimpleProductDiscovery } from "@/components/admin/simple-product-discovery";
import { AIDashboard } from "@/components/ai/ai-dashboard";
import { SimpleChatDemo } from "@/components/ai/simple-chat-demo";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Plus, ChevronDown, TreePine, Leaf, Factory, Building, Package } from "lucide-react";

// Commented out lazy loading for now due to import issues
// const AgentMonitoringDashboard = lazy(() => import("@/components/admin/agent-monitoring-dashboard"));
// import AgentMonitoringDashboardSimple from "@/components/admin/agent-monitoring-dashboard-simple";

const AdminPage = () => {
  const [selectedAddForm, setSelectedAddForm] = useState<string>("plant-types");

  const addFormOptions = [
    {
      value: "plant-types",
      label: "Plant Types",
      icon: <TreePine className="h-4 w-4" />,
      description: "Add new hemp plant varieties"
    },
    {
      value: "plant-parts",
      label: "Plant Parts",
      icon: <Leaf className="h-4 w-4" />,
      description: "Add plant components (fiber, seed, etc.)"
    },
    {
      value: "industries",
      label: "Industries",
      icon: <Factory className="h-4 w-4" />,
      description: "Add industry categories"
    },
    {
      value: "sub-industries",
      label: "Sub-Industries",
      icon: <Building className="h-4 w-4" />,
      description: "Add industry subcategories"
    },
    {
      value: "products",
      label: "Products",
      icon: <Package className="h-4 w-4" />,
      description: "Discover and add new products"
    }
  ];

  const renderAddForm = () => {
    switch (selectedAddForm) {
      case "plant-types":
        return <AddPlantTypeForm />;
      case "plant-parts":
        return <AddPlantPartForm />;
      case "industries":
        return <AddIndustryForm />;
      case "sub-industries":
        return <AddSubIndustryForm />;
      case "products":
        return <SimpleProductDiscovery />;
      default:
        return <AddPlantTypeForm />;
    }
  };

  return (
    <div className="container mx-auto p-6">
      <Helmet>
        <title>Admin Dashboard - Hemp Industry Database</title>
        <meta
          name="description"
          content="Admin dashboard for managing the Hemp Industry Database entries and data."
        />
      </Helmet>
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold tracking-tight text-primary">
            Admin Dashboard
          </h1>
          <p className="text-xl text-muted-foreground mt-2">
            Manage and update the Hemp Industry Database
          </p>
        </div>

        <Tabs defaultValue="analytics" className="w-full">
          <TabsList className="grid grid-cols-6 w-full max-w-5xl mx-auto">
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="add-content">Add Content</TabsTrigger>
            <TabsTrigger value="image-generation">Images</TabsTrigger>
            <TabsTrigger value="ai-agents">AI Agents</TabsTrigger>
            <TabsTrigger value="claude-ai">Claude AI</TabsTrigger>
            <TabsTrigger value="simple-demo">Simple Demo</TabsTrigger>
          </TabsList>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Database Analytics & Insights</CardTitle>
                <CardDescription>
                  Real-time analytics and data visualization for the Hemp Database
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DataVisualizationDashboard />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="add-content">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  Add Content
                </CardTitle>
                <CardDescription>
                  Add new entries to the Hemp Industry Database
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Dropdown Menu for Add Forms */}
                  <div className="flex items-center gap-4">
                    <label className="text-sm font-medium">Content Type:</label>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" className="w-64 justify-between">
                          <div className="flex items-center gap-2">
                            {addFormOptions.find(option => option.value === selectedAddForm)?.icon}
                            {addFormOptions.find(option => option.value === selectedAddForm)?.label}
                          </div>
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-64">
                        {addFormOptions.map((option) => (
                          <DropdownMenuItem
                            key={option.value}
                            onClick={() => setSelectedAddForm(option.value)}
                            className="flex items-start gap-3 p-3"
                          >
                            <div className="text-muted-foreground mt-0.5">
                              {option.icon}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs text-muted-foreground">
                                {option.description}
                              </div>
                            </div>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* Render Selected Form */}
                  <div className="border-t pt-6">
                    {renderAddForm()}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="image-generation">
            <ImageGenerationDashboard />
          </TabsContent>
          
          <TabsContent value="ai-agents">
            <AgentStatusCards />
          </TabsContent>
          
          <TabsContent value="claude-ai">
            <AIDashboard />
          </TabsContent>
          
          <TabsContent value="simple-demo">
            <SimpleChatDemo />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminPage;
