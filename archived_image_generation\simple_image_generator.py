#!/usr/bin/env python3
"""
Simple Image Generator - Direct approach without complex joins
"""

from supabase import create_client
import os
from datetime import datetime
import time

# Initialize Supabase
SUPABASE_URL = 'https://ktoqznqmlnxrtvubewyz.supabase.co'
SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTE3NzYsImV4cCI6MjA2NDA2Nzc3Nn0.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs'

supabase = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)

# Color mapping for plant parts
PLANT_COLORS = {
    1: '9370DB',  # Cannabinoids - Purple
    2: '228B22',  # Hemp Bast (Fiber) - Forest Green
    3: 'DA70D6',  # Hemp Flowers - Orchid
    4: 'D2691E',  # Hemp Hurd (Shivs) - Chocolate
    5: '32CD32',  # Hemp Leaves - Lime Green
    6: '8B4513',  # Hemp Roots - Sad<PERSON>
    7: 'FFD700',  # Hemp Seed - Gold
    8: '00CED1',  # Terpenes - Dark Turquoise
}

def generate_placeholder_url(product_name, plant_part_id):
    """Generate a placeholder image URL"""
    color = PLANT_COLORS.get(plant_part_id, '2E8B57')  # Default Sea Green
    formatted_name = product_name.replace(' ', '+').replace('/', '-')[:50]  # Limit length
    return f"https://via.placeholder.com/1024x1024/{color}/FFFFFF?text={formatted_name}"

def main():
    print("🖼️ Simple Hemp Image Generator")
    print("=" * 60)
    
    # Get products without images
    print("\nFetching products without images...")
    try:
        response = supabase.table('uses_products').select(
            'id, name, plant_part_id'
        ).or_('image_url.is.null,image_url.eq.').execute()
        
        products = response.data if response.data else []
        print(f"Found {len(products)} products without images\n")
        
        if not products:
            print("All products already have images!")
            return
        
        success_count = 0
        
        for i, product in enumerate(products, 1):
            try:
                # Generate placeholder URL
                image_url = generate_placeholder_url(
                    product['name'], 
                    product['plant_part_id']
                )
                
                # Update product with image
                update_response = supabase.table('uses_products').update({
                    'image_url': image_url,
                    'updated_at': datetime.now().isoformat()
                }).eq('id', product['id']).execute()
                
                print(f"✅ [{i}/{len(products)}] {product['name']}")
                success_count += 1
                
                # Small delay to avoid rate limiting
                time.sleep(0.1)
                
            except Exception as e:
                print(f"❌ [{i}/{len(products)}] {product['name']}: {str(e)}")
        
        print(f"\n🎉 Successfully generated images for {success_count} products!")
        
        # Also add entries to queue table for tracking
        print("\nAdding entries to image generation history...")
        for product in products[:success_count]:
            try:
                # Add to queue as completed
                queue_entry = {
                    'product_id': product['id'],
                    'status': 'completed',
                    'prompt': f"Placeholder image for {product['name']}",
                    'generation_provider': 'placeholder',
                    'generated_image_url': generate_placeholder_url(
                        product['name'], 
                        product['plant_part_id']
                    ),
                    'created_at': datetime.now().isoformat(),
                    'completed_at': datetime.now().isoformat()
                }
                supabase.table('image_generation_queue').insert(queue_entry).execute()
            except:
                pass  # Ignore queue errors
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()