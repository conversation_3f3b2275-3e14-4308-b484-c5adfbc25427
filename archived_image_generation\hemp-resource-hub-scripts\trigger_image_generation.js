#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load .env from current directory
dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function triggerImageGeneration() {
  console.log('🚀 Triggering Image Generation for Queued Products');
  console.log('=' .repeat(60));
  
  try {
    // Get pending images from queue
    const { data: pendingImages, error: queueError } = await supabase
      .from('image_generation_queue')
      .select('*')
      .eq('status', 'pending')
      .order('priority', { ascending: false })
      .order('created_at', { ascending: true })
      .limit(30);
    
    if (queueError) {
      console.error('❌ Error fetching queue:', queueError);
      return;
    }
    
    console.log(`📊 Found ${pendingImages?.length || 0} pending images in queue`);
    
    if (!pendingImages || pendingImages.length === 0) {
      console.log('✨ No pending images to generate');
      return;
    }
    
    // Trigger the edge function
    const response = await fetch(`${process.env.VITE_SUPABASE_URL}/functions/v1/hemp-image-generator`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.VITE_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        batchSize: 30
      })
    });
    
    if (!response.ok) {
      const error = await response.text();
      console.error('❌ Edge function error:', error);
      return;
    }
    
    const result = await response.json();
    console.log('\n✅ Edge function triggered successfully!');
    console.log(`📊 Processing ${result.processing || pendingImages.length} images...`);
    
    // Poll for status updates
    console.log('\n⏳ Monitoring progress (this may take a few minutes)...');
    
    let completed = 0;
    let failed = 0;
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes max
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
      
      // Check status of our batch
      const productIds = pendingImages.map(img => img.product_id);
      const { data: updatedQueue } = await supabase
        .from('image_generation_queue')
        .select('status')
        .in('product_id', productIds);
      
      const currentCompleted = updatedQueue?.filter(q => q.status === 'completed').length || 0;
      const currentFailed = updatedQueue?.filter(q => q.status === 'failed').length || 0;
      const currentPending = updatedQueue?.filter(q => q.status === 'pending').length || 0;
      const currentProcessing = updatedQueue?.filter(q => q.status === 'processing').length || 0;
      
      if (currentCompleted > completed || currentFailed > failed) {
        completed = currentCompleted;
        failed = currentFailed;
        console.log(`   📊 Status: ${completed} completed, ${failed} failed, ${currentProcessing} processing, ${currentPending} pending`);
      }
      
      // Check if all are done
      if (currentPending === 0 && currentProcessing === 0) {
        break;
      }
      
      attempts++;
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 Final Results:');
    console.log(`   ✅ Completed: ${completed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   ⏳ Still processing: ${pendingImages.length - completed - failed}`);
    
    // Show some of the updated products
    const { data: updatedProducts } = await supabase
      .from('uses_products')
      .select('name, id')
      .in('id', pendingImages.slice(0, 5).map(img => img.product_id));
    
    if (updatedProducts && updatedProducts.length > 0) {
      console.log('\n🎨 Sample products with new images:');
      updatedProducts.forEach(p => console.log(`   - ${p.name} (ID: ${p.id})`));
    }
    
    console.log('\n✨ Image generation process complete!');
    console.log('💡 View your products at http://localhost:3000/all-products');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the script
triggerImageGeneration().catch(console.error);