import os
import requests
import json
from datetime import datetime, timezone
from dotenv import load_dotenv
import time
from typing import Dict, List, Optional
from urllib.parse import urlparse

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL", "https://ktoqznqmlnxrtvubewyz.supabase.co")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_KEY:
    print("Error: SUPABASE_SERVICE_ROLE_KEY not found")
    exit(1)

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

class EnhancedResearchScraper:
    def __init__(self):
        self.sources = [
            {
                "name": "PubMed Central",
                "search_url": "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi",
                "detail_url": "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi",
                "params": {
                    "db": "pmc",
                    "retmode": "json",
                    "retmax": 20
                }
            },
            {
                "name": "Google Scholar",
                "search_url": "https://scholar.google.com/scholar",
                "params": {
                    "hl": "en",
                    "as_sdt": "0,5"
                }
            }
        ]
        
    def search_pubmed(self, query: str) -> List[Dict]:
        """Search PubMed Central for hemp research papers"""
        papers = []
        
        # Search for paper IDs
        search_params = {
            "db": "pmc",
            "term": f"{query} hemp cannabis",
            "retmode": "json",
            "retmax": 10
        }
        
        try:
            search_response = requests.get(
                "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi",
                params=search_params
            )
            search_data = search_response.json()
            
            if 'esearchresult' in search_data and 'idlist' in search_data['esearchresult']:
                ids = search_data['esearchresult']['idlist']
                
                # Fetch details for each paper
                for paper_id in ids[:5]:  # Limit to 5 papers
                    detail_params = {
                        "db": "pmc",
                        "id": paper_id,
                        "retmode": "xml"
                    }
                    
                    detail_response = requests.get(
                        "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi",
                        params=detail_params
                    )
                    
                    # Parse XML response (simplified)
                    paper_data = self.parse_pubmed_xml(detail_response.text, paper_id)
                    if paper_data:
                        papers.append(paper_data)
                    
                    time.sleep(0.5)  # Rate limiting
                    
        except Exception as e:
            print(f"Error searching PubMed: {e}")
            
        return papers
    
    def parse_pubmed_xml(self, xml_text: str, paper_id: str) -> Optional[Dict]:
        """Parse PubMed XML response (simplified parser)"""
        try:
            # Extract title
            title_start = xml_text.find("<article-title>")
            title_end = xml_text.find("</article-title>")
            title = xml_text[title_start+15:title_end] if title_start > -1 else f"Hemp Research Paper {paper_id}"
            
            # Extract abstract
            abstract_start = xml_text.find("<abstract>")
            abstract_end = xml_text.find("</abstract>")
            abstract = ""
            if abstract_start > -1:
                abstract_text = xml_text[abstract_start:abstract_end]
                # Clean up XML tags
                abstract = abstract_text.replace("<p>", "").replace("</p>", " ")
                abstract = abstract[:500] + "..." if len(abstract) > 500 else abstract
            
            # Extract authors
            authors = []
            author_blocks = xml_text.split("<contrib contrib-type=\"author\">")
            for block in author_blocks[1:4]:  # First 3 authors
                name_start = block.find("<surname>")
                name_end = block.find("</surname>")
                if name_start > -1:
                    surname = block[name_start+9:name_end]
                    given_start = block.find("<given-names>")
                    given_end = block.find("</given-names>")
                    given = block[given_start+13:given_end] if given_start > -1 else ""
                    authors.append(f"{given} {surname}".strip())
            
            # Extract year
            year_start = xml_text.find("<year>")
            year_end = xml_text.find("</year>")
            year = xml_text[year_start+6:year_end] if year_start > -1 else str(datetime.now().year)
            
            return {
                "title": self.clean_text(title),
                "abstract": self.clean_text(abstract),
                "authors": authors,
                "year": int(year) if year.isdigit() else datetime.now().year,
                "source": "PubMed Central",
                "pmcid": paper_id,
                "url": f"https://www.ncbi.nlm.nih.gov/pmc/articles/PMC{paper_id}/",
                "image_url": self.extract_first_figure_url(xml_text, paper_id)
            }
            
        except Exception as e:
            print(f"Error parsing PubMed XML: {e}")
            return None
    
    def extract_first_figure_url(self, xml_text: str, paper_id: str) -> Optional[str]:
        """Extract the first figure URL from the paper"""
        try:
            # Look for first figure
            fig_start = xml_text.find("<fig ")
            if fig_start > -1:
                fig_block = xml_text[fig_start:fig_start+1000]
                graphic_start = fig_block.find("xlink:href=\"")
                if graphic_start > -1:
                    graphic_end = fig_block.find("\"", graphic_start + 12)
                    graphic_id = fig_block[graphic_start+12:graphic_end]
                    # Construct PMC figure URL
                    return f"https://www.ncbi.nlm.nih.gov/pmc/articles/PMC{paper_id}/bin/{graphic_id}.jpg"
        except:
            pass
        return None
    
    def clean_text(self, text: str) -> str:
        """Clean text from XML tags and special characters"""
        # Remove common XML tags
        text = text.replace("<title>", "").replace("</title>", "")
        text = text.replace("<p>", "").replace("</p>", " ")
        text = text.replace("<italic>", "").replace("</italic>", "")
        text = text.replace("<bold>", "").replace("</bold>", "")
        text = text.replace("&lt;", "<").replace("&gt;", ">")
        text = text.replace("&amp;", "&")
        # Remove extra whitespace
        text = " ".join(text.split())
        return text.strip()
    
    def search_hemp_research(self, topics: List[str]) -> List[Dict]:
        """Search for hemp research across multiple topics"""
        all_papers = []
        
        for topic in topics:
            print(f"Searching for: {topic}")
            papers = self.search_pubmed(topic)
            all_papers.extend(papers)
            time.sleep(1)  # Rate limiting
        
        # Remove duplicates based on title
        seen_titles = set()
        unique_papers = []
        for paper in all_papers:
            if paper['title'] not in seen_titles:
                seen_titles.add(paper['title'])
                unique_papers.append(paper)
        
        return unique_papers
    
    def save_to_database(self, papers: List[Dict]) -> int:
        """Save research papers to Supabase"""
        saved_count = 0
        
        for paper in papers:
            # Check if paper already exists
            check_url = f"{SUPABASE_URL}/rest/v1/research_entries"
            params = {
                "title": f"eq.{paper['title']}",
                "select": "id"
            }
            
            response = requests.get(check_url, headers=headers, params=params)
            existing = response.json()
            
            if not existing:
                # Prepare data for database
                entry_data = {
                    "title": paper['title'],
                    "abstract_summary": paper['abstract'] or "No abstract available",
                    "authors": paper['authors'],
                    "publication_date": f"{paper['year']}-01-01",
                    "journal_name": paper.get('journal', 'PubMed Central'),
                    "source": paper['source'],
                    "full_text_url": paper['url'],
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "plant_type_id": 1,  # Default
                    "is_peer_reviewed": True,
                    "citation_count": 0,
                    "keywords": ["hemp", "cannabis", "research"],
                    "entry_type": "research_paper"
                }
                
                # Add image URL if available
                if paper.get('image_url'):
                    entry_data['metadata'] = {"image_url": paper['image_url']}
                
                # Insert into database
                insert_url = f"{SUPABASE_URL}/rest/v1/research_entries"
                response = requests.post(insert_url, headers=headers, json=entry_data)
                
                if response.status_code == 201:
                    saved_count += 1
                    print(f"✅ Saved: {paper['title'][:60]}...")
                else:
                    print(f"❌ Failed to save: {paper['title'][:60]}...")
                    print(f"Error: {response.text}")
            else:
                print(f"⏭️  Already exists: {paper['title'][:60]}...")
        
        return saved_count


def main():
    """Main function to run enhanced research scraping"""
    scraper = EnhancedResearchScraper()
    
    # Define research topics
    topics = [
        "hemp fiber composites",
        "hemp seed nutrition",
        "hemp construction materials", 
        "hemp bioplastics",
        "hemp textile innovation",
        "hemp phytoremediation",
        "hemp carbon sequestration",
        "industrial hemp applications"
    ]
    
    print("🔍 Starting enhanced research paper scraping...")
    print(f"Topics to search: {len(topics)}")
    
    # Search for papers
    papers = scraper.search_hemp_research(topics)
    print(f"\n📚 Found {len(papers)} unique research papers")
    
    # Save to database
    if papers:
        saved = scraper.save_to_database(papers)
        print(f"\n✅ Successfully saved {saved} new research papers")
    
    # Show current count
    count_url = f"{SUPABASE_URL}/rest/v1/research_entries"
    headers_count = headers.copy()
    headers_count["Prefer"] = "count=exact"
    
    response = requests.head(count_url, headers=headers_count)
    if 'content-range' in response.headers:
        total = response.headers['content-range'].split('/')[1]
        print(f"\n📊 Total research entries in database: {total}")


if __name__ == "__main__":
    main()