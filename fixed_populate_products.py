#!/usr/bin/env python3
"""
Fixed Product Population Script - Works with RLS and correct plant parts
"""

from supabase import create_client
import os
from datetime import datetime

# Use SERVICE ROLE KEY to bypass RLS
SUPABASE_URL = 'https://ktoqznqmlnxrtvubewyz.supabase.co'
SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5MTc3NiwiZXhwIjoyMDY0MDY3Nzc2fQ.hxEiOy6xebv7I4CYJEU49oCEXRvmU6iz2l8Kg-NnvTs'

# Create client with service role
supabase = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)

# Correct plant part mapping
PLANT_PARTS = {
    "Cannabinoids": 1,
    "Hemp Bast (Fiber)": 2,
    "Hemp Flowers": 3,
    "Hemp Hurd (Shivs)": 4,
    "Hemp Leaves": 5,
    "Hemp Roots": 6,
    "Hemp Seed": 7,
    "Terpenes": 8
}

# Products with correct plant part IDs
products = [
    # Textiles & Fashion
    {
        "name": "Premium Hemp T-Shirts",
        "description": "Soft, breathable t-shirts made from 100% hemp fiber. Naturally antimicrobial and UV resistant.",
        "plant_part_id": 2,  # Hemp Bast (Fiber)
        "benefits_advantages": ["Antimicrobial", "UV resistant", "Softer with each wash", "3x stronger than cotton"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["Uses 50% less water than cotton", "No pesticides needed", "Biodegradable"]
    },
    {
        "name": "Hemp Denim Jeans",
        "description": "Durable denim jeans blended with hemp fibers for superior strength and comfort.",
        "plant_part_id": 2,  # Hemp Bast (Fiber)
        "benefits_advantages": ["More durable than cotton denim", "Breathable", "Mold resistant"],
        "commercialization_stage": "growing",
        "sustainability_aspects": ["Sustainable alternative to cotton", "Low environmental impact"]
    },
    {
        "name": "Hemp Rope and Cordage",
        "description": "Strong, rot-resistant rope made from hemp fibers. Ideal for marine and outdoor applications.",
        "plant_part_id": 2,  # Hemp Bast (Fiber)
        "benefits_advantages": ["Salt water resistant", "UV stable", "Biodegradable", "High tensile strength"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["Natural and renewable", "No synthetic materials"]
    },
    # Construction Materials
    {
        "name": "Hempcrete Building Blocks",
        "description": "Lightweight, insulating building blocks made from hemp hurds and lime binder.",
        "plant_part_id": 4,  # Hemp Hurd (Shivs)
        "benefits_advantages": ["Carbon negative", "Fire resistant", "Pest resistant", "Excellent insulation"],
        "commercialization_stage": "growing",
        "sustainability_aspects": ["Sequesters CO2", "Renewable building material", "Energy efficient"]
    },
    {
        "name": "Hemp Fiber Insulation Batts",
        "description": "Natural insulation material made from hemp fibers. Safe, non-toxic alternative to fiberglass.",
        "plant_part_id": 2,  # Hemp Bast (Fiber)
        "benefits_advantages": ["Non-toxic", "Mold resistant", "Fire retardant", "Sound dampening"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["No harmful chemicals", "Compostable", "Carbon negative"]
    },
    # Food Products
    {
        "name": "Organic Hemp Hearts",
        "description": "Hulled hemp seeds rich in omega-3 and complete protein. Perfect for smoothies and salads.",
        "plant_part_id": 7,  # Hemp Seed
        "benefits_advantages": ["Complete protein", "Rich in omega-3", "Easy to digest", "Nutty flavor"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["Organic farming", "No GMO", "Minimal processing"]
    },
    {
        "name": "Cold-Pressed Hemp Seed Oil",
        "description": "Nutritious culinary oil with perfect omega fatty acid balance. Great for dressings and finishing.",
        "plant_part_id": 7,  # Hemp Seed
        "benefits_advantages": ["Perfect omega 3:6 ratio", "Rich in GLA", "Heart healthy", "Anti-inflammatory"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["Cold-pressed", "No chemicals", "Zero waste production"]
    },
    {
        "name": "Hemp Protein Powder 50%",
        "description": "Plant-based protein powder with 50% protein content. Contains all essential amino acids.",
        "plant_part_id": 7,  # Hemp Seed
        "benefits_advantages": ["Complete amino acid profile", "Easy to digest", "High in fiber", "No allergens"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["Byproduct of oil production", "No waste", "Sustainable protein source"]
    },
    # Personal Care
    {
        "name": "Hemp Seed Face Moisturizer",
        "description": "Lightweight facial moisturizer with hemp seed oil. Non-comedogenic and suitable for all skin types.",
        "plant_part_id": 7,  # Hemp Seed
        "benefits_advantages": ["Non-comedogenic", "Balances oil production", "Anti-aging", "Reduces inflammation"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["Natural ingredients", "Cruelty-free", "Recyclable packaging"]
    },
    {
        "name": "Hemp Oil Soap Bars",
        "description": "Handcrafted soap bars with hemp seed oil. Gentle cleansing with natural moisturizing.",
        "plant_part_id": 7,  # Hemp Seed
        "benefits_advantages": ["Moisturizing", "Gentle on skin", "Natural antibacterial", "Rich lather"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["Biodegradable", "No synthetic fragrances", "Minimal packaging"]
    },
    # Automotive
    {
        "name": "Hemp Composite Car Panels",
        "description": "Lightweight automotive body panels made from hemp fiber composites. 30% lighter than steel.",
        "plant_part_id": 2,  # Hemp Bast (Fiber)
        "benefits_advantages": ["30% weight reduction", "Impact resistant", "Recyclable", "Cost effective"],
        "commercialization_stage": "growing",
        "sustainability_aspects": ["Reduces vehicle emissions", "Renewable material", "End-of-life recyclable"]
    },
    # Paper Products
    {
        "name": "Hemp Copy Paper",
        "description": "High-quality printing and copy paper made from hemp fibers. Brighter and more durable than wood paper.",
        "plant_part_id": 2,  # Hemp Bast (Fiber)
        "benefits_advantages": ["More durable", "Doesn't yellow", "Higher tensile strength", "Acid-free"],
        "commercialization_stage": "growing",
        "sustainability_aspects": ["Saves forests", "Less processing chemicals", "4x more paper per acre"]
    },
    # Bioplastics
    {
        "name": "Hemp Bioplastic Pellets",
        "description": "Raw material pellets for injection molding. Made from hemp cellulose for biodegradable plastics.",
        "plant_part_id": 2,  # Hemp Bast (Fiber)
        "benefits_advantages": ["Biodegradable", "High strength", "Heat resistant", "Non-toxic"],
        "commercialization_stage": "research",
        "sustainability_aspects": ["Replaces petroleum plastics", "Compostable", "Carbon negative"]
    },
    # Environmental
    {
        "name": "Hemp Biochar Soil Amendment",
        "description": "Activated carbon from hemp stalks for soil improvement and carbon sequestration.",
        "plant_part_id": 4,  # Hemp Hurd (Shivs)
        "benefits_advantages": ["Improves soil structure", "Increases water retention", "Sequesters carbon", "pH buffer"],
        "commercialization_stage": "growing",
        "sustainability_aspects": ["Permanent carbon storage", "Reduces fertilizer need", "Waste utilization"]
    },
    {
        "name": "Hemp Animal Bedding",
        "description": "Super absorbent bedding for horses and small animals. More absorbent than wood shavings.",
        "plant_part_id": 4,  # Hemp Hurd (Shivs)
        "benefits_advantages": ["4x more absorbent", "Dust-free", "Compostable", "Odor control"],
        "commercialization_stage": "established",
        "sustainability_aspects": ["Agricultural waste product", "Compostable", "Low carbon footprint"]
    }
]

def main():
    print("🌿 Fixed Hemp Products Population Script")
    print("=" * 60)
    print("Using SERVICE ROLE KEY to bypass RLS policies\n")
    
    success_count = 0
    
    for i, product in enumerate(products, 1):
        try:
            # Add timestamp
            product['created_at'] = datetime.now().isoformat()
            
            # Insert into database
            response = supabase.table('uses_products').insert(product).execute()
            
            print(f"✅ [{i}/{len(products)}] Added: {product['name']}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ [{i}/{len(products)}] Failed: {product['name']} - {e}")
    
    print(f"\n🎉 Successfully added {success_count} products!")
    
    if success_count > 0:
        print("\n📸 Next step: Generate images for these products")
        print("Run: python image_generation\\hemp_image_generator.py")
    
if __name__ == "__main__":
    main()