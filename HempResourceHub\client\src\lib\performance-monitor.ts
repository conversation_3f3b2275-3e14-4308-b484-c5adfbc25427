import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private analyticsEndpoint = '/api/analytics/performance';
  
  constructor() {
    this.initializeMonitoring();
  }
  
  private initializeMonitoring() {
    // Core Web Vitals
    getCLS(this.handleMetric);
    getFID(this.handleMetric);
    getFCP(this.handleMetric);
    getLCP(this.handleMetric);
    getTTFB(this.handleMetric);
    
    // Custom metrics
    this.measureResourceTiming();
    this.measureApiResponseTimes();
  }
  
  private handleMetric = (metric: any) => {
    const performanceMetric: PerformanceMetric = {
      name: metric.name,
      value: metric.value,
      rating: this.getRating(metric.name, metric.value)
    };
    
    this.metrics.push(performanceMetric);
    this.sendToAnalytics(performanceMetric);
    
    // Log warnings for poor performance
    if (performanceMetric.rating === 'poor') {
      console.warn(`Poor ${metric.name} performance: ${metric.value}`);
    }
  };
  
  private getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds: Record<string, [number, number]> = {
      CLS: [0.1, 0.25],
      FID: [100, 300],
      FCP: [1800, 3000],
      LCP: [2500, 4000],
      TTFB: [800, 1800]
    };
    
    const [good, poor] = thresholds[name] || [0, 0];
    
    if (value <= good) return 'good';
    if (value <= poor) return 'needs-improvement';
    return 'poor';
  }
  
  private measureResourceTiming() {
    window.addEventListener('load', () => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      
      const slowResources = resources
        .filter(r => r.duration > 1000)
        .map(r => ({
          name: r.name,
          duration: r.duration,
          type: r.initiatorType
        }));
      
      if (slowResources.length > 0) {
        console.warn('Slow resources detected:', slowResources);
      }
    });
  }
  
  private measureApiResponseTimes() {
    // Intercept fetch to measure API response times
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const response = await originalFetch(...args);
      const duration = performance.now() - startTime;
      
      // Log slow API calls
      if (duration > 1000) {
        console.warn(`Slow API call: ${args[0]} took ${duration}ms`);
      }
      
      return response;
    };
  }
  
  private async sendToAnalytics(metric: PerformanceMetric) {
    // Only send analytics in production
    if (import.meta.env.DEV) {
      console.log('Performance metric:', metric);
      return;
    }
    
    try {
      await fetch(this.analyticsEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...metric,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      });
    } catch (error) {
      console.error('Failed to send analytics:', error);
    }
  }
  
  public getMetrics() {
    return this.metrics;
  }
  
  public logMetrics() {
    console.table(this.metrics);
  }
}

// Initialize performance monitoring
export const performanceMonitor = new PerformanceMonitor();