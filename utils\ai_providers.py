"""Multi-provider AI management with fallback support."""

import os
from typing import Dict, Any, Optional, List, Union
from abc import ABC, abstractmethod
import asyncio
from openai import Async<PERSON>penAI
try:
    from anthropic import AsyncAnthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    logger.warning("Anthropic not available")
import logging

logger = logging.getLogger(__name__)


class AIProvider(ABC):
    """Abstract base class for AI providers."""
    
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text from prompt."""
        pass
    
    @abstractmethod
    async def embed(self, text: str) -> List[float]:
        """Generate embeddings for text."""
        pass
    
    @abstractmethod
    def get_cost(self, tokens: int, operation: str = 'generation') -> float:
        """Calculate cost for token usage."""
        pass


class DeepSeekProvider(AIProvider):
    """DeepSeek API provider using OpenAI-compatible interface."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "deepseek-chat"):
        # DeepSeek uses OpenAI-compatible API
        self.client = AsyncOpenAI(
            api_key=api_key or os.getenv("DEEPSEEK_API_KEY"),
            base_url="https://api.deepseek.com/v1"
        )
        self.model = model
        
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using DeepSeek."""
        try:
            # Handle response_format for JSON mode
            response_format = kwargs.get('response_format')
            if response_format == 'json':
                # Add JSON instruction to prompt
                prompt = f"{prompt}\n\nIMPORTANT: Return ONLY the raw JSON object or array. Do not include any explanatory text, markdown formatting, or code blocks. Just the JSON data itself."
            
            response = await self.client.chat.completions.create(
                model=kwargs.get('model', self.model),
                messages=[{"role": "user", "content": prompt}],
                temperature=kwargs.get('temperature', 0.7),
                max_tokens=kwargs.get('max_tokens', 2000)
            )
            
            content = response.choices[0].message.content
            
            # Clean up DeepSeek's response if it includes markdown or extra text
            if response_format == 'json' and content:
                # Extract JSON from markdown code blocks if present
                import re
                
                # Try to find JSON in code blocks
                json_match = re.search(r'```(?:json)?\s*\n?([\s\S]*?)\n?```', content)
                if json_match:
                    content = json_match.group(1).strip()
                else:
                    # Try to find JSON by looking for { or [
                    json_start = content.find('{')
                    if json_start == -1:
                        json_start = content.find('[')
                    
                    if json_start != -1:
                        # Find the matching closing bracket
                        bracket_count = 0
                        in_string = False
                        escape_next = False
                        json_end = json_start
                        
                        for i in range(json_start, len(content)):
                            char = content[i]
                            
                            if escape_next:
                                escape_next = False
                                continue
                                
                            if char == '\\':
                                escape_next = True
                                continue
                                
                            if char == '"' and not escape_next:
                                in_string = not in_string
                                continue
                                
                            if not in_string:
                                if char in '{[':
                                    bracket_count += 1
                                elif char in '}]':
                                    bracket_count -= 1
                                    if bracket_count == 0:
                                        json_end = i + 1
                                        break
                        
                        if json_end > json_start:
                            content = content[json_start:json_end]
            
            return content
        except Exception as e:
            logger.error(f"DeepSeek generation error: {e}")
            raise
            
    async def embed(self, text: str) -> List[float]:
        """DeepSeek doesn't have embeddings, fallback to OpenAI."""
        raise NotImplementedError("Use OpenAI for embeddings")
        
    def get_cost(self, tokens: int, operation: str = 'generation') -> float:
        """Calculate DeepSeek costs."""
        # DeepSeek pricing (as of 2024)
        costs = {
            'deepseek-chat': {'input': 0.00014, 'output': 0.00028},  # Per 1K tokens
            'deepseek-coder': {'input': 0.00014, 'output': 0.00028}
        }
        
        model_costs = costs.get(self.model, costs['deepseek-chat'])
        input_cost = tokens * 0.75 * model_costs['input'] / 1000
        output_cost = tokens * 0.25 * model_costs['output'] / 1000
        return input_cost + output_cost


class OpenAIProvider(AIProvider):
    """OpenAI API provider."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4o"):
        self.client = AsyncOpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
        self.model = model
        self.embedding_model = "text-embedding-3-small"
        
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using OpenAI."""
        try:
            response = await self.client.chat.completions.create(
                model=kwargs.get('model', self.model),
                messages=[{"role": "user", "content": prompt}],
                temperature=kwargs.get('temperature', 0.7),
                max_tokens=kwargs.get('max_tokens', 2000)
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI generation error: {e}")
            raise
            
    async def embed(self, text: str) -> List[float]:
        """Generate embeddings using OpenAI."""
        response = await self.client.embeddings.create(
            model=self.embedding_model,
            input=text
        )
        return response.data[0].embedding
        
    def get_cost(self, tokens: int, operation: str = 'generation') -> float:
        """Calculate OpenAI costs."""
        costs = {
            'gpt-4o': {'input': 0.005, 'output': 0.015},
            'gpt-4o-mini': {'input': 0.00015, 'output': 0.0006},
            'embedding': 0.00002
        }
        
        if operation == 'embedding':
            return tokens * costs['embedding']
        
        # Rough estimate: 75% input, 25% output
        model_costs = costs.get(self.model, costs['gpt-4o'])
        input_cost = tokens * 0.75 * model_costs['input'] / 1000
        output_cost = tokens * 0.25 * model_costs['output'] / 1000
        return input_cost + output_cost


class AnthropicProvider(AIProvider):
    """Anthropic Claude API provider."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "claude-3-opus-20240229"):
        if not ANTHROPIC_AVAILABLE:
            raise ImportError("Anthropic package not installed")
        self.client = AsyncAnthropic(api_key=api_key or os.getenv("ANTHROPIC_API_KEY"))
        self.model = model
        
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using Claude."""
        try:
            response = await self.client.messages.create(
                model=kwargs.get('model', self.model),
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get('max_tokens', 2000),
                temperature=kwargs.get('temperature', 0.7)
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Anthropic generation error: {e}")
            raise
            
    async def embed(self, text: str) -> List[float]:
        """Claude doesn't have embeddings, fallback to OpenAI."""
        raise NotImplementedError("Use OpenAI for embeddings")
        
    def get_cost(self, tokens: int, operation: str = 'generation') -> float:
        """Calculate Anthropic costs."""
        costs = {
            'claude-3-opus-20240229': {'input': 0.015, 'output': 0.075},
            'claude-3-sonnet-20240229': {'input': 0.003, 'output': 0.015},
            'claude-3-haiku-20240307': {'input': 0.00025, 'output': 0.00125}
        }
        
        model_costs = costs.get(self.model, costs['claude-3-opus-20240229'])
        input_cost = tokens * 0.75 * model_costs['input'] / 1000
        output_cost = tokens * 0.25 * model_costs['output'] / 1000
        return input_cost + output_cost


class MultiProviderAI:
    """AI provider with automatic fallback."""
    
    def __init__(self, primary_provider: str = "deepseek", fallback_providers: List[str] = ["openai"]):
        self.providers = self._initialize_providers(primary_provider, fallback_providers)
        self.current_provider = 0
        
    def _initialize_providers(self, primary: str, fallbacks: List[str]) -> List[AIProvider]:
        """Initialize AI providers."""
        provider_map = {
            'deepseek': DeepSeekProvider,
            'openai': OpenAIProvider
        }
        
        # Only add Anthropic if available
        if ANTHROPIC_AVAILABLE:
            provider_map['anthropic'] = AnthropicProvider
        
        providers = []
        
        # Add primary provider
        if primary in provider_map:
            try:
                providers.append(provider_map[primary]())
                logger.info(f"Initialized primary provider: {primary}")
            except Exception as e:
                logger.warning(f"Failed to initialize primary provider {primary}: {e}")
            
        # Add fallback providers
        for fallback in fallbacks:
            if fallback in provider_map and fallback != primary:
                try:
                    providers.append(provider_map[fallback]())
                    logger.info(f"Initialized fallback provider: {fallback}")
                except Exception as e:
                    logger.warning(f"Failed to initialize fallback provider {fallback}: {e}")
                
        if not providers:
            raise ValueError("No valid AI providers configured")
            
        return providers
    
    async def generate(self, prompt: str, **kwargs) -> tuple[str, str, float]:
        """Generate text with automatic fallback."""
        for i, provider in enumerate(self.providers):
            try:
                # Skip providers without embedding support
                if kwargs.get('operation') == 'embedding' and isinstance(provider, (AnthropicProvider, DeepSeekProvider)):
                    continue
                    
                result = await provider.generate(prompt, **kwargs)
                
                # Estimate tokens (rough: 4 chars = 1 token)
                tokens = len(prompt + result) // 4
                cost = provider.get_cost(tokens)
                
                provider_name = provider.__class__.__name__
                logger.info(f"Successfully used {provider_name} for generation")
                
                return result, provider_name, cost
                
            except Exception as e:
                logger.warning(f"Provider {i} failed: {e}")
                if i == len(self.providers) - 1:
                    raise Exception("All AI providers failed")
                continue
    
    async def embed(self, text: str) -> tuple[List[float], float]:
        """Generate embeddings with fallback to OpenAI."""
        # Always use OpenAI for embeddings
        for provider in self.providers:
            if isinstance(provider, OpenAIProvider):
                embeddings = await provider.embed(text)
                cost = provider.get_cost(len(text) // 4, 'embedding')
                return embeddings, cost
                
        # Fallback: create OpenAI provider just for embeddings
        openai_provider = OpenAIProvider()
        embeddings = await openai_provider.embed(text)
        cost = openai_provider.get_cost(len(text) // 4, 'embedding')
        return embeddings, cost


# Convenience function to get AI provider
def get_ai_provider(provider_name: Optional[str] = None) -> Optional['MultiProviderAI']:
    """Get AI provider with specified preference."""
    try:
        if provider_name == "deepseek":
            # Only use DeepSeek, no fallbacks
            return MultiProviderAI(primary_provider="deepseek", fallback_providers=[])
        elif provider_name == "openai":
            # Only use OpenAI, no fallbacks
            return MultiProviderAI(primary_provider="openai", fallback_providers=[])
        else:
            # Default: try DeepSeek first, fallback to OpenAI
            return MultiProviderAI(primary_provider="deepseek", fallback_providers=["openai"])
    except Exception as e:
        logger.error(f"Failed to initialize AI providers: {e}")
        return None