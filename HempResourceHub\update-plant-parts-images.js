import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY environment variable is not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Custom placeholder image URL
const CUSTOM_PLACEHOLDER_URL = '/images/unknown-hemp-image.png';

async function updatePlantPartsImages() {
  try {
    console.log('Updating plant parts images...');
    
    // Update all plant parts to use the custom placeholder
    const { error: updateError } = await supabase
      .from('plant_parts')
      .update({ image_url: CUSTOM_PLACEHOLDER_URL })
      .like('image_url', '%placeholder.com%');
    
    if (updateError) {
      console.error('Error updating plant parts:', updateError);
      return;
    }
    
    console.log('✓ Successfully updated all plant parts images');
    
    // Verify the update
    const { data: plantParts, error: fetchError } = await supabase
      .from('plant_parts')
      .select('id, name, image_url');
    
    if (!fetchError) {
      console.log('\nUpdated plant parts:');
      plantParts.forEach(part => {
        console.log(`  - ${part.name}: ${part.image_url}`);
      });
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the update
updatePlantPartsImages();