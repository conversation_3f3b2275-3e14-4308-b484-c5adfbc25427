import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'wouter';

interface NavItem {
  id: string;
  label: string;
  href: string;
  icon: string;
  description: string;
  color: string;
}

interface GrowingNavElementsProps {
  items: NavItem[];
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
}

export const GrowingNavElements: React.FC<GrowingNavElementsProps> = ({
  items,
  orientation = 'horizontal',
  size = 'md'
}) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [expandedItem, setExpandedItem] = useState<string | null>(null);

  const sizeClasses = {
    sm: { container: 'p-2', item: 'w-8 h-8 text-sm', icon: 'text-lg' },
    md: { container: 'p-3', item: 'w-12 h-12 text-base', icon: 'text-xl' },
    lg: { container: 'p-4', item: 'w-16 h-16 text-lg', icon: 'text-2xl' }
  };

  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    initial: { 
      scale: 0, 
      opacity: 0,
      y: orientation === 'vertical' ? 20 : 0,
      x: orientation === 'horizontal' ? 20 : 0
    },
    animate: { 
      scale: 1, 
      opacity: 1,
      y: 0,
      x: 0,
      transition: { 
        type: "spring", 
        stiffness: 200, 
        damping: 15 
      }
    },
    hover: {
      scale: 1.1,
      transition: { type: "spring", stiffness: 400, damping: 10 }
    },
    tap: {
      scale: 0.95,
      transition: { type: "spring", stiffness: 400, damping: 10 }
    }
  };

  const leafVariants = {
    initial: { scale: 0, rotate: -45, opacity: 0 },
    animate: { 
      scale: 1, 
      rotate: 0, 
      opacity: 0.7,
      transition: { delay: 0.3, type: "spring", stiffness: 200 }
    },
    hover: {
      scale: 1.2,
      rotate: [0, 5, -5, 0],
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const stemVariants = {
    initial: { scaleY: 0, opacity: 0 },
    animate: { 
      scaleY: 1, 
      opacity: 0.6,
      transition: { delay: 0.2, duration: 0.5 }
    },
    hover: {
      scaleY: 1.1,
      opacity: 0.8,
      transition: { duration: 0.3 }
    }
  };

  const tooltipVariants = {
    initial: { opacity: 0, scale: 0.8, y: 10 },
    animate: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { type: "spring", stiffness: 300, damping: 20 }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8, 
      y: 10,
      transition: { duration: 0.2 }
    }
  };

  return (
    <motion.div
      className={`flex ${orientation === 'horizontal' ? 'flex-row space-x-4' : 'flex-col space-y-4'} ${sizeClasses[size].container}`}
      variants={containerVariants}
      initial="initial"
      animate="animate"
    >
      {items.map((item, index) => (
        <motion.div
          key={item.id}
          className="relative"
          variants={itemVariants}
          whileHover="hover"
          whileTap="tap"
          onHoverStart={() => setHoveredItem(item.id)}
          onHoverEnd={() => setHoveredItem(null)}
          onClick={() => setExpandedItem(expandedItem === item.id ? null : item.id)}
        >
          {/* Growing stem */}
          <motion.div
            className={`absolute ${
              orientation === 'horizontal' 
                ? 'bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-6' 
                : 'left-0 top-1/2 transform -translate-y-1/2 h-1 w-6'
            } bg-gradient-to-t from-green-800 to-green-600 rounded-full`}
            variants={stemVariants}
            style={{ transformOrigin: orientation === 'horizontal' ? 'bottom' : 'left' }}
          />

          {/* Decorative leaves */}
          <motion.div
            className={`absolute ${
              orientation === 'horizontal' 
                ? '-bottom-2 -left-1' 
                : '-left-2 -top-1'
            }`}
            variants={leafVariants}
          >
            <svg width="12" height="8" viewBox="0 0 12 8">
              <path
                d="M2 4 Q4 1 6 4 Q8 1 10 4 Q8 7 6 4 Q4 7 2 4"
                fill="#22c55e"
                opacity="0.7"
              />
            </svg>
          </motion.div>

          <motion.div
            className={`absolute ${
              orientation === 'horizontal' 
                ? '-bottom-2 -right-1' 
                : '-left-2 -bottom-1'
            }`}
            variants={leafVariants}
            transition={{ delay: 0.1 }}
          >
            <svg width="10" height="6" viewBox="0 0 10 6">
              <path
                d="M2 3 Q3 1 5 3 Q7 1 8 3 Q7 5 5 3 Q3 5 2 3"
                fill="#16a34a"
                opacity="0.6"
              />
            </svg>
          </motion.div>

          {/* Main navigation item */}
          <Link href={item.href}>
            <motion.div
              className={`${sizeClasses[size].item} rounded-full flex items-center justify-center cursor-pointer relative overflow-hidden border-2 transition-colors duration-300`}
              style={{ 
                backgroundColor: `${item.color}20`,
                borderColor: hoveredItem === item.id ? item.color : 'transparent'
              }}
            >
              {/* Background glow */}
              <motion.div
                className="absolute inset-0 rounded-full blur-md opacity-0"
                style={{ backgroundColor: item.color }}
                animate={{ 
                  opacity: hoveredItem === item.id ? 0.3 : 0 
                }}
                transition={{ duration: 0.3 }}
              />

              {/* Icon */}
              <span className={`${sizeClasses[size].icon} relative z-10`}>
                {item.icon}
              </span>

              {/* Ripple effect on click */}
              <motion.div
                className="absolute inset-0 rounded-full"
                style={{ backgroundColor: item.color }}
                initial={{ scale: 0, opacity: 0.5 }}
                animate={{ 
                  scale: expandedItem === item.id ? 2 : 0,
                  opacity: expandedItem === item.id ? 0 : 0.5
                }}
                transition={{ duration: 0.6 }}
              />
            </motion.div>
          </Link>

          {/* Tooltip */}
          <AnimatePresence>
            {hoveredItem === item.id && (
              <motion.div
                className={`absolute z-50 ${
                  orientation === 'horizontal'
                    ? 'top-full mt-2 left-1/2 transform -translate-x-1/2'
                    : 'left-full ml-2 top-1/2 transform -translate-y-1/2'
                } bg-gray-900/90 backdrop-blur-sm text-white p-3 rounded-lg shadow-lg border border-gray-700 min-w-max max-w-xs`}
                variants={tooltipVariants}
                initial="initial"
                animate="animate"
                exit="exit"
              >
                <h4 className="font-semibold text-green-400 mb-1 text-sm">
                  {item.label}
                </h4>
                <p className="text-xs text-gray-300">
                  {item.description}
                </p>
                
                {/* Tooltip arrow */}
                <div 
                  className={`absolute w-2 h-2 bg-gray-900 border-l border-t border-gray-700 transform rotate-45 ${
                    orientation === 'horizontal'
                      ? '-top-1 left-1/2 -translate-x-1/2'
                      : '-left-1 top-1/2 -translate-y-1/2'
                  }`}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Growing connection line to next item */}
          {index < items.length - 1 && (
            <motion.div
              className={`absolute ${
                orientation === 'horizontal'
                  ? 'top-1/2 left-full w-4 h-0.5 transform -translate-y-1/2'
                  : 'left-1/2 top-full h-4 w-0.5 transform -translate-x-1/2'
              } bg-gradient-to-r from-green-600 to-green-400 rounded-full`}
              initial={{ 
                [orientation === 'horizontal' ? 'scaleX' : 'scaleY']: 0 
              }}
              animate={{ 
                [orientation === 'horizontal' ? 'scaleX' : 'scaleY']: 1 
              }}
              transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
              style={{ 
                transformOrigin: orientation === 'horizontal' ? 'left' : 'top' 
              }}
            />
          )}
        </motion.div>
      ))}
    </motion.div>
  );
};

export default GrowingNavElements;
