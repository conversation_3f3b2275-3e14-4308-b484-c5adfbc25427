# agents/core/enhanced_base_agent.py
"""
Enhanced Base Agent with improved error handling, notifications, and monitoring
"""

import os
import asyncio
from typing import Optional, Dict, Any
import aiosmtplib
from email.message import EmailMessage
from datetime import datetime
from agents.core.base_agent import BaseAgent, track_performance, rate_limited


class NotificationService:
    """Handle critical error notifications"""
    
    async def send_email_notification(self, subject: str, body: str):
        """Send email for critical failures"""
        if not os.environ.get('NOTIFICATION_EMAIL'):
            return
            
        message = EmailMessage()
        message['From'] = '<EMAIL>'
        message['To'] = os.environ.get('NOTIFICATION_EMAIL')
        message['Subject'] = f"[Hemp Agent Alert] {subject}"
        message.set_content(body)
        
        try:
            await aiosmtplib.send(
                message,
                hostname=os.environ.get('SMTP_HOST', 'localhost'),
                port=int(os.environ.get('SMTP_PORT', 587))
            )
        except Exception as e:
            print(f"Failed to send notification: {e}")


class EnhancedBaseAgent(BaseAgent):
    """Enhanced base agent with better error handling"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.notification_service = NotificationService()
        self.retry_config = {
            'max_attempts': 3,
            'backoff_base': 2,
            'max_backoff': 60
        }
    
    async def execute_with_retry(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task with exponential backoff retry"""
        last_error = None
        
        for attempt in range(self.retry_config['max_attempts']):
            try:
                return await self.execute(task)
            except Exception as e:
                last_error = e
                wait_time = min(
                    self.retry_config['backoff_base'] ** attempt,
                    self.retry_config['max_backoff']
                )
                
                self.logger.warning(
                    f"Attempt {attempt + 1} failed: {e}. "
                    f"Retrying in {wait_time}s..."
                )
                
                if attempt == self.retry_config['max_attempts'] - 1:
                    # Final attempt failed, send notification
                    await self._handle_critical_failure(task, e)
                
                await asyncio.sleep(wait_time)
        
        raise last_error
    
    async def _handle_critical_failure(self, task: Dict[str, Any], error: Exception):
        """Handle critical failures with notifications"""
        error_context = {
            'task_id': task.get('task_id'),
            'task_type': task.get('type'),
            'agent': self.agent_name,
            'error': str(error),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Log to database
        await self._save_to_database('critical_errors', error_context)
        
        # Send notification
        await self.notification_service.send_email_notification(
            subject=f"{self.agent_name} Critical Failure",
            body=f"Task {task.get('task_id')} failed after all retries:\n\n{error}"
        )