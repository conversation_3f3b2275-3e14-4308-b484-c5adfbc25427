#!/usr/bin/env python3
"""
Comprehensive Monitoring Service for HempQuarterz
Tracks all agents, workflows, and system health
"""

import asyncio
import json
import logging
import os
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import aiohttp

from .supabase_client import get_supabase_client

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of metrics to track"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Metric:
    """Single metric data point"""
    name: str
    value: float
    type: MetricType
    labels: Dict[str, str] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    

@dataclass
class Alert:
    """Alert definition"""
    name: str
    condition: str
    message: str
    severity: AlertSeverity
    threshold: float
    current_value: float = 0
    triggered: bool = False
    last_triggered: Optional[datetime] = None


class MonitoringService:
    """
    Comprehensive monitoring for all HempQuarterz systems
    """
    
    def __init__(self, supabase_client=None):
        self.supabase = supabase_client or get_supabase_client()
        self.metrics = []
        self.alerts = []
        self._init_default_alerts()
        
    def _init_default_alerts(self):
        """Initialize default alert definitions"""
        self.alerts = [
            Alert(
                name="high_task_failure_rate",
                condition="task_failure_rate > 0.2",
                message="Task failure rate exceeds 20%",
                severity=AlertSeverity.WARNING,
                threshold=0.2
            ),
            Alert(
                name="agent_inactive",
                condition="agent_last_run > 24h",
                message="Agent has not run in 24 hours",
                severity=AlertSeverity.WARNING,
                threshold=24  # hours
            ),
            Alert(
                name="queue_backlog",
                condition="pending_tasks > 100",
                message="Task queue backlog exceeds 100 items",
                severity=AlertSeverity.ERROR,
                threshold=100
            ),
            Alert(
                name="image_generation_stalled",
                condition="image_processing_time > 300s",
                message="Image generation taking too long",
                severity=AlertSeverity.WARNING,
                threshold=300  # seconds
            ),
            Alert(
                name="database_error_rate",
                condition="db_error_rate > 0.05",
                message="Database error rate exceeds 5%",
                severity=AlertSeverity.ERROR,
                threshold=0.05
            ),
            Alert(
                name="low_product_discovery",
                condition="products_discovered_24h < 10",
                message="Less than 10 products discovered in 24 hours",
                severity=AlertSeverity.INFO,
                threshold=10
            )
        ]
        
    async def collect_metrics(self) -> Dict[str, Any]:
        """Collect all system metrics"""
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'agents': await self._collect_agent_metrics(),
            'tasks': await self._collect_task_metrics(),
            'images': await self._collect_image_metrics(),
            'database': await self._collect_database_metrics(),
            'workflows': await self._collect_workflow_metrics(),
            'system': await self._collect_system_metrics()
        }
        
        # Check alerts
        alerts = await self._check_alerts(metrics)
        metrics['alerts'] = alerts
        
        return metrics
        
    async def _collect_agent_metrics(self) -> Dict[str, Any]:
        """Collect metrics for all agents"""
        # Get agent status
        agents_result = self.supabase.table('agent_status').select('*').execute()
        
        agent_metrics = {
            'total': len(agents_result.data) if agents_result.data else 0,
            'active': 0,
            'inactive': 0,
            'by_agent': {}
        }
        
        if agents_result.data:
            for agent in agents_result.data:
                is_active = agent.get('is_active', False)
                agent_metrics['active' if is_active else 'inactive'] += 1
                
                # Per-agent metrics
                agent_metrics['by_agent'][agent['agent_name']] = {
                    'active': is_active,
                    'last_run': agent.get('last_run_at'),
                    'tasks_completed': agent.get('tasks_completed', 0),
                    'tasks_failed': agent.get('tasks_failed', 0),
                    'success_rate': self._calculate_success_rate(
                        agent.get('tasks_completed', 0),
                        agent.get('tasks_failed', 0)
                    ),
                    'avg_execution_time': agent.get('avg_execution_time_seconds', 0)
                }
                
        return agent_metrics
        
    async def _collect_task_metrics(self) -> Dict[str, Any]:
        """Collect task queue metrics"""
        # Task status counts
        statuses = ['pending', 'processing', 'completed', 'failed']
        task_metrics = {
            'by_status': {},
            'total': 0,
            'queue_depth': 0,
            'avg_wait_time': 0,
            'avg_processing_time': 0
        }
        
        for status in statuses:
            count_result = self.supabase.table('agent_tasks').select('count').eq('status', status).execute()
            count = len(count_result.data) if count_result.data else 0
            task_metrics['by_status'][status] = count
            task_metrics['total'] += count
            
        task_metrics['queue_depth'] = task_metrics['by_status'].get('pending', 0)
        
        # Recent task performance
        recent_tasks = self.supabase.table('agent_tasks').select('*').eq('status', 'completed').order('completed_at', desc=True).limit(100).execute()
        
        if recent_tasks.data:
            wait_times = []
            processing_times = []
            
            for task in recent_tasks.data:
                if task.get('started_at') and task.get('created_at'):
                    wait_time = (datetime.fromisoformat(task['started_at']) - datetime.fromisoformat(task['created_at'])).seconds
                    wait_times.append(wait_time)
                    
                if task.get('completed_at') and task.get('started_at'):
                    proc_time = (datetime.fromisoformat(task['completed_at']) - datetime.fromisoformat(task['started_at'])).seconds
                    processing_times.append(proc_time)
                    
            if wait_times:
                task_metrics['avg_wait_time'] = sum(wait_times) / len(wait_times)
            if processing_times:
                task_metrics['avg_processing_time'] = sum(processing_times) / len(processing_times)
                
        return task_metrics
        
    async def _collect_image_metrics(self) -> Dict[str, Any]:
        """Collect image generation metrics"""
        # Queue status
        queue_statuses = ['pending', 'processing', 'completed', 'failed']
        image_metrics = {
            'queue': {},
            'providers': {},
            'total_generated': 0,
            'total_failed': 0,
            'avg_generation_time': 0,
            'total_cost': 0
        }
        
        for status in queue_statuses:
            count_result = self.supabase.table('image_generation_queue').select('count').eq('status', status).execute()
            count = len(count_result.data) if count_result.data else 0
            image_metrics['queue'][status] = count
            
        # Provider performance
        provider_result = self.supabase.rpc('get_provider_stats').execute()
        if provider_result.data:
            for provider in provider_result.data:
                image_metrics['providers'][provider['name']] = {
                    'total': provider.get('total_generated', 0),
                    'success_rate': provider.get('success_rate', 0),
                    'avg_time': provider.get('avg_generation_time', 0),
                    'avg_cost': provider.get('avg_cost', 0),
                    'total_cost': provider.get('total_cost', 0)
                }
                image_metrics['total_generated'] += provider.get('total_generated', 0)
                image_metrics['total_cost'] += provider.get('total_cost', 0)
                
        # Products with/without images
        products_with_images = self.supabase.table('uses_products').select('count').not_.is_('image_url', 'null').execute()
        products_without_images = self.supabase.table('uses_products').select('count').is_('image_url', 'null').execute()
        
        image_metrics['products_with_images'] = len(products_with_images.data) if products_with_images.data else 0
        image_metrics['products_without_images'] = len(products_without_images.data) if products_without_images.data else 0
        
        return image_metrics
        
    async def _collect_database_metrics(self) -> Dict[str, Any]:
        """Collect database metrics"""
        db_metrics = {
            'tables': {},
            'total_records': 0,
            'growth_24h': 0,
            'error_rate': 0
        }
        
        # Record counts for key tables
        tables = [
            'hemp_plant_archetypes',
            'plant_parts',
            'industries',
            'uses_products',
            'hemp_companies',
            'research_entries'
        ]
        
        for table in tables:
            count_result = self.supabase.table(table).select('count').execute()
            count = len(count_result.data) if count_result.data else 0
            db_metrics['tables'][table] = count
            db_metrics['total_records'] += count
            
        # 24h growth for products
        yesterday = (datetime.now() - timedelta(days=1)).isoformat()
        new_products = self.supabase.table('uses_products').select('count').gte('created_at', yesterday).execute()
        db_metrics['growth_24h'] = len(new_products.data) if new_products.data else 0
        
        return db_metrics
        
    async def _collect_workflow_metrics(self) -> Dict[str, Any]:
        """Collect GitHub Actions and workflow metrics"""
        workflow_metrics = {
            'github_actions': {
                'last_runs': {},
                'success_rate': 0
            },
            'edge_functions': {
                'invocations_24h': 0,
                'errors_24h': 0,
                'avg_duration': 0
            }
        }
        
        # This would ideally fetch from GitHub API or workflow run logs
        # For now, we'll check agent run history
        workflow_types = ['hemp-automation', 'image-generation', 'weekly-summary']
        
        for workflow in workflow_types:
            # Check recent runs from agent history
            runs = self.supabase.table('agent_runs').select('*').eq('source', f'github_action_{workflow}').order('started_at', desc=True).limit(10).execute()
            
            if runs.data and runs.data[0]:
                workflow_metrics['github_actions']['last_runs'][workflow] = {
                    'last_run': runs.data[0].get('started_at'),
                    'status': runs.data[0].get('status', 'unknown'),
                    'duration': runs.data[0].get('duration_seconds', 0)
                }
                
        return workflow_metrics
        
    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """Collect overall system health metrics"""
        system_metrics = {
            'uptime': await self._calculate_uptime(),
            'total_automation_runs': 0,
            'automation_success_rate': 0,
            'daily_active_agents': 0,
            'total_products_discovered': 0,
            'total_companies_found': 0
        }
        
        # Total automation runs
        runs_result = self.supabase.table('agent_runs').select('count').execute()
        system_metrics['total_automation_runs'] = len(runs_result.data) if runs_result.data else 0
        
        # Success rate
        success_runs = self.supabase.table('agent_runs').select('count').eq('status', 'completed').execute()
        failed_runs = self.supabase.table('agent_runs').select('count').eq('status', 'failed').execute()
        
        success_count = len(success_runs.data) if success_runs.data else 0
        failed_count = len(failed_runs.data) if failed_runs.data else 0
        
        system_metrics['automation_success_rate'] = self._calculate_success_rate(success_count, failed_count)
        
        # Daily active agents
        today = datetime.now().date().isoformat()
        active_today = self.supabase.table('agent_runs').select('agent_type', count=True).gte('started_at', today).execute()
        if active_today.data:
            system_metrics['daily_active_agents'] = len(set(run['agent_type'] for run in active_today.data))
            
        # Total discoveries
        products_result = self.supabase.table('uses_products').select('count').execute()
        companies_result = self.supabase.table('hemp_companies').select('count').execute()
        
        system_metrics['total_products_discovered'] = len(products_result.data) if products_result.data else 0
        system_metrics['total_companies_found'] = len(companies_result.data) if companies_result.data else 0
        
        return system_metrics
        
    async def _check_alerts(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check alert conditions and return triggered alerts"""
        triggered_alerts = []
        
        for alert in self.alerts:
            triggered = False
            current_value = 0
            
            # Check specific alert conditions
            if alert.name == "high_task_failure_rate":
                total_tasks = metrics['tasks']['by_status'].get('completed', 0) + metrics['tasks']['by_status'].get('failed', 0)
                if total_tasks > 0:
                    failure_rate = metrics['tasks']['by_status'].get('failed', 0) / total_tasks
                    current_value = failure_rate
                    triggered = failure_rate > alert.threshold
                    
            elif alert.name == "agent_inactive":
                for agent_name, agent_data in metrics['agents']['by_agent'].items():
                    if agent_data['last_run']:
                        last_run = datetime.fromisoformat(agent_data['last_run'])
                        hours_since = (datetime.now() - last_run).total_seconds() / 3600
                        if hours_since > alert.threshold:
                            triggered = True
                            current_value = hours_since
                            break
                            
            elif alert.name == "queue_backlog":
                current_value = metrics['tasks']['queue_depth']
                triggered = current_value > alert.threshold
                
            elif alert.name == "image_generation_stalled":
                processing = metrics['images']['queue'].get('processing', 0)
                if processing > 0 and metrics['images']['avg_generation_time'] > alert.threshold:
                    triggered = True
                    current_value = metrics['images']['avg_generation_time']
                    
            elif alert.name == "low_product_discovery":
                current_value = metrics['database']['growth_24h']
                triggered = current_value < alert.threshold
                
            # Update alert status
            alert.current_value = current_value
            alert.triggered = triggered
            
            if triggered:
                alert.last_triggered = datetime.now()
                triggered_alerts.append({
                    'name': alert.name,
                    'severity': alert.severity.value,
                    'message': alert.message,
                    'current_value': current_value,
                    'threshold': alert.threshold,
                    'triggered_at': alert.last_triggered.isoformat()
                })
                
        return triggered_alerts
        
    async def _calculate_uptime(self) -> float:
        """Calculate system uptime percentage"""
        # Check recent agent runs for failures
        last_24h = (datetime.now() - timedelta(days=1)).isoformat()
        
        runs_result = self.supabase.table('agent_runs').select('status').gte('started_at', last_24h).execute()
        
        if not runs_result.data:
            return 100.0
            
        total_runs = len(runs_result.data)
        successful_runs = len([r for r in runs_result.data if r['status'] == 'completed'])
        
        return (successful_runs / total_runs * 100) if total_runs > 0 else 100.0
        
    def _calculate_success_rate(self, success: int, failed: int) -> float:
        """Calculate success rate percentage"""
        total = success + failed
        return (success / total * 100) if total > 0 else 0.0
        
    async def generate_report(self, period: str = '24h') -> str:
        """Generate a monitoring report"""
        metrics = await self.collect_metrics()
        
        report = f"""
# HempQuarterz Monitoring Report
Generated: {metrics['timestamp']}
Period: Last {period}

## System Overview
- Uptime: {metrics['system']['uptime']:.1f}%
- Total Automation Runs: {metrics['system']['total_automation_runs']:,}
- Success Rate: {metrics['system']['automation_success_rate']:.1f}%
- Active Agents Today: {metrics['system']['daily_active_agents']}

## Agent Status
- Total Agents: {metrics['agents']['total']}
- Active: {metrics['agents']['active']}
- Inactive: {metrics['agents']['inactive']}

### Agent Performance
"""
        
        for agent_name, agent_data in metrics['agents']['by_agent'].items():
            status = "🟢" if agent_data['active'] else "🔴"
            report += f"\n{status} **{agent_name}**\n"
            report += f"  - Success Rate: {agent_data['success_rate']:.1f}%\n"
            report += f"  - Tasks: {agent_data['tasks_completed']} completed, {agent_data['tasks_failed']} failed\n"
            report += f"  - Avg Time: {agent_data['avg_execution_time']:.1f}s\n"
            
        report += f"""
## Task Queue
- Pending: {metrics['tasks']['by_status'].get('pending', 0)}
- Processing: {metrics['tasks']['by_status'].get('processing', 0)}
- Completed: {metrics['tasks']['by_status'].get('completed', 0)}
- Failed: {metrics['tasks']['by_status'].get('failed', 0)}
- Avg Wait Time: {metrics['tasks']['avg_wait_time']:.1f}s
- Avg Processing Time: {metrics['tasks']['avg_processing_time']:.1f}s

## Image Generation
- Products with Images: {metrics['images']['products_with_images']:,}
- Products without Images: {metrics['images']['products_without_images']:,}
- Total Generated: {metrics['images']['total_generated']:,}
- Total Cost: ${metrics['images']['total_cost']:.2f}

### Queue Status
- Pending: {metrics['images']['queue'].get('pending', 0)}
- Processing: {metrics['images']['queue'].get('processing', 0)}
- Completed: {metrics['images']['queue'].get('completed', 0)}
- Failed: {metrics['images']['queue'].get('failed', 0)}

## Database Stats
- Total Records: {metrics['database']['total_records']:,}
- Products Added (24h): {metrics['database']['growth_24h']}
- Total Products: {metrics['system']['total_products_discovered']:,}
- Total Companies: {metrics['system']['total_companies_found']:,}
"""
        
        if metrics['alerts']:
            report += "\n## 🚨 Active Alerts\n"
            for alert in metrics['alerts']:
                emoji = {
                    'info': 'ℹ️',
                    'warning': '⚠️',
                    'error': '❌',
                    'critical': '🔴'
                }.get(alert['severity'], '❓')
                
                report += f"\n{emoji} **{alert['message']}**\n"
                report += f"   Current: {alert['current_value']:.2f}, Threshold: {alert['threshold']}\n"
        else:
            report += "\n## ✅ No Active Alerts\n"
            
        return report
        
    async def export_metrics(self, format: str = 'json') -> str:
        """Export metrics in various formats"""
        metrics = await self.collect_metrics()
        
        if format == 'json':
            return json.dumps(metrics, indent=2, default=str)
            
        elif format == 'prometheus':
            # Prometheus format for metrics
            output = []
            
            # Agent metrics
            output.append(f"# HELP hemp_agents_total Total number of agents")
            output.append(f"# TYPE hemp_agents_total gauge")
            output.append(f"hemp_agents_total {metrics['agents']['total']}")
            
            output.append(f"# HELP hemp_agents_active Number of active agents")
            output.append(f"# TYPE hemp_agents_active gauge")
            output.append(f"hemp_agents_active {metrics['agents']['active']}")
            
            # Task metrics
            output.append(f"# HELP hemp_tasks_pending Number of pending tasks")
            output.append(f"# TYPE hemp_tasks_pending gauge")
            output.append(f"hemp_tasks_pending {metrics['tasks']['by_status'].get('pending', 0)}")
            
            # Image metrics
            output.append(f"# HELP hemp_images_total Total images generated")
            output.append(f"# TYPE hemp_images_total counter")
            output.append(f"hemp_images_total {metrics['images']['total_generated']}")
            
            # System metrics
            output.append(f"# HELP hemp_system_uptime System uptime percentage")
            output.append(f"# TYPE hemp_system_uptime gauge")
            output.append(f"hemp_system_uptime {metrics['system']['uptime']}")
            
            return '\n'.join(output)
            
        else:
            return str(metrics)
            

# Convenience functions
async def get_system_health() -> Dict[str, Any]:
    """Quick system health check"""
    service = MonitoringService()
    metrics = await service.collect_metrics()
    
    return {
        'healthy': len(metrics.get('alerts', [])) == 0,
        'uptime': metrics['system']['uptime'],
        'alerts': metrics.get('alerts', []),
        'summary': {
            'agents_active': metrics['agents']['active'],
            'tasks_pending': metrics['tasks']['by_status'].get('pending', 0),
            'products_total': metrics['system']['total_products_discovered']
        }
    }
    

async def check_alerts() -> List[Dict[str, Any]]:
    """Check for any triggered alerts"""
    service = MonitoringService()
    metrics = await service.collect_metrics()
    return metrics.get('alerts', [])