name: Test Basic Setup (No API Required)
# Fixed: Removed all indentation from heredoc content to prevent YAML syntax errors

on:
  workflow_dispatch:
    inputs:
      debug_mode:
        description: 'Enable debug output'
        required: false
        default: false
        type: boolean

env:
  SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
  SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
  PYTHONPATH: ${{ github.workspace }}
  # Set mock mode to avoid OpenAI API calls
  MOCK_MODE: true

jobs:
  test-setup:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt || echo "Some dependencies failed, continuing..."
        
    - name: Debug environment
      if: ${{ github.event.inputs.debug_mode == 'true' }}
      run: |
        echo "=== Environment Variables ==="
        echo "PYTHONPATH: $PYTHONPATH"
        echo "SUPABASE_URL exists: ${{ env.SUPABASE_URL != '' }}"
        echo "Working directory: $(pwd)"
        echo ""
        echo "=== Directory Structure ==="
        ls -la
        echo ""
        echo "=== Python Info ==="
        python --version
        pip --version
        
    - name: Test Python imports
      run: |
        echo "🔍 Testing Python module imports..."
        cat > test_imports.py << 'EOF'
import sys
print('Python path:', sys.path[:3])
try:
    from lib import __init__
    print('✅ Can import lib module')
except Exception as e:
    print('⚠️  lib module import issue:', e)
    
try:
    from agents import __init__
    print('✅ Can import agents module')
except Exception as e:
    print('⚠️  agents module import issue:', e)
EOF
        python test_imports.py
        
    - name: Make hemp executable
      run: |
        chmod +x hemp
        echo "✅ Made hemp executable"
        
    - name: Test hemp CLI help
      run: |
        echo "🔍 Testing hemp CLI..."
        python hemp --help || echo "⚠️  Hemp CLI has issues but continuing..."
        
    - name: Test basic monitoring (no API needed)
      run: |
        echo "🔍 Testing monitoring commands..."
        # These should work without OpenAI API
        python hemp monitor --format health || echo "Health check unavailable"
        
    - name: Test database connection
      run: |
        echo "🔍 Testing database connection..."
        cat > test_db_connection.py << 'EOF'
import os
print('SUPABASE_URL:', 'Set' if os.getenv('SUPABASE_URL') else 'Not set')
print('SUPABASE_ANON_KEY:', 'Set' if os.getenv('SUPABASE_ANON_KEY') else 'Not set')

# Basic connection test without heavy operations
try:
    from lib.supabase_client import get_supabase_client
    client = get_supabase_client()
    print('✅ Supabase client created')
except Exception as e:
    print('⚠️  Supabase connection issue:', e)
EOF
        python test_db_connection.py
        
    - name: List available commands
      run: |
        echo "📋 Available hemp commands (if working):"
        python hemp --help 2>/dev/null | grep -E "^\s+[a-z]+" || echo "Command listing unavailable"
        
    - name: Create test summary
      if: always()
      run: |
        echo "## 🧪 Basic Setup Test Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Python Version:** $(python --version 2>&1)" >> $GITHUB_STEP_SUMMARY
        echo "**Working Directory:** $(pwd)" >> $GITHUB_STEP_SUMMARY
        echo "**PYTHONPATH Set:** ✅" >> $GITHUB_STEP_SUMMARY
        echo "**Secrets Configured:** ${{ env.SUPABASE_URL != '' && env.SUPABASE_ANON_KEY != '' && '✅' || '❌' }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
        echo "1. If this test passes, try running the Monitoring workflow" >> $GITHUB_STEP_SUMMARY
        echo "2. Check the logs above for any specific errors" >> $GITHUB_STEP_SUMMARY
        echo "3. OpenAI API is not required for this test" >> $GITHUB_STEP_SUMMARY