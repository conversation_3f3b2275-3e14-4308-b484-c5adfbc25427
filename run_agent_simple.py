#!/usr/bin/env python3
"""
Simple runner for research agent that bypasses Supabase issues.
Shows how the agent works with web scraping.
"""

import asyncio
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Disable some verbose logging
logging.getLogger('aiohttp').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)

async def main():
    """Run a simplified version of the research agent"""
    logger.info("=== Hemp Research Agent Demo ===\n")
    
    try:
        # Import agent components
        from agents.research.unified_research_agent import (
            UnifiedResearchAgent, ResearchConfig, ResearchFeatures
        )
        
        # Mock Supabase for demo
        class MockSupabase:
            def __init__(self):
                self.saved_products = []
                
            def table(self, name):
                self.current_table = name
                return self
                
            def select(self, *args):
                return self
                
            def eq(self, field, value):
                return self
                
            def insert(self, data):
                self.saved_products.append(data)
                return self
                
            async def execute(self):
                class Result:
                    def __init__(self, data):
                        self.data = data
                return Result(self.saved_products[-1:] if self.saved_products else [])
        
        # Create mock client
        mock_supabase = MockSupabase()
        
        # Configure research agent
        config = ResearchConfig(
            enabled_features={
                ResearchFeatures.WEB_SCRAPING,
                ResearchFeatures.FEED_MONITORING,
                ResearchFeatures.COMPANY_EXTRACTION
            },
            use_ai_analysis=False,  # No AI needed
            max_results=10,
            cache_duration=3600
        )
        
        # Create agent
        logger.info("Creating research agent with web scraping features...")
        agent = UnifiedResearchAgent(mock_supabase, ai_provider=None, config=config)
        
        # Define search task
        task = {
            'action': 'discover_products',
            'params': {
                'query': 'industrial hemp fiber products applications',
                'limit': 10
            }
        }
        
        logger.info(f"Searching for: '{task['params']['query']}'")
        logger.info("This may take a moment as we fetch from real sources...\n")
        
        # Execute search
        result = await agent.execute(task)
        
        # Display results
        print("\n" + "="*70)
        print("SEARCH RESULTS")
        print("="*70)
        print(f"Status: {result['status']}")
        print(f"Products found: {result['products_found']}")
        print(f"Products that would be saved to DB: {result['products_saved']}")
        
        if result['products_found'] > 0:
            print("\n" + "-"*70)
            print("DISCOVERED PRODUCTS:")
            print("-"*70)
            
            for i, product in enumerate(result['products'], 1):
                print(f"\n{i}. {product.get('name', 'Unknown Product')}")
                print(f"   Plant Part: {product.get('plant_part', 'N/A')}")
                print(f"   Industry: {product.get('industry', 'N/A')}")
                print(f"   Source: {product.get('data_source', 'N/A')}")
                print(f"   URL: {product.get('source_url', 'N/A')[:60]}...")
                
                if product.get('description'):
                    desc = product['description']
                    if len(desc) > 150:
                        desc = desc[:147] + "..."
                    print(f"   Description: {desc}")
                
                if product.get('companies'):
                    print(f"   Companies: {', '.join(product['companies'][:3])}")
        else:
            print("\nNo products found. Possible reasons:")
            print("- Network connectivity issues")
            print("- Source websites may be down or changed")
            print("- Search query too specific")
            
        print("\n" + "="*70)
        print("DEMO COMPLETE")
        print("="*70)
        
        # Show what would be saved
        if mock_supabase.saved_products:
            print(f"\n{len(mock_supabase.saved_products)} products were prepared for database storage.")
            print("In production, these would be saved to your Supabase database.")
        
    except Exception as e:
        logger.error(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Hemp Research Agent - Web Scraping Demo")
    print("This demonstrates product discovery without AI")
    print("-" * 50)
    asyncio.run(main())