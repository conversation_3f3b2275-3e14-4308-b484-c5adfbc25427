import { useCompany } from '@/hooks/use-companies';
import { Badge } from '@/components/ui/badge';
import { AttributedImage } from '@/components/ui/attributed-image';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Building2, Package, MapPin, Calendar, Globe, CheckCircle, ExternalLink, Loader2 } from 'lucide-react';

interface CompanyDetailModalProps {
  companyId: number | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CompanyDetailModal({ companyId, open, onOpenChange }: CompanyDetailModalProps) {
  const { data: company, isLoading } = useCompany(companyId || -1);

  if (!companyId || !open) return null;

  const getStageColor = (stage?: string) => {
    switch (stage?.toLowerCase()) {
      case 'established': return 'bg-green-100 text-green-800';
      case 'growing': return 'bg-blue-100 text-blue-800';
      case 'research': return 'bg-yellow-100 text-yellow-800';
      case 'speculative': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type?: string) => {
    switch (type) {
      case 'manufacturer': return 'bg-blue-100 text-blue-800';
      case 'distributor': return 'bg-green-100 text-green-800';
      case 'retailer': return 'bg-purple-100 text-purple-800';
      case 'brand': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-black/95 border-gray-700">
        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-green-400" />
          </div>
        ) : company ? (
          <>
            <DialogHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <DialogTitle className="text-2xl text-gray-100 flex items-center gap-3">
                    {company.logo_url && (
                      <div className="h-12 w-12 rounded overflow-hidden bg-gray-800">
                        <AttributedImage
                          src={company.logo_url}
                          alt={`${company.name} logo`}
                          attribution={
                            company.logo_attribution || {
                              source_name: company.website ? new URL(company.website).hostname : company.name,
                              source_url: company.website,
                              license: "Fair Use - Company Logo",
                              attribution_required: true,
                              alt_text: `${company.name} company logo`
                            }
                          }
                          className="h-full w-full object-contain"
                          aspectRatio="square"
                          fallbackSrc="/images/company-placeholder.png"
                        />
                      </div>
                    )}
                    {company.name}
                    {company.verified && (
                      <CheckCircle className="h-6 w-6 text-green-400" />
                    )}
                  </DialogTitle>
                  <div className="flex flex-wrap gap-2 mt-3">
                    {company.company_type && (
                      <Badge className={getTypeColor(company.company_type)}>
                        {company.company_type}
                      </Badge>
                    )}
                    {company.country && (
                      <Badge variant="outline" className="border-gray-600 text-gray-300">
                        <MapPin className="h-3 w-3 mr-1" />
                        {company.city ? `${company.city}, ` : ''}{company.country}
                      </Badge>
                    )}
                    {company.founded_year && (
                      <Badge variant="outline" className="border-gray-600 text-gray-300">
                        <Calendar className="h-3 w-3 mr-1" />
                        Founded {company.founded_year}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </DialogHeader>

            <Tabs defaultValue="overview" className="mt-6">
              <TabsList className="bg-black/40 border-gray-700">
                <TabsTrigger value="overview" className="data-[state=active]:bg-green-600">
                  Overview
                </TabsTrigger>
                <TabsTrigger value="products" className="data-[state=active]:bg-green-600">
                  Products ({company.products?.length || 0})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-6 space-y-6">
                {company.description && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-100 mb-2">About</h3>
                    <p className="text-gray-300">{company.description}</p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-100 mb-3">Company Details</h3>
                    <dl className="space-y-2">
                      {company.website && (
                        <div>
                          <dt className="text-sm text-gray-400">Website</dt>
                          <dd>
                            <a
                              href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-green-400 hover:text-green-300 flex items-center gap-1"
                            >
                              <Globe className="h-4 w-4" />
                              {company.website}
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          </dd>
                        </div>
                      )}
                      {company.city && (
                        <div>
                          <dt className="text-sm text-gray-400">Location</dt>
                          <dd className="text-gray-300">
                            {company.city}
                            {company.state_province && `, ${company.state_province}`}
                            {company.country && `, ${company.country}`}
                            {company.postal_code && ` ${company.postal_code}`}
                          </dd>
                        </div>
                      )}
                      {company.company_type && (
                        <div>
                          <dt className="text-sm text-gray-400">Type</dt>
                          <dd className="text-gray-300 capitalize">{company.company_type}</dd>
                        </div>
                      )}
                    </dl>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-100 mb-3">Product Summary</h3>
                    <div className="bg-black/40 rounded-lg p-4 border border-gray-700">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-gray-400">Total Products</span>
                        <span className="text-2xl font-bold text-green-400">
                          {company.products?.length || 0}
                        </span>
                      </div>
                      {company.products && company.products.length > 0 && (
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between text-gray-400">
                            <span>Primary Products</span>
                            <span>{company.products.filter(p => p.is_primary).length}</span>
                          </div>
                          <div className="flex justify-between text-gray-400">
                            <span>As Manufacturer</span>
                            <span>{company.products.filter(p => p.relationship_type === 'manufacturer').length}</span>
                          </div>
                          <div className="flex justify-between text-gray-400">
                            <span>As Distributor</span>
                            <span>{company.products.filter(p => p.relationship_type === 'distributor').length}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="products" className="mt-6">
                <ScrollArea className="h-[400px] pr-4">
                  {company.products && company.products.length > 0 ? (
                    <div className="space-y-4">
                      {company.products.map((product) => (
                        <div
                          key={product.id}
                          className="bg-black/40 rounded-lg p-4 border border-gray-700 hover:border-green-400 transition-colors"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="text-lg font-semibold text-gray-100 flex items-center gap-2">
                                {product.name}
                                {product.is_primary && (
                                  <Badge className="bg-green-600 text-white text-xs">Primary</Badge>
                                )}
                              </h4>
                              {product.description && (
                                <p className="text-gray-400 text-sm mt-1 line-clamp-2">
                                  {product.description}
                                </p>
                              )}
                              <div className="flex flex-wrap gap-2 mt-3">
                                {product.relationship_type && (
                                  <Badge variant="outline" className="text-xs border-gray-600">
                                    {product.relationship_type}
                                  </Badge>
                                )}
                                {product.plant_part && (
                                  <Badge variant="outline" className="text-xs border-gray-600">
                                    <Package className="h-3 w-3 mr-1" />
                                    {product.plant_part}
                                  </Badge>
                                )}
                                {product.industry && (
                                  <Badge variant="outline" className="text-xs border-gray-600">
                                    <Building2 className="h-3 w-3 mr-1" />
                                    {product.industry}
                                  </Badge>
                                )}
                                {product.commercialization_stage && (
                                  <Badge className={`text-xs ${getStageColor(product.commercialization_stage)}`}>
                                    {product.commercialization_stage}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Package className="h-12 w-12 text-gray-600 mx-auto mb-3" />
                      <p className="text-gray-400">No products associated with this company yet.</p>
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </>
        ) : (
          <div className="text-center py-20">
            <p className="text-gray-400">Company not found</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}