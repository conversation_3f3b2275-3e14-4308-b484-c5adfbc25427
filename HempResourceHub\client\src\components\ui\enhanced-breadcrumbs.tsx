import React from "react";
import { Link, useLocation } from "wouter";
import { 
  ChevronRight, 
  Home, 
  Package, 
  Leaf, 
  Factory, 
  FileText,
  Users,
  Settings,
  Search
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  isActive?: boolean;
  metadata?: {
    count?: number;
    type?: string;
    description?: string;
  };
  children?: BreadcrumbItem[];
}

interface EnhancedBreadcrumbsProps {
  items?: BreadcrumbItem[];
  showHome?: boolean;
  showContext?: boolean;
  className?: string;
}

export function EnhancedBreadcrumbs({
  items: customItems,
  showHome = true,
  showContext = true,
  className
}: EnhancedBreadcrumbsProps) {
  const [location] = useLocation();

  // Auto-generate breadcrumbs from current location if no custom items provided
  const items = customItems || generateBreadcrumbsFromLocation(location);

  const getIconForPath = (path: string): React.ReactNode => {
    if (path === '/') return <Home className="h-4 w-4" />;
    if (path.includes('/product')) return <Package className="h-4 w-4" />;
    if (path.includes('/plant')) return <Leaf className="h-4 w-4" />;
    if (path.includes('/industries')) return <Factory className="h-4 w-4" />;
    if (path.includes('/research')) return <FileText className="h-4 w-4" />;
    if (path.includes('/companies')) return <Users className="h-4 w-4" />;
    if (path.includes('/admin')) return <Settings className="h-4 w-4" />;
    if (path.includes('/hemp-dex') || path.includes('/products')) return <Search className="h-4 w-4" />;
    return <Package className="h-4 w-4" />;
  };

  return (
    <nav className={cn("flex items-center space-x-1 text-xs md:text-sm", className)}>
      {showHome && (
        <>
          <Link href="/">
            <Button
              variant="ghost"
              size="sm"
              className="h-7 md:h-8 px-2 text-gray-400 hover:text-white hover:bg-gray-800 transition-colors"
            >
              <Home className="h-3 w-3 md:h-4 md:w-4" />
            </Button>
          </Link>
          {items.length > 0 && (
            <ChevronRight className="h-3 w-3 md:h-4 md:w-4 text-gray-600" />
          )}
        </>
      )}

      {items.map((item, index) => (
        <React.Fragment key={index}>
          <BreadcrumbItem
            item={item}
            isLast={index === items.length - 1}
            showContext={showContext}
          />
          {index < items.length - 1 && (
            <ChevronRight className="h-3 w-3 md:h-4 md:w-4 text-gray-600" />
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}

interface BreadcrumbItemProps {
  item: BreadcrumbItem;
  isLast: boolean;
  showContext: boolean;
}

function BreadcrumbItem({ item, isLast, showContext }: BreadcrumbItemProps) {
  const hasChildren = item.children && item.children.length > 0;
  const hasMetadata = item.metadata && showContext;

  if (hasChildren) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-8 px-2 flex items-center gap-1",
              isLast 
                ? "text-white bg-gray-800" 
                : "text-gray-400 hover:text-white hover:bg-gray-800"
            )}
          >
            {item.icon}
            <span className="max-w-[150px] truncate">{item.label}</span>
            {hasMetadata && item.metadata?.count && (
              <Badge variant="secondary" className="ml-1 h-4 text-xs">
                {item.metadata.count}
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          {item.children.map((child, childIndex) => (
            <DropdownMenuItem key={childIndex} asChild>
              <Link href={child.href || '#'}>
                <div className="flex items-center gap-2 w-full">
                  {child.icon}
                  <span className="flex-1">{child.label}</span>
                  {child.metadata?.count && (
                    <Badge variant="outline" className="h-4 text-xs">
                      {child.metadata.count}
                    </Badge>
                  )}
                </div>
              </Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  if (item.href && !isLast) {
    return (
      <Link href={item.href}>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2 flex items-center gap-1 text-gray-400 hover:text-white hover:bg-gray-800"
        >
          {item.icon}
          <span className="max-w-[150px] truncate">{item.label}</span>
          {hasMetadata && item.metadata?.count && (
            <Badge variant="secondary" className="ml-1 h-4 text-xs">
              {item.metadata.count}
            </Badge>
          )}
        </Button>
      </Link>
    );
  }

  return (
    <div className={cn(
      "flex items-center gap-1 px-2 py-1 rounded",
      isLast ? "text-white bg-gray-800" : "text-gray-400"
    )}>
      {item.icon}
      <span className="max-w-[150px] truncate font-medium">{item.label}</span>
      {hasMetadata && (
        <div className="flex items-center gap-1 ml-1">
          {item.metadata?.count && (
            <Badge variant="secondary" className="h-4 text-xs">
              {item.metadata.count}
            </Badge>
          )}
          {item.metadata?.type && (
            <Badge variant="outline" className="h-4 text-xs">
              {item.metadata.type}
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}

function generateBreadcrumbsFromLocation(location: string): BreadcrumbItem[] {
  const segments = location.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];

  let currentPath = '';
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Decode URL parameters
    const decodedSegment = decodeURIComponent(segment);
    
    let label = decodedSegment;
    let icon: React.ReactNode = null;
    let metadata: BreadcrumbItem['metadata'] = undefined;

    // Customize labels and icons based on path segments
    switch (segment) {
      case 'plant-parts':
        label = 'Plant Parts';
        icon = <Leaf className="h-4 w-4" />;
        break;
      case 'plant-part':
        label = 'Plant Part';
        icon = <Leaf className="h-4 w-4" />;
        break;
      case 'plant-types':
        label = 'Plant Types';
        icon = <Leaf className="h-4 w-4" />;
        break;
      case 'products':
        label = 'Products';
        icon = <Package className="h-4 w-4" />;
        break;
      case 'product':
        label = 'Product';
        icon = <Package className="h-4 w-4" />;
        break;
      case 'industries':
        label = 'Industries';
        icon = <Factory className="h-4 w-4" />;
        break;
      case 'research':
        label = 'Research';
        icon = <FileText className="h-4 w-4" />;
        break;
      case 'companies':
        label = 'Companies';
        icon = <Users className="h-4 w-4" />;
        break;
      case 'hemp-companies':
        label = 'Hemp Companies';
        icon = <Users className="h-4 w-4" />;
        break;
      case 'admin':
        label = 'Admin';
        icon = <Settings className="h-4 w-4" />;
        break;
      case 'hemp-dex':
      case 'hemp-dex-unified':
      case 'hemp-dex-enhanced':
        label = 'Products';
        icon = <Search className="h-4 w-4" />;
        break;
      case 'about':
        label = 'About';
        icon = <FileText className="h-4 w-4" />;
        break;
      default:
        // If it's a number, it might be an ID
        if (/^\d+$/.test(segment)) {
          label = `#${segment}`;
          metadata = { type: 'ID' };
        } else {
          // Capitalize and format the segment
          label = segment
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
        }
        icon = getIconForCurrentPath(currentPath);
    }

    breadcrumbs.push({
      label,
      href: currentPath,
      icon,
      metadata,
      isActive: index === segments.length - 1
    });
  });

  return breadcrumbs;
}

function getIconForCurrentPath(path: string): React.ReactNode {
  if (path.includes('/product')) return <Package className="h-4 w-4" />;
  if (path.includes('/plant')) return <Leaf className="h-4 w-4" />;
  if (path.includes('/industries')) return <Factory className="h-4 w-4" />;
  if (path.includes('/research')) return <FileText className="h-4 w-4" />;
  if (path.includes('/companies')) return <Users className="h-4 w-4" />;
  if (path.includes('/admin')) return <Settings className="h-4 w-4" />;
  return <Package className="h-4 w-4" />;
}

// Context-aware breadcrumb hook for specific pages
export function useContextualBreadcrumbs(
  pageType: 'product' | 'plant-part' | 'industry' | 'research',
  data?: any
): BreadcrumbItem[] {
  const [location] = useLocation();

  return React.useMemo(() => {
    const baseBreadcrumbs = generateBreadcrumbsFromLocation(location);
    
    if (!data) return baseBreadcrumbs;

    // Enhance breadcrumbs with contextual information
    switch (pageType) {
      case 'product':
        return baseBreadcrumbs.map((item, index) => {
          if (index === baseBreadcrumbs.length - 1 && data.name) {
            return {
              ...item,
              label: data.name,
              metadata: {
                type: 'Product',
                description: data.description?.slice(0, 100) + '...'
              }
            };
          }
          return item;
        });
      
      case 'plant-part':
        return baseBreadcrumbs.map((item, index) => {
          if (index === baseBreadcrumbs.length - 1 && data.name) {
            return {
              ...item,
              label: data.name,
              metadata: {
                type: 'Plant Part',
                count: data.productCount
              }
            };
          }
          return item;
        });
      
      default:
        return baseBreadcrumbs;
    }
  }, [location, pageType, data]);
}
