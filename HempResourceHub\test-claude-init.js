// Simple test to check Claude initialization
import('dotenv/config');

async function testClaude() {
  console.log('Testing Claude initialization...');
  console.log('ANTHROPIC_API_KEY:', process.env.ANTHROPIC_API_KEY ? 'Set ✓' : 'Not set ✗');
  
  try {
    // Dynamic import to match ESM
    const { default: Anthropic } = await import('@anthropic-ai/sdk');
    
    const anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });
    
    console.log('Claude client created successfully! ✓');
    
    // Test a simple API call
    console.log('\nTesting API call...');
    const response = await anthropic.messages.create({
      model: 'claude-3-haiku-20240307',
      max_tokens: 100,
      messages: [{ role: 'user', content: 'Say hello in 5 words or less' }],
    });
    
    console.log('API Response:', response.content[0].text);
    console.log('Success! Claude API is working ✓');
  } catch (error) {
    console.error('Error:', error.message);
    if (error.status === 401) {
      console.error('Authentication failed. Check your API key.');
    }
  }
}

testClaude();