import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function retryAugmentImages() {
  console.log('🔄 Retrying Augment Product Images with Different Provider');
  console.log('='.repeat(60));
  
  // Reset retry items to pending and specify stable_diffusion as provider
  const { data: retryItems, error: updateError } = await supabase
    .from('image_generation_queue')
    .update({ 
      status: 'pending',
      retry_with_provider: 'stable_diffusion',
      attempt_count: 0,
      error_message: null,
      updated_at: new Date().toISOString()
    })
    .in('product_id', [190, 191, 192, 193, 194, 195, 196, 197, 198, 199])
    .eq('status', 'retry')
    .select();
    
  if (updateError) {
    console.error('❌ Error updating queue:', updateError);
    return;
  }
  
  console.log(`✅ Reset ${retryItems?.length || 0} items to pending with stable_diffusion provider`);
  
  // Trigger generation with stable_diffusion provider
  console.log('\n🚀 Triggering generation with Stable Diffusion...');
  
  try {
    const response = await fetch(`${process.env.VITE_SUPABASE_URL}/functions/v1/hemp-image-generator`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.VITE_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        batchSize: 10,
        forceProvider: 'stable_diffusion'
      })
    });
    
    if (!response.ok) {
      const error = await response.text();
      console.error('❌ Edge function error:', error);
      
      // If Stable Diffusion fails, try with Imagen 3
      console.log('\n🔄 Trying with Imagen 3 instead...');
      
      await supabase
        .from('image_generation_queue')
        .update({ 
          retry_with_provider: 'imagen_3',
          updated_at: new Date().toISOString()
        })
        .in('product_id', [190, 191, 192, 193, 194, 195, 196, 197, 198, 199])
        .eq('status', 'pending');
      
      const response2 = await fetch(`${process.env.VITE_SUPABASE_URL}/functions/v1/hemp-image-generator`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          batchSize: 10,
          forceProvider: 'imagen_3'
        })
      });
      
      if (!response2.ok) {
        console.error('❌ Imagen 3 also failed:', await response2.text());
        console.log('\n💡 It appears the API keys are not configured in the edge function.');
        console.log('To fix this:');
        console.log('1. Go to https://app.supabase.com/project/ktoqznqmlnxrtvubewyz/functions/hemp-image-generator/details');
        console.log('2. Click on "Settings" tab');
        console.log('3. Add these environment variables:');
        console.log('   - OPENAI_API_KEY (for DALL-E)');
        console.log('   - STABILITY_API_KEY (for Stable Diffusion)');
        console.log('   - GEMINI_API_KEY (for Imagen 3)');
        console.log('4. Save and redeploy the function');
        return;
      }
      
      const result2 = await response2.json();
      console.log('✅ Imagen 3 triggered:', result2.message);
    } else {
      const result = await response.json();
      console.log('✅ Stable Diffusion triggered:', result.message);
    }
    
    // Monitor progress
    console.log('\n⏳ Monitoring progress...');
    
    let attempts = 0;
    while (attempts < 12) { // 1 minute max
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const { data: status } = await supabase
        .from('image_generation_queue')
        .select('status, generated_image_url')
        .in('product_id', [190, 191, 192, 193, 194, 195, 196, 197, 198, 199]);
      
      const completed = status?.filter(s => s.status === 'completed' && s.generated_image_url && !s.generated_image_url.includes('placeholder')).length || 0;
      const pending = status?.filter(s => s.status === 'pending').length || 0;
      const processing = status?.filter(s => s.status === 'processing').length || 0;
      
      console.log(`   Status: ${completed} AI images, ${processing} processing, ${pending} pending`);
      
      if (pending === 0 && processing === 0) {
        break;
      }
      
      attempts++;
    }
    
    // Final check
    const { data: finalProducts } = await supabase
      .from('uses_products')
      .select('id, name, image_url')
      .in('id', [190, 191, 192, 193, 194, 195, 196, 197, 198, 199])
      .order('id');
      
    console.log('\n📊 Final Results:');
    console.log('='.repeat(60));
    
    finalProducts?.forEach(p => {
      const hasAI = p.image_url && !p.image_url.includes('placeholder');
      console.log(`[${p.id}] ${p.name}: ${hasAI ? '✅ AI Generated' : '❌ Still placeholder'}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

retryAugmentImages().catch(console.error);