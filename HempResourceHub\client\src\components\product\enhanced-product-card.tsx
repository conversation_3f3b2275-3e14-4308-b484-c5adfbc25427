import { <PERSON> } from "wouter";
import { HempProduct, PlantPart, Industry } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Leaf, TreePine, Factory, Building2, CheckCircle } from "lucide-react";

interface EnhancedProductCardProps {
  product: HempProduct;
  plantParts?: PlantPart[];
  industries?: Industry[];
  viewMode?: 'grid' | 'list';
}

const EnhancedProductCard = ({ product, plantParts, industries, viewMode = 'grid' }: EnhancedProductCardProps) => {
  // Extract stage from product properties
  const stage = product.commercializationStage || 'Research';
  
  const stageColors = {
    'Growing': 'bg-green-500/20 text-green-400 border-green-500/50',
    'Established': 'bg-blue-500/20 text-blue-400 border-blue-500/50',
    'Research': 'bg-purple-500/20 text-purple-400 border-purple-500/50',
    'Speculative': 'bg-orange-500/20 text-orange-400 border-orange-500/50'
  };
  
  // Get plant part and industry names
  const plantPart = plantParts?.find(p => p.id === product.plantPartId);
  const industry = industries?.find(i => i.id === product.industryId);
  
  // List view
  if (viewMode === 'list') {
    return (
      <Link href={`/product/${product.id}`}>
        <div className="group bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 cursor-pointer transition-all duration-300 hover:bg-gray-800/50 border border-gray-800 hover:border-gray-700">
          <div className="flex items-center gap-4">
            {/* Image - Larger for better visibility */}
            <div className="w-32 h-32 flex-shrink-0 rounded-lg overflow-hidden bg-gray-800">
              <img
                src={product.image_url || '/images/unknown-hemp-image.png'}
                alt={product.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/images/unknown-hemp-image.png';
                }}
              />
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-4 mb-2">
                <h3 className="hemp-brand-ultra font-semibold text-lg">
                  {product.name}
                </h3>
                <Badge className={`${stageColors[stage as keyof typeof stageColors] || stageColors.Research} flex-shrink-0`}>
                  {stage}
                </Badge>
              </div>
              
              <div className="flex items-center gap-4 text-xs">
                {plantPart && (
                  <div className="flex items-center gap-1 text-gray-500">
                    <TreePine className="w-3 h-3" />
                    <span>{plantPart.name}</span>
                  </div>
                )}
                {industry && (
                  <div className="flex items-center gap-1 text-gray-500">
                    <Factory className="w-3 h-3" />
                    <span>{industry.name}</span>
                  </div>
                )}
                {(product as any).hemp_company_products && (product as any).hemp_company_products.length > 0 && (
                  <div className="flex items-center gap-1 text-gray-500">
                    <Building2 className="w-3 h-3" />
                    <span>
                      {(product as any).hemp_company_products[0].hemp_companies.name}
                      {(product as any).hemp_company_products.length > 1 && ` +${(product as any).hemp_company_products.length - 1}`}
                    </span>
                  </div>
                )}
                <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-green-400 transition-colors ml-auto" />
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }
  
  // Grid view (default)
  return (
    <Link href={`/product/${product.id}`}>
      <div className="group bg-gray-900/40 backdrop-blur-sm rounded-xl overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-2xl hover:shadow-green-500/20 hover:-translate-y-1 border border-gray-800/50 hover:border-green-500/30">
        {/* Image - Larger aspect ratio for better visibility */}
        <div className="aspect-[3/2] relative overflow-hidden bg-gray-800">
          <img
            src={product.image_url || '/images/unknown-hemp-image.png'}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            onError={(e) => {
              e.currentTarget.src = '/images/unknown-hemp-image.png';
            }}
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />

          {/* Stage badge */}
          <div className="absolute top-3 left-3">
            <Badge className={`${stageColors[stage as keyof typeof stageColors] || stageColors.Research} backdrop-blur-sm border text-xs`}>
              {stage}
            </Badge>
          </div>

          {/* Product name overlay on image */}
          <div className="absolute bottom-3 left-3 right-3">
            <h3 className="hemp-brand-ultra font-semibold text-lg line-clamp-2">
              {product.name}
            </h3>
          </div>
        </div>
        
        {/* Minimal Content */}
        <div className="p-3">
          {/* Category badges and arrow */}
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-1">
              {plantPart && (
                <Badge variant="secondary" className="bg-gray-800/60 text-gray-300 text-xs px-2 py-1">
                  <TreePine className="w-3 h-3 mr-1" />
                  {plantPart.name}
                </Badge>
              )}
              {industry && (
                <Badge variant="secondary" className="bg-gray-800/60 text-gray-300 text-xs px-2 py-1">
                  <Factory className="w-3 h-3 mr-1" />
                  {industry.name}
                </Badge>
              )}
            </div>

            {/* Arrow icon */}
            <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-green-400 transition-colors flex-shrink-0" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default EnhancedProductCard;