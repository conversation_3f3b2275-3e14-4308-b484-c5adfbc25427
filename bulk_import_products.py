import csv
import os
from supabase import create_client, Client
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Supabase client
url = os.getenv("VITE_SUPABASE_URL", "https://ktoqznqmlnxrtvubewyz.supabase.co")
key = os.getenv("VITE_SUPABASE_ANON_KEY")

supabase: Client = create_client(url, key)

def import_from_csv(csv_file_path):
    """Import products from a CSV file
    
    Expected CSV columns:
    name, description, plant_parts, applications, benefits, companies, market_stage, sustainability_score
    """
    
    # Plant part mapping
    plant_part_map = {
        'cannabinoids': 1,
        'fiber': 2,
        'bast': 2,
        'flowers': 3,
        'leaves': 4, 
        'stalks': 5,
        'seeds': 6,
        'roots': 7,
        'hurds': 8,
        'whole plant': 9
    }
    
    imported_count = 0
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        for row in reader:
            try:
                # Parse plant parts
                plant_parts_str = row.get('plant_parts', '')
                plant_part_ids = []
                for part in plant_parts_str.split(','):
                    part = part.strip().lower()
                    if part in plant_part_map:
                        plant_part_ids.append(plant_part_map[part])
                
                # Prepare product data
                product_data = {
                    "name": row['name'],
                    "description": row['description'],
                    "plant_part_ids": plant_part_ids or [1],  # Default to cannabinoids if empty
                    "industry_sub_category_id": 1,
                    "applications": [a.strip() for a in row.get('applications', '').split(',') if a.strip()],
                    "benefits": [b.strip() for b in row.get('benefits', '').split(',') if b.strip()],
                    "market_stage": row.get('market_stage', 'research'),
                    "image_url": "/images/unknown-hemp-image.png",
                }
                
                # Add sustainability score if provided
                if row.get('sustainability_score'):
                    try:
                        product_data['sustainability_score'] = int(row['sustainability_score'])
                    except:
                        pass
                
                # Check if product exists
                existing = supabase.table('uses_products').select("id").ilike('name', f'%{row["name"]}%').execute()
                
                if not existing.data:
                    # Insert product
                    result = supabase.table('uses_products').insert(product_data).execute()
                    
                    if result.data:
                        product_id = result.data[0]['id']
                        print(f"✅ Imported: {row['name']}")
                        imported_count += 1
                        
                        # Handle companies
                        companies_str = row.get('companies', '')
                        if companies_str:
                            for company_name in companies_str.split(','):
                                company_name = company_name.strip()
                                if company_name:
                                    # Check/create company
                                    company_result = supabase.table('hemp_companies').select("id").eq('name', company_name).execute()
                                    
                                    if not company_result.data:
                                        new_company = supabase.table('hemp_companies').insert({
                                            "name": company_name,
                                            "company_type": "manufacturer",
                                            "verified": False
                                        }).execute()
                                        
                                        if new_company.data:
                                            company_id = new_company.data[0]['id']
                                    else:
                                        company_id = company_result.data[0]['id']
                                    
                                    # Create relationship
                                    supabase.table('hemp_company_products').insert({
                                        "company_id": company_id,
                                        "product_id": product_id,
                                        "relationship_type": "primary"
                                    }).execute()
                else:
                    print(f"⏭️  Skipped (exists): {row['name']}")
                    
            except Exception as e:
                print(f"❌ Error importing {row.get('name', 'Unknown')}: {e}")
    
    print(f"\n✅ Import complete! Imported {imported_count} products.")


# Create a sample CSV file
def create_sample_csv():
    """Create a sample CSV file for testing"""
    
    sample_data = [
        {
            'name': 'Hemp Biochar Soil Amendment',
            'description': 'Carbon-rich soil enhancer made from pyrolyzed hemp stalks, improves soil fertility and carbon sequestration',
            'plant_parts': 'stalks, hurds',
            'applications': 'agriculture, soil remediation, carbon capture',
            'benefits': 'improves water retention, increases crop yields, sequesters carbon',
            'companies': 'BioHemp Solutions, Terra Carbon',
            'market_stage': 'growing',
            'sustainability_score': '98'
        },
        {
            'name': 'Hemp-Based 3D Printing Filament',
            'description': 'Biodegradable 3D printing material made from hemp fiber-reinforced PLA composite',
            'plant_parts': 'fiber',
            'applications': '3D printing, prototyping, manufacturing',
            'benefits': 'biodegradable, stronger than pure PLA, renewable resource',
            'companies': '3D Hemp Co',
            'market_stage': 'established',
            'sustainability_score': '90'
        }
    ]
    
    with open('sample_products.csv', 'w', newline='', encoding='utf-8') as file:
        fieldnames = ['name', 'description', 'plant_parts', 'applications', 'benefits', 'companies', 'market_stage', 'sustainability_score']
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        
        writer.writeheader()
        writer.writerows(sample_data)
    
    print("Created sample_products.csv")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
        if os.path.exists(csv_file):
            import_from_csv(csv_file)
        else:
            print(f"Error: File '{csv_file}' not found")
    else:
        print("Creating sample CSV file...")
        create_sample_csv()
        print("\nTo import products, run:")
        print("python bulk_import_products.py sample_products.csv")