import { useAllHempProducts } from "@/hooks/use-product-data";
import { Skeleton } from "@/components/ui/skeleton";
import { useState, useMemo } from "react";
import Breadcrumb from "@/components/ui/breadcrumb";
import EnhancedProductCard from "@/components/product/enhanced-product-card";
import { Search, Filter, Package, ChevronLeft, ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const AllProductsPage = () => {
  const { data: products, isLoading, error } = useAllHempProducts();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStage, setSelectedStage] = useState<string | null>(null);
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);



  // Generate alphabet array for filtering
  const alphabet = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));

  // Filter and paginate products
  const { filteredProducts, totalPages, paginatedProducts } = useMemo(() => {
    if (!products) return { filteredProducts: [], totalPages: 0, paginatedProducts: [] };

    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchTerm.toLowerCase());
      // Handle both possible field names for commercialization stage
      const productStage = product.commercialization_stage || product.commercializationStage;
      const matchesStage = !selectedStage || productStage === selectedStage;
      const matchesLetter = !selectedLetter || product.name.charAt(0).toUpperCase() === selectedLetter;
      return matchesSearch && matchesStage && matchesLetter;
    });

    // Sort alphabetically
    filtered.sort((a, b) => a.name.localeCompare(b.name));

    const totalPages = Math.ceil(filtered.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedProducts = filtered.slice(startIndex, startIndex + itemsPerPage);

    return { filteredProducts: filtered, totalPages, paginatedProducts };
  }, [products, searchTerm, selectedStage, selectedLetter, currentPage, itemsPerPage]);

  // Get unique commercialization stages (ordered by development progression)
  const stages = useMemo(() => {
    if (!products) return [];
    // Handle both possible field names for commercialization stage
    const uniqueStages = [...new Set(products.map(p => p.commercialization_stage || p.commercializationStage).filter(Boolean))];

    // Sort by development progression
    const stageOrder = ['Research', 'Development', 'Pilot', 'Commercial', 'Mature'];
    return uniqueStages.sort((a, b) => {
      const aIndex = stageOrder.indexOf(a);
      const bIndex = stageOrder.indexOf(b);
      return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex);
    });
  }, [products]);

  // Reset page when filters change
  const handleFilterChange = (filterType: string, value: any) => {
    setCurrentPage(1);
    if (filterType === 'search') setSearchTerm(value);
    if (filterType === 'stage') setSelectedStage(value);
    if (filterType === 'letter') setSelectedLetter(value);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  return (
    <div className="py-12 bg-black/20 backdrop-blur-sm min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Breadcrumb
          items={[
            { label: "Home", href: "/" },
            { label: "All Products" },
          ]}
        />

        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Package className="h-8 w-8 text-green-400" />
            <h1 className="text-3xl font-bold text-gray-100">
              <span className="hemp-brand-secondary">Hemp</span> Products Database
            </h1>
          </div>
          <p className="text-gray-200">
            Exploring {products?.length || 0} innovative hemp-based products across all industries
          </p>

        </div>

        {/* Search Bar - Full Width */}
        <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 mb-6 border border-green-500/30">
          <div className="space-y-4">
            {/* Search Input - Full Width */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search products by name or description..."
                value={searchTerm}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20 text-base"
              />
              {searchTerm && (
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-200"
                >
                  ✕
                </button>
              )}
            </div>

            {/* Stage Filter - Separate Row */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-3">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-gray-400" />
                <span className="text-gray-300 text-sm font-medium">Filter by Stage:</span>
              </div>
              <div className="flex flex-wrap gap-2">
                <Badge
                  variant={selectedStage === null ? "default" : "outline"}
                  className={`cursor-pointer transition-all ${
                    selectedStage === null
                      ? 'bg-green-500 text-white hover:bg-green-600'
                      : 'bg-gray-800/60 backdrop-blur-sm text-gray-300 hover:bg-gray-700/60'
                  }`}
                  onClick={() => handleFilterChange('stage', null)}
                >
                  All Stages
                </Badge>
                {stages.map(stage => (
                  <Badge
                    key={stage}
                    variant={selectedStage === stage ? "default" : "outline"}
                    className={`cursor-pointer transition-all ${
                      selectedStage === stage
                        ? 'bg-green-500 text-white hover:bg-green-600'
                        : 'bg-gray-800/60 backdrop-blur-sm text-gray-300 hover:bg-gray-700/60'
                    }`}
                    onClick={() => handleFilterChange('stage', stage)}
                  >
                    {stage}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Alphabetical Filter - Modern & Compact */}
        <div className="bg-gray-900/30 backdrop-blur-md rounded-2xl p-4 mb-6 border border-gray-700/30">
          <div className="flex items-center gap-6">
            {/* Label */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-gray-300 font-medium text-sm">A-Z</span>
            </div>

            {/* Centered Letter Pills Container */}
            <div className="flex-1 flex items-center justify-center">
              <div className="flex items-center gap-1 flex-wrap justify-center max-w-4xl">
                {/* All Button */}
                <button
                  onClick={() => handleFilterChange('letter', null)}
                  className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
                    selectedLetter === null
                      ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                      : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300'
                  }`}
                >
                  All
                </button>

                {/* Letter Pills */}
                {alphabet.map(letter => (
                  <button
                    key={letter}
                    onClick={() => handleFilterChange('letter', letter)}
                    className={`w-7 h-7 rounded-full text-xs font-medium transition-all duration-200 flex items-center justify-center ${
                      selectedLetter === letter
                        ? 'bg-green-500 text-white shadow-lg shadow-green-500/25 scale-110'
                        : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300 hover:scale-105'
                    }`}
                  >
                    {letter}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>



        {/* Results Count and Items Per Page */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="text-gray-400">
            {searchTerm || selectedStage || selectedLetter ? (
              <p>
                Showing {paginatedProducts.length} of {filteredProducts.length} filtered products
                ({products?.length || 0} total)
              </p>
            ) : (
              <p>
                Showing {paginatedProducts.length} of {products?.length || 0} products
              </p>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span className="text-gray-400 text-sm">Items per page:</span>
            <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
              <SelectTrigger className="w-20 bg-gray-800/60 border-gray-700/50 text-gray-100">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                <SelectItem value="6">6</SelectItem>
                <SelectItem value="12">12</SelectItem>
                <SelectItem value="24">24</SelectItem>
                <SelectItem value="48">48</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/20 backdrop-blur-sm border border-red-500/50 rounded-lg p-4 mb-6">
            <p className="text-red-400">Error loading products: {error.message}</p>
          </div>
        )}

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: itemsPerPage }, (_, i) => (
              <Skeleton key={i} className="h-96 rounded-xl" />
            ))}
          </div>
        ) : paginatedProducts && paginatedProducts.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {paginatedProducts.map((product) => (
                <EnhancedProductCard key={product.id} product={product} />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 pt-6 border-t border-gray-800/50">
                <div className="text-gray-400 text-sm">
                  Page {currentPage} of {totalPages}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>

                  {/* Page Numbers */}
                  <div className="flex gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                          className={`w-10 ${
                            currentPage === pageNum
                              ? 'bg-green-500 text-white'
                              : 'bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60'
                          }`}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-heading font-semibold text-gray-100 mb-2">
              No Products Found
            </h3>
            <p className="text-gray-200">
              {searchTerm || selectedStage || selectedLetter
                ? "Try adjusting your search or filters"
                : "No products available in the database"}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AllProductsPage;