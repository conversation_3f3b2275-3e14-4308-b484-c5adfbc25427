// Updated Together AI function to handle FLUX model properly
async function generateTogetherAI(prompt: string, config: any): Promise<GenerationResult> {
  const startTime = Date.now();
  const apiKey = Deno.env.get('TOGETHER_API_KEY');
  
  if (!apiKey) {
    throw new Error('Together API key not configured');
  }
  
  // FLUX model uses different parameters
  const model = config.model || "black-forest-labs/FLUX.1-schnell-Free";
  const isFluxModel = model.includes('FLUX');
  
  const body = isFluxModel ? {
    model: model,
    prompt: prompt,
    width: 1024,
    height: 1024,
    steps: config.steps || 4,
    n: 1,
    seed: Math.floor(Math.random() * 1000000)
  } : {
    model: model,
    prompt: prompt,
    width: 1024,
    height: 1024,
    steps: 20,
    n: 1,
    negative_prompt: "blurry, bad quality, distorted, ugly, text, watermark"
  };
  
  const response = await fetch('https://api.together.xyz/v1/images/generations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Together AI error: ${error.error?.message || response.statusText}`);
  }
  
  const data = await response.json();
  
  return {
    imageUrl: data.data[0].url,
    cost: 0.0015,
    generationTimeMs: Date.now() - startTime,
    metadata: {
      model: model
    }
  };
}