import { usePlantTypes } from "@/hooks/use-plant-data";
import { <PERSON> } from "wouter";
import { Skeleton } from "@/components/ui/skeleton";
import { PlantType } from "@shared/schema";
import { ArrowRight } from "lucide-react";

const PlantTypeCards = () => {
  const { data: plantTypesData, isLoading } = usePlantTypes();
  const plantTypes = Array.isArray(plantTypesData) ? plantTypesData : [];
  
  // Debug logging
  console.log('PlantTypeCards - isLoading:', isLoading);
  console.log('PlantTypeCards - plantTypes:', plantTypes);
  console.log('PlantTypeCards - first plant type:', plantTypes[0]);

  return (
    <div className="py-16 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Choose Your <span className="hemp-brand-secondary">Hemp</span> Focus
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            Each hemp variety is optimized for specific applications. Select a type to discover 
            its unique industrial potential and sustainable solutions.
          </p>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="aspect-square">
                <Skeleton className="w-full h-full rounded-xl" />
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto">
            {plantTypes.slice(0, 4).map((plantType: PlantType) => (
              <Link key={plantType.id} href={`/plant-type/${plantType.id}`}>
                <div className="group cursor-pointer">
                  {/* Square image container */}
                  <div className="aspect-square relative overflow-hidden rounded-xl bg-gray-800 mb-3">
                    <img
                      src={plantType.imageUrl || '/images/unknown-hemp-image.png'}
                      alt={plantType.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                      onError={(e) => {
                        console.log('Image error for:', plantType.name);
                        e.currentTarget.src = '/images/unknown-hemp-image.png';
                      }}
                    />
                    {/* Subtle gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  
                  {/* Plant name */}
                  <h3 className="text-lg font-inter font-semibold text-white group-hover:text-green-400 transition-colors text-center">
                    {plantType.name}
                  </h3>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PlantTypeCards;