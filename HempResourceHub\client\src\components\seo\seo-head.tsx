import { Helmet } from "react-helmet";
import { memo } from "react";

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  structuredData?: Record<string, any>;
  breadcrumbs?: Array<{
    name: string;
    url: string;
  }>;
  noIndex?: boolean;
  canonicalUrl?: string;
}

const SEOHead = memo(({
  title = "HempDB - Interactive Industrial Hemp Applications Database",
  description = "Explore the versatile applications of industrial hemp across industries, plant parts, and product categories with our comprehensive interactive database.",
  keywords = ["hemp", "industrial hemp", "hemp products", "hemp applications", "sustainable materials", "hemp database"],
  image = "/images/hemp-og-image.jpg",
  url,
  type = "website",
  structuredData,
  breadcrumbs,
  noIndex = false,
  canonicalUrl
}: SEOHeadProps) => {
  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '');
  const canonical = canonicalUrl || currentUrl;
  
  // Generate structured data for breadcrumbs
  const breadcrumbSchema = breadcrumbs ? {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  } : null;

  // Default organization schema
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "HempQuarterz",
    "description": "Comprehensive industrial hemp database and analytics platform",
    "url": "https://hempquarterz.com",
    "logo": "https://hempquarterz.com/images/logo.png",
    "sameAs": [
      "https://twitter.com/hempquarterz",
      "https://linkedin.com/company/hempquarterz"
    ]
  };

  // Website schema
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "HempQuarterz Database",
    "description": description,
    "url": currentUrl,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${currentUrl}/hemp-dex?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };

  // Combine all schemas
  const allSchemas = [
    organizationSchema,
    websiteSchema,
    ...(breadcrumbSchema ? [breadcrumbSchema] : []),
    ...(structuredData ? [structuredData] : [])
  ];

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(", ")} />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={image} />
      <meta property="og:site_name" content="HempQuarterz Database" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:site" content="@hempquarterz" />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="HempQuarterz" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
      <meta name="theme-color" content="#22c55e" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      
      {/* Structured Data */}
      {allSchemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema)
          }}
        />
      ))}
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//supabase.co" />
      <link rel="dns-prefetch" href="//cdn.jsdelivr.net" />
    </Helmet>
  );
});

SEOHead.displayName = 'SEOHead';

export default SEOHead;

// Helper function to generate product schema
export const generateProductSchema = (product: any) => ({
  "@context": "https://schema.org",
  "@type": "Product",
  "name": product.name,
  "description": product.description,
  "image": product.image_url,
  "category": "Hemp Products",
  "brand": {
    "@type": "Brand",
    "name": "Industrial Hemp"
  },
  "offers": {
    "@type": "AggregateOffer",
    "availability": "https://schema.org/InStock",
    "priceCurrency": "USD"
  },
  "additionalProperty": [
    {
      "@type": "PropertyValue",
      "name": "Commercialization Stage",
      "value": product.commercialization_stage
    },
    {
      "@type": "PropertyValue",
      "name": "Sustainability Score",
      "value": "High"
    }
  ]
});

// Helper function to generate article schema
export const generateArticleSchema = (article: any) => ({
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": article.title,
  "description": article.description,
  "image": article.image,
  "author": {
    "@type": "Organization",
    "name": "HempQuarterz"
  },
  "publisher": {
    "@type": "Organization",
    "name": "HempQuarterz",
    "logo": {
      "@type": "ImageObject",
      "url": "https://hempquarterz.com/images/logo.png"
    }
  },
  "datePublished": article.datePublished,
  "dateModified": article.dateModified || article.datePublished
});

// Helper function to generate FAQ schema
export const generateFAQSchema = (faqs: Array<{ question: string; answer: string }>) => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
});
