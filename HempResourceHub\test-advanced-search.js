// Test advanced search features
const puppeteer = require('puppeteer');

async function testAdvancedSearch() {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    // Navigate to HempDex
    await page.goto('http://localhost:3000/hemp-dex');
    await page.waitForSelector('input[placeholder*="Search products"]');
    
    // Look for Advanced Search button
    const advancedSearchButton = await page.$('button:has-text("Advanced Search")');
    if (advancedSearchButton) {
      console.log('✅ Advanced Search button found');
      await advancedSearchButton.click();
      await page.waitForTimeout(1000);
      console.log('✅ Clicked Advanced Search button');
    } else {
      console.log('❌ Advanced Search button not found');
      
      // Try alternative selector
      const buttons = await page.$$eval('button', buttons => 
        buttons.map(btn => ({ text: btn.textContent, classes: btn.className }))
      );
      console.log('All buttons:', buttons);
    }
    
    // Test URL with advanced parameters
    await page.goto('http://localhost:3000/hemp-dex?search=fiber&parts=1,2&stages=established,growing&sort=name');
    await page.waitForTimeout(2000);
    console.log('✅ Navigated with advanced search parameters');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await browser.close();
  }
}

testAdvancedSearch();