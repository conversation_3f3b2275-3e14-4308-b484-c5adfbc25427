#!/usr/bin/env python3
"""
Run simplified scrapers without attribution columns
"""
import subprocess

def main():
    print("🚀 Hemp Resource Hub - Simplified Image Scraping\n")
    
    print("=" * 60)
    print("📢 IMPORTANT: Attribution columns not available")
    print("   Saving logos and images WITHOUT attribution for now")
    print("   Add attribution columns manually in Supabase if needed")
    print("=" * 60)
    print()
    
    # Step 1: Run simple logo scraper
    print("🏢 === SCRAPING COMPANY LOGOS ===")
    subprocess.run(["python", "simple_logo_scraper.py"])
    print()
    
    # Step 2: Run Hemp Industry Daily scraper
    print("📰 === SCRAPING ARTICLE IMAGES ===")
    subprocess.run(["python", "hemp_industry_daily_scraper.py"])
    print()
    
    print("\n✅ All tasks completed!")
    print("\n📋 Results:")
    print("1. Company logos extracted from websites")
    print("2. Hemp Industry Daily article images extracted")
    print("3. Generic images added for research without URLs")
    print("\n🎯 Next Steps:")
    print("1. Check the frontend to see the images")
    print("2. Add attribution columns in Supabase dashboard if needed")
    print("3. Re-run enhanced scrapers after adding columns")

if __name__ == "__main__":
    main()