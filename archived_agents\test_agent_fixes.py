#!/usr/bin/env python3
"""
Test script to verify agent fixes are working
"""

import os
import sys
import asyncio
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase import create_client
from agents.research.research_agent import HempResearchAgent

async def test_environment():
    """Test environment setup"""
    print("=== Testing Environment Setup ===")
    
    # Check environment variables
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    openai_key = os.environ.get('OPENAI_API_KEY')
    
    print(f"✓ Supabase URL: {'Set' if supabase_url else 'Missing'} - {supabase_url[:30]}..." if supabase_url else "✗ Supabase URL: Missing")
    print(f"✓ Supabase Key: {'Set' if supabase_key else 'Missing'} - {supabase_key[:20]}..." if supabase_key else "✗ Supabase Key: Missing")
    print(f"✓ OpenAI Key: {'Set' if openai_key else 'Missing'}")
    
    if not all([supabase_url, supabase_key]):
        print("\n❌ Missing required environment variables!")
        return None
    
    # Test Supabase connection
    try:
        supabase = create_client(supabase_url, supabase_key)
        result = supabase.table('uses_products').select('count', count='exact').execute()
        print(f"\n✓ Supabase Connection: Success")
        print(f"  - Products in database: {result.count}")
        return supabase
    except Exception as e:
        print(f"\n❌ Supabase Connection Failed: {e}")
        return None

async def test_research_agent(supabase):
    """Test the research agent with fixes"""
    print("\n=== Testing Research Agent ===")
    
    try:
        # Initialize agent
        agent = HempResearchAgent(supabase)
        
        # Test ID lookup methods
        print("\nTesting ID lookups:")
        
        # Test plant part ID lookup
        plant_parts = ['seeds', 'fiber', 'flower', 'hurds', 'leaves']
        for part in plant_parts:
            part_id = await agent._get_plant_part_id(part)
            print(f"  - Plant part '{part}' → ID: {part_id}")
        
        # Test industry subcategory lookup
        industries = [
            ('Textiles', 'Apparel'),
            ('Food and Beverage', None),
            ('Construction', 'Building Materials')
        ]
        for industry, sub in industries:
            cat_id = await agent._get_industry_subcategory_id(industry, sub)
            print(f"  - Industry '{industry}' / '{sub}' → ID: {cat_id}")
        
        # Test product discovery (limited)
        print("\n\nTesting product discovery (limited to 3 products):")
        task = {
            'action': 'discover_products',
            'params': {
                'limit': 3,
                'categories': ['all']
            }
        }
        
        # Note: This will only work if you have AI API keys set
        if os.environ.get('OPENAI_API_KEY') or os.environ.get('ANTHROPIC_API_KEY'):
            async with agent:
                result = await agent.execute(task)
                print(f"\n✓ Discovery completed:")
                print(f"  - Discovered: {result.get('discovered_count', 0)} raw items")
                print(f"  - Structured: {result.get('structured_count', 0)} products")
                print(f"  - Saved: {result.get('saved_count', 0)} to database")
                
                if result.get('products'):
                    print(f"\nSample product:")
                    product = result['products'][0]
                    print(f"  - Name: {product.get('name')}")
                    print(f"  - Plant Part: {product.get('plant_part')}")
                    print(f"  - Industry: {product.get('industry')}")
        else:
            print("\n⚠️  No AI API keys found - skipping discovery test")
            print("    Set OPENAI_API_KEY or ANTHROPIC_API_KEY to test full functionality")
        
    except Exception as e:
        print(f"\n❌ Research Agent Error: {e}")
        import traceback
        traceback.print_exc()

async def check_recent_runs(supabase):
    """Check recent agent runs"""
    print("\n=== Recent Agent Runs ===")
    
    try:
        result = supabase.table('hemp_agent_runs')\
            .select('*')\
            .order('timestamp', desc=True)\
            .limit(5)\
            .execute()
        
        if result.data:
            for run in result.data:
                print(f"\n- Agent: {run['agent_name']}")
                print(f"  Time: {run['timestamp']}")
                print(f"  Status: {run['status']}")
                print(f"  Products found: {run['products_found']}")
                print(f"  Products saved: {run['products_saved']}")
                if run.get('error_message'):
                    print(f"  Error: {run['error_message']}")
        else:
            print("No recent agent runs found")
            
    except Exception as e:
        print(f"Error checking runs: {e}")

async def main():
    """Run all tests"""
    print("🌿 Hemp AI Agent Test Suite")
    print("=" * 50)
    
    # Test environment
    supabase = await test_environment()
    if not supabase:
        return
    
    # Test research agent
    await test_research_agent(supabase)
    
    # Check recent runs
    await check_recent_runs(supabase)
    
    print("\n✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(main())