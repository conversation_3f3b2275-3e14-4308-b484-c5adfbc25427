import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  <PERSON><PERSON>hart, Bar, LineChart, Line, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer 
} from 'recharts';
import { DollarSign, TrendingUp, TrendingDown, AlertCircle, Zap } from 'lucide-react';
import { format } from 'date-fns';

interface UsageStats {
  totalRequests: number;
  totalInputTokens: number;
  totalOutputTokens: number;
  totalCost: number;
  averageResponseTime: number;
  errorRate: number;
  byAgent: Record<string, {
    requests: number;
    cost: number;
    tokens: number;
  }>;
  byModel: Record<string, {
    requests: number;
    cost: number;
  }>;
  dailyUsage?: Array<{
    date: string;
    cost: number;
    requests: number;
    tokens: number;
  }>;
}

export function AICostDashboard() {
  const { data: stats, isLoading } = useQuery<UsageStats>({
    queryKey: ['ai-usage-stats'],
    queryFn: async () => {
      const response = await fetch('/api/ai/usage-stats?days=30');
      if (!response.ok) throw new Error('Failed to fetch usage stats');
      return response.json();
    },
    refetchInterval: 60000, // Refresh every minute
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600" />
      </div>
    );
  }

  if (!stats) return null;

  // Calculate cost savings from caching
  const estimatedSavings = stats.totalRequests * 0.0001; // Rough estimate
  const costTrend = stats.dailyUsage ? 
    (stats.dailyUsage[stats.dailyUsage.length - 1]?.cost - stats.dailyUsage[0]?.cost) / stats.dailyUsage[0]?.cost * 100 : 0;

  // Prepare chart data
  const agentData = Object.entries(stats.byAgent).map(([agent, data]) => ({
    agent: agent.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    cost: data.cost,
    requests: data.requests,
    avgCost: data.cost / data.requests,
  }));

  const pieData = agentData.map(item => ({
    name: item.agent,
    value: item.cost,
  }));

  const COLORS = ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6'];

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Cost (30d)</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalCost.toFixed(4)}</div>
            <p className="text-xs text-muted-foreground">
              {costTrend > 0 ? (
                <span className="flex items-center text-red-600">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +{costTrend.toFixed(1)}% from start
                </span>
              ) : (
                <span className="flex items-center text-green-600">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  {costTrend.toFixed(1)}% from start
                </span>
              )}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalRequests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg cost: ${(stats.totalCost / stats.totalRequests).toFixed(6)}/req
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Cache Savings</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ${estimatedSavings.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">
              Estimated from cache hits
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(stats.errorRate * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Avg response: {stats.averageResponseTime.toFixed(0)}ms
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="cost-by-agent" className="space-y-4">
        <TabsList>
          <TabsTrigger value="cost-by-agent">Cost by Agent</TabsTrigger>
          <TabsTrigger value="daily-trend">Daily Trend</TabsTrigger>
          <TabsTrigger value="token-usage">Token Usage</TabsTrigger>
          <TabsTrigger value="optimization">Optimization Tips</TabsTrigger>
        </TabsList>

        <TabsContent value="cost-by-agent" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Cost Distribution by Agent</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => `$${value.toFixed(4)}`} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Average Cost per Request</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={agentData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="agent" angle={-45} textAnchor="end" height={80} />
                    <YAxis />
                    <Tooltip formatter={(value: number) => `$${value.toFixed(6)}`} />
                    <Bar dataKey="avgCost" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="daily-trend">
          <Card>
            <CardHeader>
              <CardTitle>Daily Usage Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={stats.dailyUsage || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(date) => format(new Date(date), 'MMM d')}
                  />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip 
                    labelFormatter={(date) => format(new Date(date), 'PPP')}
                    formatter={(value: number, name: string) => 
                      name === 'cost' ? `$${value.toFixed(4)}` : value.toLocaleString()
                    }
                  />
                  <Legend />
                  <Line 
                    yAxisId="left" 
                    type="monotone" 
                    dataKey="cost" 
                    stroke="#10b981" 
                    name="Cost ($)"
                  />
                  <Line 
                    yAxisId="right" 
                    type="monotone" 
                    dataKey="requests" 
                    stroke="#3b82f6" 
                    name="Requests"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="token-usage">
          <Card>
            <CardHeader>
              <CardTitle>Token Usage Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Input Tokens</h4>
                    <p className="text-2xl font-bold">{stats.totalInputTokens.toLocaleString()}</p>
                    <p className="text-sm text-muted-foreground">
                      ${((stats.totalInputTokens / 1_000_000) * 0.25).toFixed(4)} cost
                    </p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Output Tokens</h4>
                    <p className="text-2xl font-bold">{stats.totalOutputTokens.toLocaleString()}</p>
                    <p className="text-sm text-muted-foreground">
                      ${((stats.totalOutputTokens / 1_000_000) * 1.25).toFixed(4)} cost
                    </p>
                  </div>
                </div>
                
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium mb-2 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Token Efficiency
                  </h4>
                  <p className="text-sm">
                    Output tokens cost 5x more than input tokens. Optimize by:
                  </p>
                  <ul className="list-disc list-inside text-sm mt-2 space-y-1">
                    <li>Reducing max_tokens when possible</li>
                    <li>Using concise system prompts</li>
                    <li>Implementing response caching</li>
                    <li>Batching similar requests</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Cost Optimization Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-green-600 mt-1.5" />
                    <div>
                      <h4 className="font-medium">Enable Response Caching</h4>
                      <p className="text-sm text-muted-foreground">
                        Cache frequently requested data for 1-2 hours
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-green-600 mt-1.5" />
                    <div>
                      <h4 className="font-medium">Reduce Token Limits</h4>
                      <p className="text-sm text-muted-foreground">
                        Lower max_tokens from 2000-3000 to 1000-1500
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-yellow-600 mt-1.5" />
                    <div>
                      <h4 className="font-medium">Implement Prompt Templates</h4>
                      <p className="text-sm text-muted-foreground">
                        Use structured prompts to reduce input tokens
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-blue-600 mt-1.5" />
                    <div>
                      <h4 className="font-medium">Batch Similar Requests</h4>
                      <p className="text-sm text-muted-foreground">
                        Group multiple queries into single requests
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Model Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 border rounded-lg">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">Claude 3 Haiku</h4>
                        <p className="text-sm text-muted-foreground">Current model</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">$0.25/$1.25</p>
                        <p className="text-xs text-muted-foreground">per 1M tokens</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-3 border rounded-lg opacity-60">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">Claude 3 Sonnet</h4>
                        <p className="text-sm text-muted-foreground">12x more expensive</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">$3/$15</p>
                        <p className="text-xs text-muted-foreground">per 1M tokens</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm font-medium text-green-800">
                      ✓ You're using the most cost-effective model
                    </p>
                    <p className="text-xs text-green-700 mt-1">
                      Estimated savings: ${(stats.totalCost * 11).toFixed(2)} vs Sonnet
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}