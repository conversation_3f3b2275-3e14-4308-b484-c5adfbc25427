#!/usr/bin/env python3
"""
Test script to verify the research agent fixes
Tests both with and without AI provider
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from hemp_cli import HempCLI
from lib.supabase_client import get_supabase_client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_agent_discovery():
    """Test the research agent with various configurations"""
    cli = HempCLI()
    
    print("\n" + "="*60)
    print("🧪 Testing Research Agent Fixes")
    print("="*60)
    
    # Test 1: Without AI (should use web scraping and feed monitoring)
    print("\n📋 Test 1: Discovery WITHOUT AI Provider")
    print("-" * 40)
    try:
        results = await cli.run_agent(
            'research', 
            'hemp construction materials', 
            features=['web', 'feed', 'company']
        )
        print(f"✅ Found {len(results)} products using non-AI features")
        if results:
            print(f"   First product: {results[0].get('name', 'Unknown')}")
            print(f"   Plant part: {results[0].get('plant_part', 'Unknown')}")
            print(f"   Industry: {results[0].get('industry', 'Unknown')}")
    except Exception as e:
        print(f"❌ Test 1 failed: {e}")
    
    # Test 2: With all features (will auto-detect AI availability)
    print("\n📋 Test 2: Discovery with AUTO feature detection")
    print("-" * 40)
    try:
        results = await cli.run_agent(
            'research', 
            'hemp textiles and clothing',
            # No features specified - should auto-detect
        )
        print(f"✅ Found {len(results)} products with auto-detected features")
        if results:
            print(f"   First product: {results[0].get('name', 'Unknown')}")
    except Exception as e:
        print(f"❌ Test 2 failed: {e}")
    
    # Test 3: Specific features
    print("\n📋 Test 3: Web scraping + Company extraction")
    print("-" * 40)
    try:
        results = await cli.run_agent(
            'research',
            'hemp protein powder',
            features=['web', 'company'],
            max_results=5
        )
        print(f"✅ Found {len(results)} products")
        for i, product in enumerate(results[:3], 1):
            print(f"   {i}. {product.get('name', 'Unknown')}")
            if product.get('companies'):
                print(f"      Companies: {', '.join(product['companies'])}")
    except Exception as e:
        print(f"❌ Test 3 failed: {e}")
    
    # Test 4: Check database for saved products
    print("\n📋 Test 4: Verify database saves")
    print("-" * 40)
    try:
        supabase = get_supabase_client()
        
        # Check recent products
        result = supabase.table('uses_products').select('*').order(
            'created_at', desc=True
        ).limit(5).execute()
        
        print(f"✅ Recent products in database: {len(result.data)}")
        for product in result.data[:3]:
            print(f"   - {product['name']}")
            print(f"     Plant part ID: {product.get('plant_part_id', 'None')}")
            print(f"     Industry ID: {product.get('industry_sub_category_id', 'None')}")
            
        # Check companies
        company_result = supabase.table('hemp_companies').select('*').order(
            'created_at', desc=True
        ).limit(5).execute()
        
        print(f"\n✅ Recent companies in database: {len(company_result.data)}")
        for company in company_result.data[:3]:
            print(f"   - {company['name']} ({company.get('type', 'Unknown')})")
            
    except Exception as e:
        print(f"❌ Test 4 failed: {e}")
    
    # Test 5: Plant part mapping
    print("\n📋 Test 5: Plant part categorization")
    print("-" * 40)
    try:
        # Test various plant part names
        from agents.research.unified_research_agent import UnifiedResearchAgent
        from config.research_agent_defaults import ResearchConfig
        
        agent = UnifiedResearchAgent(supabase, None, ResearchConfig())
        
        test_parts = ['fiber', 'seeds', 'oil', 'flower', 'hurds', 'biomass']
        for part in test_parts:
            part_id = await agent._get_plant_part_id(part)
            print(f"   '{part}' → ID: {part_id}")
            
    except Exception as e:
        print(f"❌ Test 5 failed: {e}")
    
    print("\n" + "="*60)
    print("🎯 Test Summary")
    print("="*60)
    print("If tests passed, the agent should now:")
    print("✓ Work without AI provider using web scraping")
    print("✓ Auto-detect features based on AI availability")
    print("✓ Properly categorize plant parts")
    print("✓ Extract and save company relationships")
    print("✓ Save products to database with correct IDs")


async def quick_product_check():
    """Quick check of product discovery"""
    cli = HempCLI()
    
    print("\n🚀 Quick Product Discovery Test")
    print("-" * 40)
    
    results = await cli.run_agent(
        'research',
        'industrial hemp products',
        features=['web'],
        max_results=3
    )
    
    if results:
        print(f"✅ Success! Found {len(results)} products")
        return True
    else:
        print("❌ No products found")
        return False


if __name__ == "__main__":
    # Check if quick test requested
    if len(sys.argv) > 1 and sys.argv[1] == '--quick':
        success = asyncio.run(quick_product_check())
        sys.exit(0 if success else 1)
    
    # Run full test suite
    asyncio.run(test_agent_discovery())