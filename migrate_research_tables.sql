-- Migration: Consolidate research_papers into research_entries
-- This migration safely moves any data from research_papers to research_entries
-- and then removes the legacy research_papers table

-- Step 1: Check if research_papers table exists and has data
DO $$
BEGIN
    -- Only proceed if research_papers table exists
    IF EXISTS (SELECT FROM information_schema.tables 
               WHERE table_schema = 'public' 
               AND table_name = 'research_papers') THEN
        
        -- Step 2: Migrate any existing data from research_papers to research_entries
        -- Map the different column names appropriately
        INSERT INTO research_entries (
            title,
            authors_or_assignees,
            abstract,
            publication_or_filing_date,
            journal_or_office,
            doi_or_patent_number,
            url,
            pdf_url,
            entry_type,
            plant_type_id,
            plant_part_id,
            industry_id,
            keywords,
            created_at,
            updated_at
        )
        SELECT 
            rp.title,
            string_to_array(rp.authors, ', '),  -- Convert authors string to array
            rp.abstract,
            rp.publication_date,
            rp.journal,
            rp.doi,
            rp.url,
            rp.pdf_url,
            'Paper' as entry_type,  -- All research_papers are academic papers
            rp.archetype_id as plant_type_id,  -- Map archetype_id to plant_type_id
            rp.plant_part_id,
            rp.industry_id,
            rp.keywords,
            rp.created_at,
            rp.updated_at
        FROM research_papers rp
        WHERE NOT EXISTS (
            -- Don't insert duplicates
            SELECT 1 FROM research_entries re 
            WHERE re.title = rp.title 
            AND re.url = rp.url
        );

        -- Step 3: Log migration results
        RAISE NOTICE 'Migrated % records from research_papers to research_entries', 
            (SELECT COUNT(*) FROM research_papers);

    END IF;
END $$;

-- Step 4: Drop dependent objects first
-- Drop any views that depend on research_papers
DROP VIEW IF EXISTS research_papers_view CASCADE;

-- Drop any indexes on research_papers
DROP INDEX IF EXISTS idx_research_papers_archetype_id;
DROP INDEX IF EXISTS idx_research_papers_plant_part_id;
DROP INDEX IF EXISTS idx_research_papers_industry_id;
DROP INDEX IF EXISTS idx_research_papers_search_vector;

-- Step 5: Update or drop the product_research_papers junction table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables 
               WHERE table_schema = 'public' 
               AND table_name = 'product_research_papers') THEN
        
        -- If product_research_entries doesn't exist, rename the junction table
        IF NOT EXISTS (SELECT FROM information_schema.tables 
                       WHERE table_schema = 'public' 
                       AND table_name = 'product_research_entries') THEN
            ALTER TABLE product_research_papers RENAME TO product_research_entries;
            ALTER TABLE product_research_entries RENAME COLUMN research_paper_id TO research_entry_id;
        ELSE
            -- Otherwise migrate the data and drop the old table
            INSERT INTO product_research_entries (product_id, research_entry_id)
            SELECT prp.product_id, prp.research_paper_id
            FROM product_research_papers prp
            WHERE EXISTS (SELECT 1 FROM research_entries re WHERE re.id = prp.research_paper_id)
            ON CONFLICT DO NOTHING;
            
            DROP TABLE product_research_papers;
        END IF;
    END IF;
END $$;

-- Step 6: Finally drop the research_papers table
DROP TABLE IF EXISTS research_papers CASCADE;

-- Step 7: Add any missing indexes to research_entries
CREATE INDEX IF NOT EXISTS idx_research_entries_plant_type_id ON research_entries(plant_type_id);
CREATE INDEX IF NOT EXISTS idx_research_entries_plant_part_id ON research_entries(plant_part_id);
CREATE INDEX IF NOT EXISTS idx_research_entries_industry_id ON research_entries(industry_id);
CREATE INDEX IF NOT EXISTS idx_research_entries_entry_type ON research_entries(entry_type);

-- Step 8: Add search vector column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'research_entries' 
                   AND column_name = 'search_vector') THEN
        ALTER TABLE research_entries ADD COLUMN search_vector tsvector;
        
        -- Update search vectors for existing records
        UPDATE research_entries 
        SET search_vector = to_tsvector('english', 
            COALESCE(title, '') || ' ' || 
            COALESCE(array_to_string(authors_or_assignees, ' '), '') || ' ' || 
            COALESCE(abstract, '') || ' ' ||
            COALESCE(array_to_string(keywords, ' '), '')
        );
        
        -- Create GIN index for full-text search
        CREATE INDEX idx_research_entries_search_vector ON research_entries USING GIN(search_vector);
    END IF;
END $$;

-- Migration complete!
-- The research_papers table has been safely removed and all data migrated to research_entries