import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NDg0OTE3NzYsImV4cCI6MjA2NDA2Nzc3Nn0.JM-7iNj4o3i0-zq2FAv_eCzTMvG-olCMOBaU1ZLv8nU';

// Use service key to bypass RLS
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function addSimpleProducts() {
  console.log('Adding simple products...\n');
  
  try {
    // Get IDs we need
    const { data: plantParts } = await supabase
      .from('plant_parts')
      .select('id, name')
      .limit(3);
    
    const { data: industries } = await supabase
      .from('industry_sub_categories')
      .select('id, name')
      .limit(3);
    
    if (!plantParts || !industries) {
      console.error('Could not fetch required data');
      return;
    }
    
    // Simple products with just required fields
    const simpleProducts = [
      {
        name: "Industrial Hemp Fiber",
        description: "High-quality fiber for industrial applications",
        plant_part_id: plantParts[0].id,
        industry_sub_category_id: industries[0].id,
        commercialization_stage: "Established"
      },
      {
        name: "Hemp Building Materials",
        description: "Sustainable construction materials from hemp",
        plant_part_id: plantParts[1]?.id || plantParts[0].id,
        industry_sub_category_id: industries[1]?.id || industries[0].id,
        commercialization_stage: "Growing"
      },
      {
        name: "Hemp Food Products",
        description: "Nutritious food products made from hemp seeds",
        plant_part_id: plantParts[2]?.id || plantParts[0].id,
        industry_sub_category_id: industries[2]?.id || industries[0].id,
        commercialization_stage: "Established"
      }
    ];
    
    // Try to insert
    const { data, error } = await supabase
      .from('uses_products')
      .insert(simpleProducts)
      .select();
    
    if (error) {
      console.error('Error:', error);
      console.log('\nTrying alternative approach...');
      
      // Try one at a time
      for (const product of simpleProducts) {
        const { data: single, error: singleError } = await supabase
          .from('uses_products')
          .insert(product)
          .select();
        
        if (singleError) {
          console.error(`Failed to insert ${product.name}:`, singleError.message);
        } else {
          console.log(`✅ Inserted: ${product.name}`);
        }
      }
    } else {
      console.log(`✅ Successfully inserted ${data.length} products!`);
      data.forEach(p => console.log(`  - ${p.name}`));
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

addSimpleProducts();