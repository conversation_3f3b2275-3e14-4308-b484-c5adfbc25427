import { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentName: string;
  timestamp: number;
}

// Performance monitoring hook - Safe version
export function usePerformanceMonitor(componentName: string) {
  const renderStartTime = useRef<number>(0);
  const renderCount = useRef<number>(0);

  useEffect(() => {
    // Only run in development and if performance API is available
    if (process.env.NODE_ENV !== 'development' || typeof performance === 'undefined') {
      return;
    }

    try {
      renderStartTime.current = performance.now();
      renderCount.current += 1;
    } catch (error) {
      // Silently fail if performance API is not available
      console.warn('[Performance Monitor] Performance API not available');
    }
  });

  useEffect(() => {
    // Only run in development and if performance API is available
    if (process.env.NODE_ENV !== 'development' || typeof performance === 'undefined') {
      return;
    }

    try {
      const renderEndTime = performance.now();
      const renderTime = renderEndTime - renderStartTime.current;

      // Only log if we have valid timing data
      if (renderTime > 0 && renderTime < 10000) { // Sanity check
        console.log(`[Performance] ${componentName}: ${renderTime.toFixed(2)}ms (render #${renderCount.current})`);

        // Warn about slow renders
        if (renderTime > 16) { // 60fps threshold
          console.warn(`[Performance Warning] ${componentName} took ${renderTime.toFixed(2)}ms to render`);
        }
      }
    } catch (error) {
      // Silently fail if there's any error
      console.warn('[Performance Monitor] Error measuring performance:', error);
    }
  });

  return {
    renderCount: renderCount.current
  };
}

// Hook for measuring async operations
export function useAsyncPerformance() {
  const measureAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Async Performance] ${operationName}: ${duration.toFixed(2)}ms`);
        
        if (duration > 1000) {
          console.warn(`[Async Performance Warning] ${operationName} took ${duration.toFixed(2)}ms`);
        }
      }
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.error(`[Async Performance Error] ${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  }, []);

  return { measureAsync };
}

// Hook for Core Web Vitals monitoring
export function useWebVitals() {
  useEffect(() => {
    // Only run in browser
    if (typeof window === 'undefined') return;

    // Measure Largest Contentful Paint (LCP)
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Web Vitals] LCP: ${lastEntry.startTime.toFixed(2)}ms`);
      }
    });

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (e) {
      // LCP not supported
    }

    // Measure First Input Delay (FID)
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        const fid = entry.processingStart - entry.startTime;
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`[Web Vitals] FID: ${fid.toFixed(2)}ms`);
        }
      });
    });

    try {
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (e) {
      // FID not supported
    }

    // Measure Cumulative Layout Shift (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Web Vitals] CLS: ${clsValue.toFixed(4)}`);
      }
    });

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (e) {
      // CLS not supported
    }

    return () => {
      observer.disconnect();
      fidObserver.disconnect();
      clsObserver.disconnect();
    };
  }, []);
}

// Hook for memory usage monitoring
export function useMemoryMonitor() {
  useEffect(() => {
    if (typeof window === 'undefined' || !('memory' in performance)) return;

    const checkMemory = () => {
      const memory = (performance as any).memory;
      
      if (process.env.NODE_ENV === 'development') {
        console.log('[Memory Usage]', {
          used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
        });
      }

      // Warn if memory usage is high
      const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
      if (usagePercent > 80) {
        console.warn(`[Memory Warning] High memory usage: ${usagePercent.toFixed(1)}%`);
      }
    };

    // Check memory every 30 seconds
    const interval = setInterval(checkMemory, 30000);
    
    // Initial check
    checkMemory();

    return () => clearInterval(interval);
  }, []);
}

// Hook for bundle size analysis
export function useBundleAnalysis() {
  useEffect(() => {
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') return;

    // Analyze loaded resources
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    const jsResources = resources.filter(r => r.name.includes('.js'));
    const cssResources = resources.filter(r => r.name.includes('.css'));
    
    const totalJSSize = jsResources.reduce((sum, r) => sum + (r.transferSize || 0), 0);
    const totalCSSSize = cssResources.reduce((sum, r) => sum + (r.transferSize || 0), 0);
    
    console.log('[Bundle Analysis]', {
      jsFiles: jsResources.length,
      cssFiles: cssResources.length,
      totalJSSize: `${(totalJSSize / 1024).toFixed(2)} KB`,
      totalCSSSize: `${(totalCSSSize / 1024).toFixed(2)} KB`,
      largestJS: jsResources.sort((a, b) => (b.transferSize || 0) - (a.transferSize || 0))[0]?.name
    });
  }, []);
}

// Combined performance hook
export function usePerformance(componentName: string) {
  usePerformanceMonitor(componentName);
  useWebVitals();
  useMemoryMonitor();
  useBundleAnalysis();
  
  const { measureAsync } = useAsyncPerformance();
  
  return { measureAsync };
}
