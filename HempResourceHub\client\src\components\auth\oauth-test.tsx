import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Chrome, CheckCircle, XCircle, AlertTriangle, Settings } from 'lucide-react';
import { supabase } from '@/lib/supabase-client';

export const OAuthTest = () => {
  const [testResults, setTestResults] = useState<{
    googleConfigured: boolean | null;
    testMessage: string;
    canTestSignIn: boolean;
  }>({
    googleConfigured: null,
    testMessage: 'Click "Test Google OAuth" to check configuration',
    canTestSignIn: false,
  });

  const [isLoading, setIsLoading] = useState(false);

  const testGoogleOAuth = async () => {
    setIsLoading(true);
    setTestResults({
      googleConfigured: null,
      testMessage: 'Testing Google OAuth configuration...',
      canTestSignIn: false,
    });

    try {
      // Try to initiate Google OAuth (this will fail if not configured, but we can catch the error)
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        // Check the error message to determine if Google is configured
        if (error.message.includes('Provider not found') || 
            error.message.includes('google') ||
            error.message.includes('OAuth')) {
          setTestResults({
            googleConfigured: false,
            testMessage: `Google OAuth is not configured in Supabase. Error: ${error.message}`,
            canTestSignIn: false,
          });
        } else {
          setTestResults({
            googleConfigured: true,
            testMessage: `Google OAuth appears to be configured, but there was an error: ${error.message}`,
            canTestSignIn: true,
          });
        }
      } else {
        setTestResults({
          googleConfigured: true,
          testMessage: 'Google OAuth is properly configured! The sign-in process should work.',
          canTestSignIn: true,
        });
      }
    } catch (err) {
      setTestResults({
        googleConfigured: false,
        testMessage: `Error testing Google OAuth: ${err}`,
        canTestSignIn: false,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        setTestResults(prev => ({
          ...prev,
          testMessage: `Google sign-in failed: ${error.message}`,
        }));
      }
      // If successful, user will be redirected
    } catch (err) {
      setTestResults(prev => ({
        ...prev,
        testMessage: `Google sign-in error: ${err}`,
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (testResults.googleConfigured === null) {
      return <Settings className="h-5 w-5 text-gray-400" />;
    } else if (testResults.googleConfigured) {
      return <CheckCircle className="h-5 w-5 text-green-400" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-400" />;
    }
  };

  const getStatusColor = () => {
    if (testResults.googleConfigured === null) {
      return 'border-gray-500/30';
    } else if (testResults.googleConfigured) {
      return 'border-green-500/30';
    } else {
      return 'border-red-500/30';
    }
  };

  return (
    <Card className={`bg-gray-900/60 backdrop-blur-sm ${getStatusColor()}`}>
      <CardHeader>
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <CardTitle className="text-white">Google OAuth Configuration Test</CardTitle>
        </div>
        <CardDescription className="text-gray-400">
          Test if Google OAuth is properly configured in your Supabase project
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
          <div className="flex items-center gap-3">
            <Chrome className="h-5 w-5 text-blue-400" />
            <div>
              <p className="text-white font-medium">Google OAuth Status</p>
              <p className="text-sm text-gray-400">
                {testResults.googleConfigured === null && 'Not tested yet'}
                {testResults.googleConfigured === true && 'Configured'}
                {testResults.googleConfigured === false && 'Not configured'}
              </p>
            </div>
          </div>
          <Badge 
            variant={
              testResults.googleConfigured === null ? 'secondary' :
              testResults.googleConfigured ? 'default' : 'destructive'
            }
          >
            {testResults.googleConfigured === null && 'Unknown'}
            {testResults.googleConfigured === true && 'Ready'}
            {testResults.googleConfigured === false && 'Needs Setup'}
          </Badge>
        </div>

        <Alert className={
          testResults.googleConfigured === null ? 'border-gray-500/50 bg-gray-500/10' :
          testResults.googleConfigured ? 'border-green-500/50 bg-green-500/10' :
          'border-red-500/50 bg-red-500/10'
        }>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className={
            testResults.googleConfigured === null ? 'text-gray-400' :
            testResults.googleConfigured ? 'text-green-400' :
            'text-red-400'
          }>
            {testResults.testMessage}
          </AlertDescription>
        </Alert>

        <div className="space-y-2">
          <Button
            onClick={testGoogleOAuth}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? 'Testing...' : 'Test Google OAuth Configuration'}
          </Button>

          {testResults.canTestSignIn && (
            <Button
              onClick={testGoogleSignIn}
              disabled={isLoading}
              variant="outline"
              className="w-full border-green-500/50 text-green-400 hover:bg-green-500/10"
            >
              <Chrome className="mr-2 h-4 w-4" />
              Test Google Sign-In
            </Button>
          )}
        </div>

        {testResults.googleConfigured === false && (
          <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <h4 className="text-yellow-400 font-medium mb-2">Setup Required</h4>
            <ol className="text-sm text-gray-300 space-y-1 list-decimal list-inside">
              <li>Go to <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Google Cloud Console</a></li>
              <li>Create OAuth 2.0 credentials</li>
              <li>Add redirect URI: <code className="bg-gray-800 px-1 rounded text-xs">https://ktoqznqmlnxrtvubewyz.supabase.co/auth/v1/callback</code></li>
              <li>Go to <a href="https://supabase.com/dashboard" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Supabase Dashboard</a></li>
              <li>Navigate to Authentication → Providers</li>
              <li>Enable Google and add your Client ID & Secret</li>
            </ol>
          </div>
        )}

        {testResults.googleConfigured === true && (
          <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
            <h4 className="text-green-400 font-medium mb-2">Google OAuth Ready!</h4>
            <p className="text-sm text-gray-300">
              Google OAuth is properly configured. You should now see the "Continue with Google" button on the login page.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default OAuthTest;
