#!/usr/bin/env python3
"""
Check if all required dependencies are installed
"""

import sys
import subprocess

def check_package(package_name):
    """Check if a package is installed"""
    try:
        __import__(package_name)
        return True, "Installed"
    except ImportError:
        return False, "Not installed"

def main():
    print("=== Checking Python Dependencies ===")
    print(f"Python version: {sys.version}\n")
    
    # List of required packages
    packages = [
        ('supabase', 'supabase'),
        ('openai', 'openai'),
        ('dotenv', 'python-dotenv'),
        ('requests', 'requests'),
        ('pandas', 'pandas'),
        ('aiohttp', 'aiohttp'),
        ('bs4', 'beautifulsoup4'),
        ('feedparser', 'feedparser'),
        ('anthropic', 'anthropic'),
        ('tenacity', 'tenacity')
    ]
    
    missing = []
    
    for import_name, pip_name in packages:
        installed, status = check_package(import_name)
        emoji = "✅" if installed else "❌"
        print(f"{emoji} {import_name}: {status}")
        if not installed:
            missing.append(pip_name)
    
    if missing:
        print(f"\n❌ Missing {len(missing)} packages!")
        print("\nTo install missing packages, run:")
        print(f"pip install {' '.join(missing)}")
        
        print("\nOr install all requirements:")
        print("pip install -r requirements.txt")
    else:
        print("\n✅ All required packages are installed!")
    
    # Check environment variables
    print("\n=== Checking Environment Variables ===")
    import os
    
    # Try to load .env
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    if os.path.exists(env_path):
        print("✅ .env file found")
        # Load it
        with open(env_path) as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    try:
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value
                    except ValueError:
                        pass
    else:
        print("❌ .env file not found")
    
    env_vars = [
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'OPENAI_API_KEY'
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: Set ({value[:20]}...)")
        else:
            print(f"❌ {var}: Not set")

if __name__ == "__main__":
    main()