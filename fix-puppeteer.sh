#!/bin/bash

echo "=== Puppeteer Chrome Dependencies Fix Script ==="
echo "This script will help you install the required dependencies for Puppeteer"
echo ""

# Check if running in WSL
if grep -qi microsoft /proc/version; then
    echo "✓ Detected WSL environment"
else
    echo "⚠ Not running in WSL, but continuing anyway..."
fi

echo ""
echo "The following packages need to be installed:"
echo "- libnss3"
echo "- libnssutil3" 
echo "- libnspr4"
echo "- libasound2"
echo "- libatk-bridge2.0-0"
echo "- libx11-xcb1"
echo "- libxcb1"
echo "- libxcomposite1"
echo "- libxcursor1"
echo "- libxdamage1"
echo "- libxext6"
echo "- libxfixes3"
echo "- libxi6"
echo "- libxrandr2"
echo "- libxrender1"
echo "- libxss1"
echo "- libxtst6"
echo "- libgbm1"
echo ""

echo "To install these dependencies, run the following command:"
echo ""
echo "sudo apt-get update && sudo apt-get install -y \\"
echo "    libnss3 \\"
echo "    libnssutil3 \\"
echo "    libnspr4 \\"
echo "    libasound2 \\"
echo "    libatk-bridge2.0-0 \\"
echo "    libx11-xcb1 \\"
echo "    libxcb1 \\"
echo "    libxcomposite1 \\"
echo "    libxcursor1 \\"
echo "    libxdamage1 \\"
echo "    libxext6 \\"
echo "    libxfixes3 \\"
echo "    libxi6 \\"
echo "    libxrandr2 \\"
echo "    libxrender1 \\"
echo "    libxss1 \\"
echo "    libxtst6 \\"
echo "    libgbm1 \\"
echo "    fonts-liberation \\"
echo "    libappindicator3-1 \\"
echo "    xdg-utils"

echo ""
echo "Alternative: Install Chromium browser (includes all dependencies):"
echo "sudo apt-get update && sudo apt-get install -y chromium-browser"

echo ""
echo "After installation, you can test Puppeteer with:"
echo "node -e \"const puppeteer = require('puppeteer'); (async () => { const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox', '--disable-setuid-sandbox'] }); console.log('✓ Puppeteer works!'); await browser.close(); })().catch(console.error);\""