export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;
      
      // Don't retry on 401 (auth) or 400 (bad request)
      if (error.status === 401 || error.status === 400) {
        throw error;
      }
      
      // Only retry on 429 (rate limit) or network errors
      if (error.status === 429 || !error.status) {
        const delay = initialDelay * Math.pow(2, i);
        console.log(`Retrying after ${delay}ms (attempt ${i + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
  
  throw lastError;
}