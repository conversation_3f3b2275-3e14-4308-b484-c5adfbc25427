import React, { useState, useEffect, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Users, 
  Package, 
  Leaf,
  Factory,
  Globe,
  Zap,
  Target,
  Award,
  AlertCircle,
  ChevronDown,
  Filter
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAllHempProducts } from "@/hooks/use-product-data";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";

interface MetricCard {
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: React.ReactNode;
  description?: string;
  color: string;
}

interface ChartData {
  name: string;
  value: number;
  color?: string;
  percentage?: number;
}

export function DataVisualizationDashboard() {
  const { data: products, isLoading: productsLoading, error: productsError } = useAllHempProducts();
  const { data: plantParts, isLoading: partsLoading, error: partsError } = usePlantParts();
  const { data: industries, isLoading: industriesLoading, error: industriesError } = useIndustries();

  // Debug logging
  React.useEffect(() => {
    console.log('Dashboard Data:', {
      products: products?.length || 0,
      plantParts: plantParts?.length || 0,
      industries: industries?.length || 0,
      loading: { productsLoading, partsLoading, industriesLoading },
      errors: { productsError, partsError, industriesError }
    });
  }, [products, plantParts, industries, productsLoading, partsLoading, industriesLoading]);

  const [selectedTimeframe, setSelectedTimeframe] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [realTimeData, setRealTimeData] = useState<any[]>([]);
  const [selectedStages, setSelectedStages] = useState<string[]>([]);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      const newDataPoint = {
        time: new Date().toLocaleTimeString(),
        searches: Math.floor(Math.random() * 100) + 50,
        views: Math.floor(Math.random() * 200) + 100,
        interactions: Math.floor(Math.random() * 50) + 25
      };
      
      setRealTimeData(prev => [...prev.slice(-19), newDataPoint]);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Calculate metrics
  const metrics = useMemo<MetricCard[]>(() => {
    // Use real data if available, otherwise use demo data
    const totalProducts = products?.length || 185;
    const totalPlantParts = plantParts?.length || 5;
    const totalIndustries = industries?.length || 12;

    // Mock calculations for demonstration
    const avgSustainabilityScore = 4.2;
    const completionRate = 87;
    const growthRate = 15.3;

    return [
      {
        title: "Total Products",
        value: totalProducts,
        change: 12.5,
        changeType: 'increase',
        icon: <Package className="h-5 w-5" />,
        description: "Hemp applications tracked",
        color: "text-green-400"
      },
      {
        title: "Industries Covered",
        value: totalIndustries,
        change: 8.2,
        changeType: 'increase',
        icon: <Factory className="h-5 w-5" />,
        description: "Market sectors analyzed",
        color: "text-blue-400"
      },
      {
        title: "Plant Parts Utilized",
        value: totalPlantParts,
        change: 0,
        changeType: 'neutral',
        icon: <Leaf className="h-5 w-5" />,
        description: "Hemp components mapped",
        color: "text-emerald-400"
      },
      {
        title: "Sustainability Score",
        value: avgSustainabilityScore.toFixed(1),
        change: 5.7,
        changeType: 'increase',
        icon: <Award className="h-5 w-5" />,
        description: "Average environmental rating",
        color: "text-yellow-400"
      },
      {
        title: "Data Completion",
        value: `${completionRate}%`,
        change: 3.1,
        changeType: 'increase',
        icon: <Target className="h-5 w-5" />,
        description: "Product information filled",
        color: "text-purple-400"
      },
      {
        title: "Growth Rate",
        value: `${growthRate}%`,
        change: -2.1,
        changeType: 'decrease',
        icon: <TrendingUp className="h-5 w-5" />,
        description: "Monthly database expansion",
        color: "text-orange-400"
      }
    ];
  }, [products, plantParts, industries]);

  // Industry distribution data
  const industryData = useMemo<ChartData[]>(() => {
    // Consistent color palette for industries
    const industryColors = [
      '#22c55e', '#3b82f6', '#f59e0b', '#ef4444',
      '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'
    ];

    // If no real data, provide demo data
    if (!products || !industries || products.length === 0 || industries.length === 0) {
      return [
        { name: 'Construction', value: 45, color: industryColors[0] },
        { name: 'Textiles', value: 38, color: industryColors[1] },
        { name: 'Food & Nutrition', value: 32, color: industryColors[2] },
        { name: 'Automotive', value: 28, color: industryColors[3] },
        { name: 'Cosmetics', value: 22, color: industryColors[4] },
        { name: 'Paper & Pulp', value: 18, color: industryColors[5] },
        { name: 'Bioplastics', value: 15, color: industryColors[6] },
        { name: 'Energy', value: 12, color: industryColors[7] }
      ];
    }

    const industryCount = industries.reduce((acc, industry, index) => {
      const count = products.filter(p => p.industryId === industry.id).length;
      if (count > 0) {
        acc.push({
          name: industry.name,
          value: count,
          color: industryColors[index % industryColors.length]
        });
      }
      return acc;
    }, [] as ChartData[]);

    return industryCount.sort((a, b) => b.value - a.value).slice(0, 8);
  }, [products, industries]);

  // Plant parts distribution
  const plantPartsData = useMemo<ChartData[]>(() => {
    // If no real data, provide demo data
    if (!products || !plantParts || products.length === 0 || plantParts.length === 0) {
      return [
        { name: 'Fiber/Bast', value: 65, percentage: 35 },
        { name: 'Seeds', value: 48, percentage: 26 },
        { name: 'Hurds/Shiv', value: 42, percentage: 23 },
        { name: 'Leaves', value: 18, percentage: 10 },
        { name: 'Flowers', value: 12, percentage: 6 }
      ];
    }

    return plantParts.map(part => {
      const count = products.filter(p => p.plantPartId === part.id).length;
      return {
        name: part.name,
        value: count,
        percentage: Math.round((count / products.length) * 100)
      };
    }).filter(item => item.value > 0);
  }, [products, plantParts]);

  // Helper function for stage colors (updated for consolidated stages)
  const getStageColor = (stage: string) => {
    const colors = {
      'Research': '#ef4444',      // Red - Early stage
      'Development': '#f97316',   // Orange - In progress
      'Pilot': '#eab308',         // Yellow - Testing
      'Commercial': '#22c55e',    // Green - Available
      'Mature': '#3b82f6',        // Blue - Established
      'Unknown': '#6b7280'        // Gray - Unknown
    };
    return colors[stage as keyof typeof colors] || '#6b7280';
  };

  // Get all available stages (ordered by development progression)
  const allStages = useMemo(() => {
    if (!products || products.length === 0) {
      return ['Research', 'Development', 'Pilot', 'Commercial', 'Mature'];
    }

    const stages = [...new Set(products.map(p => p.commercialization_stage || 'Unknown'))];
    // Sort by development progression
    const stageOrder = ['Research', 'Development', 'Pilot', 'Commercial', 'Mature', 'Unknown'];
    return stages.sort((a, b) => {
      const aIndex = stageOrder.indexOf(a);
      const bIndex = stageOrder.indexOf(b);
      return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex);
    });
  }, [products]);

  // Initialize selected stages to show all stages by default
  useEffect(() => {
    if (allStages.length > 0 && selectedStages.length === 0) {
      setSelectedStages(allStages);
    }
  }, [allStages, selectedStages.length]);

  // Commercialization stages data (filtered by selection)
  const stageData = useMemo<ChartData[]>(() => {
    // If no real data, provide demo data (reduced to 4 main stages)
    if (!products || products.length === 0) {
      const demoData = [
        { name: 'Commercial', value: 85, color: getStageColor('Commercial') },
        { name: 'Development', value: 52, color: getStageColor('Development') },
        { name: 'Research', value: 38, color: getStageColor('Research') },
        { name: 'Pilot', value: 25, color: getStageColor('Pilot') }
      ];
      return selectedStages.length > 0
        ? demoData.filter(item => selectedStages.includes(item.name))
        : demoData;
    }

    const stages = products.reduce((acc, product) => {
      const stage = product.commercialization_stage || 'Unknown';
      acc[stage] = (acc[stage] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const allStageData = Object.entries(stages).map(([name, value]) => ({
      name,
      value,
      color: getStageColor(name)
    }));

    // Filter by selected stages
    return selectedStages.length > 0
      ? allStageData.filter(item => selectedStages.includes(item.name))
      : allStageData;
  }, [products, selectedStages]);

  // Helper functions for stage selection
  const toggleStage = (stage: string) => {
    setSelectedStages(prev =>
      prev.includes(stage)
        ? prev.filter(s => s !== stage)
        : [...prev, stage]
    );
  };

  const selectAllStages = () => {
    setSelectedStages(allStages);
  };

  const clearAllStages = () => {
    setSelectedStages([]);
  };

  const formatChange = (change: number, type: 'increase' | 'decrease' | 'neutral') => {
    const icon = type === 'increase' ? <TrendingUp className="h-3 w-3" /> : 
                 type === 'decrease' ? <TrendingDown className="h-3 w-3" /> : 
                 <Activity className="h-3 w-3" />;
    
    const colorClass = type === 'increase' ? 'text-green-400' : 
                      type === 'decrease' ? 'text-red-400' : 
                      'text-gray-400';

    return (
      <div className={cn("flex items-center gap-1 text-xs", colorClass)}>
        {icon}
        <span>{Math.abs(change)}%</span>
      </div>
    );
  };

  if (productsLoading || partsLoading || industriesLoading) {
    return (
      <div className="space-y-8">
        {/* Loading Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="bg-gray-900/40 border-gray-800">
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-700 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-700 rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i} className="bg-gray-900/40 border-gray-800">
              <CardHeader>
                <div className="animate-pulse">
                  <div className="h-6 bg-gray-700 rounded w-1/2 mb-2"></div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="animate-pulse">
                  <div className="h-64 bg-gray-700 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Note: We now provide demo data when real data isn't available, so this component always shows something useful

  return (
    <div className="space-y-6 md:space-y-8">
      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {metrics.map((metric, index) => (
          <Card key={index} className="bg-gray-900/40 backdrop-blur-sm border border-gray-800 hover:border-gray-700 transition-all duration-200 rounded-xl">
            <CardContent className="p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1 md:space-y-2 flex-1 min-w-0">
                  <p className="text-xs md:text-sm font-medium text-gray-400 truncate">{metric.title}</p>
                  <div className="flex items-center gap-2">
                    <p className={cn("text-xl md:text-2xl font-bold", metric.color)}>{metric.value}</p>
                    {formatChange(metric.change, metric.changeType)}
                  </div>
                  {metric.description && (
                    <p className="text-xs text-gray-500 line-clamp-2">{metric.description}</p>
                  )}
                </div>
                <div className={cn("p-2 md:p-3 rounded-full bg-gray-800 flex-shrink-0 ml-3", metric.color)}>
                  {metric.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <Tabs defaultValue="distribution" className="space-y-4 md:space-y-6">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 bg-gray-900/50 rounded-lg p-1">
          <TabsTrigger value="distribution" className="text-xs md:text-sm">Distribution</TabsTrigger>
          <TabsTrigger value="trends" className="text-xs md:text-sm">Trends</TabsTrigger>
          <TabsTrigger value="stages" className="text-xs md:text-sm">Stages</TabsTrigger>
          <TabsTrigger value="realtime" className="text-xs md:text-sm">Real-time</TabsTrigger>
        </TabsList>

        <TabsContent value="distribution" className="space-y-4 md:space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
            {/* Industry Distribution */}
            <Card className="bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-xl">
              <CardHeader className="pb-3 md:pb-4">
                <CardTitle className="flex items-center gap-2 text-white text-base md:text-lg">
                  <Factory className="h-4 w-4 md:h-5 md:w-5 text-blue-400" />
                  Industry Distribution
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {industryData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={500}>
                    <BarChart data={industryData} margin={{ top: 5, right: 5, left: 5, bottom: 60 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis
                        dataKey="name"
                        stroke="#9ca3af"
                        fontSize={10}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                        interval={0}
                      />
                      <YAxis stroke="#9ca3af" fontSize={10} />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#1f2937',
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          fontSize: '12px'
                        }}
                      />
                      <Bar dataKey="value" fill="#22c55e" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-64 flex items-center justify-center text-gray-400">
                    <div className="text-center">
                      <Factory className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No industry data available</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Plant Parts Distribution */}
            <Card className="bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-xl">
              <CardHeader className="pb-3 md:pb-4">
                <CardTitle className="flex items-center gap-2 text-white text-base md:text-lg">
                  <Leaf className="h-4 w-4 md:h-5 md:w-5 text-green-400" />
                  Plant Parts Usage
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {plantPartsData.length > 0 ? (
                  <div className="space-y-3 md:space-y-4">
                    {plantPartsData.map((item, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between text-xs md:text-sm">
                          <span className="text-gray-300 font-medium">{item.name}</span>
                          <span className="text-gray-400">{item.value} products</span>
                        </div>
                        <div className="w-full bg-gray-800 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${item.percentage}%` }}
                          />
                        </div>
                        <div className="text-right">
                          <span className="text-xs text-gray-500">{item.percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="h-64 flex items-center justify-center text-gray-400">
                    <div className="text-center">
                      <Leaf className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No plant parts data available</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <TrendingUp className="h-5 w-5 text-green-400" />
                Growth Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={600}>
                <AreaChart data={realTimeData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis dataKey="time" stroke="#9ca3af" fontSize={12} />
                  <YAxis stroke="#9ca3af" fontSize={12} />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1f2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="searches" 
                    stackId="1"
                    stroke="#22c55e" 
                    fill="#22c55e"
                    fillOpacity={0.3}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="views" 
                    stackId="1"
                    stroke="#3b82f6" 
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stages" className="space-y-4 md:space-y-6">
          <Card className="bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-xl">
            <CardHeader className="pb-3 md:pb-4">
              <CardTitle className="flex items-center gap-2 text-white text-base md:text-lg">
                <Zap className="h-4 w-4 md:h-5 md:w-5 text-yellow-400" />
                Commercialization Stages
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 md:gap-6">
                {/* Pie Chart - Now takes more space */}
                <div className="lg:col-span-3 order-2 lg:order-1">
                  <ResponsiveContainer width="100%" height={500}>
                    <PieChart>
                      <Pie
                        data={stageData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={false}
                        outerRadius={180}
                        innerRadius={60}
                        fill="#8884d8"
                        dataKey="value"
                        stroke="#374151"
                        strokeWidth={2}
                      >
                        {stageData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#1f2937',
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          fontSize: '14px'
                        }}
                        formatter={(value, name) => [
                          `${value} products`,
                          name
                        ]}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>

                {/* Compact Stage Filter & Summary */}
                <div className="lg:col-span-2 space-y-4 order-1 lg:order-2">
                  {/* Stage Filter Dropdown */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="text-base md:text-lg font-semibold text-white">Stages</h4>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-gray-800/50 border-gray-700 text-white hover:bg-gray-700/50"
                          >
                            <Filter className="h-4 w-4 mr-2" />
                            {selectedStages.length === allStages.length
                              ? 'All Stages'
                              : selectedStages.length === 0
                              ? 'No Stages'
                              : `${selectedStages.length} Selected`
                            }
                            <ChevronDown className="h-4 w-4 ml-2" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          className="w-64 bg-gray-900 border-gray-700"
                          align="end"
                        >
                          <div className="flex items-center justify-between p-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={selectAllStages}
                              className="text-xs text-green-400 hover:text-green-300"
                            >
                              Select All
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={clearAllStages}
                              className="text-xs text-red-400 hover:text-red-300"
                            >
                              Clear All
                            </Button>
                          </div>
                          <DropdownMenuSeparator className="bg-gray-700" />
                          {allStages.map((stage) => {
                            const stageCount = products?.filter(p => p.commercialization_stage === stage).length || 0;
                            return (
                              <DropdownMenuCheckboxItem
                                key={stage}
                                checked={selectedStages.includes(stage)}
                                onCheckedChange={() => toggleStage(stage)}
                                className="text-white hover:bg-gray-800 focus:bg-gray-800"
                              >
                                <div className="flex items-center justify-between w-full">
                                  <span className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-full"
                                      style={{ backgroundColor: getStageColor(stage) }}
                                    />
                                    {stage}
                                  </span>
                                  <span className="text-xs text-gray-400">
                                    {stageCount}
                                  </span>
                                </div>
                              </DropdownMenuCheckboxItem>
                            );
                          })}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    {/* Selected Stages Summary */}
                    {selectedStages.length > 0 && selectedStages.length < allStages.length && (
                      <div className="flex flex-wrap gap-2">
                        {selectedStages.slice(0, 3).map((stage) => (
                          <Badge
                            key={stage}
                            variant="secondary"
                            className="bg-gray-800/60 text-gray-300 text-xs"
                          >
                            {stage}
                          </Badge>
                        ))}
                        {selectedStages.length > 3 && (
                          <Badge
                            variant="secondary"
                            className="bg-gray-800/60 text-gray-300 text-xs"
                          >
                            +{selectedStages.length - 3} more
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Chart Legend - Compact */}
                  <div className="space-y-2">
                    {stageData.slice(0, 5).map((entry, index) => {
                      const total = stageData.reduce((sum, item) => sum + item.value, 0);
                      const percentage = Math.round((entry.value / total) * 100);

                      return (
                        <div key={index} className="flex items-center justify-between p-2 rounded bg-gray-800/20">
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full flex-shrink-0"
                              style={{ backgroundColor: entry.color }}
                            />
                            <span className="text-sm text-white truncate">{entry.name}</span>
                          </div>
                          <div className="text-xs text-gray-400">
                            {entry.value} ({percentage}%)
                          </div>
                        </div>
                      );
                    })}
                    {stageData.length > 5 && (
                      <div className="text-xs text-gray-500 text-center py-1">
                        +{stageData.length - 5} more stages
                      </div>
                    )}
                  </div>

                  {/* Total Summary */}
                  <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                    <div className="text-center">
                      <div className="text-xl font-bold text-green-400">
                        {stageData.reduce((sum, item) => sum + item.value, 0)}
                      </div>
                      <div className="text-xs text-gray-300">
                        {selectedStages.length === allStages.length ? 'Total Products' : 'Filtered Products'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-400">Live Searches</p>
                    <p className="text-2xl font-bold text-green-400">
                      {realTimeData[realTimeData.length - 1]?.searches || 0}
                    </p>
                  </div>
                  <div className="h-2 w-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-400">Page Views</p>
                    <p className="text-2xl font-bold text-blue-400">
                      {realTimeData[realTimeData.length - 1]?.views || 0}
                    </p>
                  </div>
                  <div className="h-2 w-2 bg-blue-400 rounded-full animate-pulse"></div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-400">Interactions</p>
                    <p className="text-2xl font-bold text-purple-400">
                      {realTimeData[realTimeData.length - 1]?.interactions || 0}
                    </p>
                  </div>
                  <div className="h-2 w-2 bg-purple-400 rounded-full animate-pulse"></div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <Activity className="h-5 w-5 text-green-400" />
                Real-time Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={500}>
                <LineChart data={realTimeData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis dataKey="time" stroke="#9ca3af" fontSize={12} />
                  <YAxis stroke="#9ca3af" fontSize={12} />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1f2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="searches" 
                    stroke="#22c55e" 
                    strokeWidth={2}
                    dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="views" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
