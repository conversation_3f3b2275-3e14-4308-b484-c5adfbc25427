#!/usr/bin/env python3
"""Debug why CLI is using wrong provider"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Load .env file FIRST
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from lib.supabase_client import get_supabase_client
from utils.ai_providers import MultiProviderAI, DeepSeekProvider
from utils.simple_ai_wrapper import get_simple_ai_provider
from agents.research.unified_research_agent import create_research_agent


async def test_cli_flow():
    """Test the exact flow the CLI uses"""
    
    # Step 1: Get AI provider like CLI does
    logger.info("Step 1: Getting AI provider...")
    ai_provider = get_simple_ai_provider("deepseek")
    logger.info(f"AI provider type: {type(ai_provider)}")
    
    # Test the provider
    logger.info("\nStep 2: Testing AI provider...")
    try:
        result = await ai_provider.generate("Say hello")
        logger.info(f"✅ Provider works! Result: {result[:50]}...")
    except Exception as e:
        logger.error(f"❌ Provider failed: {e}")
        return
    
    # Step 3: Create research agent
    logger.info("\nStep 3: Creating research agent...")
    supabase = get_supabase_client()
    agent = create_research_agent(supabase, ai_provider=ai_provider, features=['basic'])
    
    # Step 4: Test basic discovery
    logger.info("\nStep 4: Testing basic discovery...")
    try:
        # Test the _basic_discovery method directly
        products = await agent._basic_discovery("hemp construction materials", 2)
        logger.info(f"✅ Basic discovery found {len(products)} products")
        for p in products:
            logger.info(f"  - {p.get('title', p.get('name', 'Unknown'))}")
    except Exception as e:
        logger.error(f"❌ Basic discovery failed: {e}")
    
    # Step 5: Test full discovery
    logger.info("\nStep 5: Testing full discovery...")
    try:
        products = await agent.discover_products("hemp construction", max_results=2)
        logger.info(f"✅ Full discovery found {len(products)} products")
    except Exception as e:
        logger.error(f"❌ Full discovery failed: {e}")


if __name__ == "__main__":
    asyncio.run(test_cli_flow())