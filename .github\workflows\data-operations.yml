name: Data Operations Hub

on:
  # Scheduled runs
  schedule:
    - cron: '15 * * * *'    # Hourly discovery
    - cron: '0 12 * * *'    # Daily health check
    - cron: '0 9 * * 1'    # Weekly research (Monday)
    - cron: '0 10 1 * *'   # Monthly expansion (1st of month)
    
  # Manual trigger with options
  workflow_dispatch:
    inputs:
      operation:
        description: 'Operation to perform'
        required: true
        type: choice
        options:
          - auto              # Let schedule determine
          - hourly-discovery  # Product discovery
          - daily-health      # Health check
          - weekly-research   # Deep research
          - monthly-expansion # Market analysis
          - all-reports       # Generate all reports
        default: 'auto'
        
      max_items:
        description: 'Maximum items to process'
        required: false
        default: '10'
        type: string
        
      test_mode:
        description: 'Run in test mode (no saves)'
        required: false
        default: false
        type: boolean

permissions:
  contents: read
  actions: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event.inputs.operation || 'scheduled' }}
  cancel-in-progress: false  # Don't cancel data operations

env:
  PYTHONPATH: ${{ github.workspace }}

jobs:
  determine-operation:
    runs-on: ubuntu-latest
    outputs:
      operation: ${{ steps.determine.outputs.operation }}
      should_run: ${{ steps.determine.outputs.should_run }}
      
    steps:
    - name: Determine Operation
      id: determine
      run: |
        # Get current time
        HOUR=$(date +%H)
        DAY=$(date +%d)
        WEEKDAY=$(date +%u)
        
        # Manual override
        if [ "${{ github.event.inputs.operation }}" != "" ] && [ "${{ github.event.inputs.operation }}" != "auto" ]; then
          echo "operation=${{ github.event.inputs.operation }}" >> $GITHUB_OUTPUT
          echo "should_run=true" >> $GITHUB_OUTPUT
          exit 0
        fi
        
        # Scheduled operations
        if [ "${{ github.event_name }}" == "schedule" ]; then
          # Monthly (1st day, 10 AM)
          if [ "$DAY" == "01" ] && [ "$HOUR" == "10" ]; then
            echo "operation=monthly-expansion" >> $GITHUB_OUTPUT
            echo "should_run=true" >> $GITHUB_OUTPUT
          # Weekly (Monday, 9 AM)
          elif [ "$WEEKDAY" == "1" ] && [ "$HOUR" == "09" ]; then
            echo "operation=weekly-research" >> $GITHUB_OUTPUT
            echo "should_run=true" >> $GITHUB_OUTPUT
          # Daily (12 PM)
          elif [ "$HOUR" == "12" ]; then
            echo "operation=daily-health" >> $GITHUB_OUTPUT
            echo "should_run=true" >> $GITHUB_OUTPUT
          # Hourly (all other times)
          else
            echo "operation=hourly-discovery" >> $GITHUB_OUTPUT
            echo "should_run=true" >> $GITHUB_OUTPUT
          fi
        else
          echo "operation=hourly-discovery" >> $GITHUB_OUTPUT
          echo "should_run=true" >> $GITHUB_OUTPUT
        fi

  setup-environment:
    needs: determine-operation
    if: needs.determine-operation.outputs.should_run == 'true'
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
        cache-dependency-path: |
          requirements.txt
          **/requirements*.txt
        
    - name: Prepare Environment
      run: |
        # Create directory structure
        mkdir -p lib agents data logs reports
        touch lib/__init__.py agents/__init__.py
        
        # Install dependencies
        if [ -f requirements.txt ]; then
          pip install -r requirements.txt
        else
          pip install supabase requests beautifulsoup4 python-dotenv openai
        fi
        
        # Create shared utilities
        cat > lib/shared_utils.py << 'EOF'
import os
from datetime import datetime
from supabase import create_client

def get_supabase_client():
    """Get Supabase client with best available credentials"""
    url = os.environ.get('SUPABASE_URL')
    key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY') or os.environ.get('SUPABASE_ANON_KEY')
    
    if not url or not key:
        raise ValueError("Missing Supabase credentials")
    
    return create_client(url, key)

def log_operation(operation, status, details=None):
    """Log operation to database"""
    try:
        client = get_supabase_client()
        client.table('hemp_agent_runs').insert({
            'agent_name': f'data_ops_{operation}',
            'timestamp': datetime.now().isoformat(),
            'status': status,
            'products_saved': details.get('products_saved', 0) if details else 0,
            'companies_saved': details.get('companies_saved', 0) if details else 0
        }).execute()
    except:
        pass  # Don't fail if logging fails

def get_plant_part_mapping():
    """Standard plant part ID mapping"""
    return {
        'seeds': 8,
        'fiber': 2,
        'oil': 7,
        'flowers': 3,
        'hurds': 4,
        'biomass': 5,
        'leaves': 6,
        'roots': 9
    }
EOF

  run-operation:
    needs: [determine-operation, setup-environment]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
        cache-dependency-path: |
          requirements.txt
          **/requirements*.txt
        
    - name: Install Dependencies
      run: |
        pip install supabase requests beautifulsoup4 python-dotenv openai || true
        mkdir -p lib agents data reports
        touch lib/__init__.py agents/__init__.py
        
    - name: Setup Shared Utils
      run: |
        # Recreate shared utils file
        cat > lib/shared_utils.py << 'EOF'
import os
from datetime import datetime
from supabase import create_client

def get_supabase_client():
    """Get Supabase client with best available credentials"""
    url = os.environ.get('SUPABASE_URL')
    key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY') or os.environ.get('SUPABASE_ANON_KEY')
    
    if not url or not key:
        raise ValueError("Missing Supabase credentials")
    
    return create_client(url, key)

def log_operation(operation, status, details=None):
    """Log operation to database"""
    try:
        client = get_supabase_client()
        client.table('hemp_agent_runs').insert({
            'agent_name': f'data_ops_{operation}',
            'timestamp': datetime.now().isoformat(),
            'status': status,
            'products_saved': details.get('products_saved', 0) if details else 0,
            'companies_saved': details.get('companies_saved', 0) if details else 0
        }).execute()
    except:
        pass  # Don't fail if logging fails

def get_plant_part_mapping():
    """Standard plant part ID mapping"""
    return {
        'seeds': 8,
        'fiber': 2,
        'oil': 7,
        'flowers': 3,
        'hurds': 4,
        'biomass': 5,
        'leaves': 6,
        'roots': 9
    }
EOF
        
    - name: Run Operation - ${{ needs.determine-operation.outputs.operation }}
      env:
        OPERATION: ${{ needs.determine-operation.outputs.operation }}
        MAX_ITEMS: ${{ github.event.inputs.max_items || '10' }}
        TEST_MODE: ${{ github.event.inputs.test_mode || 'false' }}
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      run: |
        echo "🚀 Running operation: $OPERATION"
        
        # Run the operation script
        python .github/scripts/run_operation.py
        
    - name: Generate Unified Report
      if: always()
      env:
        OPERATION: ${{ needs.determine-operation.outputs.operation }}
      run: |
        # Create markdown report
        python .github/scripts/generate_report.py "$OPERATION"
        
    - name: Upload Reports
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: data-ops-${{ needs.determine-operation.outputs.operation }}-${{ github.run_number }}
        path: reports/
        retention-days: 7
        
    - name: Summary
      if: always()
      run: |
        echo "## 📊 Data Operations Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Operation**: ${{ needs.determine-operation.outputs.operation }}" >> $GITHUB_STEP_SUMMARY
        echo "**Time**: $(date +'%Y-%m-%d %H:%M UTC')" >> $GITHUB_STEP_SUMMARY
        echo "**Status**: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ -f reports/unified-report.md ]; then
          cat reports/unified-report.md | tail -n +5 >> $GITHUB_STEP_SUMMARY
        fi