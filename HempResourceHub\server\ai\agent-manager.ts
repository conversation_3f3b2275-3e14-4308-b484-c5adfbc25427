import { getClaudeService, ClaudeService } from './claude-service';
import { storage } from '../storage-db';

export interface AgentTask {
  id: string;
  agentId: string;
  conversationId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  input: any;
  output?: any;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

export class AgentManager {
  private claudeService: ClaudeService;
  private activeTasks: Map<string, AgentTask> = new Map();
  private taskQueue: AgentTask[] = [];
  private maxConcurrentTasks = 3;

  constructor() {
    this.claudeService = getClaudeService();
  }

  async runProductDiscovery(query: string): Promise<any> {
    const conversationId = await this.claudeService.createConversation('product-discovery');
    
    try {
      const prompt = `Search for industrial hemp products related to: "${query}"
      
Please provide a comprehensive analysis including:
1. Product name and detailed description
2. Specific plant parts used (seeds, flowers, stalks, leaves, roots)
3. Industry categories and applications
4. Manufacturing companies (if known)
5. Market stage (emerging, growing, mature)
6. Sustainability benefits
7. Potential market size or growth projections
8. Any relevant sources or references

Format the response as structured data that can be easily parsed.`;

      const response = await this.claudeService.sendMessage(conversationId, prompt);
      const content = response.content[0].text;

      // Parse and structure the response
      const structuredData = this.parseProductDiscoveryResponse(content);
      
      // Save to database if valid
      if (structuredData.productName && structuredData.description) {
        await this.saveDiscoveredProduct(structuredData);
      }

      return structuredData;
    } finally {
      this.claudeService.deleteConversation(conversationId);
    }
  }

  async generateCode(specification: string, codeType: string): Promise<string> {
    const conversationId = await this.claudeService.createConversation('code-generator');
    
    try {
      const prompt = `Generate ${codeType} code for the following specification:

${specification}

Requirements:
- Use TypeScript with proper type definitions
- Follow React best practices if applicable
- Use Tailwind CSS for styling
- Include error handling
- Add helpful comments
- Follow the hemp industry app conventions

Please provide complete, working code.`;

      const response = await this.claudeService.sendMessage(conversationId, prompt);
      return response.content[0].text;
    } finally {
      this.claudeService.deleteConversation(conversationId);
    }
  }

  async analyzeData(dataQuery: string, dataContext?: any): Promise<any> {
    const conversationId = await this.claudeService.createConversation('data-analyst');
    
    try {
      const prompt = `Analyze the following data query: "${dataQuery}"

${dataContext ? `Context data: ${JSON.stringify(dataContext, null, 2)}` : ''}

Please provide:
1. Key insights and trends
2. Statistical analysis if applicable
3. Visualizations recommendations
4. Actionable recommendations
5. Areas for further investigation`;

      const response = await this.claudeService.sendMessage(conversationId, prompt);
      return this.parseAnalysisResponse(response.content[0].text);
    } finally {
      this.claudeService.deleteConversation(conversationId);
    }
  }

  async generateContent(topic: string, contentType: string, keywords?: string[]): Promise<string> {
    const conversationId = await this.claudeService.createConversation('content-writer');
    
    try {
      const prompt = `Create ${contentType} content about: "${topic}"

${keywords ? `Target keywords: ${keywords.join(', ')}` : ''}

Requirements:
- SEO-optimized structure
- Engaging and informative
- Industry-appropriate terminology
- Include relevant statistics and facts
- Natural keyword integration
- Professional tone
- Cite sources when possible`;

      const response = await this.claudeService.sendMessage(conversationId, prompt);
      return response.content[0].text;
    } finally {
      this.claudeService.deleteConversation(conversationId);
    }
  }

  // Run multiple agents concurrently
  async runMultiAgentTask(tasks: Array<{
    agentId: string;
    prompt: string;
    taskId?: string;
  }>): Promise<Map<string, any>> {
    const results = new Map<string, any>();
    
    const promises = tasks.map(async (task) => {
      const taskId = task.taskId || `${task.agentId}-${Date.now()}`;
      try {
        const result = await this.claudeService.query(task.agentId, task.prompt);
        results.set(taskId, { success: true, data: result });
      } catch (error) {
        results.set(taskId, { success: false, error: error.message });
      }
    });

    await Promise.all(promises);
    return results;
  }

  private parseProductDiscoveryResponse(content: string): any {
    // Basic parsing logic - can be enhanced with better NLP
    const structured = {
      productName: '',
      description: '',
      plantParts: [],
      industries: [],
      companies: [],
      marketStage: '',
      sustainability: '',
      sources: []
    };

    // Extract sections based on numbered points or headers
    const lines = content.split('\n');
    let currentSection = '';

    for (const line of lines) {
      if (line.includes('Product name') || line.includes('1.')) {
        currentSection = 'product';
      } else if (line.includes('plant parts') || line.includes('2.')) {
        currentSection = 'parts';
      } else if (line.includes('Industry') || line.includes('3.')) {
        currentSection = 'industry';
      } else if (line.includes('companies') || line.includes('4.')) {
        currentSection = 'companies';
      } else if (line.includes('Market stage') || line.includes('5.')) {
        currentSection = 'market';
      } else if (line.includes('Sustainability') || line.includes('6.')) {
        currentSection = 'sustainability';
      }

      // Parse based on current section
      switch (currentSection) {
        case 'product':
          if (!structured.productName && line.trim() && !line.includes('1.')) {
            structured.productName = line.trim().replace(/^[-•]\s*/, '');
          }
          break;
        case 'parts':
          if (line.includes('seeds') || line.includes('flowers') || 
              line.includes('stalks') || line.includes('leaves') || 
              line.includes('roots')) {
            structured.plantParts.push(...line.match(/\b(seeds?|flowers?|stalks?|leaves?|roots?)\b/gi) || []);
          }
          break;
        // Additional parsing logic...
      }
    }

    return structured;
  }

  private parseAnalysisResponse(content: string): any {
    return {
      insights: [],
      statistics: {},
      visualizations: [],
      recommendations: [],
      furtherInvestigation: [],
      rawAnalysis: content
    };
  }

  private async saveDiscoveredProduct(productData: any): Promise<void> {
    try {
      // Check for existing product
      const existing = await storage.searchHempProducts(productData.productName);
      if (existing.length > 0) {
        console.log('Product already exists:', productData.productName);
        return;
      }

      // Map plant parts to IDs
      const plantParts = await storage.getAllPlantParts();
      const plantPartIds = productData.plantParts
        .map(part => plantParts.find(p => p.name.toLowerCase().includes(part.toLowerCase()))?.id)
        .filter(Boolean);

      // Create product
      await storage.createHempProduct({
        name: productData.productName,
        description: productData.description,
        plantPartIds: plantPartIds,
        subIndustryIds: [], // Would need additional mapping
        sustainability: productData.sustainability,
        marketStage: productData.marketStage || 'emerging',
      });

      console.log('Product saved:', productData.productName);
    } catch (error) {
      console.error('Error saving product:', error);
    }
  }
}