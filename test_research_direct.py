#!/usr/bin/env python3
"""Test research agent with DeepSeek directly"""

import asyncio
import os
import sys
import logging

# Setup
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ['DEEPSEEK_API_KEY'] = '***********************************'

# Enable debug logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from hemp_cli import HempCLI


async def main():
    """Run research test"""
    cli = HempCLI()
    
    print("Running research agent with DeepSeek...")
    
    results = await cli.run_agent(
        agent_type='research',
        task='hemp plastic alternatives for packaging',
        features=['basic'],
        ai_provider='deepseek',
        max_results=1
    )
    
    print(f"\n=== Results ===")
    if results:
        print(f"Found {len(results)} products")
        for r in results:
            print(f"- {r.get('name', 'Unknown')}")
    else:
        print("No results found")


if __name__ == "__main__":
    asyncio.run(main())