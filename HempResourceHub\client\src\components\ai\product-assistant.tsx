import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Spark<PERSON>, Send, Loader2, RefreshCw } from 'lucide-react';
import { useClaude } from '@/hooks/use-claude';

interface ProductAssistantProps {
  productName?: string;
  productDescription?: string;
}

export function ProductAssistant({ productName, productDescription }: ProductAssistantProps) {
  const [input, setInput] = useState('');
  
  // Add debugging
  useEffect(() => {
    console.log('[ProductAssistant] Component mounted', {
      productName,
      productDescription,
      timestamp: new Date().toISOString()
    });
    
    return () => {
      console.log('[ProductAssistant] Component unmounting', {
        timestamp: new Date().toISOString()
      });
    };
  }, []);
  
  const {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    conversationId,
  } = useClaude({
    agentId: 'product-discovery',
    onError: (err) => console.error('[ProductAssistant] Claude error:', err),
  });
  
  // Debug conversation creation
  useEffect(() => {
    console.log('[ProductAssistant] Conversation ID changed:', conversationId);
  }, [conversationId]);

  // Send initial context if product info is provided
  useEffect(() => {
    if (productName && productDescription && messages.length === 0) {
      const context = `I'm looking at a hemp product called "${productName}". Description: ${productDescription}. Can you help me understand its applications and market potential?`;
      sendMessage(context);
    }
  }, [productName, productDescription, messages.length, sendMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage = input;
    setInput('');
    await sendMessage(userMessage);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-green-600" />
            Hemp Product Assistant
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={clearMessages}
            disabled={messages.length === 0}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Clear
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Messages Display */}
          <div className="min-h-[200px] max-h-[400px] overflow-y-auto space-y-3 p-4 bg-gray-50 rounded-lg">
            {messages.length === 0 && (
              <p className="text-gray-500 text-center">
                Ask me anything about hemp products{productName ? `, especially about ${productName}` : ''}!
              </p>
            )}
            
            {messages.map((message, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-green-100 ml-auto max-w-[80%]'
                    : 'bg-white border border-gray-200 mr-auto max-w-[80%]'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex items-center gap-2 text-gray-500">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Analyzing...</span>
              </div>
            )}
            
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">Error: {error.message}</p>
              </div>
            )}
          </div>

          {/* Input Form */}
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Ask about hemp products, uses, benefits..."
              disabled={isLoading}
              className="flex-1"
            />
            <Button type="submit" disabled={isLoading || !input.trim()}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  );
}