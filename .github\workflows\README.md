# GitHub Actions Workflows

This directory contains automated workflows for the Hemp Resource Hub project.

## 🚀 Active Workflows

### 1. **Hourly Product Discovery** (`hourly-product-discovery.yml`) 🆕
- **Schedule**: Every hour at :15
- **Purpose**: Continuous product discovery and database population
- **Features**:
  - Auto-detects available APIs (OpenAI, web search)
  - Configurable discovery methods
  - Category-focused searches
  - Automatic retry on failures
  - Low resource usage (~5 min runtime)

### 2. **Daily Database Growth & Health Check** (`daily-health-check.yml`)
- **Schedule**: Daily at 12:00 UTC
- **Purpose**: Monitor database health and growth metrics
- **Key Metrics**:
  - Products/companies added in 24h
  - Agent success rates
  - Data quality scores
  - Growth trend analysis

### 3. **Weekly Hemp Research** (`weekly-hemp-research-optimized.yml`) 🔧
- **Schedule**: Every Monday at 9:00 AM UTC
- **Purpose**: Deep research for new products
- **Optimizations**:
  - Pre-flight checks for missing secrets
  - Fallback discovery methods
  - Self-contained scripts
  - Better error handling

### 4. **Monthly Market Expansion** (`monthly-market-expansion.yml`)
- **Schedule**: First Monday of month at 10:00 AM UTC
- **Purpose**: Market analysis and strategic planning
- **Focus Areas**:
  - Emerging markets
  - Competitive analysis
  - Geographic expansion
  - Deep discovery

## 🛠️ Utility Workflows

### 5. **Diagnostic Check** (`diagnostic-check.yml`) 🆕
- **Trigger**: Manual only
- **Purpose**: Troubleshoot workflow failures
- **Checks**:
  - Environment setup
  - Secret configuration
  - Dependencies
  - Database connectivity

### 6. **Manual Agent Operations** (`manual-agent-operations.yml`)
- **Trigger**: Manual only
- **Purpose**: Test individual components
- **Operations**:
  - Environment testing
  - Import verification
  - Database testing
  - Agent dry runs

### 7. **Enhanced Basic Test** (`test-basic-clean.yml`)
- **Trigger**: Manual only
- **Purpose**: Comprehensive test suite
- **Levels**: basic, comprehensive, database-only

## 📋 Setup Requirements

### Required GitHub Secrets
```yaml
SUPABASE_URL          # Your Supabase project URL
SUPABASE_ANON_KEY     # Supabase anonymous key
SUPABASE_SERVICE_ROLE_KEY  # (Optional) For admin operations
OPENAI_API_KEY        # (Optional) For AI-powered discovery
```

### Setting Up Secrets
1. Go to Settings → Secrets and variables → Actions
2. Click "New repository secret"
3. Add each required secret

## 🔧 Troubleshooting

### Common Issues & Solutions

1. **"Missing Supabase credentials"**
   - Ensure `SUPABASE_URL` and `SUPABASE_ANON_KEY` are set in GitHub Secrets
   - Run the Diagnostic Check workflow to verify

2. **"requirements.txt not found"**
   - The optimized workflows create minimal requirements automatically
   - Commit a `requirements.txt` file for full dependency management

3. **"Module import errors"**
   - Workflows now create necessary directory structure
   - Self-contained scripts reduce import dependencies

4. **"Database connection failed"**
   - Check Supabase credentials are correct
   - Verify Supabase project is active
   - Use SERVICE_ROLE_KEY for RLS bypass

### Running Diagnostics
```bash
# 1. Run diagnostic check first
# Go to Actions → Diagnostic Check → Run workflow

# 2. Check the Summary section for quick status
# 3. Download artifacts for detailed logs
```

## 📊 Monitoring

### Key Metrics to Track
- **Hourly Discovery**: Products saved per hour
- **Daily Health**: Growth rate and data quality
- **Weekly Research**: New products by category
- **Monthly Analysis**: Market opportunities identified

### Viewing Reports
1. Go to Actions tab
2. Click on a completed workflow run
3. Check "Summary" section for quick overview
4. Download artifacts for detailed reports

## 🚀 Quick Start

1. **Set up secrets** (see Requirements above)
2. **Run Diagnostic Check** to verify setup
3. **Enable hourly discovery** - it will start automatically
4. **Monitor daily reports** for growth tracking

## 📈 Performance Tips

1. **Hourly Discovery**: Keep max_products low (5-10) to avoid rate limits
2. **Weekly Research**: Use 'balanced' or 'high-priority-only' for efficiency
3. **Caching**: Python setup action caches pip for faster runs
4. **Parallel Limits**: Set to prevent API rate limiting

## 🔄 Workflow Dependencies

```mermaid
graph TD
    A[Hourly Discovery] -->|Feeds| B[Daily Health Check]
    B -->|Informs| C[Weekly Research]
    C -->|Guides| D[Monthly Expansion]
    E[Diagnostic Check] -->|Validates| A
    E -->|Validates| B
    E -->|Validates| C
```

## 📝 Maintenance

- **Logs**: Retained for 7-90 days based on importance
- **Artifacts**: Discovery results (3 days), Reports (30 days)
- **Cleanup**: Old artifacts auto-delete based on retention settings

---

For issues or improvements, please create a GitHub issue or submit a PR.