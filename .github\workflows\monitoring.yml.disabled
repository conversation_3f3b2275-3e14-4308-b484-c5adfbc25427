name: System Monitoring and Alerts

on:
  schedule:
    # Run every hour
    - cron: '0 * * * *'
  workflow_dispatch:
    inputs:
      alert_severity:
        description: 'Alert severity levels (comma-separated)'
        required: false
        default: 'warning,error,critical'
      export_metrics:
        description: 'Export metrics to artifact'
        required: false
        default: 'true'
        type: boolean

jobs:
  monitor:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        
    - name: Make hemp executable
      run: chmod +x hemp
        
    - name: Run monitoring check
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        PYTHONPATH: ${{ github.workspace }}
        ALERT_SEVERITY: ${{ github.event.inputs.alert_severity || 'warning,error,critical' }}
        EXPORT_METRICS: ${{ github.event.inputs.export_metrics || 'true' }}
        METRICS_FILE: monitoring-metrics.json
      run: |
        python scripts/monitor_and_alert.py
        
    - name: Upload metrics artifact
      if: ${{ github.event.inputs.export_metrics == 'true' || github.event_name == 'schedule' }}
      uses: actions/upload-artifact@v3
      with:
        name: monitoring-metrics-${{ github.run_number }}
        path: monitoring-metrics.json
        retention-days: 30
        
    - name: Generate monitoring report
      if: always()
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        PYTHONPATH: ${{ github.workspace }}
      run: |
        python hemp monitor --format report > monitoring-report.md || echo "# Monitoring Report Failed" > monitoring-report.md
        
    - name: Upload monitoring report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: monitoring-report-${{ github.run_number }}
        path: monitoring-report.md
        retention-days: 30
        
    - name: Create issue for critical alerts
      if: failure() && github.event_name == 'schedule'
      uses: actions/github-script@v6
      with:
        script: |
          const title = `🚨 Critical System Alert - ${new Date().toISOString()}`;
          const body = `
          ## System Monitoring Alert
          
          Critical alerts were triggered during the automated monitoring check.
          
          **Run Details:**
          - Run ID: ${{ github.run_id }}
          - Run Number: ${{ github.run_number }}
          - [View Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
          
          **Next Steps:**
          1. Check the [monitoring artifacts](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
          2. Review the monitoring report
          3. Check the system dashboard: \`./hemp monitor --live\`
          
          cc: @${{ github.repository_owner }}
          `;
          
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['alert', 'monitoring', 'automated']
          });

  daily-summary:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' && github.event.schedule == '0 8 * * *'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        
    - name: Make hemp executable
      run: chmod +x hemp
        
    - name: Generate daily summary
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        PYTHONPATH: ${{ github.workspace }}
      run: |
        # Generate comprehensive daily report
        python hemp monitor --format report > daily-report.md || echo "# Daily Report Failed" > daily-report.md
        
        # Export metrics for historical tracking
        python hemp monitor --format json --export daily-metrics.json || echo "{}" > daily-metrics.json
        
        # Get image generation status
        python hemp images status >> daily-report.md || echo "Image status unavailable" >> daily-report.md
        
        # Check agent performance
        echo -e "\n\n## Agent Performance (Last 24h)" >> daily-report.md
        python hemp agent orchestrator "Generate 24h agent performance summary" >> daily-report.md || echo "Agent performance unavailable" >> daily-report.md
        
    - name: Upload daily report
      uses: actions/upload-artifact@v3
      with:
        name: daily-monitoring-report-${{ github.run_number }}
        path: |
          daily-report.md
          daily-metrics.json
        retention-days: 90