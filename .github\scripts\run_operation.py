#!/usr/bin/env python3
"""Run data operations based on schedule or manual trigger."""

import os
import json
import random
from datetime import datetime, timedelta

# Get operation parameters
operation = os.environ.get('OPERATION', 'hourly-discovery')
max_items = int(os.environ.get('MAX_ITEMS', '10'))
test_mode = os.environ.get('TEST_MODE', 'false').lower() == 'true'

print(f"Operation: {operation}")
print(f"Max Items: {max_items}")
print(f"Test Mode: {test_mode}")

# Import utilities
if os.path.exists('lib/shared_utils.py'):
    exec(open('lib/shared_utils.py').read())

def hourly_discovery():
    """Quick product discovery"""
    products = []
    categories = ['food', 'textiles', 'cosmetics', 'wellness']
    
    for i in range(min(max_items, 10)):
        cat = random.choice(categories)
        product = {
            'name': f'{cat.title()} Product {datetime.now().strftime("%H%M%S")}-{i}',
            'description': f'Discovered {cat} product',
            'category': cat,
            'plant_part_id': {'food': 8, 'textiles': 2, 'cosmetics': 7, 'wellness': 3}[cat]
        }
        products.append(product)
    
    return {'products': products, 'type': 'discovery'}

def daily_health():
    """Database health check"""
    try:
        from lib.shared_utils import get_supabase_client
        client = get_supabase_client()
        
        # Get stats
        products_count = client.table('uses_products').select('id', count='exact').execute().count
        companies_count = client.table('hemp_companies').select('id', count='exact').execute().count
        
        # Recent activity
        yesterday = (datetime.now() - timedelta(days=1)).isoformat()
        recent_products = client.table('uses_products').select('id').gte('created_at', yesterday).execute()
        
        return {
            'stats': {
                'total_products': products_count,
                'total_companies': companies_count,
                'products_24h': len(recent_products.data) if recent_products.data else 0
            },
            'type': 'health'
        }
    except Exception as e:
        return {'error': str(e), 'type': 'health'}

def weekly_research():
    """Deep research for new products"""
    products = []
    research_areas = ['sustainable-packaging', 'biomedical', 'automotive', 'aerospace']
    
    for area in research_areas[:2]:  # Limit to 2 areas
        for i in range(min(max_items // 2, 5)):
            product = {
                'name': f'{area.title()} Innovation {datetime.now().strftime("%Y%m%d")}-{i}',
                'description': f'Advanced {area} application of hemp',
                'category': area,
                'plant_part_id': 2  # Default to fiber
            }
            products.append(product)
    
    return {'products': products, 'type': 'research'}

def monthly_expansion():
    """Market expansion analysis"""
    opportunities = [
        {'market': 'Asia-Pacific', 'potential': 'high', 'focus': 'CBD wellness'},
        {'market': 'Latin America', 'potential': 'medium', 'focus': 'Industrial fiber'},
        {'market': 'Africa', 'potential': 'emerging', 'focus': 'Sustainable materials'}
    ]
    
    return {
        'opportunities': opportunities,
        'recommendations': [
            'Focus on Asia-Pacific CBD market',
            'Develop partnerships in Latin America',
            'Research sustainable materials for Africa'
        ],
        'type': 'expansion'
    }

# Run the appropriate operation
result = None
if operation == 'hourly-discovery':
    result = hourly_discovery()
elif operation == 'daily-health':
    result = daily_health()
elif operation == 'weekly-research':
    result = weekly_research()
elif operation == 'monthly-expansion':
    result = monthly_expansion()
elif operation == 'all-reports':
    result = {
        'discovery': hourly_discovery(),
        'health': daily_health(),
        'research': weekly_research(),
        'expansion': monthly_expansion(),
        'type': 'all'
    }

# Save products if not in test mode
saved_count = 0
if result and 'products' in result and not test_mode:
    try:
        from lib.shared_utils import get_supabase_client
        client = get_supabase_client()
        
        for product in result['products']:
            try:
                # Check if exists
                existing = client.table('uses_products').select('id').eq('name', product['name']).execute()
                if not existing.data:
                    data = {
                        'name': product['name'],
                        'description': product['description'],
                        'plant_part_id': product.get('plant_part_id', 8),
                        'stage': 'Growing',
                        'image_url': '/images/unknown-hemp-image.png'
                    }
                    client.table('uses_products').insert(data).execute()
                    saved_count += 1
            except Exception as e:
                print(f"Error saving product: {e}")
    except Exception as e:
        print(f"Database error: {e}")

# Generate report
report = {
    'operation': operation,
    'timestamp': datetime.now().isoformat(),
    'result': result,
    'saved_count': saved_count,
    'test_mode': test_mode
}

# Save report
os.makedirs('reports', exist_ok=True)
with open(f'reports/{operation}-report.json', 'w') as f:
    json.dump(report, f, indent=2)

print(f"\n✅ Operation completed")
print(f"   Type: {operation}")
if 'products' in result:
    print(f"   Products: {len(result['products'])}")
    print(f"   Saved: {saved_count}")