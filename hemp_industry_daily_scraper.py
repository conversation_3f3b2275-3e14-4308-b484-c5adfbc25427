#!/usr/bin/env python3
"""
Scraper for Hemp Industry Daily article images
"""
import os
import requests
import time
from datetime import datetime, timezone
from dotenv import load_dotenv
from urllib.parse import urljoin

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
}

class HempIndustryDailyScraper:
    def __init__(self):
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        
    def extract_article_image(self, title: str, url: str) -> str:
        """Extract main image from Hemp Industry Daily article"""
        if not url or 'hempindustrydaily' not in url:
            return None
            
        try:
            response = requests.get(url, 
                                  headers={'User-Agent': self.user_agent}, 
                                  timeout=10)
            
            if response.status_code == 200:
                html = response.text
                
                # Hemp Industry Daily specific patterns
                import re
                
                # Method 1: Open Graph image (most reliable for news sites)
                og_match = re.search(r'property="og:image"\s+content="([^"]+)"', html, re.IGNORECASE)
                if og_match:
                    return og_match.group(1)
                
                # Method 2: Featured image pattern
                featured_patterns = [
                    r'class="featured-image"[^>]*src="([^"]+)"',
                    r'class="wp-post-image"[^>]*src="([^"]+)"',
                    r'class="attachment-[^"]*"[^>]*src="([^"]+)"',
                    r'<figure[^>]*><img[^>]*src="([^"]+)"'
                ]
                
                for pattern in featured_patterns:
                    match = re.search(pattern, html, re.IGNORECASE)
                    if match:
                        return urljoin(url, match.group(1))
                
        except Exception as e:
            print(f"   Error: {str(e)[:80]}...")
            
        return None
    
    def update_research_images(self):
        """Update research entries from Hemp Industry Daily"""
        # Get entries from Hemp Industry Daily without images
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/research_entries",
            headers=headers,
            params={
                "image_url": "is.null",
                "full_text_url": "like.*hempindustrydaily*",
                "select": "id,title,full_text_url"
            }
        )
        
        entries = response.json()
        print(f"🔍 Found {len(entries)} Hemp Industry Daily articles without images\n")
        
        updated = 0
        
        for entry in entries:
            print(f"📰 {entry['title'][:60]}...")
            print(f"   URL: {entry['full_text_url'][:80]}...")
            
            image_url = self.extract_article_image(entry['title'], entry['full_text_url'])
            
            if image_url:
                # Update the image URL
                update_data = {
                    "image_url": image_url,
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                response = requests.patch(
                    f"{SUPABASE_URL}/rest/v1/research_entries?id=eq.{entry['id']}",
                    headers=headers,
                    json=update_data
                )
                
                if response.status_code in [200, 204]:
                    updated += 1
                    print(f"   ✅ Found image: {image_url[:80]}...")
                else:
                    print(f"   ❌ Failed to save")
            else:
                print(f"   ⚠️  No image found")
                
            print()
            time.sleep(1)  # Rate limiting
        
        return updated
    
    def add_generic_images_for_other_entries(self):
        """Add placeholder images for entries without URLs"""
        # Map entry types to generic images
        generic_images = {
            "Paper": "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&q=80",  # Books/research
            "Patent": "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=800&q=80",  # Technical drawing
            "Report": "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&q=80",   # Charts/graphs
            "Article": "https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&q=80"  # News
        }
        
        # Get entries without images or URLs
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/research_entries",
            headers=headers,
            params={
                "image_url": "is.null",
                "full_text_url": "is.null",
                "select": "id,title,entry_type"
            }
        )
        
        entries = response.json()
        if entries:
            print(f"\n🖼️  Adding generic images for {len(entries)} entries without URLs\n")
            
            updated = 0
            for entry in entries:
                entry_type = entry.get('entry_type', 'Article')
                image_url = generic_images.get(entry_type, generic_images['Article'])
                
                update_data = {
                    "image_url": image_url,
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                response = requests.patch(
                    f"{SUPABASE_URL}/rest/v1/research_entries?id=eq.{entry['id']}",
                    headers=headers,
                    json=update_data
                )
                
                if response.status_code in [200, 204]:
                    updated += 1
                    print(f"✅ Added generic {entry_type} image for: {entry['title'][:50]}...")
                
            print(f"\n📊 Added {updated} generic images")
        
        return updated


def main():
    """Run Hemp Industry Daily scraper"""
    print("🚀 Hemp Industry Daily Article Image Scraper\n")
    
    scraper = HempIndustryDailyScraper()
    
    # Scrape Hemp Industry Daily articles
    hid_updated = scraper.update_research_images()
    
    # Add generic images for entries without URLs
    generic_updated = scraper.add_generic_images_for_other_entries()
    
    print(f"\n✅ Updated {hid_updated} Hemp Industry Daily images")
    print(f"✅ Added {generic_updated} generic images")
    
    # Show stats
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/research_entries?select=id&image_url=not.is.null", 
        headers={**headers, "Prefer": "count=exact"}
    )
    
    if 'content-range' in response.headers:
        total_with_images = response.headers['content-range'].split('/')[1]
        print(f"\n📊 Total research entries with images: {total_with_images}")


if __name__ == "__main__":
    main()