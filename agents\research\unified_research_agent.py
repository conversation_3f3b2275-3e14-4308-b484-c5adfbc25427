#!/usr/bin/env python3
"""
Unified Hemp Research Agent
Combines all research agent functionality with configurable features
"""

import asyncio
import json
import logging
import os
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

import aiohttp
from bs4 import BeautifulSoup
import feedparser
from tenacity import retry, stop_after_attempt, wait_exponential

# Try to import EnhancedBaseAgent, fall back to regular BaseAgent if not available
try:
    from ..core.enhanced_base_agent import EnhancedBaseAgent, rate_limited, track_performance
    BaseAgentClass = EnhancedBaseAgent
except ImportError:
    from ..core.base_agent import BaseAgent, rate_limited, track_performance
    BaseAgentClass = BaseAgent

logger = logging.getLogger(__name__)


class ResearchFeatures(Enum):
    """Available features for the research agent"""
    BASIC = "basic"  # Basic product discovery
    COMPANY_EXTRACTION = "company_extraction"  # Extract and save company data
    IMAGE_GENERATION = "image_generation"  # Auto-generate product images
    DEEP_ANALYSIS = "deep_analysis"  # Extended AI analysis
    WEB_SCRAPING = "web_scraping"  # Advanced web scraping
    FEED_MONITORING = "feed_monitoring"  # RSS/news feed monitoring
    TREND_ANALYSIS = "trend_analysis"  # Market trend analysis


@dataclass
class ResearchConfig:
    """Configuration for research agent features"""
    enabled_features: Set[ResearchFeatures] = field(default_factory=lambda: {ResearchFeatures.BASIC})
    max_results: int = 10
    search_depth: int = 2  # How many levels deep to search
    company_extraction: bool = True
    auto_generate_images: bool = False
    image_provider: str = "placeholder"  # placeholder, stable-diffusion, dall-e, etc.
    use_ai_analysis: bool = True
    cache_duration: int = 3600  # Cache results for 1 hour
    rate_limit_delay: float = 1.0  # Delay between requests
    

class UnifiedResearchAgent(BaseAgentClass):
    """Unified research agent with all features configurable"""
    
    def __init__(self, supabase_client, ai_provider=None, config: Optional[ResearchConfig] = None):
        # Check if we're using BaseAgent (needs agent_name) or EnhancedBaseAgent
        if BaseAgentClass.__name__ == 'BaseAgent':
            super().__init__(supabase_client, 'research_agent')
            self.ai_provider = ai_provider  # Set AI provider manually
        else:
            super().__init__(supabase_client, ai_provider)
        self.config = config or ResearchConfig()
        self.session = None
        self.sources = self._initialize_sources()
        self._cache = {}
        self._company_cache = {}
        
    def _initialize_sources(self) -> List[Dict]:
        """Initialize research sources based on configuration"""
        sources = [
            {
                'name': 'EIHA',
                'url': 'https://eiha.org',
                'type': 'organization',
                'feeds': ['https://eiha.org/feed/']
            },
            {
                'name': 'Hemp Industry Daily',
                'url': 'https://hempindustrydaily.com',
                'type': 'news',
                'feeds': ['https://hempindustrydaily.com/feed/']
            },
            {
                'name': 'Vote Hemp',
                'url': 'https://votehemp.com',
                'type': 'advocacy',
                'feeds': ['https://votehemp.com/feed/']
            }
        ]
        
        if ResearchFeatures.DEEP_ANALYSIS in self.config.enabled_features:
            sources.extend([
                {
                    'name': 'Hemp Business Journal',
                    'url': 'https://hempbizjournal.com',
                    'type': 'business',
                    'feeds': []
                },
                {
                    'name': 'National Hemp Association',
                    'url': 'https://nationalhempassociation.org',
                    'type': 'association',
                    'feeds': []
                }
            ])
            
        return sources
    
    async def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute research task - required by BaseAgent abstract method."""
        action = task.get('action', 'discover_products')
        params = task.get('params', {})
        
        logger.info(f"Executing research task: action={action}, params={params}")
        
        if action == 'discover_products':
            query = params.get('query', 'hemp products')
            limit = params.get('limit', 20)
            products = await self.discover_products(query, max_results=limit)
            
            # Save products to database if any found
            saved_count = 0
            if products:
                saved_count = await self._save_products_to_db(products)
            
            return {
                'status': 'completed',
                'action': action,
                'products_found': len(products),
                'products_saved': saved_count,
                'products': products[:10]  # Return sample of products
            }
        elif action == 'analyze_trends':
            # Placeholder for trend analysis
            return {
                'status': 'completed',
                'action': action,
                'message': 'Trend analysis not yet implemented'
            }
        elif action == 'monitor_regulations':
            # Placeholder for regulatory monitoring
            return {
                'status': 'completed',
                'action': action,
                'message': 'Regulatory monitoring not yet implemented'
            }
        else:
            raise ValueError(f"Unknown research action: {action}")
        
    async def discover_products(self, search_query: str, **kwargs) -> List[Dict]:
        """Main entry point for product discovery with all features"""
        max_results = kwargs.get('max_results', self.config.max_results)
        
        # Check cache first
        cache_key = f"{search_query}:{max_results}"
        if cache_key in self._cache:
            cache_entry = self._cache[cache_key]
            if datetime.now() - cache_entry['timestamp'] < timedelta(seconds=self.config.cache_duration):
                logger.info(f"Returning cached results for '{search_query}'")
                return cache_entry['data']
        
        all_products = []
        
        # Basic discovery (only if enabled AND AI provider available)
        if ResearchFeatures.BASIC in self.config.enabled_features and self.ai_provider:
            basic_products = await self._basic_discovery(search_query, max_results)
            all_products.extend(basic_products)
        
        # Web scraping if enabled
        if ResearchFeatures.WEB_SCRAPING in self.config.enabled_features:
            scraped_products = await self._web_scraping_discovery(search_query, max_results)
            all_products.extend(scraped_products)
            
        # Feed monitoring if enabled
        if ResearchFeatures.FEED_MONITORING in self.config.enabled_features:
            feed_products = await self._feed_discovery(search_query, max_results)
            all_products.extend(feed_products)
            
        # Process all discovered products
        processed_products = []
        for product in all_products[:max_results]:
            processed = await self._process_product(product)
            if processed:
                processed_products.append(processed)
                
        # Cache results
        self._cache[cache_key] = {
            'timestamp': datetime.now(),
            'data': processed_products
        }
        
        return processed_products
        
    async def _process_product(self, product: Dict) -> Optional[Dict]:
        """Process a single product with all enabled features"""
        try:
            # Basic structuring
            structured = await self._structure_product_data(product)
            if not structured:
                return None
                
            # Company extraction if enabled
            if (ResearchFeatures.COMPANY_EXTRACTION in self.config.enabled_features 
                or self.config.company_extraction):
                companies = await self._extract_companies(structured)
                structured['companies'] = companies
                structured['name'] = self._clean_product_name(structured['name'], companies)
                
            # Save to database
            saved_product = await self._save_product(structured)
            if not saved_product:
                return None
                
            # Image generation if enabled
            if (ResearchFeatures.IMAGE_GENERATION in self.config.enabled_features 
                or self.config.auto_generate_images):
                await self._queue_image_generation(saved_product)
                
            # Trend analysis if enabled
            if ResearchFeatures.TREND_ANALYSIS in self.config.enabled_features:
                trend_data = await self._analyze_trends(structured)
                structured['trend_analysis'] = trend_data
                
            return structured
            
        except Exception as e:
            logger.error(f"Error processing product: {e}")
            return None
            
    async def _basic_discovery(self, search_query: str, max_results: int) -> List[Dict]:
        """Basic product discovery using AI"""
        if not self.ai_provider:
            logger.warning("No AI provider configured for basic discovery")
            return []
            
        prompt = f"""
        Research and list {max_results} specific hemp products related to: {search_query}
        
        For each product provide:
        - Product name
        - Description
        - Plant part used
        - Industry/application
        - Key benefits
        - Companies/brands if known
        
        Focus on real, commercial products currently available or in development.
        """
        
        try:
            # Use the MultiProviderAI's generate method properly
            if hasattr(self.ai_provider, 'generate') and asyncio.iscoroutinefunction(self.ai_provider.generate):
                # Get just the response text (MultiProviderAI returns tuple)
                result = await self.ai_provider.generate(prompt)
                if isinstance(result, tuple):
                    response = result[0]  # First element is the text
                else:
                    response = result
            else:
                # Fallback for simple providers
                response = await self.ai_provider.generate(prompt)
                
            # Parse AI response into product list
            products = self._parse_ai_response(response)
            return products
        except Exception as e:
            logger.error(f"Basic discovery failed: {e}")
            return []
            
    async def _web_scraping_discovery(self, search_query: str, max_results: int) -> List[Dict]:
        """Advanced web scraping for product discovery"""
        products = []
        
        for source in self.sources:
            if len(products) >= max_results:
                break
                
            try:
                await asyncio.sleep(self.config.rate_limit_delay)
                
                search_url = f"{source['url']}/search?q={search_query}"
                async with aiohttp.ClientSession() as session:
                    async with session.get(search_url, timeout=10) as response:
                        if response.status == 200:
                            html = await response.text()
                            soup = BeautifulSoup(html, 'html.parser')
                            
                            # Extract products from search results
                            # This is a generic implementation - customize per source
                            for article in soup.find_all(['article', 'div'], class_=['product', 'post', 'entry']):
                                title = article.find(['h1', 'h2', 'h3'])
                                description = article.find(['p', 'div'], class_=['description', 'summary', 'excerpt'])
                                
                                if title:
                                    products.append({
                                        'title': title.text.strip(),
                                        'description': description.text.strip() if description else '',
                                        'source': source['name'],
                                        'url': search_url,
                                        'discovered_at': datetime.now().isoformat()
                                    })
                                    
            except Exception as e:
                logger.error(f"Web scraping failed for {source['name']}: {e}")
                
        return products[:max_results]
        
    async def _feed_discovery(self, search_query: str, max_results: int) -> List[Dict]:
        """Discover products from RSS feeds"""
        products = []
        search_terms = search_query.lower().split()
        
        for source in self.sources:
            for feed_url in source.get('feeds', []):
                try:
                    feed = feedparser.parse(feed_url)
                    
                    for entry in feed.entries:
                        # Check if entry matches search terms
                        content = f"{entry.get('title', '')} {entry.get('summary', '')}".lower()
                        if any(term in content for term in search_terms):
                            products.append({
                                'title': entry.get('title', ''),
                                'description': entry.get('summary', ''),
                                'source': source['name'],
                                'url': entry.get('link', feed_url),
                                'published': entry.get('published', ''),
                                'discovered_at': datetime.now().isoformat()
                            })
                            
                            if len(products) >= max_results:
                                return products
                                
                except Exception as e:
                    logger.error(f"Feed parsing failed for {feed_url}: {e}")
                    
        return products
        
    async def _structure_product_data(self, raw_data: Dict) -> Optional[Dict]:
        """Structure product data using AI if enabled"""
        if not self.config.use_ai_analysis or not self.ai_provider:
            # Basic structuring without AI
            return {
                'name': raw_data.get('title', 'Unknown Product'),
                'description': raw_data.get('description', ''),
                'plant_part': self._guess_plant_part(raw_data),
                'industry': self._guess_industry(raw_data),
                'source_url': raw_data.get('url', ''),
                'data_source': raw_data.get('source', 'Unknown')
            }
            
        # AI-powered structuring
        prompt = f"""
        Extract and structure hemp product information from this data:
        
        Source: {raw_data.get('source', 'Unknown')}
        Title: {raw_data.get('title', '')}
        Content: {raw_data.get('description', '')}
        
        Return a JSON object with:
        {{
            "name": "product name",
            "description": "detailed description",
            "plant_part": "seeds|fiber|oil|flower|hurds|roots|leaves|biomass",
            "industry": "main industry category",
            "sub_industry": "specific sub-industry",
            "benefits_advantages": ["list", "of", "benefits"],
            "sustainability_aspects": ["list", "of", "sustainability", "points"],
            "technical_specifications": {{"key": "value"}},
            "commercialization_stage": "R&D|Pilot|Niche|Growing|Established",
            "companies": ["list", "of", "companies"] if company extraction is enabled
        }}
        """
        
        try:
            response = await self.ai_provider.generate(prompt, response_format="json")
            
            # Debug logging
            logger.debug(f"AI response type: {type(response)}")
            logger.debug(f"AI response length: {len(response) if response else 'None'}")
            
            # Handle tuple response from MultiProviderAI
            if isinstance(response, tuple):
                logger.debug("Extracting from tuple...")
                response = response[0]
                
            # More debug logging
            logger.debug(f"Response to parse: {repr(response[:200]) if response else 'Empty/None'}")
            
            parsed = json.loads(response)
            logger.debug(f"Successfully parsed JSON with {len(parsed)} keys")
            return parsed
        except Exception as e:
            logger.error(f"AI structuring failed: {e}")
            logger.error(f"Response was: {repr(response) if 'response' in locals() else 'No response'}")
            return None
            
    async def _extract_companies(self, product: Dict) -> List[str]:
        """Extract company names from product data"""
        companies = product.get('companies', [])
        
        if self.ai_provider and not companies:
            # Use AI to extract companies from name and description
            prompt = f"""
            Extract company or brand names from this product information:
            
            Product Name: {product.get('name', '')}
            Description: {product.get('description', '')}
            
            Return a JSON array of company names found.
            Look for patterns like "Company Name Product" or "by Company Name".
            """
            
            try:
                response = await self.ai_provider.generate(prompt, response_format="json")
                # Handle tuple response from MultiProviderAI
                if isinstance(response, tuple):
                    response = response[0]
                companies = json.loads(response)
            except Exception as e:
                logger.error(f"Company extraction failed: {e}")
                
        return companies
        
    def _clean_product_name(self, name: str, companies: List[str]) -> str:
        """Remove company names from product name"""
        clean_name = name
        for company in companies:
            clean_name = clean_name.replace(company, '').strip()
        return clean_name or name
        
    async def _save_product(self, product: Dict) -> Optional[Dict]:
        """Save product to database with all relationships"""
        try:
            # Get IDs for foreign keys
            plant_part_id = await self._get_plant_part_id(product.get('plant_part'))
            industry_id = await self._get_industry_sub_category_id(
                product.get('industry'), 
                product.get('sub_industry')
            )
            
            # Prepare product data
            product_data = {
                'name': product['name'],
                'description': product.get('description', ''),
                'plant_part_id': plant_part_id,
                'industry_sub_category_id': industry_id,
                'benefits_advantages': product.get('benefits_advantages', []),
                'sustainability_aspects': product.get('sustainability_aspects', []),
                'technical_specifications': product.get('technical_specifications', {}),
                'commercialization_stage': product.get('commercialization_stage', 'Research'),
                'data_source': product.get('data_source', 'AI Research Agent'),
                'source_url': product.get('source_url'),
                'created_by': 'unified_research_agent'
            }
            
            # Save product
            result = self.supabase.table('uses_products').insert(product_data).execute()
            if not result.data:
                return None
                
            saved_product = result.data[0]
            
            # Save company relationships if enabled
            if self.config.company_extraction and product.get('companies'):
                await self._save_company_relationships(saved_product['id'], product['companies'])
                
            return saved_product
            
        except Exception as e:
            logger.error(f"Failed to save product: {e}")
            return None
            
    async def _save_company_relationships(self, product_id: int, companies: List[str]):
        """Save company-product relationships"""
        for company_name in companies:
            try:
                # Check if company exists
                company = await self._get_or_create_company(company_name)
                if company:
                    # Create relationship
                    self.supabase.table('hemp_company_products').insert({
                        'company_id': company['id'],
                        'product_id': product_id,
                        'relationship_type': 'manufacturer',
                        'is_primary': True
                    }).execute()
                    
            except Exception as e:
                logger.error(f"Failed to save company relationship: {e}")
                
    async def _get_or_create_company(self, company_name: str) -> Optional[Dict]:
        """Get existing company or create new one"""
        # Check cache first
        if company_name in self._company_cache:
            return self._company_cache[company_name]
            
        # Check database
        result = self.supabase.table('hemp_companies').select('*').eq('name', company_name).execute()
        
        if result.data:
            company = result.data[0]
        else:
            # Create new company
            company_data = {
                'name': company_name,
                'description': f"Hemp industry company discovered by research agent",
                'type': 'manufacturer',
                'verified': False,
                'data_source': 'AI Research Agent'
            }
            
            create_result = self.supabase.table('hemp_companies').insert(company_data).execute()
            company = create_result.data[0] if create_result.data else None
            
        if company:
            self._company_cache[company_name] = company
            
        return company
        
    async def _queue_image_generation(self, product: Dict):
        """Queue image generation for product using centralized service"""
        try:
            # Import here to avoid circular dependency
            from ...lib.image_generation_service import ImageGenerationService, ImageGenerationRequest, ImageProvider
            
            # Generate optimized prompt
            prompt = self._generate_image_prompt(product)
            
            # Create service and request
            service = ImageGenerationService(self.supabase)
            
            # Map string provider to enum
            provider_map = {
                'placeholder': ImageProvider.PLACEHOLDER,
                'stable-diffusion': ImageProvider.STABLE_DIFFUSION,
                'dall-e': ImageProvider.DALL_E,
                'imagen-3': ImageProvider.IMAGEN_3,
                'midjourney': ImageProvider.MIDJOURNEY,
                'replicate': ImageProvider.REPLICATE,
                'together-ai': ImageProvider.TOGETHER_AI
            }
            
            provider = provider_map.get(self.config.image_provider, ImageProvider.PLACEHOLDER)
            
            request = ImageGenerationRequest(
                product_id=product['id'],
                prompt=prompt,
                provider=provider,
                priority=5,
                metadata={'source': 'unified_research_agent'}
            )
            
            # Queue through service (async but don't wait for completion)
            asyncio.create_task(service.generate_image(request))
            logger.info(f"Queued image generation for product {product['id']} via centralized service")
            
        except Exception as e:
            logger.error(f"Failed to queue image generation: {e}")
            
    def _generate_image_prompt(self, product: Dict) -> str:
        """Generate optimized image prompt for product"""
        plant_part = product.get('plant_part', 'hemp')
        name = product.get('name', 'hemp product')
        
        # Base prompt
        prompt = f"Professional product photography of {name}"
        
        # Add context based on plant part
        if plant_part == 'fiber':
            prompt += ", textile or fabric material, natural fibers"
        elif plant_part == 'seeds':
            prompt += ", hemp seeds, food product, nutritional"
        elif plant_part == 'oil':
            prompt += ", hemp oil bottle or container, liquid product"
        elif plant_part == 'flower':
            prompt += ", hemp flower, botanical product"
            
        prompt += ", clean white background, commercial photography style"
        
        return prompt
        
    async def _analyze_trends(self, product: Dict) -> Dict:
        """Analyze market trends for product"""
        if not self.ai_provider:
            return {}
            
        prompt = f"""
        Analyze market trends for this hemp product:
        
        Product: {product.get('name')}
        Industry: {product.get('industry')}
        Plant Part: {product.get('plant_part')}
        
        Provide:
        - Market growth potential (1-10)
        - Competition level (Low/Medium/High)
        - Innovation score (1-10)
        - Key market drivers
        - Potential barriers
        
        Return as JSON.
        """
        
        try:
            response = await self.ai_provider.generate(prompt, response_format="json")
            # Handle tuple response from MultiProviderAI
            if isinstance(response, tuple):
                response = response[0]
            return json.loads(response)
        except Exception as e:
            logger.error(f"Trend analysis failed: {e}")
            return {}
            
    def _guess_plant_part(self, data: Dict) -> str:
        """Guess plant part from text without AI"""
        text = f"{data.get('title', '')} {data.get('description', '')}".lower()
        
        if any(word in text for word in ['fiber', 'textile', 'fabric', 'rope']):
            return 'fiber'
        elif any(word in text for word in ['seed', 'grain', 'food', 'protein']):
            return 'seeds'
        elif any(word in text for word in ['oil', 'extract', 'tincture']):
            return 'oil'
        elif any(word in text for word in ['flower', 'bud', 'cbd']):
            return 'flower'
        elif any(word in text for word in ['hurd', 'shiv', 'core']):
            return 'hurds'
            
        return 'biomass'  # Default
        
    def _guess_industry(self, data: Dict) -> str:
        """Guess industry from text without AI"""
        text = f"{data.get('title', '')} {data.get('description', '')}".lower()
        
        if any(word in text for word in ['food', 'nutrition', 'protein', 'supplement']):
            return 'Food & Beverage'
        elif any(word in text for word in ['textile', 'clothing', 'fabric', 'fashion']):
            return 'Textiles'
        elif any(word in text for word in ['construction', 'building', 'insulation']):
            return 'Construction'
        elif any(word in text for word in ['cosmetic', 'beauty', 'skin', 'personal care']):
            return 'Personal Care'
        elif any(word in text for word in ['plastic', 'composite', 'material']):
            return 'Materials'
            
        return 'Other'
        
    async def _get_plant_part_id(self, plant_part: Optional[str]) -> Optional[int]:
        """Get plant part ID from database"""
        if not plant_part:
            return None
            
        # Normalize plant part name
        plant_part_normalized = plant_part.lower().strip()
        
        # Map common variations to database names
        plant_part_map = {
            'fiber': 'Fiber',
            'fibers': 'Fiber',
            'seeds': 'Seeds',
            'seed': 'Seeds',
            'grain': 'Seeds',
            'oil': 'Oil',
            'oils': 'Oil',
            'flower': 'Flower',
            'flowers': 'Flower',
            'bud': 'Flower',
            'buds': 'Flower',
            'hurds': 'Hurds',
            'hurd': 'Hurds',
            'shiv': 'Hurds',
            'shivs': 'Hurds',
            'roots': 'Roots',
            'root': 'Roots',
            'leaves': 'Leaves',
            'leaf': 'Leaves',
            'biomass': 'Biomass',
            'whole plant': 'Biomass'
        }
        
        # Get mapped name or capitalize first letter
        db_name = plant_part_map.get(plant_part_normalized, plant_part.capitalize())
        
        # Try exact match first
        result = self.supabase.table('plant_parts').select('id').eq('name', db_name).execute()
        if result.data:
            return result.data[0]['id']
            
        # Try case-insensitive match
        result = self.supabase.table('plant_parts').select('id').ilike('name', db_name).execute()
        if result.data:
            return result.data[0]['id']
            
        logger.warning(f"Plant part '{plant_part}' not found in database")
        return None
        
    async def _get_industry_sub_category_id(self, industry: Optional[str], sub_industry: Optional[str]) -> Optional[int]:
        """Get industry sub-category ID from database"""
        if not industry:
            return None
            
        # Normalize industry name
        industry_normalized = industry.strip()
        
        # Map common variations to database names
        industry_map = {
            'food & beverage': 'Food & Beverage',
            'food and beverage': 'Food & Beverage',
            'food': 'Food & Beverage',
            'textiles': 'Textiles',
            'textile': 'Textiles',
            'construction': 'Construction',
            'building': 'Construction',
            'personal care': 'Personal Care',
            'cosmetics': 'Personal Care',
            'materials': 'Materials',
            'plastics': 'Materials',
            'automotive': 'Automotive',
            'health': 'Health & Wellness',
            'health & wellness': 'Health & Wellness',
            'agriculture': 'Agriculture',
            'energy': 'Energy',
            'other': 'Other'
        }
        
        # Get mapped name
        db_industry = industry_map.get(industry_normalized.lower(), industry_normalized)
        
        # First get industry
        result = self.supabase.table('industries').select('id').eq('name', db_industry).execute()
        if not result.data:
            # Try case-insensitive match
            result = self.supabase.table('industries').select('id').ilike('name', db_industry).execute()
            if not result.data:
                logger.warning(f"Industry '{industry}' not found in database")
                # Default to 'Other' industry
                result = self.supabase.table('industries').select('id').eq('name', 'Other').execute()
                if not result.data:
                    return None
            
        industry_id = result.data[0]['id']
        
        # Then get sub-category
        if sub_industry:
            sub_result = self.supabase.table('industry_sub_categories').select('id').eq('industry_id', industry_id).eq('name', sub_industry).execute()
            if sub_result.data:
                return sub_result.data[0]['id']
                
        # Return first sub-category for industry as fallback
        sub_result = self.supabase.table('industry_sub_categories').select('id').eq('industry_id', industry_id).execute()
        return sub_result.data[0]['id'] if sub_result.data else None
            
    def _parse_ai_response(self, response: str) -> List[Dict]:
        """Parse AI response into product list"""
        try:
            # Try to parse as JSON first
            data = json.loads(response)
            if isinstance(data, list):
                return data
            elif isinstance(data, dict) and 'products' in data:
                return data['products']
        except:
            pass
            
        # Fallback to text parsing
        products = []
        lines = response.split('\n')
        current_product = {}
        
        for line in lines:
            line = line.strip()
            if line.startswith('-') or line.startswith('•'):
                # New product
                if current_product:
                    products.append(current_product)
                current_product = {'title': line[1:].strip()}
            elif ':' in line and current_product:
                # Product attribute
                key, value = line.split(':', 1)
                current_product[key.strip().lower()] = value.strip()
                
        if current_product:
            products.append(current_product)
            
        return products
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()


# Convenience function to create agent with common configurations
def create_research_agent(supabase_client, ai_provider=None, features: List[str] = None) -> UnifiedResearchAgent:
    """Create a research agent with specified features
    
    If no features specified, automatically selects based on AI availability:
    - With AI: All features enabled
    - Without AI: Only web scraping and feed monitoring
    """
    config = ResearchConfig()
    
    # If no features specified, use smart defaults
    if not features:
        if ai_provider:
            # Enable all features when AI is available
            logger.info("AI provider available - enabling all research features")
            config.enabled_features = {
                ResearchFeatures.BASIC,
                ResearchFeatures.COMPANY_EXTRACTION,
                ResearchFeatures.IMAGE_GENERATION,
                ResearchFeatures.DEEP_ANALYSIS,
                ResearchFeatures.WEB_SCRAPING,
                ResearchFeatures.FEED_MONITORING,
                ResearchFeatures.TREND_ANALYSIS
            }
            config.company_extraction = True
            config.auto_generate_images = True
            config.use_ai_analysis = True
        else:
            # Only enable non-AI features
            logger.info("No AI provider - enabling web scraping and feed monitoring only")
            config.enabled_features = {
                ResearchFeatures.WEB_SCRAPING,
                ResearchFeatures.FEED_MONITORING,
                ResearchFeatures.COMPANY_EXTRACTION  # Can work without AI
            }
            config.company_extraction = True
            config.use_ai_analysis = False
            config.auto_generate_images = False
    else:
        # Parse user-specified features
        config.enabled_features = set()
        
        # Check if any AI-dependent features are requested
        ai_features = {'basic', 'deep', 'trend'}
        needs_ai = any(f in features for f in ai_features)
        
        # Only enable AI analysis if AI features are requested AND AI provider is available
        config.use_ai_analysis = needs_ai and ai_provider is not None
        
        for feature in features:
            if feature == 'company':
                config.enabled_features.add(ResearchFeatures.COMPANY_EXTRACTION)
                config.company_extraction = True
            elif feature == 'image':
                config.enabled_features.add(ResearchFeatures.IMAGE_GENERATION)
                config.auto_generate_images = True
            elif feature == 'deep':
                config.enabled_features.add(ResearchFeatures.DEEP_ANALYSIS)
            elif feature == 'web':
                config.enabled_features.add(ResearchFeatures.WEB_SCRAPING)
            elif feature == 'feed':
                config.enabled_features.add(ResearchFeatures.FEED_MONITORING)
            elif feature == 'trend':
                config.enabled_features.add(ResearchFeatures.TREND_ANALYSIS)
            elif feature == 'basic':
                config.enabled_features.add(ResearchFeatures.BASIC)
                
    return UnifiedResearchAgent(supabase_client, ai_provider, config)