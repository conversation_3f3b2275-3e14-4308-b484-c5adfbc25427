# OpenAI API Key Workarounds

Since you're having issues with OpenAI API funds, here are several workarounds:

## Option 1: Use Placeholder/Mock Mode
Modify your agents to run in a "mock" mode that doesn't make actual API calls:

```python
# In your agent files, add:
if os.getenv('MOCK_MODE', 'false').lower() == 'true':
    # Return mock data instead of calling OpenAI
    return {"products": ["Mock Hemp Product 1", "Mock Hemp Product 2"]}
```

Then in GitHub Actions, set:
```yaml
env:
  MOCK_MODE: true
```

## Option 2: Use Free Alternatives
1. **Use local LLMs** (for development only):
   - Ollama with small models
   - GPT4All
   
2. **Use other free APIs**:
   - Anthropic Claude (has free tier)
   - Google Gemini (free tier available)
   - Cohere (free tier)

## Option 3: Minimal API Usage
Configure agents to use minimal tokens:
```python
# Reduce max tokens
response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",  # Cheaper model
    messages=messages,
    max_tokens=50,  # Very limited response
    temperature=0  # More deterministic = cheaper
)
```

## Option 4: Skip AI-Dependent Workflows
For now, focus on workflows that don't need AI:
- Monitoring workflows (just check database status)
- Image generation with placeholders
- Database validation
- Report generation from existing data

## Option 5: Test Individual Components
Instead of running full agents, test specific parts:
```bash
# Test monitoring without AI
python hemp monitor --format health

# Test database connections
python hemp db validate

# Test image status
python hemp images status
```

## Recommended Approach for Testing

1. **First, test non-AI workflows**:
   - Run the monitoring workflow
   - It should work without OpenAI

2. **Add MOCK_MODE to workflows**:
   ```yaml
   env:
     OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
     MOCK_MODE: true  # Add this
   ```

3. **Create a test workflow** that doesn't use AI:
   ```yaml
   name: Test Basic Functions
   on: workflow_dispatch
   
   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
       - uses: actions/checkout@v3
       - name: Test CLI
         run: |
           python hemp --help
           python hemp monitor --format health
   ```

This way you can verify the GitHub Actions setup works before dealing with the OpenAI API issue.