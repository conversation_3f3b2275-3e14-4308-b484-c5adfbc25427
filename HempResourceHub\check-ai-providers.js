import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function checkProviders() {
  // Check AI provider config
  const { data: providers, error: providersError } = await supabase
    .from('ai_provider_config')
    .select('*')
    .order('quality_score', { ascending: false });
    
  console.log('AI Provider Configuration:');
  console.log('='.repeat(60));
  
  if (providersError) {
    console.log('❌ Error fetching providers:', providersError.message);
    return;
  }
  
  if (!providers || providers.length === 0) {
    console.log('❌ No AI providers configured in database');
    console.log('\nThis table needs to be populated with provider configurations.');
    console.log('The edge function is looking for active providers but none exist.');
    
    // Let's create the default providers
    console.log('\n🔧 Creating default AI provider configurations...');
    
    const defaultProviders = [
      {
        provider_name: 'dall_e_3',
        api_key_name: 'OPENAI_API_KEY',
        is_active: true,
        cost_per_image: 0.040,
        quality_score: 9,
        config: {
          default_size: '1024x1024',
          default_quality: 'standard'
        }
      },
      {
        provider_name: 'stable_diffusion',
        api_key_name: 'STABILITY_API_KEY',
        is_active: true,
        cost_per_image: 0.002,
        quality_score: 7,
        config: {
          default_cfg_scale: 7,
          default_steps: 30
        }
      },
      {
        provider_name: 'placeholder',
        api_key_name: '',
        is_active: true,
        cost_per_image: 0,
        quality_score: 1,
        config: {}
      }
    ];
    
    for (const provider of defaultProviders) {
      const { error } = await supabase
        .from('ai_provider_config')
        .insert(provider);
        
      if (error) {
        console.error(`❌ Error creating ${provider.provider_name}:`, error.message);
      } else {
        console.log(`✅ Created ${provider.provider_name} provider config`);
      }
    }
    
    return;
  }
  
  providers.forEach(p => {
    console.log(`\n${p.provider_name}:`);
    console.log(`  Active: ${p.is_active ? '✅' : '❌'}`);
    console.log(`  Cost per image: $${p.cost_per_image}`);
    console.log(`  Quality score: ${p.quality_score}/10`);
    console.log(`  API key env var: ${p.api_key_name}`);
    console.log(`  Config: ${JSON.stringify(p.config)}`);
  });
  
  // Check recent generation costs/errors
  const { data: recentCosts } = await supabase
    .from('ai_generation_costs')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(10);
    
  console.log('\n\nRecent Generation Attempts:');
  console.log('='.repeat(60));
  
  if (!recentCosts || recentCosts.length === 0) {
    console.log('No recent generation attempts found');
  } else {
    recentCosts.forEach(cost => {
      const status = cost.success ? '✅' : '❌';
      const time = new Date(cost.created_at).toLocaleTimeString();
      console.log(`${status} ${cost.provider_name} - Product ${cost.product_id} - ${cost.error_message || 'Success'} (${time})`);
    });
  }
  
  // Check edge function environment
  console.log('\n\nEdge Function Environment Variables Needed:');
  console.log('='.repeat(60));
  console.log('The following environment variables should be set in Supabase:');
  console.log('- OPENAI_API_KEY (for DALL-E 3)');
  console.log('- STABILITY_API_KEY (for Stable Diffusion)');
  console.log('\nSet these in: Supabase Dashboard > Edge Functions > hemp-image-generator > Settings');
}

checkProviders().catch(console.error);