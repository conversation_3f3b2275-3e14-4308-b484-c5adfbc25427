#!/usr/bin/env python3
"""
Demo: Industrial Hemp Product Discovery
Focuses on non-CBD hemp products (fiber, textiles, construction, etc.)
"""

import asyncio
import aiohttp
import feedparser
from bs4 import BeautifulSoup
from datetime import datetime
import logging
import re

# Setup logging  
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

class IndustrialHempScraper:
    """Scraper focused on industrial hemp products (not CBD)"""
    
    def __init__(self):
        self.sources = [
            {
                'name': 'Hemp Industry Daily',
                'feed': 'https://hempindustrydaily.com/feed/',
                'base_url': 'https://hempindustrydaily.com'
            },
            {
                'name': 'EIHA (European Industrial Hemp Association)',
                'url': 'https://eiha.org',
                'type': 'website'
            },
            {
                'name': 'Vote Hemp',
                'url': 'https://votehemp.com',
                'type': 'website'
            }
        ]
        
        # Industrial hemp keywords (exclude CBD)
        self.industrial_keywords = [
            'fiber', 'fibre', 'textile', 'fabric', 'clothing', 'yarn',
            'construction', 'building', 'hempcrete', 'insulation',
            'plastic', 'composite', 'bioplastic', 'material',
            'paper', 'pulp', 'packaging',
            'seed', 'oil', 'protein', 'food', 'nutrition',
            'hurd', 'shiv', 'bedding', 'mulch',
            'automotive', 'biocomposite'
        ]
        
        # CBD/Cannabis keywords to filter out
        self.exclude_keywords = [
            'cbd', 'cannabidiol', 'thc', 'cannabis', 'marijuana',
            'dispensary', 'medical marijuana', 'recreational'
        ]
        
    async def discover_products(self, limit: int = 10):
        """Discover industrial hemp products"""
        logger.info("\n🏭 INDUSTRIAL HEMP PRODUCT DISCOVERY")
        logger.info("Focus: Fiber, Textiles, Construction, Materials")
        logger.info("=" * 60)
        
        all_products = []
        
        # Scrape all sources
        for source in self.sources:
            if 'feed' in source:
                products = await self._scrape_rss(source)
                all_products.extend(products)
            elif source['type'] == 'website':
                products = await self._scrape_website(source)
                all_products.extend(products)
        
        # Add some known industrial hemp products as examples
        all_products.extend(self._get_example_products())
        
        # Filter and structure products
        final_products = []
        for raw_product in all_products:
            if self._is_industrial_hemp(raw_product):
                product = self._structure_industrial_product(raw_product)
                if product and len(final_products) < limit:
                    final_products.append(product)
        
        return final_products
    
    def _is_industrial_hemp(self, product):
        """Check if product is industrial hemp (not CBD)"""
        text = f"{product.get('title', '')} {product.get('description', '')}".lower()
        
        # Exclude if it's CBD-focused
        if any(keyword in text for keyword in self.exclude_keywords):
            return False
            
        # Include if it has industrial keywords
        return any(keyword in text for keyword in self.industrial_keywords)
    
    async def _scrape_rss(self, source):
        """Scrape RSS feed for industrial hemp products"""
        products = []
        logger.info(f"\n📡 Checking RSS: {source['name']}")
        
        try:
            feed = feedparser.parse(source['feed'])
            
            if feed.entries:
                industrial_count = 0
                
                for entry in feed.entries[:50]:  # Check more entries
                    text = f"{entry.get('title', '')} {entry.get('summary', '')}".lower()
                    
                    # Only industrial hemp
                    if any(kw in text for kw in self.industrial_keywords) and \
                       not any(kw in text for kw in self.exclude_keywords):
                        products.append({
                            'title': entry.get('title', ''),
                            'description': entry.get('summary', ''),
                            'url': entry.get('link', ''),
                            'source': source['name'],
                            'published': entry.get('published', ''),
                            'raw_text': text
                        })
                        industrial_count += 1
                        logger.info(f"   ✓ Industrial: {entry.get('title', '')[:50]}...")
                
                logger.info(f"   Found {industrial_count} industrial hemp articles")
                        
        except Exception as e:
            logger.error(f"   ✗ RSS Error: {type(e).__name__}")
            
        return products
    
    async def _scrape_website(self, source):
        """Scrape website for industrial hemp products"""
        products = []
        logger.info(f"\n🌐 Checking website: {source['name']}")
        logger.info("   (Simulated - would scrape in production)")
        
        # For demo, return empty (would implement actual scraping)
        return products
    
    def _get_example_products(self):
        """Get example industrial hemp products for demo"""
        return [
            {
                'title': 'HempWood Launches New Sustainable Flooring Line',
                'description': 'HempWood Inc announces their latest hemp-based hardwood alternative flooring, offering 20% harder surface than oak with rapid renewability.',
                'source': 'Example Product',
                'raw_text': 'hempwood flooring sustainable hardwood alternative construction material'
            },
            {
                'title': 'Patagonia Introduces Hemp Fiber Workwear Collection',
                'description': 'Outdoor clothing company debuts durable workwear line using industrial hemp fiber blended with organic cotton for enhanced durability.',
                'source': 'Example Product',
                'raw_text': 'patagonia hemp fiber textile clothing workwear durable fabric'
            },
            {
                'title': 'Ford Expands Use of Hemp Biocomposites in Vehicle Production',
                'description': 'Ford Motor Company increases hemp fiber composite usage in door panels and dashboard components, reducing vehicle weight by 10%.',
                'source': 'Example Product',
                'raw_text': 'ford hemp biocomposite automotive plastic material lightweight'
            },
            {
                'title': 'Hempcrete Blocks Certified for US Construction Market',
                'description': 'IsoHemp receives certification for hemp-lime building blocks in US market, offering superior insulation and carbon-negative construction.',
                'source': 'Example Product',
                'raw_text': 'hempcrete construction building blocks insulation carbon negative material'
            },
            {
                'title': 'Manitoba Harvest Launches Hemp Heart Bar Line',
                'description': 'Leading hemp food company introduces protein bars featuring hemp hearts, delivering 10g plant protein per serving.',
                'source': 'Example Product',
                'raw_text': 'manitoba harvest hemp hearts seed protein food nutrition bar'
            }
        ]
    
    def _structure_industrial_product(self, raw_product):
        """Structure product data for industrial hemp"""
        text = raw_product.get('raw_text', '')
        
        # Determine plant part
        plant_part = 'biomass'
        if any(word in text for word in ['fiber', 'fibre', 'textile', 'fabric']):
            plant_part = 'fiber'
        elif any(word in text for word in ['seed', 'heart', 'protein', 'oil']):
            plant_part = 'seeds'
        elif any(word in text for word in ['hurd', 'shiv', 'core']):
            plant_part = 'hurds'
        elif 'hempcrete' in text:
            plant_part = 'hurds'
            
        # Determine industry
        industry = 'Other'
        if any(word in text for word in ['textile', 'fabric', 'clothing', 'fashion', 'apparel']):
            industry = 'Textiles'
        elif any(word in text for word in ['construction', 'building', 'hempcrete', 'insulation']):
            industry = 'Construction'
        elif any(word in text for word in ['food', 'nutrition', 'protein', 'beverage', 'edible']):
            industry = 'Food & Beverage'
        elif any(word in text for word in ['plastic', 'composite', 'material', 'polymer']):
            industry = 'Materials'
        elif any(word in text for word in ['automotive', 'vehicle', 'car']):
            industry = 'Automotive'
        elif any(word in text for word in ['paper', 'pulp', 'packaging']):
            industry = 'Paper & Packaging'
            
        # Extract companies
        companies = []
        # Look for company names
        company_patterns = [
            r'([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\s+(?:Inc|Corp|Company|Co|Ltd|LLC)',
            r'([A-Z][A-Za-z]+(?:\s+[&]\s+[A-Z][A-Za-z]+)*)',  # X & Y format
        ]
        
        title = raw_product.get('title', '')
        desc = raw_product.get('description', '')
        
        for pattern in company_patterns:
            matches = re.findall(pattern, title + ' ' + desc)
            companies.extend(matches)
        
        # Also check for known companies
        known_companies = ['Ford', 'Patagonia', 'HempWood', 'IsoHemp', 'Manitoba Harvest']
        for company in known_companies:
            if company.lower() in text:
                companies.append(company)
        
        # Clean product name
        name = title
        if ' - ' in name:
            name = name.split(' - ')[0]
        
        return {
            'name': name.strip()[:100],
            'description': desc[:500] if desc else '',
            'plant_part': plant_part,
            'industry': industry,
            'companies': list(set(companies))[:3],
            'benefits': self._extract_benefits(text),
            'source_url': raw_product.get('url', ''),
            'data_source': raw_product.get('source', ''),
            'discovered_date': datetime.now().isoformat(),
            'commercialization_stage': self._determine_stage(text)
        }
    
    def _extract_benefits(self, text):
        """Extract product benefits"""
        benefits = []
        
        benefit_keywords = {
            'sustainable': 'Sustainable and eco-friendly',
            'renewable': 'Rapidly renewable resource',
            'durable': 'Enhanced durability',
            'lightweight': 'Lightweight material',
            'insulation': 'Superior insulation properties',
            'carbon negative': 'Carbon negative',
            'biodegradable': 'Biodegradable',
            'stronger': 'Higher strength',
            'protein': 'High protein content'
        }
        
        for keyword, benefit in benefit_keywords.items():
            if keyword in text:
                benefits.append(benefit)
                
        return benefits[:4]  # Max 4 benefits
    
    def _determine_stage(self, text):
        """Determine commercialization stage"""
        if any(word in text for word in ['launches', 'introduces', 'debuts']):
            return 'Growing'
        elif any(word in text for word in ['expands', 'increases']):
            return 'Established'
        elif any(word in text for word in ['develops', 'testing', 'pilot']):
            return 'Pilot'
        elif any(word in text for word in ['research', 'study']):
            return 'R&D'
        return 'Niche'

async def main():
    """Run the industrial hemp demo"""
    logger.info("\n🌿 INDUSTRIAL HEMP PRODUCTS DEMO 🌿")
    logger.info("Non-CBD Hemp Applications")
    
    scraper = IndustrialHempScraper()
    
    # Discover products
    products = await scraper.discover_products(limit=10)
    
    # Display results
    logger.info(f"\n\n📊 RESULTS: Found {len(products)} industrial hemp products")
    logger.info("=" * 60)
    
    if products:
        for i, product in enumerate(products, 1):
            logger.info(f"\n{i}. {product['name']}")
            logger.info(f"   🌾 Plant Part: {product['plant_part']}")
            logger.info(f"   🏭 Industry: {product['industry']}")
            logger.info(f"   📈 Stage: {product['commercialization_stage']}")
            logger.info(f"   📰 Source: {product['data_source']}")
            
            if product['companies']:
                logger.info(f"   🏢 Companies: {', '.join(product['companies'])}")
                
            if product['benefits']:
                logger.info(f"   ✨ Benefits: {', '.join(product['benefits'])}")
                
            if product['description']:
                desc = product['description']
                if len(desc) > 200:
                    desc = desc[:197] + "..."
                logger.info(f"   📝 {desc}")
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ Industrial hemp product discovery complete!")
    logger.info("\nThese products demonstrate hemp's versatility in:")
    logger.info("• Textiles & Apparel")
    logger.info("• Construction Materials") 
    logger.info("• Automotive Components")
    logger.info("• Food & Nutrition")
    logger.info("• Sustainable Materials")

if __name__ == "__main__":
    asyncio.run(main())