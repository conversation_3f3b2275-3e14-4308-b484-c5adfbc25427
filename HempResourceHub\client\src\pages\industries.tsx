import { useState, useMemo } from "react";
import { <PERSON> } from "wouter";
import { Helmet } from "react-helmet";
import { Industry } from "@shared/schema";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowRight, Search, Filter, Package, ChevronLeft, ChevronRight, Factory } from "lucide-react";
import { useIndustries } from "@/hooks/use-plant-data";
import { EnhancedBreadcrumbs } from "@/components/ui/enhanced-breadcrumbs";

const IndustriesPage = () => {
  const { data: industries, isLoading } = useIndustries();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);

  // Industry description placeholders - in a real app, these would come from the database
  const industryDescriptions: Record<string, string> = {
    Textiles:
      "Hemp fibers are used to create durable, sustainable textiles for clothing, accessories, and industrial applications.",
    Construction:
      "Hemp-based materials are used in construction for insulation, hempcrete, and other building materials.",
    "Food & Nutrition":
      "Hemp seeds and oils provide nutritious ingredients for various food products.",
    "Paper & Packaging":
      "Hemp fibers can be processed into paper products that require fewer chemicals and less processing than wood pulp.",
    Biofuels:
      "Hemp biomass can be converted into sustainable biofuels for energy production.",
    Pharmaceuticals:
      "Hemp extracts are used in various pharmaceutical applications and wellness products.",
    Cosmetics:
      "Hemp-derived ingredients are used in skincare, haircare, and other personal care products.",
    Agriculture:
      "Hemp cultivation provides solutions for crop rotation, soil remediation, and sustainable farming practices.",
  };

  // Industry icons using emoji as placeholders - in a real app, these would be proper icons
  const industryIcons: Record<string, string> = {
    Textiles: "👕",
    Construction: "🏗️",
    "Food & Nutrition": "🥗",
    "Paper & Packaging": "📦",
    Biofuels: "⚡",
    Pharmaceuticals: "💊",
    Cosmetics: "💄",
    Agriculture: "🌱",
  };

  // Generate alphabet array for filtering
  const alphabet = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));

  // Filter and paginate industries
  const { filteredIndustries, totalPages, paginatedIndustries } = useMemo(() => {
    if (!industries) return { filteredIndustries: [], totalPages: 0, paginatedIndustries: [] };

    let filtered = industries.filter(industry => {
      const matchesSearch = industry.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           industryDescriptions[industry.name]?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesLetter = !selectedLetter || industry.name.charAt(0).toUpperCase() === selectedLetter;
      return matchesSearch && matchesLetter;
    });

    // Sort alphabetically
    filtered.sort((a, b) => a.name.localeCompare(b.name));

    const totalPages = Math.ceil(filtered.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedIndustries = filtered.slice(startIndex, startIndex + itemsPerPage);

    return { filteredIndustries: filtered, totalPages, paginatedIndustries };
  }, [industries, searchTerm, selectedLetter, currentPage, itemsPerPage, industryDescriptions]);

  // Reset page when filters change
  const handleFilterChange = (filterType: string, value: any) => {
    setCurrentPage(1);
    if (filterType === 'search') setSearchTerm(value);
    if (filterType === 'letter') setSelectedLetter(value);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  // Background colors for cards
  const cardColors = [
    "bg-gradient-to-br from-gray-800/40 to-gray-900/40",
    "bg-gradient-to-br from-gray-900/40 to-gray-800/40",
    "bg-gradient-to-br from-gray-800/40 to-gray-900/40",
    "bg-gradient-to-br from-gray-900/40 to-gray-800/40",
  ];

  return (
    <>
      <Helmet>
        <title>Industrial Hemp Industries | HempQuarterz</title>
        <meta
          name="description"
          content="Explore the diverse industrial applications of hemp across various industries including textiles, construction, food, biofuels, and more."
        />
      </Helmet>

      {/* Enhanced Breadcrumb */}
      <div className="bg-black/20 backdrop-blur-sm py-6 border-b border-gray-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <EnhancedBreadcrumbs
            showHome={true}
            showContext={true}
          />
        </div>
      </div>

      {/* Hero section */}
      <div className="bg-gradient-to-b from-black/20 to-black/30 backdrop-blur-sm py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-inter font-black text-white">
            Industrial Hemp Applications
          </h1>
          <p className="mt-4 text-lg text-gray-100 max-w-3xl mx-auto font-source-sans">
            Discover how industrial hemp is revolutionizing various industries
            with sustainable, eco-friendly solutions and innovative
            applications.
          </p>
        </div>
      </div>

      {/* Industries grid */}
      <div className="bg-black/20 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-white mb-12 text-center">
            Explore Industries Using <span className="hemp-brand-secondary">Hemp</span>
          </h2>

          {/* Search Bar - Full Width */}
          <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 mb-6 border border-green-500/30">
            <div className="space-y-4">
              {/* Search Input - Full Width */}
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search industries by name or description..."
                  value={searchTerm}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full pl-12 pr-4 py-3 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20 text-base"
                />
                {searchTerm && (
                  <button
                    onClick={() => handleFilterChange('search', '')}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Alphabetical Filter - Modern & Compact */}
          <div className="bg-gray-900/30 backdrop-blur-md rounded-2xl p-4 mb-6 border border-gray-700/30">
            <div className="flex items-center gap-6">
              {/* Label */}
              <div className="flex items-center gap-2 flex-shrink-0">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-gray-300 font-medium text-sm">A-Z</span>
              </div>

              {/* Centered Letter Pills Container */}
              <div className="flex-1 flex items-center justify-center">
                <div className="flex items-center gap-1 flex-wrap justify-center max-w-4xl">
                  {/* All Button */}
                  <button
                    onClick={() => handleFilterChange('letter', null)}
                    className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
                      selectedLetter === null
                        ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                        : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300'
                    }`}
                  >
                    All
                  </button>

                  {/* Letter Pills */}
                  {alphabet.map(letter => (
                    <button
                      key={letter}
                      onClick={() => handleFilterChange('letter', letter)}
                      className={`w-7 h-7 rounded-full text-xs font-medium transition-all duration-200 flex items-center justify-center ${
                        selectedLetter === letter
                          ? 'bg-green-500 text-white shadow-lg shadow-green-500/25 scale-110'
                        : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300 hover:scale-105'
                      }`}
                    >
                      {letter}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Results Summary and View Controls */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="text-gray-400 text-sm">
              Showing {paginatedIndustries.length} of {filteredIndustries.length} industries
            </div>

            <div className="flex items-center gap-2">
              <span className="text-gray-400 text-sm">Items per page:</span>
              <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                <SelectTrigger className="w-20 bg-gray-800/60 border-gray-700/50 text-gray-100">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700">
                  <SelectItem value="6">6</SelectItem>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="24">24</SelectItem>
                  <SelectItem value="48">48</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <CardContent className="p-6">
                    <Skeleton className="h-8 w-24 mb-4" />
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-3/4 mb-6" />
                    <Skeleton className="h-10 w-36" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : paginatedIndustries && paginatedIndustries.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                {paginatedIndustries.map((industry: Industry, index: number) => (
                <Card
                  key={industry.id}
                  className={`overflow-hidden border border-green-500/30 hover:border-green-400/50 transition-all ${cardColors[index % cardColors.length]} backdrop-blur-sm hover:shadow-lg hover:shadow-green-500/20`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <span
                        className="text-4xl mr-3"
                        role="img"
                        aria-label={industry.name}
                      >
                        {industryIcons[industry.name] || "🌿"}
                      </span>
                      <h3 className="text-2xl font-heading font-semibold hemp-brand-ultra">
                        {industry.name}
                      </h3>
                    </div>
                    <p className="text-gray-100 mb-6">
                      {industryDescriptions[industry.name] ||
                        "Industrial hemp provides sustainable solutions for this industry with its versatile applications."}
                    </p>
                    <Link href={`/industry/${industry.id}`}>
                      <Button
                        variant="outline"
                        className="group border-green-500/50 text-green-400 hover:bg-green-500/10 hover:border-green-400"
                      >
                        Explore Applications
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 pt-6 border-t border-gray-800/50">
                  <div className="text-gray-400 text-sm">
                    Page {currentPage} of {totalPages}
                  </div>

                  <div className="flex items-center gap-2">
                    {/* Previous Button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>

                    {/* Page Numbers */}
                    <div className="flex gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                            className={`w-10 ${
                              currentPage === pageNum
                                ? 'bg-green-500 text-white'
                                : 'bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60'
                            }`}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>

                    {/* Next Button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12 border border-green-800/30 rounded-md bg-black/50">
              <p className="text-green-400 text-xl">
                No industries found matching your search criteria.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Industry stats section */}
      <div className="bg-black/30 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl sm:text-3xl font-heading font-semibold text-green-400 mb-12">
            Hemp Industry Growth
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-8 border border-green-500/30">
              <div className="text-4xl font-bold text-green-400 mb-2">
                35%
              </div>
              <p className="text-gray-100">
                Annual growth in the hemp textiles market
              </p>
            </div>
            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-8 border border-green-500/30">
              <div className="text-4xl font-bold text-green-400 mb-2">
                $15B
              </div>
              <p className="text-gray-100">
                Projected global hemp market by 2027
              </p>
            </div>
            <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg shadow-lg shadow-black/50 p-8 border border-green-500/30">
              <div className="text-4xl font-bold text-green-400 mb-2">
                25+
              </div>
              <p className="text-gray-100">
                Major industries using hemp materials
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="bg-gradient-to-b from-black/30 to-black/40 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-heading font-semibold hemp-brand-ultra mb-4">
            Discover Hemp Plant Types
          </h2>
          <p className="text-lg text-gray-100 max-w-3xl mx-auto mb-8">
            Explore different hemp plant types and their specific applications
            across industries.
          </p>
          <Link href="/plant-types">
            <Button className="bg-green-600 hover:bg-green-700 text-white">
              View Hemp Plant Types
            </Button>
          </Link>
        </div>
      </div>
    </>
  );
};

export default IndustriesPage;
