#!/usr/bin/env python3
"""
Comprehensive fix for all products - ensures proper company separation
Maps known product patterns to their companies
"""

import os
import sys
from supabase import create_client

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

# Comprehensive mapping of products to their companies
PRODUCT_COMPANY_MAPPINGS = {
    # Food & Beverage
    "Manitoba Harvest Hemp Hearts": {
        "clean_name": "Hemp Hearts (Shelled Hemp Seeds)",
        "companies": ["Manitoba Harvest"]
    },
    "Nutiva Organic Hemp Oil": {
        "clean_name": "Cold-Pressed Hemp Seed Oil",
        "companies": ["Nutiva"]
    },
    "Good Hemp Seed Oil": {
        "clean_name": "Cold-Pressed Hemp Seed Oil",
        "companies": ["Good Hemp"]
    },
    "Victory Hemp V-70 Hemp Protein": {
        "clean_name": "Hemp Protein Powder (70% Protein)",
        "companies": ["Victory Hemp Foods"]
    },
    "Fresh Hemp Foods Hemp Oil Capsules": {
        "clean_name": "Hemp Oil Capsules",
        "companies": ["Fresh Hemp Foods"]
    },
    "Hemp Foods Australia Organic Hemp Seeds": {
        "clean_name": "Organic Hemp Seeds (Hulled)",
        "companies": ["Hemp Foods Australia"]
    },
    "Buddha Teas Organic Hemp Leaf Tea": {
        "clean_name": "Hemp Leaf Tea",
        "companies": ["Buddha Teas"]
    },
    
    # Textiles
    "Patagonia Hemp Canvas Pants": {
        "clean_name": "Hemp Canvas Pants",
        "companies": ["Patagonia"]
    },
    "WAMA Hemp Underwear": {
        "clean_name": "Hemp Underwear",
        "companies": ["WAMA Underwear"]
    },
    "Hemp Fortex Hemp Denim Fabric": {
        "clean_name": "Hemp Denim Fabric",
        "companies": ["Hemp Fortex"]
    },
    "Toad&Co Hemp Trail Shorts": {
        "clean_name": "Hemp Trail Shorts",
        "companies": ["Toad&Co"]
    },
    "tentree Hemp Hoodie": {
        "clean_name": "Hemp Hoodie",
        "companies": ["tentree"]
    },
    "Hempsmith Hemp Work Shirt": {
        "clean_name": "Hemp Work Shirt",
        "companies": ["Hempsmith Clothing"]
    },
    "American Hemp LLC Industrial Hemp Yarn": {
        "clean_name": "Industrial Hemp Yarn",
        "companies": ["American Hemp LLC"]
    },
    
    # Construction
    "Hempcrete Building Blocks": {
        "clean_name": "Hempcrete Building Blocks",
        "companies": ["Just BioFiber", "IsoHemp", "Hemp Block USA"]
    },
    "Hemp Wood Flooring": {
        "clean_name": "Hemp Wood Flooring",
        "companies": ["HempWood"]
    },
    "HempWood Flooring Planks": {
        "clean_name": "Hemp Wood Flooring",
        "companies": ["HempWood"]
    },
    "Hempitecture HempWool Insulation": {
        "clean_name": "Hemp Fiber Insulation",
        "companies": ["Hempitecture"]
    },
    "HempTraders Hempcrete Blocks": {
        "clean_name": "Hempcrete Building Blocks",
        "companies": ["HempTraders"]
    },
    "BioFiber Industries Hemp-Lime Plaster": {
        "clean_name": "Hemp-Lime Plaster",
        "companies": ["BioFiber Industries"]
    },
    "Doylestown Hemp Company Hempcrete Spray System": {
        "clean_name": "Hempcrete Spray Insulation",
        "companies": ["Doylestown Hemp Company"]
    },
    
    # Cosmetics
    "Kiehl's Cannabis Sativa Seed Oil Concentrate": {
        "clean_name": "Hemp Seed Oil Face Serum",
        "companies": ["Kiehl's"]
    },
    "The Body Shop Hemp Hand Protector": {
        "clean_name": "Hemp Hand Cream",
        "companies": ["The Body Shop"]
    },
    
    # Medicine/Wellness
    "Hemp Leaf Extract Tincture": {
        "clean_name": "Hemp Leaf Extract Tincture",
        "companies": ["Various Brands"]
    }
}

# Generic product types that should be consolidated
GENERIC_PRODUCTS = {
    "Hemp Hearts": ["Hemp Hearts (Shelled Hemp Seeds)", "Hemp Hearts Original"],
    "Hemp Oil": ["Cold-Pressed Hemp Seed Oil", "Hemp Seed Oil", "Organic Hemp Oil"],
    "Hemp Protein": ["Hemp Protein Powder", "Hemp Protein Powder (70% Protein)"],
    "Hemp Milk": ["Hemp Milk", "Hemp Seed Milk"],
    "Hempcrete": ["Hempcrete Building Blocks", "Hempcrete Blocks"],
    "Hemp Insulation": ["Hemp Fiber Insulation", "HempWool Insulation", "Hemp Insulation Batts"],
    "Hemp T-Shirt": ["Hemp T-Shirt", "Hemp Tee", "Hemp T-Shirt Basic"],
    "Hemp Jeans": ["Hemp Denim Jeans", "Hemp Jeans", "Hemp Denim"],
    "Hemp Tea": ["Hemp Leaf Tea", "Hemp Tea", "Organic Hemp Leaf Tea"]
}

def get_or_create_company(supabase, company_name):
    """Get existing company or create new one"""
    # Skip if company name is generic
    if company_name in ["Various Brands", "Multiple Brands"]:
        return None
        
    # Check if company exists
    result = supabase.table('hemp_companies').select('id').eq('name', company_name).execute()
    
    if result.data:
        return result.data[0]['id']
    
    # Create new company
    new_company = {
        'name': company_name,
        'description': f'{company_name} - Hemp product manufacturer and brand',
        'verified': True,
        'company_type': 'manufacturer'
    }
    
    result = supabase.table('hemp_companies').insert(new_company).execute()
    if result.data:
        return result.data[0]['id']
    return None

def link_product_to_company(supabase, product_id, company_id):
    """Create product-company relationship"""
    # Check if relationship exists
    existing = supabase.table('hemp_company_products')\
        .select('id')\
        .eq('product_id', product_id)\
        .eq('company_id', company_id)\
        .execute()
    
    if not existing.data:
        relationship = {
            'product_id': product_id,
            'company_id': company_id,
            'is_primary': True,
            'verified': True,
            'relationship_type': 'manufacturer'
        }
        result = supabase.table('hemp_company_products').insert(relationship).execute()
        return result.data is not None
    return False

def fix_all_products():
    """Comprehensive fix for all products"""
    # Initialize Supabase
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials!")
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("🔧 Comprehensive Product-Company Fix")
    print("=" * 60)
    
    # Get all products
    products = supabase.table('uses_products').select('id, name').execute()
    
    products_updated = 0
    relationships_created = 0
    products_renamed = 0
    
    # Process known mappings
    print("\n📋 Processing known product mappings...")
    for product in products.data:
        original_name = product['name']
        product_id = product['id']
        
        # Check if this product is in our mappings
        if original_name in PRODUCT_COMPANY_MAPPINGS:
            mapping = PRODUCT_COMPANY_MAPPINGS[original_name]
            new_name = mapping['clean_name']
            companies = mapping['companies']
            
            print(f"\n🔄 {original_name}")
            
            # Update product name if needed
            if new_name != original_name:
                result = supabase.table('uses_products')\
                    .update({'name': new_name})\
                    .eq('id', product_id)\
                    .execute()
                if result.data:
                    print(f"   ✅ Renamed to: {new_name}")
                    products_renamed += 1
            
            # Link to companies
            for company_name in companies:
                company_id = get_or_create_company(supabase, company_name)
                if company_id:
                    if link_product_to_company(supabase, product_id, company_id):
                        print(f"   ✅ Linked to: {company_name}")
                        relationships_created += 1
            
            products_updated += 1
    
    # Consolidate generic products
    print("\n\n🔄 Consolidating generic products...")
    for generic_name, variations in GENERIC_PRODUCTS.items():
        # Find the main product (first in list)
        main_product_name = variations[0]
        main_product = supabase.table('uses_products')\
            .select('id')\
            .eq('name', main_product_name)\
            .limit(1)\
            .execute()
        
        if main_product.data:
            main_id = main_product.data[0]['id']
            
            # Find other variations
            for variant_name in variations[1:]:
                variants = supabase.table('uses_products')\
                    .select('id')\
                    .eq('name', variant_name)\
                    .execute()
                
                for variant in variants.data:
                    if variant['id'] != main_id:
                        # Transfer any company relationships
                        relationships = supabase.table('hemp_company_products')\
                            .select('company_id')\
                            .eq('product_id', variant['id'])\
                            .execute()
                        
                        for rel in relationships.data:
                            link_product_to_company(supabase, main_id, rel['company_id'])
                        
                        # Instead of deleting, update the variant to have a unique name
                        # This avoids foreign key constraint issues
                        unique_name = f"{variant_name} (Duplicate - {variant['id']})"
                        supabase.table('uses_products')\
                            .update({'name': unique_name})\
                            .eq('id', variant['id'])\
                            .execute()
                        
                        print(f"   ⚠️  Marked duplicate: '{variant_name}' → '{unique_name}'")
    
    # Final summary
    print(f"\n\n📊 Fix Summary:")
    print(f"   - Products processed: {products_updated}")
    print(f"   - Products renamed: {products_renamed}")
    print(f"   - Company relationships created: {relationships_created}")
    
    # Show current state
    print(f"\n📈 Current Database State:")
    
    # Total products
    total_products = supabase.table('uses_products').select('count', count='exact').execute()
    print(f"   - Total products: {total_products.count}")
    
    # Total companies
    total_companies = supabase.table('hemp_companies').select('count', count='exact').execute()
    print(f"   - Total companies: {total_companies.count}")
    
    # Products with companies
    products_with_companies = supabase.table('products_with_companies')\
        .select('product_name, companies')\
        .neq('companies', None)\
        .limit(10)\
        .execute()
    
    print(f"\n📦 Sample Products with Companies:")
    for item in products_with_companies.data:
        if item['companies']:
            print(f"   - {item['product_name']}")
            print(f"     Brands: {item['companies']}")
    
    # Products without companies
    orphaned = supabase.table('products_with_companies')\
        .select('product_name')\
        .is_('companies', 'null')\
        .execute()
    
    if orphaned.data:
        print(f"\n⚠️  Products without companies: {len(orphaned.data)}")
        for p in orphaned.data[:5]:
            print(f"   - {p['product_name']}")

if __name__ == "__main__":
    fix_all_products()