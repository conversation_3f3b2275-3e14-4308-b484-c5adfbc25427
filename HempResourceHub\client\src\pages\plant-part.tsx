import { useEffect, useState, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "wouter";
import { Helmet } from "react-helmet";
import { usePlantPart } from "@/hooks/use-plant-data";
import { useHempProducts, useHempProduct } from "@/hooks/use-product-data";

import { EnhancedBreadcrumbs, useContextualBreadcrumbs } from "@/components/ui/enhanced-breadcrumbs";

import ProductCard from "@/components/product/product-card";
import ProductPagination from "@/components/product/product-pagination";
import { Skeleton } from "@/components/ui/skeleton";
import { InfoIcon } from "lucide-react";
import { AlphabetFilter } from "@/components/ui/alphabet-filter";

// Function to get relevant facts for each plant part
const getPlantPartFacts = (partName: string): string[] => {
  const name = partName?.toLowerCase() || '';

  if (name.includes('stalk') || name.includes('stem')) {
    return [
      'Hemp stalks contain both bast fiber (outer) and hurd (inner core)',
      'Bast fibers are 3-5 times stronger than cotton fibers',
      'Hemp hurd can absorb up to 3.5 times its weight in moisture',
      'Stalks can grow up to 16 feet tall in a single growing season'
    ];
  }

  if (name.includes('seed')) {
    return [
      'Hemp seeds contain all 20 amino acids, including 9 essential ones',
      'Seeds are 25% protein and 35% healthy fats (omega-3 and omega-6)',
      'A single hemp plant can produce up to 1,000 seeds',
      'Hemp seeds have a perfect 3:1 ratio of omega-6 to omega-3 fatty acids'
    ];
  }

  if (name.includes('leaf') || name.includes('leaves')) {
    return [
      'Hemp leaves contain over 100 different cannabinoids and terpenes',
      'Fan leaves can photosynthesize more efficiently than most crops',
      'Leaves naturally repel many common agricultural pests',
      'Hemp leaves decompose quickly, enriching soil with nitrogen'
    ];
  }

  if (name.includes('flower') || name.includes('bud')) {
    return [
      'Hemp flowers contain the highest concentration of cannabinoids',
      'Flowers produce terpenes that give hemp its distinctive aroma',
      'Female flowers can be harvested without seeds for higher CBD content',
      'Hemp flowers bloom when daylight hours drop below 12 hours'
    ];
  }

  if (name.includes('root')) {
    return [
      'Hemp roots can extend up to 9 feet deep into soil',
      'Root system helps prevent soil erosion and improves soil structure',
      'Hemp roots naturally detoxify soil by absorbing heavy metals',
      'Taproot system allows hemp to access deep water sources during drought'
    ];
  }

  if (name.includes('fiber') || name.includes('bast')) {
    return [
      'Hemp bast fibers are naturally antimicrobial and UV resistant',
      'Fibers become softer and more comfortable with each wash',
      'Hemp fiber production requires 50% less water than cotton',
      'Bast fibers can be processed into textiles finer than linen'
    ];
  }

  if (name.includes('hurd') || name.includes('shiv')) {
    return [
      'Hemp hurd is naturally fire-resistant and pest-resistant',
      'Hurd can be used to create carbon-negative building materials',
      'The woody core provides excellent insulation properties',
      'Hemp hurd concrete (hempcrete) regulates humidity naturally'
    ];
  }

  // Default facts for unknown or general plant parts
  return [
    'Hemp has been cultivated for over 10,000 years across civilizations',
    'Every part of the hemp plant has commercial applications',
    'Hemp grows in most climates and improves soil health',
    'Industrial hemp contains less than 0.3% THC by law'
  ];
};

const PlantPartPage = () => {
  const [match, params] = useRoute("/plant-part/:id");
  const plantPartId = match ? parseInt(params.id) : null;
  const [currentPage, setCurrentPage] = useState(1);

  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const itemsPerPage = 6;

  const { data: plantPart, isLoading: isLoadingPlantPart } =
    usePlantPart(plantPartId);
  const { data: allProductsData, isLoading: isLoadingProducts } = useHempProducts(
    plantPartId,
    null, // No industry filter
    1, // Get all pages
    1000, // Large number to get all products
  );

  // Client-side filtering for letter filter
  const productsData = useMemo(() => {
    if (!allProductsData?.products) return allProductsData;

    let filteredProducts = allProductsData.products;

    // Apply letter filter
    if (selectedLetter) {
      filteredProducts = filteredProducts.filter(product =>
        product.name.charAt(0).toUpperCase() === selectedLetter
      );
    }

    // Apply pagination
    const totalCount = filteredProducts.length;
    const totalPages = Math.ceil(totalCount / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage);

    return {
      products: paginatedProducts,
      totalCount,
      totalPages,
      currentPage,
      itemsPerPage
    };
  }, [allProductsData, selectedLetter, currentPage, itemsPerPage]);

  // Scroll to top on page load or filter change
  useEffect(() => {
    window.scrollTo(0, 0);
    setCurrentPage(1); // Reset to first page when filter changes
  }, [plantPartId, selectedLetter]);

  // Create lookup objects for plant part names
  const plantPartNames: Record<number, string> = {};

  if (plantPart) {
    plantPartNames[plantPart.id] = plantPart.name;
  }

  // Enhanced breadcrumbs with contextual information
  const breadcrumbs = useContextualBreadcrumbs('plant-part', {
    ...plantPart,
    productCount: productsData?.totalCount || 0
  });

  if (!match) {
    return (
      <div className="py-12 bg-gray-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-8 text-center border border-green-500/30">
            <h1 className="text-2xl font-heading font-bold text-white mb-4">
              Plant Part Not Found
            </h1>
            <p className="text-gray-400 mb-6">
              The requested plant part could not be found. Please select a plant part from our collection.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/plant-parts">
                <div className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors cursor-pointer">
                  Browse Plant Parts
                </div>
              </Link>
              <Link href="/">
                <div className="text-green-400 hover:text-green-300 px-6 py-3 font-medium transition-colors cursor-pointer">
                  Return to Homepage
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleLetterFilter = (letter: string | null) => {
    setSelectedLetter(letter);
  };

  return (
    <>
      <Helmet>
        <title>
          {isLoadingPlantPart
            ? "Loading Plant Part..."
            : `${plantPart?.name || "Plant Part"} Applications - HempDB`}
        </title>
        <meta
          name="description"
          content={
            isLoadingPlantPart
              ? "Loading plant part information..."
              : `Explore hemp ${plantPart?.name.toLowerCase() || "part"} applications and products across various industries. ${plantPart?.description || ""}`
          }
        />
      </Helmet>

      <div className="py-12 bg-gray-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {isLoadingPlantPart ? (
            <div className="mb-6">
              <Skeleton className="h-6 w-48 mb-2" />
              <Skeleton className="h-8 w-96" />
            </div>
          ) : (
            <>
              <div className="mb-6">
                <EnhancedBreadcrumbs
                  items={breadcrumbs}
                  showHome={true}
                  showContext={true}
                />
              </div>

              <h2 className="text-3xl font-heading font-bold hemp-brand-ultra mt-2 mb-6">
                {plantPart?.name} Applications by Industry
              </h2>
            </>
          )}

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Enhanced Sidebar with plant part info */}
            <div className="lg:w-1/3">
              <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 sticky top-6 border border-green-500/30">
                {isLoadingPlantPart ? (
                  <>
                    <div className="text-center mb-6">
                      <Skeleton className="h-12 w-12 rounded-full mx-auto mb-3" />
                      <Skeleton className="h-6 w-32 mx-auto mb-2" />
                      <Skeleton className="h-1 w-16 mx-auto" />
                    </div>
                    <Skeleton className="aspect-square w-full rounded-xl mb-6" />
                    <Skeleton className="h-6 w-48 mb-2" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-full mb-4" />
                    <div className="space-y-3 mb-6">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  </>
                ) : (
                  <>
                    {/* Enhanced header with better spacing */}
                    <div className="text-center mb-6">
                      <h2 className="text-3xl font-heading font-bold hemp-brand-ultra mb-2">
                        Hemp {plantPart?.name}
                      </h2>
                      <div className="w-16 h-1 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full mx-auto"></div>
                    </div>

                    {/* Enhanced part image with consistent styling matching plant parts page */}
                    <div className="aspect-square relative overflow-hidden rounded-xl bg-gray-800 mb-6 group">
                      <img
                        src={plantPart?.image_url || '/images/unknown-hemp-image.png'}
                        alt={`Hemp ${plantPart?.name}`}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                        onError={(e) => {
                          e.currentTarget.src = '/images/unknown-hemp-image.png';
                        }}
                      />
                      {/* Enhanced gradient overlay matching plant parts page */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                      {/* Image label overlay */}
                      <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
                        <p className="text-white text-sm font-medium">{plantPart?.name}</p>
                      </div>
                    </div>

                    {/* Enhanced description section */}
                    <div className="mb-6">
                      <h3 className="font-heading font-semibold text-xl hemp-brand-ultra mb-3 flex items-center">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        About This Part
                      </h3>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        {plantPart?.description}
                      </p>
                    </div>

                    {/* Enhanced key facts section with dynamic content */}
                    <div className="p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-xl border border-green-500/30">
                      <h4 className="font-heading font-semibold text-green-400 mb-3 flex items-center">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        Key Facts
                      </h4>
                      <ul className="space-y-3 text-sm text-gray-300">
                        {getPlantPartFacts(plantPart?.name || '').map((fact, index) => (
                          <li key={index} className="flex items-start">
                            <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 mr-3 shrink-0"></span>
                            {fact}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Enhanced Main content with applications */}
            <div className="lg:w-2/3">
              {/* Enhanced header section */}
              <div className="mb-8">
                <h3 className="text-3xl font-heading font-bold hemp-brand-ultra mb-2">
                  Applications & Products
                </h3>
                <p className="text-gray-400 text-sm">
                  Discover the diverse applications of hemp {plantPart?.name?.toLowerCase()} across various industries
                </p>
              </div>

              {/* Filter tabs */}
              <div className="space-y-4 mb-8">
                {/* A-Z Filter for Products */}
                <AlphabetFilter
                  selectedLetter={selectedLetter}
                  onLetterSelect={handleLetterFilter}
                />
              </div>

              {/* Application cards */}
              {isLoadingProducts ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[1, 2, 3, 4].map((i) => (
                    <Skeleton key={i} className="h-80 rounded-xl" />
                  ))}
                </div>
              ) : (
                <>
                  {productsData?.products &&
                  productsData.products.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {productsData.products.map((product) => (
                        <ProductCard
                          key={product.id}
                          product={product}
                          plantPartNames={plantPartNames}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-8 text-center border border-green-500/30">
                      <h3 className="text-xl font-heading font-semibold mb-2 text-white">
                        No Products Found
                      </h3>
                      <p className="text-gray-400 mb-4">
                        No products were found for the selected filters. Try
                        selecting a different letter or explore all applications.
                      </p>
                    </div>
                  )}
                </>
              )}

              {/* Pagination controls */}
              {!isLoadingProducts &&
                productsData?.pagination &&
                productsData.pagination.total > itemsPerPage && (
                  <ProductPagination
                    currentPage={currentPage}
                    totalPages={productsData.pagination.pages}
                    totalItems={productsData.pagination.total}
                    itemsPerPage={itemsPerPage}
                    onPageChange={handlePageChange}
                  />
                )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PlantPartPage;
