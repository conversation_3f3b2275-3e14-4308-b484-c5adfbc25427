import { useEffect, useState } from "react";
import { use<PERSON>out<PERSON>, <PERSON> } from "wouter";
import { Helmet } from "react-helmet";
import { useHempProduct } from "@/hooks/use-product-data";
import { useIndustries } from "@/hooks/use-plant-data";
import { EnhancedBreadcrumbs, useContextualBreadcrumbs } from "@/components/ui/enhanced-breadcrumbs";
import ProductDetailView from "@/components/product/product-detail-view";
import ProductImageManagement from "@/components/product/product-image-management";
import { ProductAssistant } from "@/components/ai/product-assistant";
import { Skeleton } from "@/components/ui/skeleton";

const ProductDetailPage = () => {
  const [match, params] = useRoute("/product/:id");
  const productId = match ? parseInt(params.id) : null;
  const { data: product, isLoading: isLoadingProduct } = useHempProduct(productId);
  const { data: industries, isLoading: isLoadingIndustries } = useIndustries();
  const [showImageManagement, setShowImageManagement] = useState(false);

  // Enhanced breadcrumbs with contextual information
  const breadcrumbs = useContextualBreadcrumbs('product', product);

  // Create lookup objects for industry and subindustry names
  const industryNames: Record<number, string> = {};
  const subIndustryNames: Record<number, string> = {};
  
  if (industries) {
    industries.forEach(industry => {
      industryNames[industry.id] = industry.name;
    });
  }
  
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [productId]);

  // Check if user is admin (you may want to implement proper auth check)
  useEffect(() => {
    const isAdmin = localStorage.getItem('isAdmin') === 'true' || 
                    window.location.search.includes('admin=true');
    setShowImageManagement(isAdmin);
  }, []);

  if (!match) {
    return (
      <div className="py-12 bg-gray-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gray-900 rounded-xl shadow-lg shadow-black/50 p-8 text-center border border-green-500/30">
            <h1 className="text-2xl font-heading font-bold text-gray-100 mb-4">Product Not Found</h1>
            <p className="text-gray-300 mb-6">The requested product could not be found.</p>
            <Link href="/hemp-dex" className="text-green-400 hover:text-green-300 font-medium transition-colors mr-4">
              Browse Products
            </Link>
            <Link href="/" className="text-green-400 hover:text-green-300 font-medium transition-colors">
              Return to Homepage
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Debug logging
  console.log('Product Detail Page - Product ID:', productId);
  console.log('Product Detail Page - Product Data:', product);
  console.log('Product Detail Page - Loading State:', isLoadingProduct);

  return (
    <>
      <Helmet>
        <title>
          {isLoadingProduct 
            ? "Loading Product..." 
            : `${product?.name || "Hemp Product"} - HempDB`
          }
        </title>
        <meta 
          name="description" 
          content={
            isLoadingProduct 
              ? "Loading hemp product information..." 
              : `Detailed information about ${product?.name || "this hemp product"}. ${product?.description?.substring(0, 150) || ""}`
          } 
        />
      </Helmet>

      <div className="py-12 bg-gray-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {isLoadingProduct || isLoadingIndustries ? (
            <div className="mb-6">
              <Skeleton className="h-6 w-48 mb-2" />
            </div>
          ) : (
            <div className="mb-6">
              <EnhancedBreadcrumbs
                items={breadcrumbs}
                showHome={true}
                showContext={true}
              />
            </div>
          )}

          <div className="space-y-6">
            {/* Product detail view */}
            {productId && (
              <ProductDetailView 
                productId={productId} 
                industryNames={industryNames}
                subIndustryNames={subIndustryNames}
              />
            )}
            
            {/* Image Management Section (Admin Only) */}
            {showImageManagement && productId && product && (
              <ProductImageManagement
                productId={productId}
                productName={product.name}
                currentImageUrl={product.image_url}
              />
            )}
            
            {/* AI Product Assistant */}
            {product && (
              <ProductAssistant 
                productName={product.name}
                productDescription={product.description}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductDetailPage;