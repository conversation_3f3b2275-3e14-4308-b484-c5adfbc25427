import React, { useState } from 'react';
import { 
  Card, 
  Metric, 
  Text, 
  Flex, 
  Grid, 
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  BadgeDelta,
  Button,
  Tab,
  TabGroup,
  TabList,
  TabPanel,
  TabPanels,
  ProgressBar,
  Badge
} from '@tremor/react';
import { 
  RefreshCw, 
  Play, 
  Download, 
  Activity,
  Users,
  Package,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';
import { useAgentMetrics, useSystemHealth, useRecentActivities } from '@/hooks/use-admin-data';

const AdminDashboardRedesigned = () => {
  const [timeRange, setTimeRange] = useState('24h');
  const { data: metrics, refetch: refetchMetrics } = useAgentMetrics(timeRange);
  const { data: health } = useSystemHealth();
  const { data: activities } = useRecentActivities();
  
  // Mock data for now - replace with real data from hooks
  const mockMetrics = {
    activeAgents: 6,
    tasksCompleted: 1247,
    successRate: 94.5,
    avgResponseTime: 342,
    totalProducts: 149,
    newThisWeek: 23,
    aiCreditsUsed: 3247,
    aiCreditsLimit: 10000
  };
  
  const performanceData = [
    { timestamp: '00:00', Research: 45, Content: 62, Monetization: 38, Outreach: 41 },
    { timestamp: '04:00', Research: 52, Content: 71, Monetization: 42, Outreach: 48 },
    { timestamp: '08:00', Research: 68, Content: 85, Monetization: 55, Outreach: 62 },
    { timestamp: '12:00', Research: 91, Content: 95, Monetization: 78, Outreach: 85 },
    { timestamp: '16:00', Research: 88, Content: 92, Monetization: 82, Outreach: 89 },
    { timestamp: '20:00', Research: 75, Content: 78, Monetization: 68, Outreach: 72 },
    { timestamp: '24:00', Research: 82, Content: 86, Monetization: 74, Outreach: 78 }
  ];
  
  const taskDistribution = [
    { type: 'Product Discovery', count: 487 },
    { type: 'Content Creation', count: 312 },
    { type: 'Partnership Outreach', count: 198 },
    { type: 'Market Analysis', count: 156 },
    { type: 'SEO Optimization', count: 94 }
  ];
  
  const agentStatus = [
    { name: 'Research Agent', status: 'active', lastActive: '2 min ago', tasksToday: 47, successRate: 96 },
    { name: 'Content Agent', status: 'active', lastActive: '5 min ago', tasksToday: 32, successRate: 91 },
    { name: 'Monetization Agent', status: 'idle', lastActive: '1 hour ago', tasksToday: 18, successRate: 88 },
    { name: 'Outreach Agent', status: 'active', lastActive: '12 min ago', tasksToday: 25, successRate: 94 },
    { name: 'SEO Agent', status: 'error', lastActive: '3 hours ago', tasksToday: 8, successRate: 72 },
    { name: 'Compliance Agent', status: 'maintenance', lastActive: '1 day ago', tasksToday: 0, successRate: 100 }
  ];
  
  const getStatusColor = (status: string) => {
    switch(status) {
      case 'active': return 'emerald';
      case 'idle': return 'yellow';
      case 'error': return 'rose';
      case 'maintenance': return 'gray';
      default: return 'blue';
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch(status) {
      case 'active': return <Activity className="h-4 w-4" />;
      case 'idle': return <Clock className="h-4 w-4" />;
      case 'error': return <AlertCircle className="h-4 w-4" />;
      case 'maintenance': return <RefreshCw className="h-4 w-4" />;
      default: return <CheckCircle className="h-4 w-4" />;
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      {/* Header with Quick Actions */}
      <div className="mb-8">
        <Flex justifyContent="between" alignItems="center">
          <div>
            <Title className="text-3xl font-bold">Admin Dashboard</Title>
            <Text className="mt-1">Monitor and manage your hemp database agents</Text>
          </div>
          <div className="flex gap-3">
            <Button 
              icon={RefreshCw} 
              onClick={() => refetchMetrics()}
              variant="secondary"
            >
              Sync Data
            </Button>
            <Button 
              icon={Play}
              color="emerald"
            >
              Run All Agents
            </Button>
            <Button 
              icon={Download}
              variant="secondary"
            >
              Export Report
            </Button>
          </div>
        </Flex>
      </div>
      
      {/* Key Metrics */}
      <Grid numItems={1} numItemsSm={2} numItemsLg={4} className="gap-6 mb-6">
        <Card>
          <Flex alignItems="start">
            <div>
              <Text>Active Agents</Text>
              <Metric>{mockMetrics.activeAgents}</Metric>
            </div>
            <BadgeDelta deltaType="increase" className="ml-auto">12.3%</BadgeDelta>
          </Flex>
          <Flex className="mt-4">
            <Text className="truncate">vs last period</Text>
            <Zap className="ml-auto h-5 w-5 text-emerald-500" />
          </Flex>
        </Card>
        
        <Card>
          <Flex alignItems="start">
            <div>
              <Text>Tasks Completed</Text>
              <Metric>{mockMetrics.tasksCompleted.toLocaleString()}</Metric>
            </div>
            <BadgeDelta deltaType="decrease" className="ml-auto">2.1%</BadgeDelta>
          </Flex>
          <ProgressBar value={75} className="mt-4" />
        </Card>
        
        <Card>
          <Flex alignItems="start">
            <div>
              <Text>Success Rate</Text>
              <Metric>{mockMetrics.successRate}%</Metric>
            </div>
            <BadgeDelta deltaType="increase" className="ml-auto">5.4%</BadgeDelta>
          </Flex>
          <ProgressBar value={mockMetrics.successRate} className="mt-4" color="emerald" />
        </Card>
        
        <Card>
          <Flex alignItems="start">
            <div>
              <Text>Avg Response Time</Text>
              <Metric>{mockMetrics.avgResponseTime}ms</Metric>
            </div>
            <BadgeDelta deltaType="decrease" isIncreasePositive={false} className="ml-auto">
              15.2%
            </BadgeDelta>
          </Flex>
          <Flex className="mt-4">
            <Text className="truncate">Target: &lt;500ms</Text>
            <CheckCircle className="ml-auto h-5 w-5 text-emerald-500" />
          </Flex>
        </Card>
      </Grid>
      
      {/* Main Content Area */}
      <TabGroup>
        <TabList className="mb-6">
          <Tab>Overview</Tab>
          <Tab>Agents</Tab>
          <Tab>Analytics</Tab>
          <Tab>Resources</Tab>
        </TabList>
        
        <TabPanels>
          {/* Overview Tab */}
          <TabPanel>
            <Grid numItems={1} numItemsLg={2} className="gap-6">
              {/* Agent Performance Chart */}
              <Card>
                <Title>Agent Performance Over Time</Title>
                <Text>Success rate by agent type (last 24 hours)</Text>
                <LineChart
                  className="h-72 mt-4"
                  data={performanceData}
                  index="timestamp"
                  categories={["Research", "Content", "Monetization", "Outreach"]}
                  colors={["blue", "emerald", "yellow", "purple"]}
                  valueFormatter={(value) => `${value}%`}
                  showLegend={true}
                  showGridLines={false}
                />
              </Card>
              
              {/* Task Distribution */}
              <Card>
                <Title>Task Distribution</Title>
                <Text>Breakdown by task type</Text>
                <DonutChart
                  className="h-72 mt-4"
                  data={taskDistribution}
                  category="count"
                  index="type"
                  colors={["blue", "cyan", "indigo", "violet", "fuchsia"]}
                  showLabel={true}
                  valueFormatter={(value) => value.toLocaleString()}
                />
              </Card>
            </Grid>
            
            {/* Recent Activities */}
            <Card className="mt-6">
              <Title>Recent Activities</Title>
              <div className="mt-4 space-y-4">
                <ActivityItem 
                  agent="Research Agent"
                  action="Discovered 5 new hemp textile products"
                  time="2 minutes ago"
                  status="success"
                />
                <ActivityItem 
                  agent="Content Agent"
                  action="Generated blog post: 'Hemp in Construction Industry'"
                  time="15 minutes ago"
                  status="success"
                />
                <ActivityItem 
                  agent="SEO Agent"
                  action="Failed to optimize meta descriptions"
                  time="1 hour ago"
                  status="error"
                />
                <ActivityItem 
                  agent="Outreach Agent"
                  action="Sent 12 partnership emails"
                  time="2 hours ago"
                  status="success"
                />
              </div>
            </Card>
          </TabPanel>
          
          {/* Agents Tab */}
          <TabPanel>
            <Card>
              <Title>Agent Status & Health</Title>
              <div className="mt-6 overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700">
                      <th className="text-left py-3 px-4">Agent</th>
                      <th className="text-left py-3 px-4">Status</th>
                      <th className="text-left py-3 px-4">Last Active</th>
                      <th className="text-left py-3 px-4">Tasks Today</th>
                      <th className="text-left py-3 px-4">Success Rate</th>
                      <th className="text-left py-3 px-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {agentStatus.map((agent) => (
                      <tr key={agent.name} className="border-b border-gray-100 dark:border-gray-800">
                        <td className="py-3 px-4 font-medium">{agent.name}</td>
                        <td className="py-3 px-4">
                          <Badge color={getStatusColor(agent.status)} icon={getStatusIcon(agent.status)}>
                            {agent.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4 text-gray-500">{agent.lastActive}</td>
                        <td className="py-3 px-4">{agent.tasksToday}</td>
                        <td className="py-3 px-4">
                          <Flex>
                            <Text>{agent.successRate}%</Text>
                            <ProgressBar 
                              value={agent.successRate} 
                              className="ml-4 w-24" 
                              color={agent.successRate > 90 ? 'emerald' : agent.successRate > 75 ? 'yellow' : 'rose'}
                            />
                          </Flex>
                        </td>
                        <td className="py-3 px-4">
                          <Flex className="gap-2">
                            <Button size="xs" variant="secondary">View</Button>
                            <Button size="xs" color="emerald">Run</Button>
                          </Flex>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </TabPanel>
          
          {/* Analytics Tab */}
          <TabPanel>
            <Grid numItems={1} numItemsLg={2} className="gap-6">
              <Card>
                <Title>Product Discovery Trends</Title>
                <BarChart
                  className="h-72 mt-4"
                  data={[
                    { week: 'Week 1', Products: 23, Research: 156 },
                    { week: 'Week 2', Products: 31, Research: 189 },
                    { week: 'Week 3', Products: 28, Research: 178 },
                    { week: 'Week 4', Products: 42, Research: 234 }
                  ]}
                  index="week"
                  categories={["Products", "Research"]}
                  colors={["emerald", "blue"]}
                />
              </Card>
              
              <Card>
                <Title>AI Credits Usage</Title>
                <Text>Current billing period</Text>
                <Metric className="mt-4">{mockMetrics.aiCreditsUsed.toLocaleString()} / {mockMetrics.aiCreditsLimit.toLocaleString()}</Metric>
                <ProgressBar 
                  value={(mockMetrics.aiCreditsUsed / mockMetrics.aiCreditsLimit) * 100} 
                  className="mt-4"
                  color="blue"
                />
                <Flex className="mt-4">
                  <Text>32.5% used</Text>
                  <Text>Resets in 18 days</Text>
                </Flex>
              </Card>
            </Grid>
          </TabPanel>
          
          {/* Resources Tab */}
          <TabPanel>
            <Grid numItems={1} numItemsSm={2} numItemsLg={3} className="gap-6">
              <Card>
                <Flex>
                  <Package className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <Text>Total Products</Text>
                    <Metric>{mockMetrics.totalProducts}</Metric>
                    <Text className="mt-2 text-sm text-gray-500">+{mockMetrics.newThisWeek} this week</Text>
                  </div>
                </Flex>
              </Card>
              
              <Card>
                <Flex>
                  <Users className="h-8 w-8 text-emerald-500" />
                  <div className="ml-4">
                    <Text>Active Companies</Text>
                    <Metric>87</Metric>
                    <Text className="mt-2 text-sm text-gray-500">12 pending review</Text>
                  </div>
                </Flex>
              </Card>
              
              <Card>
                <Flex>
                  <TrendingUp className="h-8 w-8 text-purple-500" />
                  <div className="ml-4">
                    <Text>Database Growth</Text>
                    <Metric>+18.2%</Metric>
                    <Text className="mt-2 text-sm text-gray-500">Last 30 days</Text>
                  </div>
                </Flex>
              </Card>
            </Grid>
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  );
};

// Activity Item Component
const ActivityItem = ({ agent, action, time, status }: { 
  agent: string; 
  action: string; 
  time: string; 
  status: 'success' | 'error' | 'warning' 
}) => {
  const statusColors = {
    success: 'text-emerald-500',
    error: 'text-rose-500',
    warning: 'text-yellow-500'
  };
  
  return (
    <Flex>
      <div className="flex-1">
        <Text className="font-medium">{agent}</Text>
        <Text className="text-sm text-gray-500">{action}</Text>
      </div>
      <div className="text-right">
        <Text className={`text-sm ${statusColors[status]}`}>{status}</Text>
        <Text className="text-xs text-gray-400">{time}</Text>
      </div>
    </Flex>
  );
};

export default AdminDashboardRedesigned;