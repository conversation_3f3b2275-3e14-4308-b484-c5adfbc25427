import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function syncProductImages() {
  console.log('🔄 Syncing Product Images from Queue to Products');
  console.log('='.repeat(60));
  
  // Get completed queue items with generated images
  const { data: completedItems } = await supabase
    .from('image_generation_queue')
    .select('product_id, generated_image_url')
    .in('product_id', [190, 191, 192, 193, 194, 195, 196, 197, 198, 199])
    .eq('status', 'completed')
    .not('generated_image_url', 'is', null);
    
  if (!completedItems || completedItems.length === 0) {
    console.log('No completed images found for Augment products');
    return;
  }
  
  console.log(`Found ${completedItems.length} completed images to sync`);
  
  // Update each product
  for (const item of completedItems) {
    if (item.generated_image_url && !item.generated_image_url.includes('placeholder')) {
      console.log(`\nUpdating product ${item.product_id}...`);
      
      // Update uses_products table
      const { error: productError } = await supabase
        .from('uses_products')
        .update({ 
          image_url: item.generated_image_url,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.product_id);
        
      if (productError) {
        console.error(`   ❌ Error updating product: ${productError.message}`);
      } else {
        console.log(`   ✅ Updated uses_products table`);
      }
      
      // Update product_images table
      const { error: imageError } = await supabase
        .from('product_images')
        .update({ 
          image_url: item.generated_image_url
        })
        .eq('use_product_id', item.product_id)
        .eq('is_primary', true);
        
      if (imageError) {
        console.error(`   ❌ Error updating product_images: ${imageError.message}`);
      } else {
        console.log(`   ✅ Updated product_images table`);
      }
    }
  }
  
  // Verify the sync
  console.log('\n' + '='.repeat(60));
  console.log('📊 Verification:');
  
  const { data: products } = await supabase
    .from('uses_products')
    .select('id, name, image_url')
    .in('id', [190, 191, 192, 193, 194, 195, 196, 197, 198, 199])
    .order('id');
    
  products.forEach(p => {
    const hasGenerated = p.image_url && !p.image_url.includes('placeholder');
    console.log(`[${p.id}] ${p.name}: ${hasGenerated ? '✅ Has AI image' : '❌ Still placeholder'}`);
  });
}

syncProductImages().catch(console.error);