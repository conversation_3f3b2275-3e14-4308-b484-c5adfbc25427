"""Rate limiting utilities for API calls."""

import time
import asyncio
from functools import wraps
from typing import Dict, Optional, Callable
import logging

logger = logging.getLogger(__name__)


class RateLimiter:
    """Simple rate limiter for API calls."""
    
    def __init__(self, calls_per_minute: int = 60):
        self.calls_per_minute = calls_per_minute
        self.min_interval = 60.0 / calls_per_minute
        self.last_call_time: Dict[str, float] = {}
        
    async def wait_if_needed(self, key: str = "default"):
        """Wait if necessary to respect rate limits."""
        current_time = time.time()
        last_time = self.last_call_time.get(key, 0)
        time_since_last = current_time - last_time
        
        if time_since_last < self.min_interval:
            wait_time = self.min_interval - time_since_last
            logger.debug(f"Rate limiting: waiting {wait_time:.2f}s for key '{key}'")
            await asyncio.sleep(wait_time)
            
        self.last_call_time[key] = time.time()
        
    def reset(self, key: Optional[str] = None):
        """Reset rate limit tracking."""
        if key:
            self.last_call_time.pop(key, None)
        else:
            self.last_call_time.clear()


def rate_limited(calls_per_minute: int = 60):
    """Decorator for rate-limited async functions."""
    limiter = RateLimiter(calls_per_minute)
    
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Use function name as rate limit key
            key = func.__name__
            await limiter.wait_if_needed(key)
            return await func(*args, **kwargs)
        return wrapper
    return decorator