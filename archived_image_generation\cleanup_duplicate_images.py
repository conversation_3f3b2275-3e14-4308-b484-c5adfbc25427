#!/usr/bin/env python3
"""
Cleanup duplicate image generation entries
Keeps only the most recent completed entry per product
"""

import os
from dotenv import load_dotenv
from supabase import create_client

# Load environment variables
load_dotenv()

def cleanup_duplicates():
    """Remove duplicate image generation entries"""
    
    # Initialize Supabase client
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("Error: Missing Supabase credentials")
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("Analyzing duplicate entries...")
    
    # Get all products with multiple queue entries
    query = """
    SELECT product_id, COUNT(*) as count
    FROM image_generation_queue
    WHERE product_id IS NOT NULL
    GROUP BY product_id
    HAVING COUNT(*) > 1
    ORDER BY count DESC
    """
    
    try:
        # Get products with duplicates
        response = supabase.table('image_generation_queue').select('product_id').execute()
        
        # Count duplicates per product
        product_counts = {}
        for item in response.data:
            pid = item['product_id']
            if pid:
                product_counts[pid] = product_counts.get(pid, 0) + 1
        
        # Find products with duplicates
        duplicate_products = [pid for pid, count in product_counts.items() if count > 1]
        
        print(f"Found {len(duplicate_products)} products with duplicate entries")
        
        total_deleted = 0
        
        for product_id in duplicate_products:
            # Get all entries for this product
            entries = supabase.table('image_generation_queue').select(
                'id, status, created_at, completed_at'
            ).eq('product_id', product_id).order('created_at', desc=True).execute()
            
            if not entries.data or len(entries.data) <= 1:
                continue
            
            # Keep the most recent completed entry, or the most recent entry if none completed
            entries_list = entries.data
            keep_entry = None
            
            # First, try to find a completed entry
            for entry in entries_list:
                if entry['status'] == 'completed':
                    keep_entry = entry
                    break
            
            # If no completed entry, keep the most recent one
            if not keep_entry:
                keep_entry = entries_list[0]
            
            # Delete all other entries
            for entry in entries_list:
                if entry['id'] != keep_entry['id']:
                    supabase.table('image_generation_queue').delete().eq('id', entry['id']).execute()
                    total_deleted += 1
            
            if total_deleted % 100 == 0:
                print(f"Deleted {total_deleted} duplicate entries...")
        
        print(f"\nCleanup complete!")
        print(f"Total duplicate entries removed: {total_deleted}")
        
        # Show final stats
        stats_response = supabase.table('image_generation_queue').select('status').execute()
        status_counts = {}
        for item in stats_response.data:
            status = item['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print("\nFinal queue status:")
        for status, count in status_counts.items():
            print(f"  {status}: {count}")
        
    except Exception as e:
        print(f"Error during cleanup: {e}")

if __name__ == "__main__":
    cleanup_duplicates()