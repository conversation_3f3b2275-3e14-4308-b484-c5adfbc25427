import { pgTable, text, serial, integer, timestamp, varchar, decimal, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { relations } from "drizzle-orm";

// AI Usage tracking table
export const aiUsage = pgTable("ai_usage", {
  id: serial("id").primaryKey(),
  conversationId: varchar("conversation_id", { length: 255 }).notNull(),
  agentId: varchar("agent_id", { length: 100 }).notNull(),
  model: varchar("model", { length: 100 }).notNull(),
  
  // Token counts
  inputTokens: integer("input_tokens").notNull().default(0),
  outputTokens: integer("output_tokens").notNull().default(0),
  totalTokens: integer("total_tokens").notNull().default(0),
  
  // Cost tracking (in USD)
  inputCost: decimal("input_cost", { precision: 10, scale: 6 }).notNull().default('0'),
  outputCost: decimal("output_cost", { precision: 10, scale: 6 }).notNull().default('0'),
  totalCost: decimal("total_cost", { precision: 10, scale: 6 }).notNull().default('0'),
  
  // Request details
  requestType: varchar("request_type", { length: 50 }), // 'conversation', 'one-shot', 'stream'
  userId: varchar("user_id", { length: 255 }), // For future user tracking
  ipAddress: varchar("ip_address", { length: 45 }), // For rate limiting
  userAgent: text("user_agent"),
  
  // Performance metrics
  responseTimeMs: integer("response_time_ms"),
  error: text("error"),
  
  // Metadata
  metadata: jsonb("metadata"), // Store additional context
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertAiUsageSchema = createInsertSchema(aiUsage);

// AI Usage Summary table (for daily/monthly aggregations)
export const aiUsageSummary = pgTable("ai_usage_summary", {
  id: serial("id").primaryKey(),
  date: timestamp("date").notNull(),
  agentId: varchar("agent_id", { length: 100 }).notNull(),
  model: varchar("model", { length: 100 }).notNull(),
  
  // Aggregated counts
  totalRequests: integer("total_requests").notNull().default(0),
  totalInputTokens: integer("total_input_tokens").notNull().default(0),
  totalOutputTokens: integer("total_output_tokens").notNull().default(0),
  totalCost: decimal("total_cost", { precision: 10, scale: 6 }).notNull().default('0'),
  
  // Average metrics
  avgResponseTimeMs: integer("avg_response_time_ms"),
  errorCount: integer("error_count").notNull().default(0),
  
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow().$onUpdate(() => new Date()),
});

// Model pricing configuration
export const aiModelPricing = pgTable("ai_model_pricing", {
  id: serial("id").primaryKey(),
  model: varchar("model", { length: 100 }).notNull().unique(),
  inputPricePerMillion: decimal("input_price_per_million", { precision: 10, scale: 6 }).notNull(),
  outputPricePerMillion: decimal("output_price_per_million", { precision: 10, scale: 6 }).notNull(),
  contextWindow: integer("context_window").notNull(),
  isActive: boolean("is_active").notNull().default(true),
  effectiveDate: timestamp("effective_date").notNull().defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
});

// Relations
export const aiUsageRelations = relations(aiUsage, ({ one }) => ({
  // Future: relate to users table
}));

export const aiUsageSummaryRelations = relations(aiUsageSummary, ({ one }) => ({
  // Future relations
}));