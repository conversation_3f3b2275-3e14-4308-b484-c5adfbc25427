#!/usr/bin/env python3
"""
Merge and consolidate companies from AI agents with manually created ones
Handles duplicates and ensures all products are properly linked
"""

import os
import sys
import re
from typing import Dict, List, Optional
from supabase import create_client
import asyncio
from datetime import datetime

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

def normalize_company_name(name: str) -> str:
    """Normalize company name for comparison"""
    # Convert to lowercase
    normalized = name.lower()
    
    # Remove common suffixes
    suffixes = [
        ' inc', ' incorporated', ' corp', ' corporation', 
        ' llc', ' ltd', ' limited', ' co', ' company',
        ' gmbh', ' ag', ' sa', ' plc', ' pty'
    ]
    
    for suffix in suffixes:
        if normalized.endswith(suffix):
            normalized = normalized[:-len(suffix)]
    
    # Remove extra spaces and punctuation
    normalized = re.sub(r'[^\w\s]', '', normalized)
    normalized = ' '.join(normalized.split())
    
    return normalized

async def find_duplicate_companies(supabase):
    """Find potential duplicate companies"""
    print("\n🔍 Finding duplicate companies...")
    
    # Get all companies
    companies = supabase.table('hemp_companies').select('*').execute()
    
    if not companies.data:
        print("No companies found")
        return []
    
    # Group by normalized name
    company_groups = {}
    for company in companies.data:
        normalized = normalize_company_name(company['name'])
        if normalized not in company_groups:
            company_groups[normalized] = []
        company_groups[normalized].append(company)
    
    # Find duplicates
    duplicates = []
    for normalized_name, group in company_groups.items():
        if len(group) > 1:
            # Sort by: verified first, then by created_at
            group.sort(key=lambda x: (not x.get('verified', False), x.get('created_at', '')))
            duplicates.append({
                'normalized_name': normalized_name,
                'companies': group,
                'primary': group[0],  # The one to keep
                'duplicates': group[1:]  # The ones to merge
            })
    
    return duplicates

async def merge_companies(supabase, primary_id: int, duplicate_ids: List[int]):
    """Merge duplicate companies into primary"""
    merged_count = 0
    
    for dup_id in duplicate_ids:
        try:
            # Get all product relationships for duplicate
            relationships = supabase.table('hemp_company_products')\
                .select('*')\
                .eq('company_id', dup_id)\
                .execute()
            
            # Transfer relationships to primary company
            for rel in relationships.data:
                # Check if relationship already exists
                existing = supabase.table('hemp_company_products')\
                    .select('id')\
                    .eq('product_id', rel['product_id'])\
                    .eq('company_id', primary_id)\
                    .execute()
                
                if not existing.data:
                    # Create new relationship
                    new_rel = {
                        'product_id': rel['product_id'],
                        'company_id': primary_id,
                        'relationship_type': rel.get('relationship_type', 'manufacturer'),
                        'is_primary': rel.get('is_primary', False),
                        'verified': rel.get('verified', False),
                        'notes': f"Merged from company ID {dup_id}"
                    }
                    supabase.table('hemp_company_products').insert(new_rel).execute()
            
            # Update any products that have this as primary_company_id
            supabase.table('uses_products')\
                .update({'primary_company_id': primary_id})\
                .eq('primary_company_id', dup_id)\
                .execute()
            
            # Delete relationships from duplicate
            supabase.table('hemp_company_products')\
                .delete()\
                .eq('company_id', dup_id)\
                .execute()
            
            # Delete duplicate company
            supabase.table('hemp_companies')\
                .delete()\
                .eq('id', dup_id)\
                .execute()
            
            merged_count += 1
            print(f"   ✅ Merged company ID {dup_id} into {primary_id}")
            
        except Exception as e:
            print(f"   ❌ Error merging company {dup_id}: {e}")
    
    return merged_count

async def update_ai_discovered_companies(supabase):
    """Update AI-discovered companies with better information"""
    print("\n🤖 Updating AI-discovered companies...")
    
    # Get unverified companies (likely AI-created)
    unverified = supabase.table('hemp_companies')\
        .select('*')\
        .eq('verified', False)\
        .execute()
    
    updated_count = 0
    
    for company in unverified.data:
        updates = {}
        
        # Try to enhance description
        if not company.get('description') or 'discovered by AI' in company.get('description', ''):
            # Get products for this company
            products = supabase.table('hemp_company_products')\
                .select('*, uses_products!inner(name)')\
                .eq('company_id', company['id'])\
                .execute()
            
            if products.data:
                product_names = [p['uses_products']['name'] for p in products.data[:3]]
                product_list = ', '.join(product_names)
                if len(products.data) > 3:
                    product_list += f' and {len(products.data) - 3} more'
                
                updates['description'] = f"{company['name']} manufactures hemp products including {product_list}"
        
        # Try to guess company type based on name
        if not company.get('company_type') or company.get('company_type') == 'manufacturer':
            name_lower = company['name'].lower()
            if any(term in name_lower for term in ['foods', 'nutrition', 'harvest']):
                updates['company_type'] = 'food_manufacturer'
            elif any(term in name_lower for term in ['textiles', 'clothing', 'apparel']):
                updates['company_type'] = 'textile_manufacturer'
            elif any(term in name_lower for term in ['build', 'construction', 'materials']):
                updates['company_type'] = 'construction_manufacturer'
        
        if updates:
            try:
                supabase.table('hemp_companies')\
                    .update(updates)\
                    .eq('id', company['id'])\
                    .execute()
                updated_count += 1
            except Exception as e:
                print(f"   ❌ Error updating company {company['name']}: {e}")
    
    return updated_count

async def run_enhanced_agent():
    """Run the enhanced research agent to discover more products with companies"""
    print("\n🤖 Running Enhanced Research Agent...")
    
    try:
        from agents.research.enhanced_research_agent import EnhancedHempResearchAgent
        
        # Initialize Supabase
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_ANON_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ Missing Supabase credentials!")
            return
        
        from supabase import create_client, Client
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # Create agent
        agent = EnhancedHempResearchAgent(supabase)
        
        # Run discovery
        result = await agent.discover_products_with_companies({
            'limit': 20,
            'categories': ['all']
        })
        
        if result['success']:
            print(f"✅ Discovered {result['products_discovered']} products")
            print(f"✅ Created {result.get('companies_discovered', 0)} companies")
        else:
            print(f"❌ Discovery failed: {result.get('error', 'Unknown error')}")
            
    except ImportError:
        print("⚠️  Enhanced agent not available, skipping...")
    except Exception as e:
        print(f"❌ Error running agent: {e}")

def main():
    """Main function to merge and consolidate companies"""
    # Initialize Supabase
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials!")
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("🏢 Company Consolidation and Merge Tool")
    print("=" * 60)
    
    # Run async operations
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # First, run the enhanced agent if available
        if '--run-agent' in sys.argv:
            loop.run_until_complete(run_enhanced_agent())
        
        # Find duplicates
        duplicates = loop.run_until_complete(find_duplicate_companies(supabase))
        
        if duplicates:
            print(f"\nFound {len(duplicates)} groups of duplicate companies:")
            
            for dup_group in duplicates:
                print(f"\n📦 Group: {dup_group['normalized_name']}")
                primary = dup_group['primary']
                print(f"   Primary: {primary['name']} (ID: {primary['id']}, Verified: {primary.get('verified', False)})")
                
                for dup in dup_group['duplicates']:
                    print(f"   Duplicate: {dup['name']} (ID: {dup['id']})")
            
            # Ask for confirmation
            if '--auto' in sys.argv or input("\nMerge all duplicates? (y/n): ").lower() == 'y':
                total_merged = 0
                for dup_group in duplicates:
                    primary_id = dup_group['primary']['id']
                    duplicate_ids = [d['id'] for d in dup_group['duplicates']]
                    
                    merged = loop.run_until_complete(
                        merge_companies(supabase, primary_id, duplicate_ids)
                    )
                    total_merged += merged
                
                print(f"\n✅ Merged {total_merged} duplicate companies")
        else:
            print("\n✅ No duplicate companies found")
        
        # Update AI-discovered companies
        updated = loop.run_until_complete(update_ai_discovered_companies(supabase))
        if updated:
            print(f"\n✅ Enhanced {updated} AI-discovered companies")
        
        # Show final stats
        print("\n📊 Final Company Statistics:")
        
        # Total companies
        total = supabase.table('hemp_companies').select('count', count='exact').execute()
        print(f"   Total companies: {total.count}")
        
        # Verified vs unverified
        verified = supabase.table('hemp_companies')\
            .select('count', count='exact')\
            .eq('verified', True)\
            .execute()
        print(f"   Verified: {verified.count}")
        print(f"   Unverified: {total.count - verified.count}")
        
        # Products with companies
        with_companies = supabase.table('products_with_companies')\
            .select('count', count='exact')\
            .gt('company_count', 0)\
            .execute()
        print(f"   Products with companies: {with_companies.count}")
        
    finally:
        loop.close()

if __name__ == "__main__":
    main()