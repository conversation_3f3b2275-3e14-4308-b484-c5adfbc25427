import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY environment variable is not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Custom placeholder image URL
const CUSTOM_PLACEHOLDER_URL = '/images/unknown-hemp-image.png';

async function updateRemainingPlaceholders() {
  try {
    console.log('Fetching products with any placeholder images...');
    
    // Get all products that have "placeholder" in their image URL
    const { data: products, error: fetchError } = await supabase
      .from('uses_products')
      .select('id, name, image_url')
      .like('image_url', '%placeholder%');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products with placeholder images`);
    
    if (products.length === 0) {
      console.log('No products with placeholder images found!');
      return;
    }
    
    // Update each product
    let updateCount = 0;
    let errorCount = 0;
    
    for (const product of products) {
      const { error: updateError } = await supabase
        .from('uses_products')
        .update({ image_url: CUSTOM_PLACEHOLDER_URL })
        .eq('id', product.id);
      
      if (updateError) {
        console.error(`Error updating product ${product.id} (${product.name}):`, updateError);
        errorCount++;
      } else {
        console.log(`✓ Updated product ${product.id}: ${product.name} (was: ${product.image_url})`);
        updateCount++;
      }
    }
    
    console.log('\n=== Update Summary ===');
    console.log(`Total products found: ${products.length}`);
    console.log(`Successfully updated: ${updateCount}`);
    console.log(`Errors: ${errorCount}`);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the update
updateRemainingPlaceholders();