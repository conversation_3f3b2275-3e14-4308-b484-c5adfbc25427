import { useEffect, useRef, useState } from 'react';
import { HempCompany } from '@/hooks/use-companies';

interface HempCompaniesGlobeProps {
  companies: HempCompany[];
  onCompanyClick?: (company: HempCompany) => void;
}

export function HempCompaniesGlobe({ companies, onCompanyClick }: HempCompaniesGlobeProps) {
  const globeEl = useRef<HTMLDivElement>(null);
  const [selectedCompany, setSelectedCompany] = useState<HempCompany | null>(null);
  const globeRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!globeEl.current) return;

    let globe: any;
    let mounted = true;

    // Dynamically import Globe.gl to avoid SSR issues
    import('globe.gl')
      .then((GlobeModule) => {
        if (!mounted || !globeEl.current) return;
        
        const Globe = GlobeModule.default;
        
        // Initialize Globe
        globe = Globe(globeEl.current)
          .globeImageUrl('//unpkg.com/three-globe/example/img/earth-dark.jpg')
          .bumpImageUrl('//unpkg.com/three-globe/example/img/earth-topology.png')
          .backgroundImageUrl('//unpkg.com/three-globe/example/img/night-sky.png')
          .backgroundColor('rgba(0,0,0,0)')
          .width(globeEl.current.offsetWidth)
          .height(600)
          .pointAltitude(0.01)
          .pointRadius(0.5)
          .pointColor((d: any) => {
            const company = d as HempCompany;
            // Color based on company type
            switch (company.company_type) {
              case 'manufacturer': return '#3B82F6'; // Blue
              case 'distributor': return '#10B981'; // Green
              case 'retailer': return '#8B5CF6'; // Purple
              case 'brand': return '#F97316'; // Orange
              default: return '#FFD700'; // Gold
            }
          })
          .pointLabel((d: any) => {
            const company = d as HempCompany;
            return `
              <div style="
                background: rgba(0, 0, 0, 0.9);
                border: 1px solid #10B981;
                border-radius: 4px;
                padding: 8px;
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                max-width: 200px;
              ">
                <div style="font-weight: bold; color: #10B981; margin-bottom: 4px;">
                  ${company.name}
                </div>
                ${company.city ? `<div style="font-size: 12px; color: #9CA3AF;">${company.city}, ${company.country}</div>` : ''}
                ${company.company_type ? `<div style="font-size: 11px; color: #6B7280; margin-top: 4px;">${company.company_type}</div>` : ''}
                ${company.product_count ? `<div style="font-size: 11px; color: #6B7280;">${company.product_count} products</div>` : ''}
              </div>
            `;
          })
          .onPointClick((point: any) => {
            const company = point as HempCompany;
            setSelectedCompany(company);
            if (onCompanyClick) {
              onCompanyClick(company);
            }
          })
          .onPointHover((point: any) => {
            globeEl.current!.style.cursor = point ? 'pointer' : 'grab';
          });

        // Add data
        const companiesWithLocation = companies.filter(c => c.latitude && c.longitude);
        globe.pointsData(companiesWithLocation.map(company => ({
          ...company,
          lat: company.latitude,
          lng: company.longitude,
        })));

        // Add glow effect to points
        globe.pointsMerge(true);
        globe.pointResolution(12);

        // Set initial view after a short delay to ensure globe is ready
        setTimeout(() => {
          if (globe.controls && typeof globe.controls === 'function') {
            const controls = globe.controls();
            if (controls) {
              controls.autoRotate = true;
              controls.autoRotateSpeed = 0.5;
              controls.enableZoom = true;
            }
          }
        }, 100);

        globeRef.current = globe;
        setIsLoading(false);
      })
      .catch((err) => {
        console.error('Failed to load Globe.gl:', err);
        setError('Failed to load 3D globe visualization');
        setIsLoading(false);
      });

    // Handle resize
    const handleResize = () => {
      if (globeEl.current && globeRef.current) {
        globeRef.current
          .width(globeEl.current.offsetWidth)
          .height(600);
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      mounted = false;
      window.removeEventListener('resize', handleResize);
      if (globeRef.current && globeRef.current._destructor) {
        globeRef.current._destructor();
      }
    };
  }, [companies, onCompanyClick]);

  if (error) {
    return (
      <div className="relative w-full h-[600px] flex items-center justify-center bg-black/40 rounded-lg">
        <div className="text-center">
          <p className="text-red-400 mb-2">{error}</p>
          <p className="text-gray-400 text-sm">Please try refreshing the page</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center h-[600px] bg-black/40 rounded-lg z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading 3D globe...</p>
          </div>
        </div>
      )}
      
      <div ref={globeEl} className="w-full" style={{ height: '600px' }} />
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-black/80 backdrop-blur-sm rounded-lg p-4 border border-gray-700">
        <h4 className="text-sm font-semibold text-gray-100 mb-2">Company Types</h4>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-xs text-gray-300">Manufacturer</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-xs text-gray-300">Distributor</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-purple-500"></div>
            <span className="text-xs text-gray-300">Retailer</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
            <span className="text-xs text-gray-300">Brand</span>
          </div>
        </div>
      </div>

      {/* Controls hint */}
      <div className="absolute top-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-700">
        <p className="text-xs text-gray-400">Click and drag to rotate • Scroll to zoom • Click markers for details</p>
      </div>

      {/* Selected company details */}
      {selectedCompany && (
        <div className="absolute top-4 left-4 bg-black/90 backdrop-blur-sm rounded-lg p-4 border border-green-400/50 max-w-sm">
          <button
            onClick={() => setSelectedCompany(null)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-200"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <h3 className="text-lg font-semibold text-green-400 mb-2">{selectedCompany.name}</h3>
          {selectedCompany.description && (
            <p className="text-sm text-gray-300 mb-2">{selectedCompany.description}</p>
          )}
          <div className="space-y-1 text-xs text-gray-400">
            {selectedCompany.city && (
              <p>{selectedCompany.city}, {selectedCompany.state_province || ''} {selectedCompany.country}</p>
            )}
            {selectedCompany.website && (
              <a
                href={selectedCompany.website.startsWith('http') ? selectedCompany.website : `https://${selectedCompany.website}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-green-400 hover:text-green-300"
              >
                {selectedCompany.website}
              </a>
            )}
          </div>
        </div>
      )}
    </div>
  );
}