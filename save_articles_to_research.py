#!/usr/bin/env python3
"""
Save discovered hemp articles to research_entries table in Supabase
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional
import re

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ResearchEntrySaver:
    """Saves hemp articles to research_entries table"""
    
    def __init__(self, supabase_client):
        self.supabase = supabase_client
        self.plant_type_cache = {}
        self.plant_part_cache = {}
        self.industry_cache = {}
        
    async def save_articles_as_research(self, articles: List[Dict]) -> Dict:
        """Save articles to research_entries table"""
        saved_count = 0
        failed_count = 0
        results = []
        
        logger.info(f"Processing {len(articles)} articles for research database...")
        
        for article in articles:
            try:
                # Convert article to research entry format
                research_entry = await self._convert_to_research_entry(article)
                
                # Check if already exists (by title and source)
                existing = self.supabase.table('research_entries').select('id').eq(
                    'title', research_entry['title']
                ).execute()
                
                if not existing.data:
                    # Save to database
                    result = self.supabase.table('research_entries').insert(research_entry).execute()
                    
                    if result.data:
                        saved_count += 1
                        entry_id = result.data[0]['id']
                        logger.info(f"✅ Saved: {research_entry['title'][:60]}... (ID: {entry_id})")
                        
                        # Link to products if applicable
                        await self._link_to_products(entry_id, article)
                        
                        results.append({
                            'id': entry_id,
                            'title': research_entry['title'],
                            'status': 'saved'
                        })
                    else:
                        failed_count += 1
                        logger.error(f"❌ Failed to save: {research_entry['title'][:60]}...")
                else:
                    logger.info(f"⏭️  Already exists: {research_entry['title'][:60]}...")
                    results.append({
                        'id': existing.data[0]['id'],
                        'title': research_entry['title'],
                        'status': 'exists'
                    })
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"❌ Error processing article: {e}")
                logger.error(f"   Article: {article.get('title', 'Unknown')}")
                
        return {
            'total': len(articles),
            'saved': saved_count,
            'failed': failed_count,
            'results': results
        }
    
    async def _convert_to_research_entry(self, article: Dict) -> Dict:
        """Convert article data to research_entries table format"""
        
        # Determine entry type based on content
        entry_type = self._determine_entry_type(article)
        
        # Extract publication info
        publication_date = self._parse_date(article.get('published', ''))
        
        # Get foreign key IDs
        plant_type_id = await self._get_plant_type_id(article)
        plant_part_id = await self._get_plant_part_id(article.get('plant_part'))
        industry_id = await self._get_industry_id(article.get('industry'))
        
        # Extract authors/sources
        authors = self._extract_authors(article)
        
        # Build research entry
        research_entry = {
            'title': article.get('title', '').strip()[:500],  # Limit title length
            'authors_or_assignees': authors,
            'abstract_summary': article.get('description', '')[:1000],  # Use description as abstract
            'publication_or_filing_date': publication_date,
            'journal_or_office': article.get('source', 'Unknown Source'),
            'doi_or_patent_number': None,  # Articles typically don't have DOIs
            'full_text_url': article.get('url', ''),
            'pdf_url': None,
            'entry_type': entry_type,
            'key_findings': self._extract_key_findings(article),
            'methodology': None,  # Not typically available for news articles
            'plant_type_id': plant_type_id,
            'plant_part_id': plant_part_id,
            'industry_id': industry_id,
            'research_institution_id': None,  # Could be enhanced later
            'keywords': self._extract_keywords(article),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        return research_entry
    
    def _determine_entry_type(self, article: Dict) -> str:
        """Determine the type of research entry"""
        text = f"{article.get('title', '')} {article.get('description', '')}".lower()
        
        if 'study' in text or 'research' in text:
            return 'Paper'
        elif 'patent' in text:
            return 'Patent'
        elif 'report' in text:
            return 'Report'
        elif 'trial' in text:
            return 'Clinical Trial'
        else:
            return 'Article'  # Default for news articles
    
    def _parse_date(self, date_str: str) -> Optional[str]:
        """Parse various date formats to ISO format"""
        if not date_str:
            return None
            
        try:
            # Try parsing RSS date format
            from email.utils import parsedate_to_datetime
            dt = parsedate_to_datetime(date_str)
            return dt.date().isoformat()
        except:
            try:
                # Try parsing ISO format
                dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                return dt.date().isoformat()
            except:
                # Return current date as fallback
                return datetime.now().date().isoformat()
    
    def _extract_authors(self, article: Dict) -> List[str]:
        """Extract authors or source attribution"""
        authors = []
        
        # Check for explicit author field
        if article.get('author'):
            authors.append(article['author'])
        
        # Use source as author if no explicit author
        if not authors and article.get('source'):
            authors.append(f"{article['source']} Editorial")
            
        # Extract from text if contains "by"
        text = article.get('title', '') + ' ' + article.get('description', '')
        by_pattern = r'[Bb]y\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        matches = re.findall(by_pattern, text)
        authors.extend(matches[:2])  # Max 2 authors from text
        
        return list(set(authors)) if authors else ['Unknown Author']
    
    def _extract_key_findings(self, article: Dict) -> List[str]:
        """Extract key findings from article"""
        findings = []
        text = article.get('description', '')
        
        if not text:
            return None
            
        # Split into sentences
        sentences = text.split('. ')
        
        # Take first 3 sentences as key findings
        for sentence in sentences[:3]:
            if len(sentence) > 20:  # Meaningful sentence
                findings.append(sentence.strip() + '.')
                
        return findings if findings else None
    
    def _extract_keywords(self, article: Dict) -> List[str]:
        """Extract keywords from article"""
        keywords = []
        text = f"{article.get('title', '')} {article.get('description', '')}".lower()
        
        # Hemp-related keywords
        hemp_keywords = [
            'hemp', 'fiber', 'textile', 'seed', 'oil', 'cbd', 'cannabinoid',
            'construction', 'plastic', 'composite', 'sustainable', 'biomass',
            'protein', 'nutrition', 'building', 'material'
        ]
        
        # Add matching keywords
        for keyword in hemp_keywords:
            if keyword in text:
                keywords.append(keyword)
                
        # Add plant part and industry
        if article.get('plant_part'):
            keywords.append(article['plant_part'].lower())
        if article.get('industry'):
            keywords.append(article['industry'].lower())
            
        return list(set(keywords))[:10]  # Max 10 keywords
    
    async def _get_plant_type_id(self, article: Dict) -> Optional[int]:
        """Get plant type ID based on article content"""
        # For now, default to a general hemp type
        # Could be enhanced to detect specific types
        
        if 'fiber' not in self.plant_type_cache:
            result = self.supabase.table('hemp_plant_archetypes').select('id').eq(
                'name', 'Fiber Hemp'
            ).execute()
            
            if result.data:
                self.plant_type_cache['fiber'] = result.data[0]['id']
            else:
                # Try to find any hemp type
                result = self.supabase.table('hemp_plant_archetypes').select('id').limit(1).execute()
                if result.data:
                    self.plant_type_cache['fiber'] = result.data[0]['id']
                    
        return self.plant_type_cache.get('fiber')
    
    async def _get_plant_part_id(self, plant_part: Optional[str]) -> Optional[int]:
        """Get plant part ID from name"""
        if not plant_part:
            return None
            
        if plant_part not in self.plant_part_cache:
            # Map to database names
            part_map = {
                'fiber': 'Fiber',
                'seeds': 'Seeds',
                'oil': 'Oil',
                'flower': 'Flower',
                'hurds': 'Hurds',
                'biomass': 'Whole Plant'
            }
            
            db_name = part_map.get(plant_part.lower(), plant_part)
            
            result = self.supabase.table('plant_parts').select('id').eq(
                'name', db_name
            ).execute()
            
            if result.data:
                self.plant_part_cache[plant_part] = result.data[0]['id']
                
        return self.plant_part_cache.get(plant_part)
    
    async def _get_industry_id(self, industry: Optional[str]) -> Optional[int]:
        """Get industry ID from name"""
        if not industry:
            return None
            
        if industry not in self.industry_cache:
            result = self.supabase.table('industries').select('id').eq(
                'name', industry
            ).execute()
            
            if result.data:
                self.industry_cache[industry] = result.data[0]['id']
                
        return self.industry_cache.get(industry)
    
    async def _link_to_products(self, research_entry_id: int, article: Dict):
        """Link research entry to related products"""
        # This could be enhanced to actually find related products
        # For now, it's a placeholder for future implementation
        pass


async def main():
    """Demo: Save scraped articles to research database"""
    logger.info("Hemp Research Entry Saver Demo")
    logger.info("=" * 50)
    
    # Import dependencies
    from lib.supabase_client import get_supabase_client
    from demo_web_scraping import HempProductScraper
    
    try:
        # Get Supabase client
        logger.info("\nConnecting to Supabase...")
        supabase = get_supabase_client()
        logger.info("✅ Connected to Supabase")
        
        # Create saver
        saver = ResearchEntrySaver(supabase)
        
        # Get some articles to save (using our scraper)
        logger.info("\nFetching hemp articles...")
        scraper = HempProductScraper()
        articles = await scraper.discover_products("hemp research", limit=5)
        
        if articles:
            logger.info(f"✅ Found {len(articles)} articles to process")
            
            # Save to research_entries
            logger.info("\nSaving to research_entries table...")
            results = await saver.save_articles_as_research(articles)
            
            # Display results
            logger.info("\n" + "=" * 50)
            logger.info("RESULTS:")
            logger.info(f"Total articles: {results['total']}")
            logger.info(f"Successfully saved: {results['saved']}")
            logger.info(f"Failed: {results['failed']}")
            logger.info(f"Already existed: {len([r for r in results['results'] if r['status'] == 'exists'])}")
            
        else:
            logger.info("❌ No articles found to save")
            
    except Exception as e:
        logger.error(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    logger.info("Starting research entry saver...")
    asyncio.run(main())