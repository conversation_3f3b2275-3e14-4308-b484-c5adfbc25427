# Using DeepSeek with Hemp CLI

DeepSeek is now integrated as the default AI provider for the Hemp research agent. It's a cost-effective alternative to OpenAI with good performance.

## Setup

1. **Get a DeepSeek API Key**:
   - Sign up at https://platform.deepseek.com/
   - Go to API Keys section
   - Create a new API key

2. **Add to .env file**:
   ```bash
   DEEPSEEK_API_KEY=sk-your-actual-deepseek-api-key-here
   ```

3. **Test DeepSeek**:
   ```powershell
   .venv\Scripts\python.exe test_deepseek.py
   ```

## Using with Hemp CLI

DeepSeek is now the default AI provider. Just run commands normally:

```powershell
# Research with DeepSeek (default)
.venv\Scripts\python.exe hemp_cli.py agent research "hemp construction materials"

# Explicitly use DeepSeek
.venv\Scripts\python.exe hemp_cli.py agent research "hemp textiles" --ai-provider deepseek

# Use OpenAI instead (if you have quota)
.venv\Scripts\python.exe hemp_cli.py agent research "hemp products" --ai-provider openai

# Run without AI (only web scraping and feeds)
.venv\Scripts\python.exe hemp_cli.py agent research "hemp news" --features web feed
```

## Features

- **Cost-effective**: DeepSeek is much cheaper than GPT-4
- **Good performance**: Works well for product discovery and analysis
- **Automatic fallback**: Falls back to OpenAI if DeepSeek fails
- **No AI mode**: Can run with just web scraping if no API keys work

## Pricing Comparison

- **DeepSeek**: $0.14 per 1M input tokens, $0.28 per 1M output tokens
- **GPT-4o-mini**: $0.15 per 1M input tokens, $0.60 per 1M output tokens
- **GPT-4o**: $5.00 per 1M input tokens, $15.00 per 1M output tokens

DeepSeek is ~10x cheaper than GPT-4o and comparable to GPT-4o-mini!

## Troubleshooting

If you get API key errors:
1. Make sure your DeepSeek API key is valid
2. Check you have credits in your DeepSeek account
3. Try the test script first: `test_deepseek.py`

If DeepSeek fails, the system will automatically try OpenAI as a fallback (if configured).