import { Router } from 'express';
import { getClaudeService } from '../ai/claude-service';
import { AgentManager } from '../ai/agent-manager';
import { authMiddleware } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = Router();
const agentManager = new AgentManager();

// Rate limiter for AI endpoints (more restrictive)
// In development, use a more lenient rate limit for testing
const isDevelopment = process.env.NODE_ENV === 'development';
const aiRateLimiter = rateLimit({
  windowMs: isDevelopment ? 1 * 60 * 1000 : 15 * 60 * 1000, // 1 minute in dev, 15 minutes in prod
  max: isDevelopment ? 30 : 10, // 30 requests in dev, 10 in prod
  message: 'Too many AI requests, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks and test endpoints
    return req.path === '/health' || req.path === '/test';
  },
});

// Health check endpoint (no rate limit)
router.get('/health', async (req, res) => {
  res.json({ status: 'ok', service: 'Claude AI API' });
});

// Test endpoint for simple queries (no rate limit for testing)
router.post('/test', async (req, res) => {
  try {
    const { message } = req.body;
    if (!message) {
      return res.status(400).json({ error: 'message is required' });
    }

    const claudeService = getClaudeService();
    const response = await claudeService.query('product-discovery', message);
    res.json({ response });
  } catch (error) {
    console.error('Test endpoint error:', error);
    res.status(500).json({ 
      error: 'Failed to process request',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get all available agents
router.get('/agents', async (req, res) => {
  try {
    const claudeService = getClaudeService();
    const agents = claudeService.getAllAgents();
    res.json(agents);
  } catch (error) {
    console.error('Error fetching agents:', error);
    res.status(500).json({ 
      error: 'Failed to fetch agents',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create a new conversation
router.post('/conversations', aiRateLimiter, async (req, res) => {
  try {
    const { agentId } = req.body;
    if (!agentId) {
      return res.status(400).json({ error: 'agentId is required' });
    }

    const claudeService = getClaudeService();
    const conversationId = await claudeService.createConversation(agentId);
    res.json({ conversationId, agentId });
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({ error: 'Failed to create conversation' });
  }
});

// Send a message to a conversation
router.post('/conversations/:conversationId/messages', aiRateLimiter, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { message, stream } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'message is required' });
    }

    const claudeService = getClaudeService();

    if (stream) {
      // Set up SSE for streaming
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');

      const stream = await claudeService.sendMessage(conversationId, message, true);

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          res.write(`data: ${JSON.stringify({ text: chunk.delta.text })}\n\n`);
        }
      }

      res.write('data: [DONE]\n\n');
      res.end();
    } else {
      const response = await claudeService.sendMessage(conversationId, message, false);
      res.json({
        message: response.content[0].text,
        usage: response.usage,
      });
    }
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
});

// Get conversation history
router.get('/conversations/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const claudeService = getClaudeService();
    const conversation = claudeService.getConversation(conversationId);

    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }

    res.json(conversation);
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({ error: 'Failed to fetch conversation' });
  }
});

// Delete a conversation
router.delete('/conversations/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const claudeService = getClaudeService();
    claudeService.deleteConversation(conversationId);
    res.json({ message: 'Conversation deleted' });
  } catch (error) {
    console.error('Error deleting conversation:', error);
    res.status(500).json({ error: 'Failed to delete conversation' });
  }
});

// Product Discovery endpoint
router.post('/discover-products', aiRateLimiter, async (req, res) => {
  try {
    const { query } = req.body;
    if (!query) {
      return res.status(400).json({ error: 'query is required' });
    }

    const result = await agentManager.runProductDiscovery(query);
    res.json(result);
  } catch (error) {
    console.error('Error discovering products:', error);
    res.status(500).json({ error: 'Failed to discover products' });
  }
});

// Code Generation endpoint
router.post('/generate-code', aiRateLimiter, async (req, res) => {
  try {
    const { specification, codeType } = req.body;
    if (!specification || !codeType) {
      return res.status(400).json({ error: 'specification and codeType are required' });
    }

    const code = await agentManager.generateCode(specification, codeType);
    res.json({ code });
  } catch (error) {
    console.error('Error generating code:', error);
    res.status(500).json({ error: 'Failed to generate code' });
  }
});

// Data Analysis endpoint
router.post('/analyze-data', aiRateLimiter, async (req, res) => {
  try {
    const { query, context } = req.body;
    if (!query) {
      return res.status(400).json({ error: 'query is required' });
    }

    const analysis = await agentManager.analyzeData(query, context);
    res.json(analysis);
  } catch (error) {
    console.error('Error analyzing data:', error);
    res.status(500).json({ error: 'Failed to analyze data' });
  }
});

// Content Generation endpoint
router.post('/generate-content', aiRateLimiter, async (req, res) => {
  try {
    const { topic, contentType, keywords } = req.body;
    if (!topic || !contentType) {
      return res.status(400).json({ error: 'topic and contentType are required' });
    }

    const content = await agentManager.generateContent(topic, contentType, keywords);
    res.json({ content });
  } catch (error) {
    console.error('Error generating content:', error);
    res.status(500).json({ error: 'Failed to generate content' });
  }
});

// Multi-agent task endpoint
router.post('/multi-agent-task', aiRateLimiter, async (req, res) => {
  try {
    const { tasks } = req.body;
    if (!tasks || !Array.isArray(tasks)) {
      return res.status(400).json({ error: 'tasks array is required' });
    }

    const results = await agentManager.runMultiAgentTask(tasks);
    res.json({
      results: Object.fromEntries(results),
    });
  } catch (error) {
    console.error('Error running multi-agent task:', error);
    res.status(500).json({ error: 'Failed to run multi-agent task' });
  }
});

export default router;