# Google OAuth Setup Guide

## Why You Don't See the Google Login Button

The Google login button is implemented in the code but won't appear until Google OAuth is properly configured in your Supabase project. Here's how to set it up:

## Step-by-Step Setup

### 1. Google Cloud Console Setup

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **Create a new project** (or select existing)
3. **Enable Google+ API**:
   - Navigate to "APIs & Services" → "Library"
   - Search for "Google+ API"
   - Click "Enable"

4. **Create OAuth 2.0 Credentials**:
   - Go to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "OAuth 2.0 Client IDs"
   - Application type: "Web application"
   - Name: "HempQuarterz Auth"

5. **Add Authorized Redirect URIs**:
   ```
   http://localhost:5173/auth/callback
   https://ktoqznqmlnxrtvubewyz.supabase.co/auth/v1/callback
   ```

6. **Copy your credentials**:
   - Client ID (starts with numbers, ends with `.apps.googleusercontent.com`)
   - Client Secret

### 2. Supabase Configuration

1. **Go to [Supabase Dashboard](https://supabase.com/dashboard)**
2. **Select your project**: `ktoqznqmlnxrtvubewyz`
3. **Navigate to**: Authentication → Providers
4. **Find Google** in the list and click to configure
5. **Enable the Google provider**
6. **Enter your credentials**:
   - Client ID: (from Google Cloud Console)
   - Client Secret: (from Google Cloud Console)
7. **Save the configuration**

### 3. Test the Setup

1. **Visit `/enhanced-login`** in your app
2. **Use the OAuth Test component** that's now on the page
3. **Click "Test Google OAuth Configuration"**
4. **If configured correctly**, you'll see the Google login button

## What Happens After Setup

Once Google OAuth is configured:

✅ **Google login button will appear** on `/enhanced-login`
✅ **Users can sign in with one click**
✅ **Automatic account creation** for new Google users
✅ **Profile data synchronization** (name, email, avatar)
✅ **Secure authentication** via Google's OAuth 2.0

## Troubleshooting

### "Provider not found" Error
- Google OAuth is not enabled in Supabase
- Check Authentication → Providers in Supabase dashboard

### "Invalid redirect URI" Error
- Redirect URI in Google Cloud Console doesn't match
- Make sure you added: `https://ktoqznqmlnxrtvubewyz.supabase.co/auth/v1/callback`

### Button Still Not Showing
- Clear browser cache
- Check browser console for errors
- Verify Supabase configuration is saved

## Alternative: Test Without Google OAuth

If you don't want to set up Google OAuth right now, you can:

1. **Use the demo credentials**:
   - Admin: `<EMAIL>` / `admin123`
   - User: `<EMAIL>` / `user123`

2. **Create accounts manually** using email/password

3. **Set up Google OAuth later** when ready for production

## Security Notes

- **Never commit** Google Client Secret to version control
- **Use environment variables** for production
- **Regularly rotate** OAuth credentials
- **Monitor** authentication logs in both Google and Supabase

## Production Considerations

For production deployment:

1. **Add production domain** to Google Cloud Console redirect URIs
2. **Update Supabase site URL** in project settings
3. **Configure custom domain** if using one
4. **Set up proper error handling** for OAuth failures
5. **Implement user consent** and privacy policy links

---

**Note**: The OAuth test component on the login page is temporary for testing. Remove it once Google OAuth is working properly.
