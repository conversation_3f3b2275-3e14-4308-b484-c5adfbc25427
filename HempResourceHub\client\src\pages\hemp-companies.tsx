import { useState, useMemo } from 'react';
import {
  Building2,
  Globe,
  Grid,
  Search,
  MapPin,
  Calendar,
  Package,
  CheckCircle,
  Eye,
  TrendingUp,
  Users,
  Factory,
  ExternalLink,
  ArrowRight,
  Star,
  Award,
  Filter
} from 'lucide-react';
import { useCompanies, HempCompany } from '@/hooks/use-companies';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HempCompaniesLeafletMap } from '@/components/hemp-companies-leaflet-map';
import { CompanyDetailModal } from '@/components/company-detail-modal';
import { EnhancedBreadcrumbs } from '@/components/ui/enhanced-breadcrumbs';
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function HempCompanies() {
  const { data: companies = [], isLoading } = useCompanies();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [countryFilter, setCountryFilter] = useState('all');
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [view, setView] = useState<'list' | 'grid'>('grid');
  const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('list');

  // Generate alphabet array for filtering
  const alphabet = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));

  // Filter and paginate companies
  const { filteredCompanies, totalPages, paginatedCompanies } = useMemo(() => {
    if (!companies) return { filteredCompanies: [], totalPages: 0, paginatedCompanies: [] };

    let filtered = companies.filter(company => {
      const matchesSearch = company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = typeFilter === 'all' || company.company_type === typeFilter;
      const matchesCountry = countryFilter === 'all' || company.country === countryFilter;
      const matchesLetter = !selectedLetter || company.name.charAt(0).toUpperCase() === selectedLetter;
      return matchesSearch && matchesType && matchesCountry && matchesLetter;
    });

    // Sort alphabetically
    filtered.sort((a, b) => a.name.localeCompare(b.name));

    const totalPages = Math.ceil(filtered.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedCompanies = filtered.slice(startIndex, startIndex + itemsPerPage);

    return { filteredCompanies: filtered, totalPages, paginatedCompanies };
  }, [companies, searchTerm, typeFilter, countryFilter, selectedLetter, currentPage, itemsPerPage]);

  // Get unique countries and types for filters
  const countries = [...new Set(companies.map(c => c.country).filter(Boolean))].sort();
  const companyTypes = [...new Set(companies.map(c => c.company_type).filter(Boolean))].sort();

  // Calculate statistics
  const totalCompanies = companies.length;
  const verifiedCompanies = companies.filter(c => c.verified).length;
  const totalCountries = countries.length;
  const totalProducts = companies.reduce((sum, company) => sum + (company.product_count || 0), 0);

  // Reset page when filters change
  const handleFilterChange = (filterType: string, value: any) => {
    setCurrentPage(1);
    if (filterType === 'search') setSearchTerm(value);
    if (filterType === 'type') setTypeFilter(value);
    if (filterType === 'country') setCountryFilter(value);
    if (filterType === 'letter') setSelectedLetter(value);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  const getTypeColor = (type?: string) => {
    switch (type) {
      case 'manufacturer': return 'bg-blue-100 text-blue-800';
      case 'distributor': return 'bg-green-100 text-green-800';
      case 'retailer': return 'bg-purple-100 text-purple-800';
      case 'brand': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCompanyClick = (company: HempCompany) => {
    setSelectedCompanyId(company.id);
    setModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-blue-900/20">
      <div className="container mx-auto px-4 py-8">
        {/* Enhanced Breadcrumbs */}
        <div className="mb-6">
          <EnhancedBreadcrumbs
            showHome={true}
            showContext={true}
          />
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-blue-500/10 border border-blue-500/20 rounded-full px-4 py-2 mb-6">
            <Building2 className="h-4 w-4 text-blue-400" />
            <span className="text-sm text-blue-400 font-medium">Company Directory</span>
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            <span className="hemp-brand-secondary">Hemp</span> Companies
            <span className="block text-blue-400 drop-shadow-[0_0_8px_rgba(59,130,246,0.3)]">
              Directory
            </span>
          </h1>

          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Discover the global network of hemp companies, from innovative manufacturers
            to cutting-edge retailers shaping the future of sustainable business.
          </p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-blue-500/30 transition-colors">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">{totalCompanies}</div>
              <div className="text-sm text-gray-400">Total Companies</div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-green-500/30 transition-colors">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">{verifiedCompanies}</div>
              <div className="text-sm text-gray-400">Verified Companies</div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-purple-500/30 transition-colors">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">{totalCountries}</div>
              <div className="text-sm text-gray-400">Countries</div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-orange-500/30 transition-colors">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-orange-400 mb-2">{totalProducts}</div>
              <div className="text-sm text-gray-400">Total Products</div>
            </CardContent>
          </Card>
        </div>

        {/* Company Types */}
        {companyTypes.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Factory className="h-5 w-5 text-blue-400" />
              Company Types
            </h3>
            <div className="flex flex-wrap gap-2">
              {companyTypes.map((type) => {
                const count = companies.filter(c => c.company_type === type).length;
                return (
                  <Badge
                    key={type}
                    variant="outline"
                    className="bg-gray-900/40 text-gray-300 border-gray-700/50 hover:border-blue-500/50 transition-colors"
                  >
                    {type} ({count})
                  </Badge>
                );
              })}
            </div>
          </div>
        )}

        {/* Search Bar - Full Width */}
        <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 mb-6 border border-green-500/30">
          <div className="space-y-4">
            {/* Search Input - Full Width */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search companies by name or description..."
                value={searchTerm}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20 text-base"
              />
              {searchTerm && (
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                >
                  ×
                </button>
              )}
            </div>
            {/* Filters - Separate Row */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-3">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-gray-400" />
                <span className="text-gray-300 text-sm font-medium">Filters:</span>
              </div>
              <div className="flex flex-wrap gap-2">
                <Select value={typeFilter} onValueChange={(value) => handleFilterChange('type', value)}>
                  <SelectTrigger className="w-40 bg-gray-800/60 border-gray-700/50 text-gray-100">
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="all">All Types</SelectItem>
                    {companyTypes.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={countryFilter} onValueChange={(value) => handleFilterChange('country', value)}>
                  <SelectTrigger className="w-40 bg-gray-800/60 border-gray-700/50 text-gray-100">
                    <SelectValue placeholder="All Countries" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="all">All Countries</SelectItem>
                    {countries.map(country => (
                      <SelectItem key={country} value={country}>{country}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Alphabetical Filter - Modern & Compact */}
        <div className="bg-gray-900/30 backdrop-blur-md rounded-2xl p-4 mb-6 border border-gray-700/30">
          <div className="flex items-center gap-6">
            {/* Label */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-gray-300 font-medium text-sm">A-Z</span>
            </div>

            {/* Centered Letter Pills Container */}
            <div className="flex-1 flex items-center justify-center">
              <div className="flex items-center gap-1 flex-wrap justify-center max-w-4xl">
                {/* All Button */}
                <button
                  onClick={() => handleFilterChange('letter', null)}
                  className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
                    selectedLetter === null
                      ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                      : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300'
                  }`}
                >
                  All
                </button>

                {/* Letter Pills */}
                {alphabet.map(letter => (
                  <button
                    key={letter}
                    onClick={() => handleFilterChange('letter', letter)}
                    className={`w-7 h-7 rounded-full text-xs font-medium transition-all duration-200 flex items-center justify-center ${
                      selectedLetter === letter
                        ? 'bg-green-500 text-white shadow-lg shadow-green-500/25 scale-110'
                        : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300 hover:scale-105'
                    }`}
                  >
                    {letter}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Results Summary and View Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="text-gray-400 text-sm">
            Showing {paginatedCompanies.length} of {filteredCompanies.length} companies
          </div>

          <div className="flex items-center gap-2">
            <span className="text-gray-400 text-sm">Items per page:</span>
            <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
              <SelectTrigger className="w-20 bg-gray-800/60 border-gray-700/50 text-gray-100">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                <SelectItem value="6">6</SelectItem>
                <SelectItem value="12">12</SelectItem>
                <SelectItem value="24">24</SelectItem>
                <SelectItem value="48">48</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex justify-end mb-6">
            <TabsList className="bg-black/40">
              <TabsTrigger value="list" className="flex items-center gap-2">
                <Grid className="h-4 w-4" />
                List View
              </TabsTrigger>
              <TabsTrigger value="map" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Map View
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="list" className="mt-0">
            <div className="flex justify-between items-center mb-4">
              <p className="text-gray-300">
                Showing {filteredCompanies.length} of {companies.length} companies
              </p>
              <div className="flex gap-2">
                <Button
                  variant={view === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setView('grid')}
                  className={view === 'grid' ? 'bg-green-600' : ''}
                >
                  Grid
                </Button>
                <Button
                  variant={view === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setView('list')}
                  className={view === 'list' ? 'bg-green-600' : ''}
                >
                  List
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto"></div>
              </div>
            ) : view === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {paginatedCompanies.map((company) => (
                  <Card
                    key={company.id}
                    className="group overflow-hidden bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-blue-500/30 hover:bg-gray-900/60 transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/10 hover:-translate-y-1 cursor-pointer"
                    onClick={() => {
                      setSelectedCompanyId(company.id);
                      setModalOpen(true);
                    }}
                  >
                    {/* Header with gradient background */}
                    <div className="bg-gradient-to-r from-blue-800/20 via-blue-600/10 to-blue-500/20 p-6 pb-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          {company.verified && (
                            <Badge className="bg-green-500/20 text-green-400 border-green-500/50 text-xs">
                              <Award className="h-3 w-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                          {company.company_type && (
                            <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/50 text-xs">
                              {company.company_type}
                            </Badge>
                          )}
                        </div>
                        {company.logo_url && (
                          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-2">
                            <img
                              src={company.logo_url}
                              alt={company.name}
                              className="h-10 w-10 object-contain"
                            />
                          </div>
                        )}
                      </div>

                      <CardTitle className="text-2xl font-heading font-bold hemp-brand-ultra line-clamp-2 mb-2">
                        {company.name}
                      </CardTitle>

                      {company.website && (
                        <div className="flex items-center gap-2 text-sm text-gray-300">
                          <Globe className="h-4 w-4 text-blue-400" />
                          <span className="line-clamp-1">{company.website}</span>
                        </div>
                      )}
                    </div>
                    <CardContent className="p-6 pt-4">
                      {/* Description */}
                      <p className="text-sm text-gray-300 leading-relaxed line-clamp-3 mb-4">
                        {company.description || 'No description available'}
                      </p>

                      {/* Company info */}
                      <div className="flex items-center gap-4 mb-4 text-xs text-gray-400">
                        {company.country && (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span>{company.country}</span>
                          </div>
                        )}
                        {company.founded_year && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>Est. {company.founded_year}</span>
                          </div>
                        )}
                        {company.product_count !== undefined && (
                          <div className="flex items-center gap-1">
                            <Package className="h-3 w-3" />
                            <span>{company.product_count} products</span>
                          </div>
                        )}
                      </div>

                      {/* Footer with action button */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-800/50">
                        <div className="flex items-center gap-2">
                          {company.website && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-400 hover:text-blue-400 p-0 h-auto"
                              onClick={(e) => {
                                e.stopPropagation();
                                window.open(company.website, '_blank');
                              }}
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                          )}
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-400 hover:text-blue-300 opacity-0 group-hover:opacity-100 transition-all hover:bg-blue-900/20"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedCompanyId(company.id);
                            setModalOpen(true);
                          }}
                        >
                          <span className="text-xs mr-2">View Details</span>
                          <ArrowRight className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {paginatedCompanies.map((company) => (
                  <Card 
                    key={company.id} 
                    className="bg-black/40 border-gray-700 hover:border-green-400 transition-colors cursor-pointer"
                    onClick={() => {
                      setSelectedCompanyId(company.id);
                      setModalOpen(true);
                    }}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-semibold hemp-brand-ultra flex items-center gap-2">
                              {company.name}
                              {company.verified && (
                                <CheckCircle className="h-5 w-5 text-green-400" />
                              )}
                            </h3>
                            <div className="flex gap-2">
                              {company.company_type && (
                                <Badge className={getTypeColor(company.company_type)}>
                                  {company.company_type}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <p className="text-gray-300 mb-3">
                            {company.description || 'No description available'}
                          </p>
                          <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                            {company.website && (
                              <a
                                href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:text-green-400 transition-colors"
                              >
                                {company.website}
                              </a>
                            )}
                            {company.country && (
                              <span className="flex items-center">
                                <MapPin className="h-4 w-4 mr-1" />
                                {company.country}
                              </span>
                            )}
                            {company.founded_year && (
                              <span className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                Founded {company.founded_year}
                              </span>
                            )}
                            {company.product_count !== undefined && (
                              <span className="flex items-center">
                                <Package className="h-4 w-4 mr-1" />
                                {company.product_count} products
                              </span>
                            )}
                          </div>
                        </div>
                        {company.logo_url && (
                          <img
                            src={company.logo_url}
                            alt={company.name}
                            className="h-16 w-16 rounded object-contain ml-4"
                          />
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-8 pt-6 border-t border-gray-800/50">
                <div className="text-gray-400 text-sm">
                  Page {currentPage} of {totalPages}
                </div>

                <div className="flex items-center gap-2">
                  {/* Previous Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page Numbers */}
                  <div className="flex gap-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={currentPage === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                          className={`w-10 ${
                            currentPage === pageNum
                              ? 'bg-green-500 text-white'
                              : 'bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60'
                          }`}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  {/* Next Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="bg-gray-800/60 border-gray-700/50 text-gray-300 hover:bg-gray-700/60 disabled:opacity-50"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="map" className="mt-0">
            <Card className="bg-black/40 border-gray-700">
              <CardContent className="p-0">
                {isLoading ? (
                  <div className="text-center py-16">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto"></div>
                  </div>
                ) : (
                  <div className="relative">
                    <HempCompaniesLeafletMap
                      companies={filteredCompanies}
                      onCompanyClick={(company) => {
                        setSelectedCompanyId(company.id);
                        setModalOpen(true);
                      }}
                    />
                    <div className="absolute bottom-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700">
                      <p className="text-sm text-gray-300">
                        Showing {filteredCompanies.filter(c => c.latitude && c.longitude).length} companies with location data
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      
      <CompanyDetailModal
        companyId={selectedCompanyId}
        open={modalOpen}
        onOpenChange={setModalOpen}
      />
    </div>
  );
}