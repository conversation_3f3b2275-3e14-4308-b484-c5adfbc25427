#!/usr/bin/env python3
"""Test research functionality"""

import asyncio
import os
from dotenv import load_dotenv
from lib.supabase_client import get_supabase_client

load_dotenv()

async def test_research():
    """Test basic research functionality"""
    print("Testing research agent...")
    
    # Get Supabase client
    client = get_supabase_client()
    
    # Check products count
    result = client.table('uses_products').select('id', count='exact').execute()
    print(f"Current products in database: {result.count}")
    
    # Try to create a research agent
    try:
        from agents.research.unified_research_agent import UnifiedResearchAgent, ResearchConfig, ResearchFeatures
        
        config = ResearchConfig(
            enabled_features={ResearchFeatures.BASIC, ResearchFeatures.WEB_SCRAPING},
            max_results=5,
            use_ai_analysis=False  # Don't use AI
        )
        
        agent = UnifiedResearchAgent(client, ai_provider=None, config=config)
        print("Research agent created successfully")
        
        # Try a simple search
        print("\nSearching for hemp products...")
        results = await agent.discover_products("hemp packaging", max_results=5)
        
        if results:
            print(f"\nFound {len(results)} results:")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.get('name', 'Unknown')}")
        else:
            print("\nNo results found. This might be because:")
            print("- No AI provider is configured")
            print("- Web scraping sources might be unavailable")
            print("- The agent needs additional configuration")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_research())