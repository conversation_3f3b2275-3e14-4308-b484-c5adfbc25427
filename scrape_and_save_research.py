#!/usr/bin/env python3
"""
Complete workflow: Scrape hemp articles and save to research_entries
"""

import asyncio
import aiohttp
import feedparser
from bs4 import BeautifulSoup
from datetime import datetime
import logging
import re
from typing import List, Dict, Optional

# Supabase imports
from supabase import create_client, Client
from dotenv import load_dotenv
import os

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()


class HempResearchScraper:
    """Scrapes hemp articles and saves them as research entries"""
    
    def __init__(self):
        self.supabase = self._get_supabase()
        self.sources = [
            {
                'name': 'Hemp Industry Daily',
                'feed': 'https://hempindustrydaily.com/feed/',
                'type': 'news'
            },
            {
                'name': 'Vote Hemp',
                'url': 'https://votehemp.com',
                'type': 'advocacy',
                'feed': 'https://votehemp.com/feed/'
            },
            {
                'name': 'EIHA News',
                'url': 'https://eiha.org',
                'type': 'industry',
                'feed': 'https://eiha.org/feed/'
            }
        ]
        
        # Cache IDs to avoid repeated lookups
        self.plant_type_id = None
        self.plant_part_ids = {}
        self.industry_ids = {}
        
    def _get_supabase(self) -> Client:
        """Get Supabase client"""
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_ANON_KEY")
        
        if not url or not key:
            raise ValueError("Missing SUPABASE_URL or SUPABASE_ANON_KEY")
            
        return create_client(url, key)
    
    async def scrape_and_save(self, limit: int = 20) -> Dict:
        """Main method: scrape articles and save to research_entries"""
        logger.info("Starting hemp research scraping...")
        
        # Load reference data
        await self._load_reference_data()
        
        # Scrape articles
        all_articles = await self._scrape_all_sources(limit)
        logger.info(f"Scraped {len(all_articles)} articles total")
        
        # Convert to research entries
        research_entries = []
        for article in all_articles:
            entry = self._convert_to_research_entry(article)
            if entry:
                research_entries.append(entry)
        
        logger.info(f"Converted {len(research_entries)} articles to research entries")
        
        # Save to database
        saved_count = self._save_research_entries(research_entries)
        
        return {
            'scraped': len(all_articles),
            'converted': len(research_entries),
            'saved': saved_count,
            'entries': research_entries[:5]  # Return sample
        }
    
    async def _load_reference_data(self):
        """Load IDs for plant types, parts, and industries"""
        # Get default plant type
        plant_types = self.supabase.table('hemp_plant_archetypes').select('id, name').execute()
        if plant_types.data:
            self.plant_type_id = plant_types.data[0]['id']
            logger.info(f"Using plant type: {plant_types.data[0]['name']}")
        
        # Load plant parts
        parts = self.supabase.table('plant_parts').select('id, name').execute()
        self.plant_part_ids = {part['name']: part['id'] for part in parts.data}
        
        # Load industries
        industries = self.supabase.table('industries').select('id, name').execute()
        self.industry_ids = {ind['name']: ind['id'] for ind in industries.data}
    
    async def _scrape_all_sources(self, limit: int) -> List[Dict]:
        """Scrape all configured sources"""
        all_articles = []
        
        for source in self.sources:
            try:
                if source.get('feed'):
                    articles = await self._scrape_rss(source, limit)
                    all_articles.extend(articles)
                else:
                    articles = await self._scrape_website(source, limit)
                    all_articles.extend(articles)
            except Exception as e:
                logger.error(f"Error scraping {source['name']}: {e}")
        
        return all_articles[:limit]
    
    async def _scrape_rss(self, source: Dict, limit: int) -> List[Dict]:
        """Scrape RSS feed"""
        articles = []
        logger.info(f"Scraping RSS: {source['name']}")
        
        try:
            feed = feedparser.parse(source['feed'])
            
            for entry in feed.entries[:limit]:
                # Filter for hemp-related content
                text = f"{entry.get('title', '')} {entry.get('summary', '')}".lower()
                if 'hemp' in text or 'cannabis' in text:
                    article = {
                        'title': entry.get('title', ''),
                        'description': entry.get('summary', ''),
                        'url': entry.get('link', ''),
                        'published': entry.get('published', ''),
                        'source': source['name'],
                        'source_type': source['type']
                    }
                    articles.append(article)
                    
            logger.info(f"  Found {len(articles)} hemp articles")
            
        except Exception as e:
            logger.error(f"RSS error for {source['name']}: {e}")
            
        return articles
    
    async def _scrape_website(self, source: Dict, limit: int) -> List[Dict]:
        """Scrape website (placeholder for future implementation)"""
        # This would implement actual web scraping
        return []
    
    def _convert_to_research_entry(self, article: Dict) -> Optional[Dict]:
        """Convert scraped article to research_entries format"""
        if not article.get('title'):
            return None
        
        # Determine entry type
        entry_type = self._determine_entry_type(article)
        
        # Extract metadata
        plant_part_id = self._determine_plant_part(article)
        industry_id = self._determine_industry(article)
        keywords = self._extract_keywords(article)
        authors = self._extract_authors(article)
        
        # Parse date
        pub_date = self._parse_date(article.get('published', ''))
        
        entry = {
            'title': article['title'][:500],
            'authors_or_assignees': authors,
            'abstract_summary': article.get('description', '')[:1000],
            'publication_or_filing_date': pub_date,
            'journal_or_office': article.get('source', 'Unknown'),
            'full_text_url': article.get('url', ''),
            'entry_type': entry_type,
            'plant_type_id': self.plant_type_id,
            'plant_part_id': plant_part_id,
            'industry_id': industry_id,
            'keywords': keywords,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        return entry
    
    def _determine_entry_type(self, article: Dict) -> str:
        """Determine the type of entry"""
        text = f"{article.get('title', '')} {article.get('description', '')}".lower()
        
        if 'study' in text or 'research' in text:
            return 'Paper'
        elif 'report' in text:
            return 'Report'
        elif 'patent' in text:
            return 'Patent'
        else:
            return 'Article'
    
    def _determine_plant_part(self, article: Dict) -> Optional[int]:
        """Determine plant part from content"""
        text = f"{article.get('title', '')} {article.get('description', '')}".lower()
        
        if 'fiber' in text or 'textile' in text:
            return self.plant_part_ids.get('Fiber')
        elif 'seed' in text or 'protein' in text or 'oil' in text:
            return self.plant_part_ids.get('Seeds')
        elif 'flower' in text or 'cbd' in text:
            return self.plant_part_ids.get('Flower')
        elif 'hurd' in text or 'shiv' in text or 'hempcrete' in text:
            return self.plant_part_ids.get('Hurds')
        
        return None
    
    def _determine_industry(self, article: Dict) -> Optional[int]:
        """Determine industry from content"""
        text = f"{article.get('title', '')} {article.get('description', '')}".lower()
        
        if any(word in text for word in ['food', 'nutrition', 'protein', 'beverage']):
            return self.industry_ids.get('Food & Beverage')
        elif any(word in text for word in ['textile', 'fabric', 'clothing', 'fashion']):
            return self.industry_ids.get('Textiles')
        elif any(word in text for word in ['construction', 'building', 'hempcrete']):
            return self.industry_ids.get('Construction')
        elif any(word in text for word in ['automotive', 'vehicle', 'car']):
            return self.industry_ids.get('Automotive')
        elif any(word in text for word in ['cosmetic', 'beauty', 'skin']):
            return self.industry_ids.get('Personal Care')
        
        return None
    
    def _extract_keywords(self, article: Dict) -> List[str]:
        """Extract keywords from article"""
        text = f"{article.get('title', '')} {article.get('description', '')}".lower()
        
        # Common hemp keywords
        keywords = []
        keyword_list = [
            'hemp', 'fiber', 'textile', 'seed', 'oil', 'cbd', 'protein',
            'sustainable', 'biodegradable', 'construction', 'automotive',
            'plastic', 'composite', 'nutrition', 'farming', 'processing'
        ]
        
        for keyword in keyword_list:
            if keyword in text:
                keywords.append(keyword)
        
        return keywords[:10]  # Limit to 10
    
    def _extract_authors(self, article: Dict) -> List[str]:
        """Extract authors or source"""
        authors = []
        
        # Try to extract from text
        text = article.get('title', '') + ' ' + article.get('description', '')
        by_pattern = r'[Bb]y\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        matches = re.findall(by_pattern, text)
        authors.extend(matches[:2])
        
        # Use source as fallback
        if not authors and article.get('source'):
            authors.append(article['source'])
        
        return authors or ['Unknown Author']
    
    def _parse_date(self, date_str: str) -> Optional[str]:
        """Parse date string to ISO format"""
        if not date_str:
            return datetime.now().date().isoformat()
        
        try:
            from email.utils import parsedate_to_datetime
            dt = parsedate_to_datetime(date_str)
            return dt.date().isoformat()
        except:
            return datetime.now().date().isoformat()
    
    def _save_research_entries(self, entries: List[Dict]) -> int:
        """Save research entries to database"""
        saved_count = 0
        
        for entry in entries:
            try:
                # Check if already exists
                existing = self.supabase.table('research_entries').select('id').eq(
                    'title', entry['title']
                ).execute()
                
                if not existing.data:
                    result = self.supabase.table('research_entries').insert(entry).execute()
                    if result.data:
                        saved_count += 1
                        logger.info(f"✅ Saved: {entry['title'][:60]}...")
                else:
                    logger.info(f"⏭️  Already exists: {entry['title'][:60]}...")
                    
            except Exception as e:
                logger.error(f"Error saving entry: {e}")
                logger.error(f"Entry: {entry['title']}")
        
        return saved_count


async def main():
    """Run the scraper"""
    scraper = HempResearchScraper()
    
    logger.info("=" * 60)
    logger.info("HEMP RESEARCH SCRAPER & SAVER")
    logger.info("=" * 60)
    
    # Scrape and save
    results = await scraper.scrape_and_save(limit=10)
    
    # Display results
    logger.info("\n" + "=" * 60)
    logger.info("RESULTS")
    logger.info("=" * 60)
    logger.info(f"Articles scraped: {results['scraped']}")
    logger.info(f"Articles converted: {results['converted']}")
    logger.info(f"Articles saved: {results['saved']}")
    
    if results['entries']:
        logger.info("\nSample entries:")
        for entry in results['entries'][:3]:
            logger.info(f"\n- {entry['title']}")
            logger.info(f"  Type: {entry['entry_type']}")
            logger.info(f"  Keywords: {', '.join(entry['keywords'][:5])}")
    
    # Show total in database
    supabase = scraper.supabase
    total = supabase.table('research_entries').select('id', count='exact').execute()
    logger.info(f"\nTotal research entries in database: {total.count}")


if __name__ == "__main__":
    asyncio.run(main())