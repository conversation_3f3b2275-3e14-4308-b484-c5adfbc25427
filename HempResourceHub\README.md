# Industrial Hemp Database

An interactive web application designed to demystify industrial hemp through engaging, educational interfaces that showcase diverse hemp applications and environmental potential.

## Project Overview

This application organizes hemp data hierarchically by:
- Plant type (e.g., Fiber Hemp, Seed Hemp)
- Plant part (e.g., Stalk, Seeds, Leaves)
- Industry (e.g., Construction, Textiles, Food)
- Subcategory
- Product

## Recent Enhancements (July 2025)

### 🎨 Typography & Accessibility Improvements (July 2025)
- **Sweet Leaf Font Overhaul**: Complete redesign of hemp brand typography for better readability
  - Removed all text-shadow outlines, text-stroke effects, and glow animations
  - Increased font sizes by 15-50% across all hemp brand classes
  - Standardized color to consistent #22c55e green for all Sweet Leaf text
  - Improved accessibility and mobile experience with cleaner, larger text
  - Enhanced performance by removing complex CSS visual effects

### 🎯 Product Page Enhancements (June 2025)
- **A-Z Alphabetical Filtering**: Quick navigation through products by first letter
- **Advanced Pagination**: Customizable page sizes (6, 12, 24, 48 items) with smart navigation
- **Enhanced Search**: Full-width search bar with improved UX and clear functionality
- **Data Consolidation**: Reduced commercialization stages from 15 to 5 logical categories
- **Mobile Optimization**: Responsive design with touch-friendly interactions
- **Code Cleanup**: Removed 4 redundant product page components for better maintainability

### 🗃️ Database Improvements
- **Stage Consolidation**: Standardized commercialization stages (Research → Development → Pilot → Commercial → Mature)
- **Data Quality**: Eliminated case inconsistencies and duplicate stage values
- **Performance**: Optimized queries with consolidated data structure

### 🔧 Technical Architecture
- **Streamlined Routing**: Consolidated product pages with proper redirects
- **Enhanced Components**: Improved data visualization with ordered stages
- **Better State Management**: Efficient filtering with useMemo optimization

### 📊 Legacy Features
- Added full-text search capabilities with `search_vector` columns
- Improved schema with array data types for benefits, uses, authors, and keywords
- Enhanced Supabase integration with auto refresh and session persistence

## Features

- Interactive 3D models of hemp plants
- Comprehensive database of hemp products and applications
- Searchable research paper repository with enhanced full-text search
- Industry-specific categorization
- Environmental and economic impact data
- Matrix-inspired visual design

## Technology Stack

- **Frontend**: React.js with Vite, Tailwind CSS, shadcn/ui components
- **Backend**: Express.js, Node.js
- **Database**: PostgreSQL via Supabase
- **ORM**: Drizzle ORM
- **3D Visualization**: Three.js, React Three Fiber
- **API**: RESTful API endpoints
- **Authentication**: Passport.js (future implementation)

## Exporting to Windsurf

See the [Export Guide](./EXPORT_GUIDE.md) for detailed instructions on exporting this project from Replit to Windsurf.

## Database Schema

The database schema for this application is documented in [Database Schema](./DATABASE_SCHEMA.md).

## Database Migration

To set up the database with the enhanced schema:

1. Navigate to your Supabase project
2. Open the SQL Editor
3. Execute the SQL in `supabase/migrations/20250519123456_enhanced_schema/up.sql`

This will create the tables with full-text search capabilities.

## Key Files

- `server/db.ts`: Database connection configuration
- `shared/schema.ts`: Database schema definitions
- `server/storage-db.ts`: Data access layer
- `server/routes.ts`: API endpoints
- `client/src/lib/supabase-client.ts`: Supabase client configuration
- `client/src/hooks/use-plant-data.ts`: Data fetching hooks for plant data
- `client/src/hooks/use-product-data.ts`: Data fetching hooks for product data
- `client/src/hooks/use-research-papers.ts`: Data fetching hooks for research papers
- `supabase/migrations/`: Database migration files

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables in `.env`:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_anon_key
   ```
4. Start the development server: `npm run dev`

## API Endpoints

- `/api/plant-types`: Get all plant types
- `/api/plant-type/:id`: Get a specific plant type
- `/api/plant-parts`: Get all plant parts
- `/api/plant-parts/:plantTypeId`: Get plant parts by type
- `/api/plant-part/:id`: Get a specific plant part
- `/api/industries`: Get all industries
- `/api/industry/:id`: Get a specific industry
- `/api/hemp-products`: Get all hemp products
- `/api/hemp-product/:id`: Get a specific hemp product
- `/api/hemp-products/plant-part/:plantPartId`: Get products by plant part
- `/api/hemp-products/search?q=query`: Search hemp products
- `/api/research-papers`: Get all research papers
- `/api/research-paper/:id`: Get a specific research paper
- `/api/stats`: Get application statistics

## License

This project is licensed under the MIT License.