# GitHub Actions Troubleshooting Guide

## Common Issues and Solutions

### 1. Permission Denied Error
**Error**: `bash: ./hemp: Permission denied`

**Solution**: 
- The workflow now includes `chmod +x hemp` to fix this
- Alternatively, use `python hemp` instead of `./hemp`

### 2. Module Not Found Error
**Error**: `ModuleNotFoundError: No module named 'lib'`

**Solution**:
- Add `PYTHONPATH: ${{ github.workspace }}` to environment variables
- Ensure all Python files have proper `__init__.py` files

### 3. Missing Environment Variables
**Error**: `KeyError: 'SUPABASE_URL'`

**Solution**:
1. Go to your GitHub repository
2. Click Settings → Secrets and variables → Actions
3. Add these repository secrets:
   - `SUPABASE_URL`
   - `SUPABASE_ANON_KEY`
   - `OPENAI_API_KEY`

### 4. Database Connection Failed
**Error**: `Connection to Supabase failed`

**Solution**:
- Verify your Supabase URL and key are correct
- Check if your Supabase project is active
- Ensure the tables exist in your database

### 5. Import Errors for Optional Dependencies
**Error**: `ImportError: cannot import name 'HQzOrchestrator'`

**Solution**:
- The code already handles this with try/except blocks
- Install optional dependencies if needed: `pip install langgraph`

### 6. Workflow Not Triggering
**Issue**: Scheduled workflows not running

**Solution**:
- GitHub Actions may disable scheduled workflows after 60 days of inactivity
- Go to Actions tab and re-enable the workflow
- Test with manual trigger first using `workflow_dispatch`

### 7. Artifact Upload Failures
**Error**: `Unable to find any artifacts for the associated workflow`

**Solution**:
- The fixed workflow uses `if-no-files-found: ignore`
- Ensures metrics file exists before uploading

## Testing Your Fix

### Step 1: Test Locally
```bash
# Run the test script
python test_setup.py

# Test the CLI directly
python hemp --help
python hemp monitor --format health
```

### Step 2: Test in GitHub Actions
1. Push the fixed workflow to your repository
2. Go to Actions tab
3. Select "Automated Operations" workflow
4. Click "Run workflow"
5. Choose options and run

### Step 3: Check Logs
- Click on the workflow run
- Expand each job to see detailed logs
- Look for error messages in red

## Debug Commands

Add these to your workflow for debugging:

```yaml
- name: Debug Environment
  run: |
    echo "Current directory: $(pwd)"
    echo "Python path: $PYTHONPATH"
    echo "Directory contents:"
    ls -la
    echo "Lib directory:"
    ls -la lib/
    echo "Agents directory:"
    ls -la agents/
```

## Getting Help

If issues persist:
1. Check the workflow logs for specific error messages
2. Run the test script locally to isolate the issue
3. Verify all files are committed to the repository
4. Ensure secrets are properly configured

## Prevention Tips

1. **Always test locally first** before pushing to GitHub
2. **Use explicit Python paths** instead of relying on shell scripts
3. **Add error handling** to prevent cascading failures
4. **Monitor your workflows** regularly to catch issues early
5. **Keep dependencies updated** but test changes first