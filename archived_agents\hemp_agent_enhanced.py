# hemp_agent_enhanced.py
"""
Enhanced Hemp Product Research Agent - Consolidated Version
This version reduces agents from 19 to 9 by merging similar categories
"""

import os
import json
import time
import argparse
from datetime import datetime
from openai import OpenAI
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class EnhancedHempResearchAgent:
    def __init__(self, plant_part, industries, agent_name=None):
        self.plant_part = plant_part
        self.industries = industries if isinstance(industries, list) else [industries]
        self.agent_name = agent_name or f"{plant_part}_{industries[0] if isinstance(industries, list) else industries}"
        
        # Initialize clients
        self.openai = OpenAI(api_key=os.environ['OPENAI_API_KEY'])
        self.supabase = create_client(
            os.environ['SUPABASE_URL'],
            os.environ['SUPABASE_ANON_KEY']
        )
        
        # Cache for IDs to avoid repeated lookups
        self.plant_part_id = None
        self.industry_ids = {}
        self.industry_sub_category_ids = {}
        
        print(f"🌿 Initialized Consolidated {plant_part} agent for {', '.join(self.industries)}")
    
    def get_or_create_plant_part(self):
        """Get or create plant part and archetype"""
        # First, check if we need to create archetype
        archetype_name = f"{self.plant_part.capitalize()} Archetype"
        archetype_result = self.supabase.table('hemp_plant_archetypes').select('id').eq('name', archetype_name).execute()
        
        if not archetype_result.data:
            archetype_data = {
                'name': archetype_name,
                'description': f'Hemp plants optimized for {self.plant_part} production',
                'cultivation_focus_notes': f'Focus on maximizing {self.plant_part} yield'
            }
            archetype_result = self.supabase.table('hemp_plant_archetypes').insert(archetype_data).execute()
            archetype_id = archetype_result.data[0]['id']
        else:
            archetype_id = archetype_result.data[0]['id']
        
        # Now get or create plant part
        part_result = self.supabase.table('plant_parts').select('id').eq('name', self.plant_part).eq('archetype_id', archetype_id).execute()
        
        if not part_result.data:
            part_data = {
                'archetype_id': archetype_id,
                'name': self.plant_part,
                'description': f'The {self.plant_part} part of the hemp plant'
            }
            part_result = self.supabase.table('plant_parts').insert(part_data).execute()
            self.plant_part_id = part_result.data[0]['id']
        else:
            self.plant_part_id = part_result.data[0]['id']
        
        return self.plant_part_id
    
    def get_or_create_industry_category(self, industry):
        """Get or create industry and sub-category"""
        # Map agent industries to main industries
        industry_mapping = {
            'food_beverage': 'Food & Beverage',
            'nutritional_supplements': 'Health & Wellness',
            'textiles': 'Textiles & Fashion',
            'composites': 'Manufacturing',
            'cosmetics': 'Personal Care',
            'personal_care': 'Personal Care',
            'wellness': 'Health & Wellness',
            'biofuel': 'Energy',
            'pharmaceuticals': 'Health & Wellness',
            'cbd_products': 'Health & Wellness',
            'health_products': 'Health & Wellness',
            'construction': 'Construction',
            'building_materials': 'Construction',
            'materials': 'Construction',
            'animal_bedding': 'Agriculture',
            'hempcrete': 'Construction',
            'traditional_medicine': 'Health & Wellness',
            'biotech': 'Biotechnology',
            'animal_feed': 'Agriculture',
            'medicine': 'Health & Wellness',
            'energy': 'Energy',
            'sustainability': 'Environmental'
        }
        
        main_industry = industry_mapping.get(industry, 'Other')
        
        # Get or create main industry
        industry_result = self.supabase.table('industries').select('id').eq('name', main_industry).execute()
        
        if not industry_result.data:
            industry_data = {
                'name': main_industry,
                'description': f'{main_industry} industry applications for hemp'
            }
            industry_result = self.supabase.table('industries').insert(industry_data).execute()
            industry_id = industry_result.data[0]['id']
        else:
            industry_id = industry_result.data[0]['id']
        
        self.industry_ids[industry] = industry_id
        
        # Get or create sub-category
        sub_category_name = industry.replace('_', ' ').title()
        sub_result = self.supabase.table('industry_sub_categories').select('id').eq('name', sub_category_name).eq('industry_id', industry_id).execute()
        
        if not sub_result.data:
            sub_data = {
                'industry_id': industry_id,
                'name': sub_category_name,
                'description': f'{sub_category_name} applications'
            }
            sub_result = self.supabase.table('industry_sub_categories').insert(sub_data).execute()
            sub_category_id = sub_result.data[0]['id']
        else:
            sub_category_id = sub_result.data[0]['id']
        
        self.industry_sub_category_ids[industry] = sub_category_id
        
        return sub_category_id
    
    def research_products(self, limit=5):
        """Research hemp products using AI for multiple industries"""
        # Build comprehensive prompt for all industries
        industries_text = " and ".join(self.industries)
        
        prompt = f"""
        Research current hemp {self.plant_part} products across these industries: {industries_text}.
        Find {limit} specific products that actually exist in the market today, distributing them across the mentioned industries.
        
        For each product provide:
        - Product name (exact name as marketed)
        - Company name (exact company name)
        - Product description (2-3 detailed sentences)
        - Industry category (which of these it belongs to: {', '.join(self.industries)})
        - Key benefits/features (list of 3-4 specific benefits)
        - Target market (specific demographics)
        - Price range (if available, e.g., "$20-30")
        - Website or where to buy (if available)
        - Manufacturing process summary (brief overview)
        - Sustainability aspects (2-3 points)
        - Technical specifications (any relevant specs)
        
        Focus on real, currently available products. Include both well-known brands and smaller companies.
        Ensure a good distribution across all mentioned industries.
        
        Format as JSON array:
        [{{
            "product_name": "",
            "company_name": "",
            "description": "",
            "industry": "",
            "benefits": ["", "", ""],
            "target_market": "",
            "price_range": "",
            "website": "",
            "manufacturing_process": "",
            "sustainability": ["", "", ""],
            "specifications": {{}}
        }}]
        """
        
        try:
            response = self.openai.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a hemp industry research specialist with deep knowledge of current hemp products in the market. Always provide accurate, real product information."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=3000  # Increased for multiple industries
            )
            
            # Parse response
            content = response.choices[0].message.content
            start = content.find('[')
            end = content.rfind(']') + 1
            
            if start != -1 and end != 0:
                products = json.loads(content[start:end])
                return products
            else:
                print(f"Could not parse JSON from response")
                return []
                
        except Exception as e:
            print(f"Error in research: {e}")
            return []
    
    def save_to_database(self, products_data):
        """Save products and companies to main database tables"""
        saved_products = 0
        saved_companies = 0
        products_by_industry = {}
        
        # Ensure we have plant part ID
        if not self.plant_part_id:
            self.get_or_create_plant_part()
        
        # Process all industries to ensure IDs exist
        for industry in self.industries:
            self.get_or_create_industry_category(industry)
            products_by_industry[industry] = 0
        
        for product in products_data:
            try:
                # Determine which industry this product belongs to
                product_industry = product.get('industry', self.industries[0])
                # Map to our internal industry names
                industry_key = product_industry.lower().replace(' ', '_').replace('&', 'and')
                
                # Find matching industry from our list
                matched_industry = None
                for ind in self.industries:
                    if ind in industry_key or industry_key in ind:
                        matched_industry = ind
                        break
                
                if not matched_industry:
                    matched_industry = self.industries[0]  # Default to first industry
                
                # Get the sub-category ID for this product's industry
                industry_sub_category_id = self.industry_sub_category_ids.get(matched_industry)
                
                # Check if company exists in main companies table
                company_result = self.supabase.table('companies').select('id').eq('name', product['company_name']).execute()
                
                if not company_result.data:
                    # Create company in main table
                    company_data = {
                        'name': product['company_name'],
                        'website': product.get('website', ''),
                        'primary_activity': f'Hemp {matched_industry}',
                        'specialization': f'{self.plant_part} products',
                        'description': f'Company specializing in hemp {self.plant_part} for {matched_industry}'
                    }
                    company_result = self.supabase.table('companies').insert(company_data).execute()
                    saved_companies += 1
                    company_id = company_result.data[0]['id']
                    print(f"  ✅ New company: {product['company_name']}")
                else:
                    company_id = company_result.data[0]['id']
                
                # Check if product exists in uses_products table
                product_result = self.supabase.table('uses_products').select('id').eq('name', product['product_name']).eq('plant_part_id', self.plant_part_id).execute()
                
                if not product_result.data:
                    # Create product in main uses_products table
                    product_data = {
                        'name': product['product_name'],
                        'description': product['description'],
                        'plant_part_id': self.plant_part_id,
                        'industry_sub_category_id': industry_sub_category_id,
                        'benefits_advantages': product.get('benefits', []),
                        'commercialization_stage': 'Market Ready',
                        'manufacturing_processes_summary': product.get('manufacturing_process', ''),
                        'sustainability_aspects': product.get('sustainability', []),
                        'technical_specifications': product.get('specifications', {}),
                        'miscellaneous_info': {
                            'target_market': product.get('target_market', ''),
                            'price_range': product.get('price_range', ''),
                            'availability': product.get('website', ''),
                            'industry': matched_industry
                        }
                    }
                    product_result = self.supabase.table('uses_products').insert(product_data).execute()
                    saved_products += 1
                    products_by_industry[matched_industry] += 1
                    product_id = product_result.data[0]['id']
                    print(f"  ✅ New product: {product['product_name']} ({matched_industry})")
                    
                    # Create product-company relationship
                    self.supabase.table('product_companies').insert({
                        'use_product_id': product_id,
                        'company_id': company_id
                    }).execute()
                    
                    # Also save to automation tables for tracking
                    self.save_to_automation_tables(product, company_id, matched_industry)
                    
                else:
                    print(f"  ℹ️  Product already exists: {product['product_name']}")
                    
            except Exception as e:
                print(f"  ❌ Error saving {product.get('product_name', 'Unknown')}: {e}")
        
        # Print industry distribution
        print(f"\n📊 Products by industry:")
        for industry, count in products_by_industry.items():
            if count > 0:
                print(f"   - {industry}: {count} products")
        
        return saved_products, saved_companies
    
    def save_to_automation_tables(self, product, company_id, industry):
        """Also save to automation tables for backward compatibility and tracking"""
        try:
            # Check/create in automation companies table
            auto_company_result = self.supabase.table('hemp_automation_companies').select('id').eq('name', product['company_name']).execute()
            
            if not auto_company_result.data:
                auto_company_data = {
                    'name': product['company_name'],
                    'website': product.get('website', ''),
                    'primary_focus': industry,
                    'created_at': datetime.now().isoformat()
                }
                auto_company_result = self.supabase.table('hemp_automation_companies').insert(auto_company_data).execute()
                auto_company_id = auto_company_result.data[0]['id']
            else:
                auto_company_id = auto_company_result.data[0]['id']
            
            # Save to automation products table
            auto_product_data = {
                'name': product['product_name'],
                'company_id': auto_company_id,
                'description': product['description'],
                'plant_part': self.plant_part,
                'industry': industry,
                'benefits': product.get('benefits', []),
                'target_market': product.get('target_market', ''),
                'price_range': product.get('price_range', ''),
                'availability': product.get('website', ''),
                'created_at': datetime.now().isoformat()
            }
            self.supabase.table('hemp_automation_products').insert(auto_product_data).execute()
            
        except Exception as e:
            print(f"  ⚠️  Error saving to automation tables: {e}")
    
    def run(self, limit=5):
        """Execute the agent"""
        print(f"\n{'='*60}")
        print(f"🚀 Starting Consolidated {self.agent_name} Agent")
        print(f"🏭 Industries: {', '.join(self.industries)}")
        print(f"⏰ Time: {datetime.now()}")
        print(f"{'='*60}")
        
        # Research products
        products = self.research_products(limit)
        print(f"\n📊 Found {len(products)} products")
        
        if products:
            # Save to database
            saved_products, saved_companies = self.save_to_database(products)
            print(f"\n📈 Results:")
            print(f"   Products saved: {saved_products}")
            print(f"   Companies saved: {saved_companies}")
            
            # Log the run - using existing schema without agent_type
            log_data = {
                'agent_name': self.agent_name,
                'products_found': len(products),
                'products_saved': saved_products,
                'companies_saved': saved_companies,
                'timestamp': datetime.now().isoformat(),
                'status': 'success'
            }
            
            # Check if table has agent_type column
            try:
                # Try with agent_type first
                log_data['agent_type'] = 'consolidated'
                log_data['details'] = {
                    'plant_part': self.plant_part,
                    'industries': self.industries
                }
                self.supabase.table('hemp_agent_runs').insert(log_data).execute()
            except Exception as e:
                if 'agent_type' in str(e):
                    # Remove agent_type and details if not supported
                    log_data.pop('agent_type', None)
                    log_data.pop('details', None)
                    self.supabase.table('hemp_agent_runs').insert(log_data).execute()
                else:
                    raise e
            
            print(f"\n✅ Agent run logged successfully")
        else:
            print("❌ No products found in this run")
            
            # Log the failed run
            log_data = {
                'agent_name': self.agent_name,
                'products_found': 0,
                'products_saved': 0,
                'companies_saved': 0,
                'timestamp': datetime.now().isoformat(),
                'status': 'no_results'
            }
            
            # Try without agent_type for compatibility
            try:
                self.supabase.table('hemp_agent_runs').insert(log_data).execute()
            except Exception as e:
                print(f"⚠️ Error logging run: {e}")
        
        print(f"\n{'='*60}\n")


# Parse command-line arguments
def parse_arguments():
    parser = argparse.ArgumentParser(description='Enhanced Hemp Product Research Agent - Consolidated Version')
    parser.add_argument('agent_type', nargs='?', help='Agent type to run')
    parser.add_argument('--type', dest='agent_type_flag', help='Agent type (alternative syntax)')
    parser.add_argument('--limit', type=int, default=10, help='Maximum products to research per run')
    
    args = parser.parse_args()
    
    # Handle both positional and flag-based agent type
    agent_type = args.agent_type or args.agent_type_flag
    
    return agent_type, args.limit


# Main execution
if __name__ == "__main__":
    import sys
    
    # Check environment variables
    required_vars = ['OPENAI_API_KEY', 'SUPABASE_URL', 'SUPABASE_ANON_KEY']
    missing = [var for var in required_vars if not os.environ.get(var)]
    
    if missing:
        print("❌ Missing environment variables:")
        for var in missing:
            print(f"   - {var}")
        print("\n📋 Please update your .env file with the required keys")
        exit(1)
    
    # Define consolidated agents (reduced from 19 to 9)
    agents = {
        # Seeds agents (2)
        'seeds-food': ('seeds', ['food_beverage']),
        'seeds-nutrition': ('seeds', ['nutritional_supplements']),
        
        # Fiber agents (2)
        'fiber-textiles': ('fiber', ['textiles']),
        'fiber-composites': ('fiber', ['composites']),
        
        # Oil agents (2) - consolidated personal care
        'oil-personal-care': ('oil', ['cosmetics', 'personal_care', 'wellness']),
        'oil-biofuel': ('oil', ['biofuel']),
        
        # Flower agents (1) - consolidated all flower products
        'flower-products': ('flower', ['pharmaceuticals', 'cbd_products', 'wellness', 'health_products']),
        
        # Hurds agents (1) - consolidated all materials
        'hurds-materials': ('hurds', ['construction', 'hempcrete', 'animal_bedding', 'building_materials']),
        
        # Roots agents (2)
        'roots-medicine': ('roots', ['traditional_medicine']),
        'roots-biotech': ('roots', ['biotech']),
        
        # Leaves agents (2)
        'leaves-feed': ('leaves', ['animal_feed']),
        'leaves-medicine': ('leaves', ['medicine']),
        
        # Biomass/whole plant agents (2)
        'biomass-energy': ('biomass', ['energy']),
        'whole-plant': ('whole_plant', ['sustainability'])
    }
    
    # Parse arguments
    agent_type, limit = parse_arguments()
    
    if agent_type:
        if agent_type == 'all':
            # Run all agents
            print(f"🌿 Running ALL consolidated hemp research agents (limit: {limit} products each)...")
            print(f"📊 Total agents: {len(agents)} (reduced from 19)")
            
            # Check OpenAI API key status
            try:
                test_client = OpenAI(api_key=os.environ['OPENAI_API_KEY'])
                test_response = test_client.models.list()
                print("✅ OpenAI API key is valid\n")
            except Exception as e:
                if 'insufficient_quota' in str(e):
                    print("❌ OpenAI API quota exceeded!")
                    print("   Please visit: https://platform.openai.com/account/billing")
                    print("   Add credits or update your payment method\n")
                    exit(1)
                else:
                    print(f"⚠️ OpenAI API warning: {e}\n")
            
            for name, (plant_part, industries) in agents.items():
                agent = EnhancedHempResearchAgent(plant_part, industries, name)
                agent.run(limit=limit)
                time.sleep(2)  # Small delay between agents
        elif agent_type in agents:
            # Run specific agent
            plant_part, industries = agents[agent_type]
            agent = EnhancedHempResearchAgent(plant_part, industries, agent_type)
            agent.run(limit=limit)
        else:
            print(f"❌ Unknown agent type: {agent_type}")
            print(f"\n📋 Available consolidated agents:")
            for name, (plant_part, industries) in agents.items():
                industries_str = ', '.join(industries)
                print(f"   {name:20} - {plant_part} for {industries_str}")
            print(f"   {'all':20} - Run all agents")
    else:
        # Show help
        print("🌿 Enhanced Hemp Research Agent - Consolidated Version")
        print("\n✨ Reduced from 19 agents to 9 for better efficiency!")
        print("\nUsage: python hemp_agent_enhanced.py [agent-type]")
        print("\nAvailable consolidated agents:")
        for name, (plant_part, industries) in agents.items():
            industries_str = ', '.join(industries)
            print(f"   {name:20} - Research {plant_part} in {industries_str}")
        print(f"   {'all':20} - Run all agents")
        print("\nExample: python hemp_agent_enhanced.py flower-products")
        print("\n📊 Consolidation summary:")
        print("   - flower-products: combines pharma, CBD, and wellness")
        print("   - oil-personal-care: combines cosmetics and wellness")
        print("   - hurds-materials: combines construction, hempcrete, and bedding")
        
        # Check API status
        print("\n🔍 Checking OpenAI API status...")
        try:
            test_client = OpenAI(api_key=os.environ.get('OPENAI_API_KEY', ''))
            test_response = test_client.models.list()
            print("✅ OpenAI API key is valid and has quota")
        except Exception as e:
            if 'insufficient_quota' in str(e):
                print("❌ OpenAI API quota exceeded!")
                print("   Please visit: https://platform.openai.com/account/billing")
                print("   Add credits or update your payment method")
            else:
                print(f"⚠️ OpenAI API issue: {e}")
