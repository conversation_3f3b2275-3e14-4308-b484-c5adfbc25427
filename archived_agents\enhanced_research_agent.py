#!/usr/bin/env python3
"""
Enhanced Research Agent with Company Extraction
Discovers hemp products and extracts/saves company information
"""

import asyncio
import json
import re
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from agents.research.research_agent import HempResearchAgent

logger = logging.getLogger(__name__)

class EnhancedHempResearchAgent(HempResearchAgent):
    """Enhanced research agent that extracts and saves company information"""
    
    async def _structure_product_data(self, raw_data: Dict) -> Optional[Dict]:
        """Enhanced version that also extracts company information"""
        prompt = f"""
        Extract hemp product information from this data and structure it for our database.
        
        IMPORTANT: Also identify any company or brand names mentioned.
        
        Source: {raw_data.get('source', 'Unknown')}
        Title: {raw_data.get('title', '')}
        Content: {raw_data.get('description', '')}
        
        Return a JSON object with:
        {{
            "name": "product name (without brand/company)",
            "companies": ["list", "of", "company", "names"],
            "description": "detailed description",
            "plant_part": "seeds|fiber|oil|flower|hurds|roots|leaves|biomass",
            "industry": "main industry category",
            "sub_industry": "specific sub-industry if applicable",
            "benefits_advantages": ["list", "of", "benefits"],
            "sustainability_aspects": ["list", "of", "sustainability", "points"],
            "technical_specifications": {{"key": "value"}},
            "commercialization_stage": "R&D|Pilot|Niche|Growing|Established",
            "data_source": "{raw_data.get('source', '')}",
            "source_url": "{raw_data.get('url', '')}"
        }}
        
        Extract company/brand names from the content and product title.
        If the title is like "Company Name Product Type", extract the company.
        """
        
        try:
            # Call parent's AI processing
            response = await self.ai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a hemp industry research assistant that extracts structured product and company data."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=500
            )
            
            content = response.choices[0].message.content
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                product_data = json.loads(json_match.group())
                product_data['discovered_date'] = datetime.now().isoformat()
                return product_data
            else:
                logger.error("No JSON found in AI response")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"Error structuring product data: {e}")
            return None
    
    def _extract_company_from_name(self, product_name: str) -> tuple[str, List[str]]:
        """Extract company names from product name"""
        companies = []
        clean_name = product_name
        
        # Known company patterns
        company_patterns = [
            r'^([\w\s&]+?)\s+(Hemp|Cannabis|CBD)',
            r'^([\w\s]+?)\s+(?:Organic|Premium|Natural)',
            r'^([A-Z][\w\s&]+?)\s+[A-Z]',  # Capitalized company name
        ]
        
        for pattern in company_patterns:
            match = re.match(pattern, product_name)
            if match:
                potential_company = match.group(1).strip()
                # Verify it's likely a company name
                if len(potential_company) > 2 and potential_company[0].isupper():
                    companies.append(potential_company)
                    clean_name = product_name.replace(potential_company, '').strip()
                    break
        
        return clean_name, companies
    
    async def _get_or_create_company(self, company_name: str) -> Optional[int]:
        """Get existing company or create new one"""
        try:
            # Check if company exists
            result = await self.supabase.table('hemp_companies').select('id').eq('name', company_name).execute()
            
            if result.data:
                return result.data[0]['id']
            
            # Create new company
            new_company = {
                'name': company_name,
                'description': f'{company_name} - Hemp product manufacturer/brand discovered by AI',
                'verified': False,  # Mark as unverified since AI-discovered
                'company_type': 'manufacturer',
                'created_at': datetime.now().isoformat()
            }
            
            result = await self.supabase.table('hemp_companies').insert(new_company).execute()
            if result.data:
                logger.info(f"Created new company: {company_name}")
                return result.data[0]['id']
                
        except Exception as e:
            logger.error(f"Error creating company {company_name}: {e}")
        
        return None
    
    async def _link_product_to_companies(self, product_id: int, company_ids: List[int]):
        """Create relationships between product and companies"""
        for idx, company_id in enumerate(company_ids):
            try:
                # Check if relationship exists
                existing = await self.supabase.table('hemp_company_products')\
                    .select('id')\
                    .eq('product_id', product_id)\
                    .eq('company_id', company_id)\
                    .execute()
                
                if not existing.data:
                    relationship = {
                        'product_id': product_id,
                        'company_id': company_id,
                        'is_primary': idx == 0,  # First company is primary
                        'verified': False,  # AI-discovered, needs verification
                        'relationship_type': 'manufacturer',
                        'notes': 'Discovered by AI agent'
                    }
                    
                    await self.supabase.table('hemp_company_products').insert(relationship).execute()
                    logger.info(f"Linked product {product_id} to company {company_id}")
                    
            except Exception as e:
                logger.error(f"Error linking product to company: {e}")
    
    async def _save_products_to_db(self, products: List[Dict]) -> int:
        """Enhanced save that also handles companies"""
        saved_count = 0
        
        for product in products:
            try:
                # Extract companies from product name if not already done
                clean_name = product['name']
                companies = product.get('companies', [])
                
                # Additional extraction from product name
                name_clean, name_companies = self._extract_company_from_name(product['name'])
                if name_companies and not companies:
                    companies = name_companies
                    clean_name = name_clean
                
                # Check if product already exists
                existing = await self.supabase.table('uses_products').select('id').eq('name', clean_name).execute()
                
                if not existing.data:
                    # Get IDs for foreign keys
                    plant_part_id = await self._get_plant_part_id(product['plant_part'])
                    if not plant_part_id:
                        logger.warning(f"Skipping product {clean_name} - invalid plant part")
                        continue
                    
                    industry_sub_category_id = await self._get_industry_subcategory_id(
                        product['industry'], 
                        product.get('sub_industry')
                    )
                    
                    # Handle primary company if exists
                    primary_company_id = None
                    if companies:
                        company_id = await self._get_or_create_company(companies[0])
                        if company_id:
                            primary_company_id = company_id
                    
                    # Map to database schema
                    db_product = {
                        'name': clean_name,
                        'description': product['description'],
                        'plant_part_id': plant_part_id,
                        'industry_sub_category_id': industry_sub_category_id,
                        'primary_company_id': primary_company_id,
                        'benefits_advantages': product.get('benefits_advantages', []),
                        'sustainability_aspects': product.get('sustainability_aspects', []),
                        'technical_specifications': product.get('technical_specifications', {}),
                        'commercialization_stage': product.get('commercialization_stage', 'R&D'),
                        'keywords': self._generate_keywords(product),
                        'data_sources': {
                            'source': product.get('data_source', ''),
                            'url': product.get('source_url', ''),
                            'discovered_date': product.get('discovered_date', datetime.now().isoformat()),
                            'ai_extracted_companies': companies
                        }
                    }
                    
                    # Insert product
                    result = await self.supabase.table('uses_products').insert(db_product).execute()
                    
                    if result.data:
                        saved_count += 1
                        product_id = result.data[0]['id']
                        logger.info(f"Saved new product: {clean_name}")
                        
                        # Create company relationships
                        if companies:
                            company_ids = []
                            for company_name in companies:
                                company_id = await self._get_or_create_company(company_name)
                                if company_id:
                                    company_ids.append(company_id)
                            
                            if company_ids:
                                await self._link_product_to_companies(product_id, company_ids)
                else:
                    logger.info(f"Product already exists: {clean_name}")
                    
                    # Still check if we need to add company relationships
                    if companies and existing.data:
                        product_id = existing.data[0]['id']
                        for company_name in companies:
                            company_id = await self._get_or_create_company(company_name)
                            if company_id:
                                await self._link_product_to_companies(product_id, [company_id])
                    
            except Exception as e:
                logger.error(f"Error saving product: {e}")
                continue
        
        return saved_count
    
    async def discover_products_with_companies(self, params: Dict = None) -> Dict[str, Any]:
        """Enhanced product discovery that extracts companies"""
        params = params or {}
        
        logger.info("Starting enhanced product discovery with company extraction")
        
        # Run the standard discovery
        result = await self.discover_products(params)
        
        # Log company extraction stats
        if result['success']:
            companies_created = await self._get_company_stats()
            result['companies_discovered'] = companies_created
            logger.info(f"Discovered {companies_created} new companies")
        
        return result
    
    async def _get_company_stats(self) -> int:
        """Get count of recently created companies"""
        try:
            # Get companies created in last hour
            from datetime import timedelta
            one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()
            
            result = await self.supabase.table('hemp_companies')\
                .select('count', count='exact')\
                .gte('created_at', one_hour_ago)\
                .execute()
            
            return result.count or 0
        except:
            return 0