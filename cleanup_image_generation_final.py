#!/usr/bin/env python3
"""
Final cleanup for image generation tables
This script removes all entries from image generation tables and optionally drops them
"""

import os
import sys
from datetime import datetime
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def cleanup_image_generation_tables(drop_tables=False):
    """Clean up image generation tables"""
    
    # Initialize Supabase client
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials")
        return
    
    supabase: Client = create_client(supabase_url, supabase_key)
    
    print(f"🧹 Starting image generation cleanup at {datetime.now()}")
    
    try:
        # Get current counts
        queue_count = supabase.table('image_generation_queue').select('id', count='exact').execute()
        history_count = supabase.table('image_generation_history').select('id', count='exact').execute()
        
        print(f"\n📊 Current Status:")
        print(f"   - Queue entries: {queue_count.count}")
        print(f"   - History entries: {history_count.count}")
        
        if queue_count.count == 0 and history_count.count == 0:
            print("\n✅ Tables are already empty!")
            return
        
        # Confirm deletion
        confirm = input("\n⚠️  Delete all entries from image generation tables? (yes/no): ")
        if confirm.lower() != 'yes':
            print("❌ Cleanup cancelled")
            return
        
        # Delete all entries from queue
        if queue_count.count > 0:
            print("\n🗑️  Deleting queue entries...")
            # Delete in batches to avoid timeout
            deleted = 0
            while deleted < queue_count.count:
                result = supabase.table('image_generation_queue').delete().limit(1000).execute()
                if result.data:
                    deleted += len(result.data)
                    print(f"   Deleted {deleted}/{queue_count.count} queue entries...")
                else:
                    break
        
        # Delete all entries from history
        if history_count.count > 0:
            print("\n🗑️  Deleting history entries...")
            # Delete in batches to avoid timeout
            deleted = 0
            while deleted < history_count.count:
                result = supabase.table('image_generation_history').delete().limit(1000).execute()
                if result.data:
                    deleted += len(result.data)
                    print(f"   Deleted {deleted}/{history_count.count} history entries...")
                else:
                    break
        
        print("\n✅ Successfully cleaned up image generation tables!")
        
        if drop_tables:
            confirm_drop = input("\n⚠️  Drop the image generation tables completely? (yes/no): ")
            if confirm_drop.lower() == 'yes':
                print("\n📝 To drop tables, run these SQL commands in Supabase SQL editor:")
                print("   DROP TABLE IF EXISTS image_generation_queue CASCADE;")
                print("   DROP TABLE IF EXISTS image_generation_history CASCADE;")
                print("   DROP TABLE IF EXISTS ai_provider_config CASCADE;")
                print("   DROP TABLE IF EXISTS ai_provider_usage CASCADE;")
        
    except Exception as e:
        print(f"\n❌ Error during cleanup: {e}")
        return


def check_product_images():
    """Check status of product images"""
    
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials")
        return
    
    supabase: Client = create_client(supabase_url, supabase_key)
    
    print("\n📸 Checking product image status...")
    
    try:
        # Get counts
        total = supabase.table('uses_products').select('id', count='exact').execute()
        no_image = supabase.table('uses_products').select('id', count='exact').or_('image_url.is.null,image_url.eq.').execute()
        placeholder = supabase.table('uses_products').select('id', count='exact').like('image_url', '%placeholder%').execute()
        
        print(f"\n📊 Product Image Statistics:")
        print(f"   - Total products: {total.count}")
        print(f"   - Products without images: {no_image.count}")
        print(f"   - Products with placeholder images: {placeholder.count}")
        print(f"   - Products with real images: {total.count - no_image.count - placeholder.count}")
        
    except Exception as e:
        print(f"\n❌ Error checking images: {e}")


if __name__ == "__main__":
    print("🚫 IMAGE GENERATION CLEANUP SCRIPT")
    print("==================================")
    print("\nThis script will remove all entries from:")
    print("  - image_generation_queue")
    print("  - image_generation_history")
    print("\nThis action cannot be undone!")
    
    # Check product images first
    check_product_images()
    
    # Run cleanup
    cleanup_image_generation_tables(drop_tables=False)
    
    print("\n✅ Cleanup complete!")
    print("\n📝 Next steps:")
    print("1. Commit these changes: git add -A && git commit -m 'Remove image generation system'")
    print("2. Push to GitHub: git push origin main")
    print("3. Image generation is now completely disabled")
    print("4. Products can still have images added manually if needed")