#!/usr/bin/env python3
"""
Save research entries using service role key (bypasses RLS)
Use this for administrative tasks like bulk imports
"""

import os
from datetime import datetime
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_supabase_admin() -> Client:
    """Get Supabase client with service role key (bypasses RLS)"""
    url = os.getenv("SUPABASE_URL")
    # Try service role key first, fall back to anon key
    key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    if not url or not key:
        raise ValueError("Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in .env file")
    
    if "service_role" not in key and "anon" not in key:
        print("⚠️  Using anon key - may be subject to RLS policies")
    else:
        print("✅ Using service role key - bypassing RLS")
    
    return create_client(url, key)

def save_hemp_articles_as_research(articles):
    """Save hemp articles to research_entries table"""
    print("\nSaving Hemp Articles to Research Database")
    print("=" * 50)
    
    # Get Supabase client
    supabase = get_supabase_admin()
    
    saved_count = 0
    
    for article in articles:
        try:
            # Check if already exists
            existing = supabase.table('research_entries').select('id').eq(
                'title', article['title']
            ).execute()
            
            if not existing.data:
                # Add timestamps
                article['created_at'] = datetime.now().isoformat()
                article['updated_at'] = datetime.now().isoformat()
                
                # Save to database
                result = supabase.table('research_entries').insert(article).execute()
                
                if result.data:
                    saved_count += 1
                    print(f"✅ Saved: {article['title'][:60]}...")
                else:
                    print(f"❌ Failed to save: {article['title']}")
            else:
                print(f"⏭️  Already exists: {article['title'][:60]}...")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            print(f"   Article: {article['title']}")
    
    return saved_count

# Example usage
if __name__ == "__main__":
    # Sample articles
    articles = [
        {
            'title': 'New Hemp Processing Facility Opens in Colorado',
            'authors_or_assignees': ['Colorado Hemp News'],
            'abstract_summary': 'A state-of-the-art hemp processing facility has opened in Colorado, capable of processing 10,000 acres of hemp annually.',
            'publication_or_filing_date': '2024-01-25',
            'journal_or_office': 'Hemp Industry Daily',
            'full_text_url': 'https://example.com/colorado-hemp-facility',
            'entry_type': 'Article',
            'keywords': ['hemp', 'processing', 'colorado', 'facility']
        },
        {
            'title': 'Hemp Textile Innovation Wins Sustainability Award',
            'authors_or_assignees': ['Sustainable Fashion Weekly'],
            'abstract_summary': 'Revolutionary hemp textile processing method reduces water usage by 80% compared to cotton.',
            'publication_or_filing_date': '2024-01-22',
            'journal_or_office': 'Textile Innovation Journal',
            'full_text_url': 'https://example.com/hemp-textile-award',
            'entry_type': 'Article',
            'keywords': ['hemp', 'textile', 'sustainability', 'innovation']
        }
    ]
    
    # Get reference IDs (simplified for demo)
    supabase = get_supabase_admin()
    
    # Get first plant type
    plant_types = supabase.table('hemp_plant_archetypes').select('id').limit(1).execute()
    plant_type_id = plant_types.data[0]['id'] if plant_types.data else None
    
    # Add IDs to articles
    for article in articles:
        article['plant_type_id'] = plant_type_id
    
    # Save articles
    saved = save_hemp_articles_as_research(articles)
    
    print(f"\nTotal saved: {saved}")
    
    # Show all entries
    print("\nAll research entries:")
    all_entries = supabase.table('research_entries').select(
        'id, title, entry_type, publication_or_filing_date'
    ).order('publication_or_filing_date', desc=True).limit(10).execute()
    
    for entry in all_entries.data:
        print(f"- [{entry['entry_type']}] {entry['title'][:50]}... ({entry['publication_or_filing_date']})")