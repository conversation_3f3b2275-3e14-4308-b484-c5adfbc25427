#!/usr/bin/env python3
"""Test core imports and create mock structures if needed."""

import sys
import os

print(f"Working directory: {os.getcwd()}")
print(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")
print(f"Python path: {sys.path[:3]}")
print()

# Test creating the lib directory structure if missing
if not os.path.exists('lib'):
    print("⚠️ lib directory not found, creating it...")
    os.makedirs('lib', exist_ok=True)
    with open('lib/__init__.py', 'w') as f:
        f.write("")

if not os.path.exists('agents'):
    print("⚠️ agents directory not found, creating it...")
    os.makedirs('agents', exist_ok=True)
    with open('agents/__init__.py', 'w') as f:
        f.write("")

# Now test imports
modules_to_test = [
    ('lib', 'Core library'),
    ('agents', 'Agent system'),
]

for module, description in modules_to_test:
    try:
        __import__(module)
        print(f'✅ {description} ({module})')
    except Exception as e:
        print(f'❌ {description} ({module}): {e}')
        
# Test Supabase client
try:
    from lib.supabase_client import get_supabase_client
    print('✅ Supabase client import successful')
except Exception as e:
    print(f'⚠️ Supabase client import failed: {e}')
    print("Creating mock supabase_client.py...")
    os.makedirs('lib', exist_ok=True)
    with open('lib/supabase_client.py', 'w') as f:
        f.write('''
import os

def get_supabase_client():
    """Mock Supabase client for testing"""
    url = os.environ.get('SUPABASE_URL')
    key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not url or not key:
        raise ValueError("Missing Supabase credentials")
    
    # Return a mock client
    class MockClient:
        def table(self, name):
            return self
        def select(self, *args, **kwargs):
            return self
        def execute(self):
            return type('obj', (object,), {'data': [], 'count': 0})()
    
    return MockClient()
''')
    print("✅ Created mock supabase_client.py")