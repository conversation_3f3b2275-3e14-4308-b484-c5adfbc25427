#!/usr/bin/env python3
"""
Run the enhanced research agent that extracts company information
"""

import os
import sys
import asyncio
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

async def run_enhanced_discovery():
    """Run enhanced product discovery with company extraction"""
    print("🤖 Enhanced Hemp Product Discovery with Company Extraction")
    print("=" * 60)
    
    try:
        # Import after env setup
        from supabase import create_client
        from agents.research.enhanced_research_agent import EnhancedHempResearchAgent
        
        # Initialize Supabase
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_ANON_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ Missing Supabase credentials!")
            print("Please check your .env file")
            return
        
        print(f"📡 Connecting to Supabase...")
        supabase = create_client(supabase_url, supabase_key)
        
        # Test connection
        try:
            test = supabase.table('uses_products').select('count', count='exact').execute()
            print(f"✅ Connected! Current products: {test.count}")
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return
        
        # Initialize agent
        print("\n🤖 Initializing Enhanced Research Agent...")
        agent = EnhancedHempResearchAgent(supabase)
        
        # Configure discovery parameters
        params = {
            'limit': 10,  # Start small
            'categories': ['all'],
            'sources': [
                {
                    'name': 'Hemp Industry Daily',
                    'url': 'https://hempindustrydaily.com/feed/',
                    'type': 'rss'
                },
                {
                    'name': 'Hemp Business Journal',
                    'url': 'https://www.hempbizjournal.com/feed/',
                    'type': 'rss'
                }
            ]
        }
        
        print("\n🔍 Starting product discovery...")
        print(f"   - Limit: {params['limit']} products")
        print(f"   - Categories: {params['categories']}")
        print(f"   - Sources: {len(params.get('sources', []))} configured")
        
        # Run discovery
        start_time = datetime.now()
        result = await agent.discover_products_with_companies(params)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Show results
        print(f"\n📊 Discovery Results:")
        print(f"   - Success: {result.get('success', False)}")
        print(f"   - Products discovered: {result.get('products_discovered', 0)}")
        print(f"   - Companies discovered: {result.get('companies_discovered', 0)}")
        print(f"   - Duration: {duration:.2f} seconds")
        
        if result.get('error'):
            print(f"   - Error: {result['error']}")
        
        # Show sample products with companies
        if result.get('success'):
            print("\n📦 Sample Products with Companies:")
            
            sample = supabase.table('products_with_companies')\
                .select('product_name, companies')\
                .not_.is_('companies', 'null')\
                .order('id', desc=True)\
                .limit(5)\
                .execute()
            
            if sample.data:
                for item in sample.data:
                    print(f"   - {item['product_name']}")
                    if item['companies']:
                        print(f"     Companies: {item['companies']}")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\nMake sure you have all dependencies installed:")
        print("pip install openai supabase aiohttp beautifulsoup4")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def show_company_stats():
    """Show current company statistics"""
    from supabase import create_client
    
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("\n📊 Current Company Statistics:")
    
    # Total companies
    total = supabase.table('hemp_companies').select('count', count='exact').execute()
    print(f"   - Total companies: {total.count}")
    
    # By verification status
    verified = supabase.table('hemp_companies')\
        .select('count', count='exact')\
        .eq('verified', True)\
        .execute()
    unverified = supabase.table('hemp_companies')\
        .select('count', count='exact')\
        .eq('verified', False)\
        .execute()
    
    print(f"   - Verified: {verified.count}")
    print(f"   - Unverified (AI): {unverified.count}")
    
    # Products with companies
    with_companies = supabase.table('products_with_companies')\
        .select('count', count='exact')\
        .gt('company_count', 0)\
        .execute()
    
    total_products = supabase.table('uses_products')\
        .select('count', count='exact')\
        .execute()
    
    print(f"   - Products with companies: {with_companies.count}/{total_products.count}")
    
    # Recent companies
    print("\n🏢 Recent Companies:")
    recent = supabase.table('hemp_companies')\
        .select('name, verified, created_at')\
        .order('created_at', desc=True)\
        .limit(5)\
        .execute()
    
    for company in recent.data:
        verified = "✓" if company.get('verified') else "?"
        print(f"   - {company['name']} [{verified}]")

def main():
    """Main entry point"""
    print("🚀 Enhanced Hemp Research Agent Runner")
    print("=" * 60)
    
    # Check OpenAI API key
    if not os.environ.get('OPENAI_API_KEY'):
        print("⚠️  Warning: OPENAI_API_KEY not found!")
        print("The agent may not work without it.")
        print("\nTrying alternative methods...")
    
    # Create event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # Run discovery
        loop.run_until_complete(run_enhanced_discovery())
        
        # Show stats
        loop.run_until_complete(show_company_stats())
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Interrupted by user")
    finally:
        loop.close()
    
    print("\n✅ Done!")

if __name__ == "__main__":
    main()