#!/usr/bin/env python3
"""
Check products in the database
"""

import os
from supabase import create_client

# Load .env file
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                if '=' in line:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

# Connect to Supabase
url = os.environ.get('SUPABASE_URL')
key = os.environ.get('SUPABASE_ANON_KEY')

if not url or not key:
    print("Error: Missing Supabase credentials")
    exit(1)

try:
    supabase = create_client(url, key)
    
    # Check products count
    result = supabase.table('uses_products').select('*', count='exact').execute()
    print(f"\n=== Products in Database ===")
    print(f"Total products: {result.count}")
    
    if result.count > 0:
        # Show first 5 products
        products = supabase.table('uses_products').select('*').limit(5).execute()
        print("\nFirst 5 products:")
        for i, product in enumerate(products.data, 1):
            print(f"\n{i}. {product.get('name', 'No name')}")
            print(f"   ID: {product.get('id')}")
            print(f"   Plant Part ID: {product.get('plant_part_id')}")
            print(f"   Industry Sub Category ID: {product.get('industry_sub_category_id')}")
            print(f"   Description: {product.get('description', 'No description')[:100]}...")
    
    # Check plant parts
    plant_parts = supabase.table('plant_parts').select('*').execute()
    print(f"\n=== Plant Parts ===")
    print(f"Total plant parts: {len(plant_parts.data)}")
    for part in plant_parts.data:
        print(f"- {part['name']} (ID: {part['id']})")
    
    # Check industries
    industries = supabase.table('industries').select('*').execute()
    print(f"\n=== Industries ===")
    print(f"Total industries: {len(industries.data)}")
    for industry in industries.data:
        print(f"- {industry['name']} (ID: {industry['id']})")
        
    # Check plant types
    plant_types = supabase.table('hemp_plant_archetypes').select('*').execute()
    print(f"\n=== Plant Types ===")
    print(f"Total plant types: {len(plant_types.data)}")
    for pt in plant_types.data:
        print(f"- {pt['name']} (ID: {pt['id']})")
    
except Exception as e:
    print(f"Error: {e}")