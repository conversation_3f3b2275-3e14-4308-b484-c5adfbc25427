-- Add keywords column to uses_products table
ALTER TABLE public.uses_products 
ADD COLUMN IF NOT EXISTS keywords TEXT[];

-- Drop the existing search_vector column and its index
DROP INDEX IF EXISTS uses_products_search_idx;
ALTER TABLE public.uses_products DROP COLUMN IF EXISTS search_vector;

-- Recreate search_vector with keywords included
ALTER TABLE public.uses_products 
ADD COLUMN search_vector TSVECTOR 
GENERATED ALWAYS AS (
    to_tsvector('english', 
        coalesce(name, '') || ' ' || 
        coalesce(description, '') || ' ' || 
        coalesce(array_to_string(keywords, ' '), '')
    )
) STORED;

-- Recreate the search index
CREATE INDEX uses_products_search_idx ON public.uses_products USING GIN (search_vector);

-- Update any existing rows to have an empty keywords array if NULL
UPDATE public.uses_products 
SET keywords = '{}' 
WHERE keywords IS NULL;