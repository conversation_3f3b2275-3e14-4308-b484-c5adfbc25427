import { useEffect, useRef } from 'react';
import { HempCompany } from '@/hooks/use-companies';

interface HempCompaniesMapProps {
  companies: HempCompany[];
  onCompanyClick?: (company: HempCompany) => void;
}

export function HempCompaniesMap({ companies, onCompanyClick }: HempCompaniesMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // For now, let's create a simple placeholder that shows the companies
    // This demonstrates the data is available and the component is mounting
    const container = mapRef.current;
    
    // Clear previous content
    container.innerHTML = '';

    // Create a simple visualization
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '600');
    svg.style.background = '#111827';
    
    // Add a world map background (simplified)
    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    rect.setAttribute('width', '100%');
    rect.setAttribute('height', '100%');
    rect.setAttribute('fill', '#0a0a0a');
    svg.appendChild(rect);

    // Add title
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', '50%');
    text.setAttribute('y', '50');
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('fill', '#10b981');
    text.setAttribute('font-size', '24');
    text.setAttribute('font-weight', 'bold');
    text.textContent = 'Hemp Companies Global Map';
    svg.appendChild(text);

    // Add company count
    const countText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    countText.setAttribute('x', '50%');
    countText.setAttribute('y', '100');
    countText.setAttribute('text-anchor', 'middle');
    countText.setAttribute('fill', '#9ca3af');
    countText.setAttribute('font-size', '16');
    countText.textContent = `${companies.filter(c => c.latitude && c.longitude).length} companies with location data`;
    svg.appendChild(countText);

    // Add some sample points for companies
    const companiesWithLocation = companies.filter(c => c.latitude && c.longitude);
    companiesWithLocation.slice(0, 20).forEach((company, index) => {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      
      // Convert lat/lng to simple x/y (very simplified projection)
      const x = ((company.longitude! + 180) / 360) * container.offsetWidth;
      const y = ((90 - company.latitude!) / 180) * 600;
      
      circle.setAttribute('cx', x.toString());
      circle.setAttribute('cy', y.toString());
      circle.setAttribute('r', '8');
      circle.setAttribute('fill', getCompanyColor(company.company_type));
      circle.setAttribute('fill-opacity', '0.8');
      circle.style.cursor = 'pointer';
      
      // Add click handler
      circle.addEventListener('click', () => {
        if (onCompanyClick) {
          onCompanyClick(company);
        }
      });
      
      // Add tooltip
      const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
      title.textContent = `${company.name} (${company.city}, ${company.country})`;
      circle.appendChild(title);
      
      svg.appendChild(circle);
    });

    container.appendChild(svg);
  }, [companies, onCompanyClick]);

  function getCompanyColor(type?: string): string {
    switch (type) {
      case 'manufacturer': return '#3B82F6';
      case 'distributor': return '#10B981';
      case 'retailer': return '#8B5CF6';
      case 'brand': return '#F97316';
      default: return '#FFD700';
    }
  }

  return (
    <div className="relative w-full">
      <div ref={mapRef} className="w-full" />
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-black/80 backdrop-blur-sm rounded-lg p-4 border border-gray-700">
        <h4 className="text-sm font-semibold text-gray-100 mb-2">Company Types</h4>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-xs text-gray-300">Manufacturer</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-xs text-gray-300">Distributor</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-purple-500"></div>
            <span className="text-xs text-gray-300">Retailer</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
            <span className="text-xs text-gray-300">Brand</span>
          </div>
        </div>
      </div>

      {/* Info */}
      <div className="absolute top-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-700">
        <p className="text-xs text-gray-400">
          This is a simplified 2D map view. 3D globe visualization coming soon.
        </p>
      </div>
    </div>
  );
}