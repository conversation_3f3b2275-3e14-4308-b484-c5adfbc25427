# GitHub Actions Migration Guide

This guide explains the consolidation of GitHub Actions workflows from 5 separate files into 2 comprehensive workflows.

## Overview

### Before (5 workflows):
1. `hemp-automation.yml` - Agent runs on various schedules
2. `image-generation.yml` - Image generation tasks
3. `status-check.yml` - System status checks
4. `weekly-summary.yml` - Weekly reports
5. `monitoring.yml` - Monitoring and alerts

### After (2 workflows):
1. **`automated-operations.yml`** - All agent runs and image generation
2. **`monitoring-and-reporting.yml`** - All monitoring, health checks, and reporting

## Benefits of Consolidation

1. **Simplified Management**
   - 2 files instead of 5
   - Clear separation of concerns (operations vs monitoring)
   - Easier to understand workflow dependencies

2. **Better Resource Usage**
   - Shared job dependencies reduce redundant runs
   - Caching between related jobs
   - Parallel execution where appropriate

3. **Improved Scheduling**
   - All schedules in one place per workflow type
   - No conflicting schedules
   - Easier to adjust timing

4. **Enhanced Features**
   - Manual triggers with more options
   - Better error handling and retries
   - Consolidated reporting

## Workflow Details

### 1. Automated Operations (`automated-operations.yml`)

**Purpose**: Runs all agents and handles image generation

**Schedule**:
- High priority (research): Every 6 hours
- Medium priority (content, SEO): Every 12 hours  
- Daily (outreach, monetization): Once per day
- Low priority (compliance): Every 2 days

**Key Features**:
- Dynamic agent selection based on schedule
- Parallel agent execution (max 3)
- Automatic image generation after research
- Consolidated reporting
- Manual trigger with custom options

**Jobs**:
1. `determine-agents` - Decides which agents to run
2. `run-agents` - Executes agents in parallel
3. `image-generation` - Generates images for new products
4. `consolidate-results` - Creates summary report

### 2. Monitoring and Reporting (`monitoring-and-reporting.yml`)

**Purpose**: System health monitoring and report generation

**Schedule**:
- Health checks: Every hour
- Daily summary: 8 AM UTC daily
- Weekly report: Mondays at 8 AM UTC

**Key Features**:
- Automatic alert creation as GitHub issues
- Multiple report formats
- Configurable alert levels
- Historical metrics tracking

**Jobs**:
1. `health-check` - Hourly system health monitoring
2. `daily-summary` - Comprehensive daily reports
3. `weekly-report` - Weekly performance analysis
4. `alert-management` - Creates issues for critical alerts
5. `create-summary` - Workflow summary generation

## Migration Steps

### 1. Test New Workflows

Before removing old workflows:

```bash
# Test automated operations
gh workflow run automated-operations.yml \
  --field agent_type=research \
  --field task_priority=high \
  --field include_images=true

# Test monitoring
gh workflow run monitoring-and-reporting.yml \
  --field report_type=health \
  --field alert_level=warning \
  --field create_issues=false
```

### 2. Update Secrets

Ensure all required secrets are set:
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `OPENAI_API_KEY`

### 3. Disable Old Workflows

```bash
# Disable old workflows via GitHub UI
# Settings → Actions → Disable individual workflows

# Or rename them to .yml.old
mv hemp-automation.yml hemp-automation.yml.old
mv image-generation.yml image-generation.yml.old
mv status-check.yml status-check.yml.old
mv weekly-summary.yml weekly-summary.yml.old
```

### 4. Monitor New Workflows

Watch the first few runs:
- Check Actions tab for successful runs
- Verify artifacts are created
- Ensure schedules trigger correctly
- Monitor issue creation for alerts

### 5. Clean Up

After confirming everything works:
```bash
# Remove old workflow files
rm *.yml.old

# Update documentation
# Update any references to old workflow names
```

## Workflow Customization

### Adjusting Schedules

Edit the `cron` expressions in the `on.schedule` section:

```yaml
schedule:
  # Change to every 4 hours
  - cron: '0 */4 * * *'
```

### Adding New Agents

In `automated-operations.yml`, add to the agent selection logic:

```yaml
"new-agent")
  TASK="New agent task description"
  FEATURES="--features specific to agent"
  ;;
```

### Customizing Alerts

In `monitoring-and-reporting.yml`, adjust alert thresholds:

```yaml
env:
  ALERT_SEVERITY: "error,critical"  # Only high-severity alerts
```

### Report Formats

Modify report generation in the respective jobs:
- Daily summary format in `daily-summary` job
- Weekly report format in `weekly-report` job

## Troubleshooting

### Workflows Not Triggering

1. Check schedule syntax:
   ```bash
   # Validate cron expression
   # Use https://crontab.guru/
   ```

2. Verify workflow is enabled:
   - Go to Actions tab
   - Check workflow is not disabled

3. Check for syntax errors:
   ```bash
   # Validate YAML
   yamllint .github/workflows/*.yml
   ```

### Agent Failures

1. Check job logs in Actions tab
2. Verify secrets are set correctly
3. Run manually with debug:
   ```yaml
   env:
     ACTIONS_RUNNER_DEBUG: true
   ```

### Missing Artifacts

1. Ensure upload steps complete
2. Check retention settings
3. Verify artifact names are unique

## Monitoring Workflow Performance

### Via GitHub UI
- Actions tab → Workflow runs
- Check duration, success rate
- View job dependencies

### Via CLI
```bash
# List recent runs
gh run list --workflow=automated-operations.yml

# View specific run
gh run view <run-id>

# Download artifacts
gh run download <run-id>
```

### Via Monitoring Dashboard
```bash
# Check workflow metrics
./hemp monitor --format report | grep "github_actions"
```

## Best Practices

1. **Regular Reviews**
   - Check workflow performance weekly
   - Adjust schedules based on needs
   - Update agent tasks as needed

2. **Cost Management**
   - Monitor GitHub Actions minutes
   - Use concurrency limits
   - Cache dependencies

3. **Error Handling**
   - Set up notifications for failures
   - Create issues for critical problems
   - Regular artifact cleanup

4. **Security**
   - Rotate secrets regularly
   - Use environment protection rules
   - Limit workflow permissions

## Rollback Plan

If issues occur, you can quickly rollback:

1. Re-enable old workflows
2. Disable new workflows
3. Monitor system health
4. Fix issues and retry migration

The consolidated workflows provide better organization, improved performance, and easier maintenance while preserving all original functionality.