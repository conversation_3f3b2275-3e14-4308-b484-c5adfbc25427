<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Particle Background Demo</title>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/framer-motion@11/dist/framer-motion.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { margin: 0; overflow: hidden; }
    </style>
</head>
<body>
    <div id="root"></div>
    <script type="text/babel" data-presets="react">
        const { motion } = window["framer-motion"];
        const { useState, useEffect } = React;

        function ModernParticleBackground() {
            const [particles, setParticles] = useState([]);

            useEffect(() => {
                const hempChars = ['0', '1', 'H', 'E', 'M', 'P', '△', '◇', '○'];
                const particleCount = 40;
                
                const newParticles = Array.from({ length: particleCount }, (_, i) => ({
                    id: i,
                    x: Math.random() * 100,
                    y: Math.random() * 120 - 20,
                    size: Math.random() * 3 + 1,
                    duration: Math.random() * 20 + 15,
                    delay: Math.random() * 10,
                    char: hempChars[Math.floor(Math.random() * hempChars.length)]
                }));
                
                setParticles(newParticles);
            }, []);

            return (
                <div className="fixed inset-0 -z-10 overflow-hidden bg-black">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-950/20 via-black to-emerald-950/10" />
                    
                    <div className="absolute inset-0">
                        <div className="absolute top-0 left-1/4 w-96 h-96 bg-green-900/5 rounded-full blur-3xl" />
                        <div className="absolute bottom-0 right-1/3 w-[600px] h-[600px] bg-emerald-900/5 rounded-full blur-3xl" />
                    </div>

                    {particles.map((particle) => (
                        <motion.div
                            key={particle.id}
                            className="absolute font-mono text-green-500/30 select-none pointer-events-none"
                            style={{
                                left: `${particle.x}%`,
                                fontSize: `${particle.size * 8 + 10}px`,
                                textShadow: '0 0 10px currentColor',
                                filter: 'blur(0.5px)',
                            }}
                            initial={{
                                y: `${particle.y}vh`,
                                opacity: 0,
                                rotate: Math.random() * 360,
                            }}
                            animate={{
                                y: [`${particle.y}vh`, `${particle.y - 150}vh`],
                                opacity: [0, 0.3, 0.3, 0],
                                rotate: [0, Math.random() * 180 - 90],
                            }}
                            transition={{
                                duration: particle.duration,
                                repeat: Infinity,
                                ease: "linear",
                                delay: particle.delay,
                                times: [0, 0.1, 0.9, 1],
                            }}
                        >
                            {particle.char}
                        </motion.div>
                    ))}

                    {Array.from({ length: 8 }).map((_, i) => (
                        <motion.div
                            key={`line-${i}`}
                            className="absolute w-px bg-gradient-to-b from-transparent via-green-500/10 to-transparent"
                            style={{
                                left: `${(i + 1) * 12.5}%`,
                                height: '200px',
                            }}
                            animate={{
                                y: ['-200px', `${window.innerHeight + 200}px`],
                            }}
                            transition={{
                                duration: Math.random() * 10 + 20,
                                repeat: Infinity,
                                ease: "linear",
                                delay: Math.random() * 5,
                            }}
                        />
                    ))}

                    <motion.div
                        className="absolute inset-0 bg-green-500/[0.02]"
                        animate={{
                            opacity: [0.02, 0.05, 0.02],
                        }}
                        transition={{
                            duration: 8,
                            repeat: Infinity,
                            ease: "easeInOut",
                        }}
                    />
                </div>
            );
        }

        function App() {
            return (
                <div className="min-h-screen relative">
                    <ModernParticleBackground />
                    <div className="relative z-10 flex items-center justify-center min-h-screen">
                        <div className="text-center text-white">
                            <h1 className="text-6xl font-bold mb-4">Hemp Intelligence</h1>
                            <p className="text-xl">Modern Particle Background Demo</p>
                            <p className="text-sm mt-4 text-gray-400">
                                Subtle, performant, professional
                            </p>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</body>
</html>