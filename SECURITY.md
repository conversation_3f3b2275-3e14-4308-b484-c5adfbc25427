# Security Policy

## Supported Versions

We release patches for security vulnerabilities. Which versions are eligible for receiving such patches depends on the CVSS v3.0 Rating:

| Version | Supported          |
| ------- | ------------------ |
| latest  | :white_check_mark: |
| < 1.0   | :x:                |

## Reporting a Vulnerability

We take the security of the HempQuarterz Database project seriously. If you believe you have found a security vulnerability, please report it to us as described below.

### How to Report a Security Vulnerability?

**Please do not report security vulnerabilities through public GitHub issues.**

Instead, please report them via email to:
- Primary: <EMAIL> (if available)
- Alternative: Create a private security advisory on GitHub

Please include the following information (as much as you can provide):
- Type of issue (e.g., SQL injection, authentication bypass, data exposure, etc.)
- Full paths of source file(s) related to the manifestation of the issue
- The location of the affected source code (tag/branch/commit or direct URL)
- Any special configuration required to reproduce the issue
- Step-by-step instructions to reproduce the issue
- Proof-of-concept or exploit code (if possible)
- Impact of the issue, including how an attacker might exploit it

### What to Expect

- **Acknowledgment**: We will acknowledge receipt of your vulnerability report within 48 hours
- **Initial Assessment**: Within 7 days, we'll provide an initial assessment and expected timeline
- **Updates**: We'll keep you informed about our progress
- **Resolution**: We aim to resolve critical issues within 30 days
- **Credit**: We'll credit you for the discovery (unless you prefer to remain anonymous)

## Security Best Practices for Users

### Environment Variables
- **Never commit** `.env` files or any files containing secrets
- Use `.env.example` as a template
- Store production secrets in secure environment variable management systems
- Rotate credentials regularly

### Database Security
- Always use strong, unique passwords for database access
- Enable SSL/TLS for database connections in production
- Implement proper access controls and row-level security (RLS) in Supabase
- Regularly backup your database

### API Keys
- Keep Supabase anon keys safe - they are meant to be public but should still be domain-restricted
- Never expose service role keys in client-side code
- Use environment variables for all sensitive configurations
- Implement API rate limiting to prevent abuse

### Dependencies
- Regularly update dependencies to patch known vulnerabilities
- Run `npm audit` periodically to check for vulnerable packages
- Review and understand third-party dependencies before including them

## Security Features in This Project

### Current Security Measures
1. **Environment-based Configuration**: All sensitive data stored in environment variables
2. **Database Security**: PostgreSQL with Supabase's built-in security features
3. **Input Validation**: Parameterized queries to prevent SQL injection
4. **Content Security Policy**: Implemented in the Express server
5. **HTTPS Only**: Enforced in production environments

### Planned Security Enhancements
- [ ] Implement comprehensive API rate limiting
- [ ] Add request validation middleware
- [ ] Enhance CSP policies
- [ ] Add security headers (HSTS, X-Frame-Options, etc.)
- [ ] Implement audit logging
- [ ] Add automated security scanning to CI/CD

## Disclosure Policy

When we receive a security bug report, we will:

1. Confirm the problem and determine the affected versions
2. Audit code to find any similar problems
3. Prepare fixes for all supported versions
4. Release new security fix versions

## Comments on this Policy

If you have suggestions on how this process could be improved, please submit a pull request or open an issue.

## References

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Supabase Security Best Practices](https://supabase.com/docs/guides/auth/security)
- [Node.js Security Best Practices](https://nodejs.org/en/docs/guides/security/)

---

Last Updated: January 2025