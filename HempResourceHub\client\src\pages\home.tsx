import { Helmet } from "react-helmet";
import HomepageHero from "@/components/home/<USER>";
import PlantTypeCards from "@/components/home/<USER>";
import FeaturedProducts from "@/components/home/<USER>";
import StatsCounter from "@/components/home/<USER>";
import { DataVisualizationDashboard } from "@/components/ui/data-visualization-dashboard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const HomePage = () => {
  return (
    <>
      <Helmet>
        <title>HempDB - Interactive Industrial Hemp Applications Database</title>
        <meta
          name="description"
          content="Explore the versatile applications of industrial hemp across industries, plant parts, and product categories with our comprehensive interactive database."
        />
        <meta name="keywords" content="hemp database, industrial hemp, hemp products, hemp applications, sustainable materials, hemp industry, hemp research, eco-friendly products, green technology, hemp innovation" />

        {/* Enhanced Open Graph tags */}
        <meta property="og:title" content="HempDB - Interactive Industrial Hemp Applications Database" />
        <meta property="og:description" content="Explore the versatile applications of industrial hemp across industries, plant parts, and product categories with our comprehensive interactive database." />
        <meta property="og:type" content="website" />
        <meta property="og:site_name" content="HempQuarterz Database" />

        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="HempDB - Interactive Industrial Hemp Applications Database" />
        <meta name="twitter:description" content="Explore the versatile applications of industrial hemp across industries, plant parts, and product categories." />

        {/* Mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
        <meta name="theme-color" content="#22c55e" />
      </Helmet>

      {/* Hero section with stats counters */}
      <HomepageHero />

      {/* Plant type selection cards */}
      <PlantTypeCards />

      {/* Featured Products Section */}
      <FeaturedProducts />

      {/* Enhanced Search and total count section */}
      <StatsCounter />

      {/* Database Insights Section */}
      <section className="py-12 md:py-16 lg:py-20 bg-gray-950/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-4 md:mb-6">
              Database Insights
            </h2>
            <p className="text-base md:text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed">
              Explore real-time analytics and trends in hemp applications across industries and plant parts.
            </p>
          </div>

          <Card className="bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-xl">
            <CardContent className="p-4 md:p-6 lg:p-8">
              <DataVisualizationDashboard />
            </CardContent>
          </Card>
        </div>
      </section>
    </>
  );
};

export default HomePage;
