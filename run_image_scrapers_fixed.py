#!/usr/bin/env python3
"""
Run all image scrapers with URL fixes and attribution
"""
import os
import subprocess
import requests
from dotenv import load_dotenv

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

def main():
    print("🚀 Hemp Resource Hub - Enhanced Image Scraping Pipeline\n")
    
    # Step 1: Fix URLs
    print("🔧 === FIXING URLs ===")
    print("1. Fixing company URLs (adding https://)...")
    subprocess.run(["python", "fix_company_urls.py"])
    print()
    
    print("2. Fixing specific problematic URLs...")
    subprocess.run(["python", "fix_specific_urls.py"])
    print()
    
    # Step 2: Analyze research URLs
    print("📊 === ANALYZING RESEARCH URLs ===")
    subprocess.run(["python", "check_research_urls.py"])
    print()
    
    # Step 3: Fix research image fields
    print("🔧 === FIXING RESEARCH IMAGE FIELDS ===")
    subprocess.run(["python", "fix_research_image_field.py"])
    print()
    
    # Step 4: Run enhanced scrapers
    print("🌐 === RUNNING ENHANCED SCRAPERS ===")
    subprocess.run(["python", "enhanced_scraper_with_attribution.py"])
    print()
    
    print("\n✅ All tasks completed!")
    print("\n📋 Summary:")
    print("1. Fixed company URLs to include https://")
    print("2. Fixed specific problematic URLs")
    print("3. Analyzed research entry sources")
    print("4. Attempted to scrape logos and images")
    print("\n🔍 If no images were found:")
    print("- Research entries might not be from PubMed (check analysis above)")
    print("- Company websites might block scrapers or have unusual logo implementations")
    print("- Consider adding manual image upload functionality")

if __name__ == "__main__":
    main()