# Image Generation Migration Guide

This guide explains how to migrate from the various image generation methods to the centralized Image Generation Service.

## Overview

The new centralized Image Generation Service consolidates all image generation through a single API that routes to the Supabase Edge Function. This provides:

- Single point of control for all image generation
- Consistent error handling and retry logic
- Unified cost tracking and monitoring
- Better provider management
- Simplified integration for all agents and scripts

## Migration Steps

### 1. Python Scripts

#### Old Method (Direct Queue Insertion)
```python
# Old way - directly inserting into queue
supabase.table('hemp_image_queue').insert({
    'product_id': product_id,
    'provider': 'stable-diffusion',
    'status': 'pending',
    'priority': 5
}).execute()
```

#### New Method (Using Service)
```python
from lib.image_generation_service import ImageGenerationService, ImageProvider

# New way - using centralized service
service = ImageGenerationService()
result = await service.generate_for_products(
    [product_id], 
    ImageProvider.STABLE_DIFFUSION
)
```

### 2. Research Agent Integration

#### Old Method (In research_agent_with_images.py)
```python
# Old way - agent queues images directly
async def _queue_image_generation(self, product):
    queue_data = {
        'product_id': product['id'],
        'prompt': self._generate_prompt(product),
        'status': 'pending'
    }
    self.supabase.table('hemp_image_queue').insert(queue_data).execute()
```

#### New Method (In unified_research_agent.py)
```python
# New way - agent uses service
from lib.image_generation_service import ImageGenerationService

async def _queue_image_generation(self, product):
    service = ImageGenerationService(self.supabase)
    await service.generate_image(ImageGenerationRequest(
        product_id=product['id'],
        prompt=self._generate_image_prompt(product),
        provider=self.config.image_provider
    ))
```

### 3. CLI Commands

#### Old Method
```bash
# Old way - various scripts
python trigger_image_generation.py
python complete_all_images.py
node trigger_image_generation.js
```

#### New Method
```bash
# New way - unified CLI
./hemp images generate                          # All products without images
./hemp images generate --products 1 2 3        # Specific products
./hemp images generate --provider dall-e       # Specific provider
./hemp images status                           # Check queue status
./hemp images retry --limit 100                # Retry failed generations
```

### 4. Monitoring

#### Old Method
```python
# Old way - manual database queries
result = supabase.table('image_generation_queue').select('*').eq('status', 'pending').execute()
print(f"Pending: {len(result.data)}")
```

#### New Method
```python
# New way - service provides structured status
service = ImageGenerationService()
status = await service.get_queue_status()
print(f"Pending: {status['queue']['pending']}")
print(f"Provider stats: {status['providers']}")
```

### 5. Admin Dashboard Integration

The React admin dashboard components should be updated to use the service API endpoints:

```typescript
// Old way - direct Supabase queries
const { data } = await supabase
  .from('image_generation_queue')
  .insert({ product_id, provider, status: 'pending' })

// New way - use API endpoint that calls the service
const response = await fetch('/api/images/generate', {
  method: 'POST',
  body: JSON.stringify({ product_ids: [product_id], provider })
})
```

## Benefits of Centralization

### 1. Consistent Provider Management
- All provider selection logic in one place
- Automatic fallback to available providers
- Cost-aware provider selection

### 2. Better Error Handling
- Unified retry logic with exponential backoff
- Consistent error messages and logging
- Automatic fallback to placeholder images

### 3. Improved Monitoring
- Single source of truth for queue status
- Provider performance metrics
- Cost tracking across all generation sources

### 4. Simplified Integration
- One API to learn instead of multiple methods
- Consistent response format
- Built-in batching and optimization

## Deprecation Timeline

1. **Immediate**: Start using centralized service for new code
2. **Phase 1** (2 weeks): Update all active scripts to use service
3. **Phase 2** (1 month): Update agents to use service
4. **Phase 3** (2 months): Remove old generation scripts

## Scripts to Update

The following scripts should be updated or removed:

### To Update:
- `agents/research/research_agent_with_images.py` → Use service in unified agent
- `image_generation/hemp_image_generator.py` → Keep as fallback, update to use service
- `HempResourceHub/client/src/components/admin/image-generation-dashboard.tsx` → Update API calls

### To Remove (After Migration):
- `trigger_image_generation.js` → Replaced by CLI
- `complete_all_images.py` → Replaced by CLI
- `run_generation.py` → Replaced by CLI
- Individual provider scripts → Consolidated in service

## Testing the Migration

1. **Test Service Directly**:
```python
import asyncio
from lib.image_generation_service import generate_image

# Test single image generation
result = asyncio.run(generate_image(product_id=1, provider='placeholder'))
print(result)
```

2. **Test via CLI**:
```bash
# Generate test image
./hemp images generate --products 1 --provider placeholder

# Check status
./hemp images status
```

3. **Verify Edge Function**:
- Check Supabase dashboard for Edge Function logs
- Verify images are uploaded to storage bucket
- Confirm product records are updated

## Troubleshooting

### Common Issues:

1. **"Provider not available"**
   - Check Edge Function secrets for API keys
   - Verify provider is active in `ai_provider_config` table

2. **"Edge Function timeout"**
   - Check Edge Function logs in Supabase dashboard
   - Reduce batch size in service configuration

3. **"Images not updating"**
   - Verify Edge Function has storage bucket permissions
   - Check `image_generation_queue` status

### Debug Commands:
```bash
# Check queue directly
./hemp db export --format json --output debug
# Look at image_generation_queue.json

# Monitor Edge Function logs
# Go to Supabase Dashboard > Functions > hemp-image-generator > Logs
```

## Next Steps

After migrating to the centralized service:

1. Monitor performance metrics via `./hemp images status`
2. Set up alerts for failed generations
3. Configure provider priorities based on cost/quality trade-offs
4. Implement automatic provider rotation for load balancing