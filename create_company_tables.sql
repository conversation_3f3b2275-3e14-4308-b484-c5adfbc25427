-- Create hemp_companies table for tracking brands/manufacturers
CREATE TABLE IF NOT EXISTS hemp_companies (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    description TEXT,
    website VARCHAR(255),
    country VARCHAR(100),
    founded_year INTEGER,
    company_type VARCHAR(50), -- manufacturer, distributor, retailer, brand
    verified BOOLEAN DEFAULT FALSE,
    logo_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create junction table for many-to-many relationship between products and companies
CREATE TABLE IF NOT EXISTS hemp_company_products (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES uses_products(id) ON DELETE CASCADE,
    company_id INTEGER NOT NULL REFERENCES hemp_companies(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) DEFAULT 'manufacturer', -- manufacturer, distributor, retailer
    is_primary BOOLEAN DEFAULT FALSE, -- primary manufacturer/brand
    verified BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, company_id, relationship_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_company_products_product ON hemp_company_products(product_id);
CREATE INDEX IF NOT EXISTS idx_company_products_company ON hemp_company_products(company_id);
CREATE INDEX IF NOT EXISTS idx_companies_name ON hemp_companies(name);

-- Add company_id column to uses_products table (optional - for primary company)
ALTER TABLE uses_products 
ADD COLUMN IF NOT EXISTS primary_company_id INTEGER REFERENCES hemp_companies(id);

-- Create a view to easily see products with their companies
CREATE OR REPLACE VIEW products_with_companies AS
SELECT 
    p.id,
    p.name as product_name,
    p.description,
    pp.name as plant_part,
    i.name as industry,
    p.commercialization_stage,
    string_agg(DISTINCT c.name, ', ' ORDER BY c.name) as companies,
    COUNT(DISTINCT c.id) as company_count
FROM uses_products p
LEFT JOIN plant_parts pp ON p.plant_part_id = pp.id
LEFT JOIN industry_sub_categories isc ON p.industry_sub_category_id = isc.id
LEFT JOIN industries i ON isc.industry_id = i.id
LEFT JOIN hemp_company_products hcp ON p.id = hcp.product_id
LEFT JOIN hemp_companies c ON hcp.company_id = c.id
GROUP BY p.id, p.name, p.description, pp.name, i.name, p.commercialization_stage;

-- Insert some example companies
INSERT INTO hemp_companies (name, description, website, country, company_type, verified) VALUES
('Manitoba Harvest', 'Leading hemp food company specializing in hemp hearts and hemp oil', 'www.manitobaharvest.com', 'Canada', 'manufacturer', true),
('Nutiva', 'Organic superfoods company with extensive hemp product line', 'www.nutiva.com', 'USA', 'manufacturer', true),
('Patagonia', 'Outdoor clothing company using hemp in sustainable apparel', 'www.patagonia.com', 'USA', 'manufacturer', true),
('HempTraders', 'Hemp materials supplier and manufacturer', 'www.hemptraders.com', 'USA', 'distributor', true),
('Good Hemp', 'UK-based hemp food and beverage company', 'www.goodhemp.com', 'UK', 'manufacturer', true)
ON CONFLICT (name) DO NOTHING;