#!/usr/bin/env python3
"""
Test script to verify your Hemp Database setup
Run this locally to ensure everything is configured correctly
"""

import os
import sys
import subprocess

def check_environment():
    """Check if required environment variables are set"""
    print("🔍 Checking environment variables...")
    
    required_vars = ['SUPABASE_URL', 'SUPABASE_ANON_KEY', 'OPENAI_API_KEY']
    missing = []
    
    for var in required_vars:
        if os.getenv(var):
            print(f"✅ {var} is set")
        else:
            print(f"❌ {var} is missing")
            missing.append(var)
    
    if missing:
        print("\n⚠️  Missing environment variables. Create a .env file with:")
        for var in missing:
            print(f"{var}=your_value_here")
        return False
    
    return True

def check_dependencies():
    """Check if all Python dependencies are installed"""
    print("\n🔍 Checking Python dependencies...")
    
    try:
        import supabase
        print("✅ supabase installed")
    except ImportError:
        print("❌ supabase not installed")
        return False
    
    try:
        import openai
        print("✅ openai installed")
    except ImportError:
        print("❌ openai not installed")
        return False
    
    try:
        import langchain
        print("✅ langchain installed")
    except ImportError:
        print("❌ langchain not installed")
        return False
    
    return True

def check_module_imports():
    """Check if local modules can be imported"""
    print("\n🔍 Checking module imports...")
    
    # Add project root to path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from lib.supabase_client import get_supabase_client
        print("✅ Can import supabase_client")
    except ImportError as e:
        print(f"❌ Cannot import supabase_client: {e}")
        return False
    
    try:
        from lib.monitoring_service import MonitoringService
        print("✅ Can import monitoring_service")
    except ImportError as e:
        print(f"❌ Cannot import monitoring_service: {e}")
        return False
    
    try:
        from agents.research.unified_research_agent import create_research_agent
        print("✅ Can import research agent")
    except ImportError as e:
        print(f"❌ Cannot import research agent: {e}")
        return False
    
    return True

def test_hemp_cli():
    """Test if the hemp CLI works"""
    print("\n🔍 Testing hemp CLI...")
    
    try:
        result = subprocess.run(
            ['python', 'hemp', '--help'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Hemp CLI works!")
        else:
            print(f"❌ Hemp CLI failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Hemp CLI error: {e}")
        return False
    
    return True

def test_database_connection():
    """Test database connection"""
    print("\n🔍 Testing database connection...")
    
    try:
        from lib.supabase_client import get_supabase_client
        client = get_supabase_client()
        
        # Try a simple query
        result = client.table('hemp_plant_archetypes').select('id').limit(1).execute()
        print("✅ Database connection successful!")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Hemp Database Setup Test ===\n")
    
    # Load .env file if it exists
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Loaded .env file")
    except ImportError:
        print("⚠️  python-dotenv not installed, skipping .env file")
    
    all_passed = True
    
    # Run all checks
    if not check_environment():
        all_passed = False
    
    if not check_dependencies():
        all_passed = False
        print("\n💡 Install missing dependencies with: pip install -r requirements.txt")
    
    if not check_module_imports():
        all_passed = False
    
    if not test_hemp_cli():
        all_passed = False
    
    if check_environment() and check_dependencies():
        if not test_database_connection():
            all_passed = False
    
    # Summary
    print("\n" + "="*50)
    if all_passed:
        print("✅ All tests passed! Your setup is ready.")
        print("\nYou can now:")
        print("1. Push the fixed workflow to GitHub")
        print("2. Add the required secrets in GitHub Settings")
        print("3. Run the workflow manually to test it")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        print("\nCommon fixes:")
        print("1. Create a .env file with your credentials")
        print("2. Run: pip install -r requirements.txt")
        print("3. Ensure all files are in the correct directories")

if __name__ == "__main__":
    main()