// Find this section (around line 43) and REPLACE the entire PROVIDERS object with this:

const PROVIDERS: { [key: string]: ProviderConfig } = {
  stable_diffusion: {
    name: 'stable_diffusion',
    apiKeyName: 'STABILITY_API_KEY',
    generateImage: generateStableDiffusion
  },
  dall_e_3: {
    name: 'dall_e_3',
    apiKeyName: 'OPENAI_API_KEY',
    generateImage: generateDallE3
  },
  imagen_3: {
    name: 'imagen_3',
    apiKeyName: 'GOOGLE_AI_API_KEY',
    generateImage: generateImagen3
  },
  midjourney: {
    name: 'midjourney',
    apiKeyName: 'MIDJOURNEY_API_KEY',
    generateImage: generateMidjourney
  },
  replicate: {
    name: 'replicate',
    apiKeyName: 'REPLICATE_API_KEY',
    generateImage: generateReplicate
  },
  together_ai: {
    name: 'together_ai', 
    apiKeyName: 'TOGETHER_API_KEY',
    generateImage: generateTogetherAI
  },
  placeholder: {
    name: 'placeholder',
    apiKeyName: '',
    generateImage: generatePlaceholder
  }
};

// Then ADD these two functions at the END of your Edge Function file (before the final closing brace):

async function generateReplicate(prompt: string, config: any): Promise<GenerationResult> {
  const startTime = Date.now();
  const apiKey = Deno.env.get('REPLICATE_API_KEY');
  
  if (!apiKey) {
    throw new Error('Replicate API key not configured');
  }
  
  // Using SDXL Lightning for fast, cheap generation
  const model = config.model || "bytedance/sdxl-lightning-4step:5f24084160c9089501c1b3545d9be3c27883ae2239b6f412990e82d4a6210f8f";
  
  const response = await fetch('https://api.replicate.com/v1/predictions', {
    method: 'POST',
    headers: {
      'Authorization': `Token ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      version: model,
      input: {
        prompt: prompt,
        negative_prompt: "blurry, bad quality, distorted, ugly, text, watermark",
        width: 1024,
        height: 1024,
        num_inference_steps: 4,
        guidance_scale: 0,
        scheduler: "K_EULER"
      }
    }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Replicate API error: ${error.detail || response.statusText}`);
  }
  
  const prediction = await response.json();
  
  // Wait for prediction to complete
  let result = prediction;
  while (result.status !== 'succeeded' && result.status !== 'failed') {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const statusResponse = await fetch(`https://api.replicate.com/v1/predictions/${result.id}`, {
      headers: {
        'Authorization': `Token ${apiKey}`,
      }
    });
    
    result = await statusResponse.json();
  }
  
  if (result.status === 'failed') {
    throw new Error(`Replicate generation failed: ${result.error}`);
  }
  
  return {
    imageUrl: result.output[0],
    cost: 0.00036, // Approximate cost for SDXL Lightning 4-step
    generationTimeMs: Date.now() - startTime,
    metadata: {
      model: 'sdxl-lightning-4step',
      prediction_id: result.id
    }
  };
}

async function generateTogetherAI(prompt: string, config: any): Promise<GenerationResult> {
  const startTime = Date.now();
  const apiKey = Deno.env.get('TOGETHER_API_KEY');
  
  if (!apiKey) {
    throw new Error('Together API key not configured');
  }
  
  const response = await fetch('https://api.together.xyz/v1/images/generations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: config.model || "stabilityai/stable-diffusion-xl-base-1.0",
      prompt: prompt,
      width: 1024,
      height: 1024,
      steps: 20,
      n: 1,
      negative_prompt: "blurry, bad quality, distorted, ugly, text, watermark"
    }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Together AI error: ${error.error?.message || response.statusText}`);
  }
  
  const data = await response.json();
  
  return {
    imageUrl: data.data[0].url,
    cost: 0.0015,
    generationTimeMs: Date.now() - startTime,
    metadata: {
      model: 'stable-diffusion-xl-base-1.0'
    }
  };
}