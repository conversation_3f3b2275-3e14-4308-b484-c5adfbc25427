# Enhanced Authentication Setup Guide

## Overview

This guide covers the setup and configuration of the enhanced authentication system for HempQuarterz, including Google OAuth and Multi-Factor Authentication (MFA).

## Features Implemented

### ✅ **Enhanced Login/Signup Pages**
- Modern, responsive design with hemp branding
- Google OAuth integration
- Multi-step authentication support
- Demo credentials for testing
- Password visibility toggles
- Form validation and error handling

### ✅ **Google OAuth Integration**
- One-click sign-in with Google
- Automatic account creation
- Secure redirect handling
- Profile data synchronization

### ✅ **Multi-Factor Authentication (MFA)**
- TOTP-based 2FA using authenticator apps
- QR code generation for easy setup
- Backup codes for account recovery
- Admin-level security enforcement

### ✅ **Demo User System**
- Pre-configured admin and user accounts
- Easy testing and development
- Automated user creation script

## Quick Start

### 1. Set Up Demo Users

Run the demo user setup script to create test accounts:

```bash
npm run setup:demo-users
```

This creates:
- **Admin**: `<EMAIL>` / `admin123`
- **User**: `<EMAIL>` / `user123`

### 2. Configure Google OAuth (Optional)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:5173/auth/callback` (development)
   - `https://yourdomain.com/auth/callback` (production)
6. Add the credentials to your Supabase dashboard:
   - Go to Authentication > Providers
   - Enable Google provider
   - Add your Client ID and Client Secret

### 3. Test the Authentication

1. Visit `/enhanced-login` for the new login page
2. Visit `/enhanced-register` for the new registration page
3. Use demo credentials or create a new account
4. Test Google OAuth if configured
5. Set up MFA in user settings

## File Structure

```
client/src/
├── components/auth/
│   ├── enhanced-auth-provider.tsx    # Enhanced auth context with OAuth & MFA
│   └── mfa-setup.tsx                 # MFA configuration component
├── pages/
│   ├── enhanced-login.tsx            # New login page with OAuth & MFA
│   ├── enhanced-register.tsx         # New registration page
│   └── auth-callback.tsx             # OAuth redirect handler
└── scripts/
    └── setup-demo-users.js           # Demo user creation script
```

## Environment Variables

Ensure these variables are set in your `.env` file:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key  # For demo user setup

# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

## Authentication Flow

### Standard Email/Password Flow
1. User enters credentials on `/enhanced-login`
2. Supabase validates credentials
3. If MFA enabled, user enters TOTP code
4. User redirected to appropriate dashboard

### Google OAuth Flow
1. User clicks "Continue with Google"
2. Redirected to Google authorization
3. Google redirects to `/auth/callback`
4. Supabase processes OAuth response
5. User redirected to dashboard

### MFA Setup Flow
1. User navigates to account settings
2. Clicks "Enable Two-Factor Authentication"
3. QR code displayed for authenticator app
4. User scans QR code and enters verification code
5. MFA enabled and backup codes generated

## Security Features

### ✅ **Password Security**
- Minimum 8 character requirement
- Password confirmation validation
- Secure password hashing via Supabase

### ✅ **Session Management**
- Automatic session refresh
- Secure token storage
- Session timeout handling

### ✅ **Role-Based Access**
- Admin role detection
- Protected route handling
- Granular permission system

### ✅ **MFA Protection**
- TOTP-based authentication
- Backup code generation
- Device trust management

## Troubleshooting

### Demo Credentials Not Working
1. Run the setup script: `npm run setup:demo-users`
2. Check Supabase dashboard for user creation
3. Verify environment variables are correct
4. Check browser console for errors

### Google OAuth Issues
1. Verify OAuth credentials in Google Console
2. Check redirect URIs match exactly
3. Ensure Google provider is enabled in Supabase
4. Check for popup blockers

### MFA Problems
1. Ensure authenticator app time is synchronized
2. Try generating new QR code
3. Use backup codes if available
4. Contact admin for MFA reset

## Migration from Old Auth

The enhanced authentication system is designed to work alongside the existing auth system:

- Old routes (`/login`, `/register`) still work
- New routes (`/enhanced-login`, `/enhanced-register`) provide enhanced features
- Existing users can continue using their accounts
- Gradual migration to enhanced features

## Next Steps

1. **Test thoroughly** with demo credentials
2. **Configure Google OAuth** for production
3. **Set up MFA policies** for admin users
4. **Update navigation** to use enhanced auth pages
5. **Train users** on new features

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Supabase authentication logs
3. Check browser developer console
4. Contact the development team

---

**Note**: This enhanced authentication system provides enterprise-grade security while maintaining ease of use. All sensitive operations are handled securely through Supabase's proven authentication infrastructure.
