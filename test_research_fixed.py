#!/usr/bin/env python3
"""
Fixed test script for research agent that handles database schema correctly
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from lib.supabase_client import get_supabase_client


async def test_feed_discovery():
    """Test feed discovery directly without the agent"""
    import feedparser
    
    sources = [
        {
            'name': 'EIHA',
            'feeds': ['https://eiha.org/feed/']
        },
        {
            'name': 'Hemp Industry Daily',
            'feeds': ['https://hempindustrydaily.com/feed/']
        },
        {
            'name': 'Vote Hemp',
            'feeds': ['https://votehemp.com/feed/']
        }
    ]
    
    all_items = []
    
    for source in sources:
        for feed_url in source['feeds']:
            logger.info(f"Fetching feed from {source['name']}: {feed_url}")
            try:
                feed = feedparser.parse(feed_url)
                
                if feed.entries:
                    logger.info(f"Found {len(feed.entries)} entries from {source['name']}")
                    
                    # Take first 5 entries
                    for entry in feed.entries[:5]:
                        all_items.append({
                            'title': entry.get('title', 'No title'),
                            'description': entry.get('summary', 'No description'),
                            'url': entry.get('link', 'No URL'),
                            'source': source['name'],
                            'published': entry.get('published', 'Unknown')
                        })
                else:
                    logger.warning(f"No entries found in {source['name']} feed")
                    
            except Exception as e:
                logger.error(f"Error fetching {source['name']} feed: {e}")
    
    return all_items


async def save_test_product():
    """Test saving a product with correct schema"""
    supabase = get_supabase_client()
    
    # Create a test product with proper plant_part_id and industry_sub_category_id
    test_product = {
        'name': 'Hemp Fiber Insulation Boards',
        'description': 'Sustainable building insulation made from hemp fibers with excellent thermal and acoustic properties',
        'plant_part_id': 2,  # Hemp Bast (Fiber)
        'industry_sub_category_id': 1,  # Using first sub-category
        'benefits_advantages': ['Sustainable', 'Excellent insulation', 'Mold resistant', 'Fire retardant'],
        'commercialization_stage': 'Growing',
        'sustainability_aspects': ['Carbon negative', 'Renewable', 'Biodegradable'],
        'data_sources': ['Feed Discovery Test'],
        'image_url': 'https://placeholder.com/hemp-insulation.jpg'
    }
    
    try:
        # First check sub-categories
        logger.info("Checking industry sub-categories...")
        sub_cats = supabase.table('industry_sub_categories').select('id, name, industry_id').limit(10).execute()
        logger.info(f"Sample sub-categories: {sub_cats.data[:3]}")
        
        # Use first sub-category ID
        if sub_cats.data:
            test_product['industry_sub_category_id'] = sub_cats.data[0]['id']
        
        logger.info(f"Attempting to save product: {test_product['name']}")
        result = supabase.table('uses_products').insert(test_product).execute()
        
        if result.data:
            logger.info(f"✅ Successfully saved product! ID: {result.data[0]['id']}")
            return result.data[0]
        else:
            logger.error("Failed to save product - no data returned")
            return None
            
    except Exception as e:
        logger.error(f"Error saving product: {e}")
        return None


async def main():
    """Main test function"""
    logger.info("Starting fixed research test...")
    
    # Test 1: Direct feed discovery
    logger.info("\n=== Testing Feed Discovery ===")
    feed_items = await test_feed_discovery()
    
    if feed_items:
        logger.info(f"\n✅ Found {len(feed_items)} items from feeds")
        for i, item in enumerate(feed_items[:3], 1):
            logger.info(f"\n{i}. {item['title']}")
            logger.info(f"   Source: {item['source']}")
            logger.info(f"   URL: {item['url']}")
    else:
        logger.warning("No items found from feeds")
    
    # Test 2: Save a test product
    logger.info("\n=== Testing Product Save ===")
    saved_product = await save_test_product()
    
    # Test 3: Check database stats
    logger.info("\n=== Database Statistics ===")
    supabase = get_supabase_client()
    
    # Count products
    product_count = supabase.table('uses_products').select('count', count='exact').execute()
    logger.info(f"Total products: {product_count.count}")
    
    # Get recent products
    recent = supabase.table('uses_products').select('id, name, created_at').order('created_at', desc=True).limit(5).execute()
    if recent.data:
        logger.info("\nRecent products:")
        for product in recent.data:
            logger.info(f"  - {product['name']} (ID: {product['id']})")
    
    # Test 4: Manual product creation from feed data
    if feed_items:
        logger.info("\n=== Creating Products from Feed Data ===")
        
        # Map simple keywords to our database IDs
        plant_part_map = {
            'hemp': 2,  # Default to Hemp Bast (Fiber)
            'fiber': 2,  # Hemp Bast (Fiber)
            'seed': 7,   # Hemp Seed
            'oil': 1,    # Cannabinoids (closest match)
            'cbd': 1,    # Cannabinoids
            'flower': 3, # Hemp Flowers
            'hurd': 4,   # Hemp Hurd (Shivs)
            'construction': 4  # Hemp Hurd often used in construction
        }
        
        # Get first industry sub-category as default
        sub_cat_result = supabase.table('industry_sub_categories').select('id').limit(1).execute()
        default_sub_cat_id = sub_cat_result.data[0]['id'] if sub_cat_result.data else 1
        
        created_count = 0
        for item in feed_items[:3]:  # Try first 3 items
            try:
                # Guess plant part from title/description
                text = f"{item['title']} {item['description']}".lower()
                plant_part_id = 2  # Default to fiber
                
                for keyword, part_id in plant_part_map.items():
                    if keyword in text:
                        plant_part_id = part_id
                        break
                
                product_data = {
                    'name': item['title'][:100],  # Limit length
                    'description': item['description'][:500] if item['description'] else 'Hemp product from feed discovery',
                    'plant_part_id': plant_part_id,
                    'industry_sub_category_id': default_sub_cat_id,
                    'commercialization_stage': 'Research',
                    'data_sources': [f"Feed: {item['source']}"],
                    'image_url': f"https://placeholder.com/{item['title'].replace(' ', '-')[:20]}.jpg"
                }
                
                result = supabase.table('uses_products').insert(product_data).execute()
                if result.data:
                    created_count += 1
                    logger.info(f"✅ Created product: {item['title'][:50]}...")
                    
            except Exception as e:
                logger.error(f"Failed to create product from feed item: {e}")
        
        logger.info(f"\n✅ Successfully created {created_count} products from feed data")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()