import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON>rkles, 
  Code2, 
  BarChart3, 
  FileText, 
  Brain,
  Activity,
  Settings
} from 'lucide-react';
import { ProductAssistant } from './product-assistant';
import { CodeGenerator } from './code-generator';
// import { spawnCustomAssistant } from '@/lib/claude-instances'; // Temporarily disabled

export function AIDashboard() {
  const [activeInstances, setActiveInstances] = useState({
    productAnalyzer: true,
    codeGenerator: true,
    dataAnalyst: false,
    contentWriter: false
  });

  const toggleInstance = (instance: keyof typeof activeInstances) => {
    setActiveInstances(prev => ({
      ...prev,
      [instance]: !prev[instance]
    }));
  };

  return (
    <div className="space-y-6">
      {/* Instance Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Claude AI Instances
          </CardTitle>
          <CardDescription>
            Manage and monitor your Claude AI assistants
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Product Analyzer */}
            <div className="border rounded-lg p-4 space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Product Analyzer</h3>
                <Badge variant={activeInstances.productAnalyzer ? "default" : "secondary"}>
                  {activeInstances.productAnalyzer ? "Active" : "Inactive"}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">
                Analyzes hemp products for market potential
              </p>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => toggleInstance('productAnalyzer')}
              >
                <Activity className="h-4 w-4 mr-1" />
                {activeInstances.productAnalyzer ? "Disable" : "Enable"}
              </Button>
            </div>

            {/* Code Generator */}
            <div className="border rounded-lg p-4 space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Code Generator</h3>
                <Badge variant={activeInstances.codeGenerator ? "default" : "secondary"}>
                  {activeInstances.codeGenerator ? "Active" : "Inactive"}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">
                Generates TypeScript/React code
              </p>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => toggleInstance('codeGenerator')}
              >
                <Activity className="h-4 w-4 mr-1" />
                {activeInstances.codeGenerator ? "Disable" : "Enable"}
              </Button>
            </div>

            {/* Data Analyst */}
            <div className="border rounded-lg p-4 space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Data Analyst</h3>
                <Badge variant={activeInstances.dataAnalyst ? "default" : "secondary"}>
                  {activeInstances.dataAnalyst ? "Active" : "Inactive"}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">
                Analyzes trends and patterns
              </p>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => toggleInstance('dataAnalyst')}
              >
                <Activity className="h-4 w-4 mr-1" />
                {activeInstances.dataAnalyst ? "Disable" : "Enable"}
              </Button>
            </div>

            {/* Content Writer */}
            <div className="border rounded-lg p-4 space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Content Writer</h3>
                <Badge variant={activeInstances.contentWriter ? "default" : "secondary"}>
                  {activeInstances.contentWriter ? "Active" : "Inactive"}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">
                Creates SEO-optimized content
              </p>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => toggleInstance('contentWriter')}
              >
                <Activity className="h-4 w-4 mr-1" />
                {activeInstances.contentWriter ? "Disable" : "Enable"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Tools */}
      <Tabs defaultValue="product" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="product" disabled={!activeInstances.productAnalyzer}>
            <Sparkles className="h-4 w-4 mr-2" />
            Products
          </TabsTrigger>
          <TabsTrigger value="code" disabled={!activeInstances.codeGenerator}>
            <Code2 className="h-4 w-4 mr-2" />
            Code
          </TabsTrigger>
          <TabsTrigger value="data" disabled={!activeInstances.dataAnalyst}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="content" disabled={!activeInstances.contentWriter}>
            <FileText className="h-4 w-4 mr-2" />
            Content
          </TabsTrigger>
        </TabsList>

        <TabsContent value="product" className="mt-4">
          {activeInstances.productAnalyzer ? (
            <ProductAssistant />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-gray-500">Product Analyzer is currently disabled</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="code" className="mt-4">
          {activeInstances.codeGenerator ? (
            <CodeGenerator />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-gray-500">Code Generator is currently disabled</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="data" className="mt-4">
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">Data Analyst interface coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="mt-4">
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">Content Writer interface coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Instance Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Instance Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Spawn Custom Instance</h4>
              <p className="text-sm text-gray-600 mb-3">
                Create a specialized Claude instance for a specific task
              </p>
              <Button variant="outline" size="sm">
                <Brain className="h-4 w-4 mr-2" />
                Create Custom Assistant
              </Button>
            </div>
            
            <div className="text-sm text-gray-600">
              <p>Active Instances: {Object.values(activeInstances).filter(Boolean).length}/4</p>
              <p>Memory Usage: ~{Object.values(activeInstances).filter(Boolean).length * 25}MB</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}