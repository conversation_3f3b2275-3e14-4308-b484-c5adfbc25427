const { createClient } = require('../HempResourceHub/node_modules/@supabase/supabase-js');
require('../HempResourceHub/node_modules/dotenv').config({ path: '../.env' });

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkProducts() {
  try {
    // Check products count
    const { data: products, count, error: productsError } = await supabase
      .from('uses_products')
      .select('*', { count: 'exact' })
      .limit(10);
    
    if (productsError) {
      console.error('Error fetching products:', productsError);
      return;
    }
    
    console.log('\n=== Products in Database ===');
    console.log(`Total products: ${count}`);
    
    if (products && products.length > 0) {
      console.log('\nFirst 10 products:');
      products.forEach((product, i) => {
        console.log(`\n${i + 1}. ${product.name || 'No name'}`);
        console.log(`   ID: ${product.id}`);
        console.log(`   Plant Part ID: ${product.plant_part_id}`);
        console.log(`   Industry Sub Category ID: ${product.industry_sub_category_id}`);
        console.log(`   Description: ${(product.description || 'No description').substring(0, 100)}...`);
      });
    }
    
    // Check plant parts
    const { data: plantParts, error: partsError } = await supabase
      .from('plant_parts')
      .select('*');
    
    if (!partsError) {
      console.log('\n=== Plant Parts ===');
      console.log(`Total plant parts: ${plantParts.length}`);
      plantParts.forEach(part => {
        console.log(`- ${part.name} (ID: ${part.id})`);
      });
    }
    
    // Check industries
    const { data: industries, error: industriesError } = await supabase
      .from('industries')
      .select('*');
    
    if (!industriesError) {
      console.log('\n=== Industries ===');
      console.log(`Total industries: ${industries.length}`);
      industries.forEach(industry => {
        console.log(`- ${industry.name} (ID: ${industry.id})`);
      });
    }
    
    // Check plant types
    const { data: plantTypes, error: typesError } = await supabase
      .from('hemp_plant_archetypes')
      .select('*');
    
    if (!typesError) {
      console.log('\n=== Plant Types ===');
      console.log(`Total plant types: ${plantTypes.length}`);
      plantTypes.forEach(pt => {
        console.log(`- ${pt.name} (ID: ${pt.id})`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkProducts();