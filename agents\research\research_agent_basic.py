#!/usr/bin/env python3
"""
Basic Research Agent without Image Generation
This agent discovers hemp products without automatically generating images
"""

import json
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any
import aiohttp

try:
    from .enhanced_research_agent import EnhancedResearchAgent
except ImportError:
    # If enhanced agent fails to import, create a base class
    from typing import List, Dict, Optional, Any
    
    class EnhancedResearchAgent:
        """Fallback base class if enhanced agent can't be imported"""
        def __init__(self, supabase_url: str, supabase_key: str, openai_api_key: Optional[str] = None):
            from supabase import create_client
            self.supabase = create_client(supabase_url, supabase_key)
            self.supabase_url = supabase_url
            self.supabase_key = supabase_key
            self.openai_api_key = openai_api_key
            
        async def discover_products(self, search_query: str, max_results: int = 10) -> List[Dict]:
            """Fallback discovery using hardcoded data"""
            return []
            
        def _extract_companies(self, product: Dict) -> List[str]:
            return product.get('companies', [])
            
        def _clean_product_name(self, name: str, companies: List[str]) -> str:
            return name
            
        async def _get_plant_part_id(self, plant_part: Optional[str]) -> Optional[int]:
            if not plant_part:
                return None
            result = await self.supabase.table('plant_parts').select('id').eq('name', plant_part).execute()
            return result.data[0]['id'] if result.data else None
            
        async def _get_industry_sub_category_id(self, industry: Optional[str], sub_industry: Optional[str]) -> Optional[int]:
            if not industry:
                return None
            result = await self.supabase.table('industries').select('id').eq('name', industry).execute()
            if result.data:
                industry_id = result.data[0]['id']
                sub_result = await self.supabase.table('industry_sub_categories').select('id').eq('industry_id', industry_id).execute()
                return sub_result.data[0]['id'] if sub_result.data else None
            return None
            
        async def _get_or_create_company(self, company_name: str) -> Optional[int]:
            result = await self.supabase.table('hemp_companies').select('id').eq('name', company_name).execute()
            if result.data:
                return result.data[0]['id']
            # Create new company
            new_company = {
                'name': company_name,
                'description': f'{company_name} - Hemp product manufacturer',
                'verified': False,
                'company_type': 'manufacturer'
            }
            result = await self.supabase.table('hemp_companies').insert(new_company).execute()
            return result.data[0]['id'] if result.data else None
            
        async def _link_product_to_companies(self, product_id: int, company_ids: List[int]):
            for company_id in company_ids:
                await self.supabase.table('hemp_company_products').insert({
                    'product_id': product_id,
                    'company_id': company_id
                }).execute()
                
        def _generate_keywords(self, product: Dict) -> List[str]:
            return []

logger = logging.getLogger(__name__)

class ResearchAgentBasic(EnhancedResearchAgent):
    """Basic research agent without automatic image generation"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Explicitly disable image generation
        self.image_generation_enabled = False
        
    async def save_products(self, products: List[Dict]) -> Dict[str, Any]:
        """Save discovered products to database WITHOUT image generation"""
        results = {
            'saved': 0,
            'failed': 0,
            'errors': []
        }
        
        for product in products:
            try:
                # Extract companies
                companies = self._extract_companies(product)
                cleaned_name = self._clean_product_name(product['name'], companies)
                
                # Get IDs
                plant_part_id = await self._get_plant_part_id(product.get('plant_part'))
                industry_sub_category_id = await self._get_industry_sub_category_id(
                    product.get('industry'),
                    product.get('sub_industry')
                )
                
                # Check if product exists
                existing = await self.supabase.table('uses_products').select('id').eq('name', cleaned_name).execute()
                
                if not existing.data:
                    # Insert product WITHOUT image_url
                    new_product = {
                        'name': cleaned_name,
                        'description': product.get('description', ''),
                        'plant_part_id': plant_part_id,
                        'industry_sub_category_id': industry_sub_category_id,
                        'sustainability_score': product.get('sustainability_score'),
                        'market_stage': product.get('market_stage', 'research'),
                        'source': product.get('source', 'AI Research Agent'),
                        'verified': False,
                        'active': True,
                        # NO image_url field - let it remain NULL
                    }
                    
                    result = await self.supabase.table('uses_products').insert(new_product).execute()
                    
                    if result.data:
                        product_id = result.data[0]['id']
                        results['saved'] += 1
                        
                        # Link to companies
                        if companies:
                            company_ids = []
                            for company in companies:
                                company_id = await self._get_or_create_company(company)
                                if company_id:
                                    company_ids.append(company_id)
                            
                            if company_ids:
                                await self._link_product_to_companies(product_id, company_ids)
                        
                        logger.info(f"Saved product: {cleaned_name} (ID: {product_id})")
                else:
                    logger.info(f"Product already exists: {cleaned_name}")
                    
            except Exception as e:
                results['failed'] += 1
                results['errors'].append(str(e))
                logger.error(f"Error saving product {product.get('name', 'Unknown')}: {e}")
        
        return results
        
    async def run(self, search_query: str, max_results: int = 10) -> Dict[str, Any]:
        """Run the research agent without image generation"""
        logger.info(f"Starting basic research for: {search_query}")
        
        # Discover products
        products = await self.discover_products(search_query, max_results)
        
        if not products:
            logger.warning("No products discovered")
            return {
                'discovered': 0,
                'saved': 0,
                'failed': 0,
                'errors': ['No products discovered']
            }
        
        # Save products WITHOUT image generation
        save_results = await self.save_products(products)
        
        return {
            'discovered': len(products),
            'saved': save_results['saved'],
            'failed': save_results['failed'],
            'errors': save_results['errors']
        }


# Example usage
if __name__ == "__main__":
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    
    async def main():
        agent = ResearchAgentBasic(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_ANON_KEY'),
            os.getenv('OPENAI_API_KEY')
        )
        
        results = await agent.run("innovative hemp construction materials", max_results=5)
        print(json.dumps(results, indent=2))
    
    asyncio.run(main())