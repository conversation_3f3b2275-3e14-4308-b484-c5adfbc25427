import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function cleanupImageQueue() {
  console.log('🧹 Cleaning Up Image Generation Queue');
  console.log('='.repeat(80));
  
  let totalDeleted = 0;
  
  // 1. Remove duplicate completed entries (keep only the most recent successful one)
  console.log('\n1️⃣ Removing duplicate completed entries...');
  
  const { data: completedEntries } = await supabase
    .from('image_generation_queue')
    .select('*')
    .eq('status', 'completed')
    .order('product_id')
    .order('completed_at', { ascending: false });
    
  const seenProducts = new Set();
  const toDelete = [];
  
  completedEntries?.forEach(entry => {
    if (entry.product_id) {
      if (seenProducts.has(entry.product_id)) {
        toDelete.push(entry.id);
      } else {
        seenProducts.add(entry.product_id);
      }
    }
  });
  
  if (toDelete.length > 0) {
    // Delete in batches to avoid URI too long error
    const batchSize = 50;
    let deleted = 0;
    
    for (let i = 0; i < toDelete.length; i += batchSize) {
      const batch = toDelete.slice(i, i + batchSize);
      const { error } = await supabase
        .from('image_generation_queue')
        .delete()
        .in('id', batch);
        
      if (!error) {
        deleted += batch.length;
      } else {
        console.error(`   ❌ Error deleting batch:`, error);
      }
    }
    
    console.log(`   ✅ Deleted ${deleted} duplicate completed entries`);
    totalDeleted += deleted;
  } else {
    console.log('   ✨ No duplicate completed entries found');
  }
  
  // 2. Remove duplicate pending/retry entries for the same product
  console.log('\n2️⃣ Removing duplicate pending/retry entries...');
  
  const { data: pendingEntries } = await supabase
    .from('image_generation_queue')
    .select('*')
    .in('status', ['pending', 'retry'])
    .order('product_id')
    .order('created_at');
    
  const pendingByProduct = {};
  pendingEntries?.forEach(entry => {
    if (entry.product_id) {
      if (!pendingByProduct[entry.product_id]) {
        pendingByProduct[entry.product_id] = [];
      }
      pendingByProduct[entry.product_id].push(entry);
    }
  });
  
  const pendingToDelete = [];
  Object.entries(pendingByProduct).forEach(([productId, entries]) => {
    if (entries.length > 1) {
      // Keep only the first (oldest) entry
      entries.slice(1).forEach(entry => pendingToDelete.push(entry.id));
    }
  });
  
  if (pendingToDelete.length > 0) {
    // Delete in batches
    const batchSize = 50;
    let deleted = 0;
    
    for (let i = 0; i < pendingToDelete.length; i += batchSize) {
      const batch = pendingToDelete.slice(i, i + batchSize);
      const { error } = await supabase
        .from('image_generation_queue')
        .delete()
        .in('id', batch);
        
      if (!error) {
        deleted += batch.length;
      } else {
        console.error(`   ❌ Error deleting batch:`, error);
      }
    }
    
    console.log(`   ✅ Deleted ${deleted} duplicate pending/retry entries`);
    totalDeleted += deleted;
  } else {
    console.log('   ✨ No duplicate pending/retry entries found');
  }
  
  // 3. Remove old failed entries (keep only the most recent failure for debugging)
  console.log('\n3️⃣ Cleaning up old failed entries...');
  
  const { data: failedEntries } = await supabase
    .from('image_generation_queue')
    .select('*')
    .eq('status', 'failed')
    .order('product_id')
    .order('updated_at', { ascending: false });
    
  const failedByProduct = {};
  failedEntries?.forEach(entry => {
    if (entry.product_id) {
      if (!failedByProduct[entry.product_id]) {
        failedByProduct[entry.product_id] = [];
      }
      failedByProduct[entry.product_id].push(entry);
    }
  });
  
  const failedToDelete = [];
  Object.entries(failedByProduct).forEach(([productId, entries]) => {
    if (entries.length > 1) {
      // Keep only the most recent failure
      entries.slice(1).forEach(entry => failedToDelete.push(entry.id));
    }
  });
  
  if (failedToDelete.length > 0) {
    // Delete in batches
    const batchSize = 50;
    let deleted = 0;
    
    for (let i = 0; i < failedToDelete.length; i += batchSize) {
      const batch = failedToDelete.slice(i, i + batchSize);
      const { error } = await supabase
        .from('image_generation_queue')
        .delete()
        .in('id', batch);
        
      if (!error) {
        deleted += batch.length;
      } else {
        console.error(`   ❌ Error deleting batch:`, error);
      }
    }
    
    console.log(`   ✅ Deleted ${deleted} old failed entries`);
    totalDeleted += deleted;
  } else {
    console.log('   ✨ No old failed entries to clean up');
  }
  
  // 4. Summary
  console.log('\n' + '='.repeat(80));
  console.log('📊 CLEANUP SUMMARY:');
  console.log(`   🗑️ Total entries deleted: ${totalDeleted}`);
  
  // Get updated stats
  const { data: stats } = await supabase
    .from('image_generation_queue')
    .select('status')
    .then(result => {
      const counts = {};
      result.data?.forEach(item => {
        counts[item.status] = (counts[item.status] || 0) + 1;
      });
      return { data: counts };
    });
    
  console.log('\n📈 Updated Queue Stats:');
  Object.entries(stats || {}).forEach(([status, count]) => {
    console.log(`   ${status}: ${count}`);
  });
  
  const { count: totalQueue } = await supabase
    .from('image_generation_queue')
    .select('*', { count: 'exact', head: true });
    
  const { count: totalProducts } = await supabase
    .from('uses_products')
    .select('*', { count: 'exact', head: true });
    
  console.log(`\n   Ratio: ${(totalQueue / totalProducts).toFixed(2)} queue entries per product`);
}

// Create safe image queue function that prevents duplicates
async function safeAddToImageQueue(productId, prompt, options = {}) {
  // Check if there's already an entry for this product
  const { data: existing } = await supabase
    .from('image_generation_queue')
    .select('id, status')
    .eq('product_id', productId)
    .in('status', ['pending', 'processing', 'completed']);
    
  if (existing && existing.length > 0) {
    console.log(`⚠️ Image queue entry already exists for product ${productId}`);
    return null;
  }
  
  // Add new entry
  const { data, error } = await supabase
    .from('image_generation_queue')
    .insert({
      product_id: productId,
      prompt: prompt,
      status: 'pending',
      priority: options.priority || 1,
      ...options
    })
    .select()
    .single();
    
  if (error) {
    console.error('Error adding to queue:', error);
    return null;
  }
  
  return data;
}

// Export for use in other scripts
export { safeAddToImageQueue };

// Run cleanup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanupImageQueue().catch(console.error);
}