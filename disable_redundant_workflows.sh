#!/bin/bash
# Script to disable redundant GitHub Actions workflows

echo "=== Disabling Redundant GitHub Actions Workflows ==="
echo ""

# Change to workflows directory
cd .github/workflows

# Function to disable a workflow
disable_workflow() {
    local workflow=$1
    if [ -f "$workflow" ]; then
        mv "$workflow" "${workflow}.disabled"
        echo "✅ Disabled: $workflow → ${workflow}.disabled"
    else
        echo "⚠️  Not found: $workflow"
    fi
}

echo "Workflows to disable:"
echo "-------------------"

# Disable redundant workflows
disable_workflow "hemp-automation.yml"
disable_workflow "image-generation.yml"
disable_workflow "monitoring.yml"
disable_workflow "status-check.yml"
disable_workflow "weekly-summary.yml"

echo ""
echo "Workflows to keep active:"
echo "------------------------"
ls -1 *.yml 2>/dev/null | while read file; do
    echo "✅ Active: $file"
done

echo ""
echo "=== Summary ==="
echo "Active workflows: $(ls -1 *.yml 2>/dev/null | wc -l)"
echo "Disabled workflows: $(ls -1 *.disabled 2>/dev/null | wc -l)"
echo ""
echo "To re-enable a workflow, rename it back from .disabled to .yml"