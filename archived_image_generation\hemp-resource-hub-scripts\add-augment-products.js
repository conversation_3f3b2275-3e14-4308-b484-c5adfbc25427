#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

// Products discovered by Augment Code
const AUGMENT_PRODUCTS = [
  {
    name: "Hemp-Based Ultracapacitors",
    description: "High-performance energy storage devices using hemp biomass-derived activated carbon that provides superior energy density compared to traditional ultracapacitors. Developed by Florrent using regeneratively grown hemp.",
    plant_part: "Fiber",
    industry: "Energy",
    sub_industry: "Energy Storage",
    companies: ["Florrent"],
    benefits: ["Higher energy density than conventional ultracapacitors", "Made from regeneratively grown hemp biomass", "Supports BIPOC farmers in supply chain"],
    commercialization_stage: "Commercial",
    technical_specs: "High energy density ultracapacitor technology for hybrid energy storage applications",
    environmental_impact: "Carbon negative production using regeneratively grown hemp, supports sustainable agriculture",
    market_size: "$15 billion ultracapacitor market by 2030"
  },
  {
    name: "Hemp Molded Fiber Packaging",
    description: "Plastic-free molded fiber packaging products made primarily from industrial hemp, offering sustainable alternative to traditional plastic packaging for food and non-food applications.",
    plant_part: "Fiber",
    industry: "Packaging",
    sub_industry: "Sustainable Packaging",
    companies: ["RENW", "element6 Dynamics", "PAPACKS"],
    benefits: ["100% plastic-free packaging solution", "Fully circular and compostable", "Made from agricultural waste hemp"],
    commercialization_stage: "Commercial",
    environmental_impact: "Eliminates plastic waste, uses agricultural hemp waste, fully compostable"
  },
  {
    name: "Hemp Protein Isolate (90%+ Purity)",
    description: "World's first commercial soluble hemp protein isolate with greater than 90% protein content, developed through innovative processing technology for food and beverage applications.",
    plant_part: "Seeds",
    industry: "Food & Beverage",
    sub_industry: "Protein Ingredients",
    companies: ["Burcon NutraScience", "HPS Food and Ingredients"],
    benefits: ["Greater than 90% protein content", "Highly soluble and functional", "Complete amino acid profile"],
    commercialization_stage: "Commercial",
    technical_specs: "Protein isolate with >90% protein content, high solubility, neutral taste"
  },
  {
    name: "Hemp-Reinforced 3D Printing Filaments",
    description: "Advanced 3D printing filaments incorporating hemp fibers with polypropylene and PLA matrices, offering improved mechanical properties and sustainability for additive manufacturing.",
    plant_part: "Fiber",
    industry: "Technology",
    sub_industry: "3D Printing Materials",
    companies: ["Various research institutions"],
    benefits: ["Enhanced mechanical properties vs pure polymer", "Sustainable bio-based reinforcement", "Reduced material costs"],
    commercialization_stage: "Pilot",
    technical_specs: "Hemp fiber reinforced PP and PLA composites with fiber ratios of 10-30%"
  },
  {
    name: "HempWool Thermal Insulation Batts",
    description: "High-performance thermal insulation batts made from 90% natural hemp fiber, offering superior insulation properties with health and environmental benefits for construction applications.",
    plant_part: "Fiber",
    industry: "Construction",
    sub_industry: "Insulation Materials",
    companies: ["Hempitecture"],
    benefits: ["90% natural hemp fiber content", "Superior thermal performance", "Non-toxic and safe for installers"],
    commercialization_stage: "Commercial",
    technical_specs: "R-values ranging from R-13 to R-30, available in various thicknesses"
  },
  {
    name: "Hemp-Based Automotive Interior Panels",
    description: "Sustainable automotive interior components including side panels and seat frames made from hemp fiber composites, developed for next-generation vehicle interiors.",
    plant_part: "Fiber",
    industry: "Automotive",
    sub_industry: "Interior Components",
    companies: ["Volkswagen", "Revoltech GmbH"],
    benefits: ["Lightweight compared to traditional materials", "Sustainable alternative to petroleum-based plastics"],
    commercialization_stage: "R&D",
    technical_specs: "Hemp fiber reinforced composites suitable for automotive interior applications"
  },
  {
    name: "Hemp-Based Sustainable Aviation Fuel (SAF)",
    description: "Advanced biofuel produced from hemp biomass for aviation applications, offering carbon-neutral alternative to conventional jet fuel with potential for commercial aviation use.",
    plant_part: "Seeds",
    industry: "Energy",
    sub_industry: "Aviation Fuels",
    companies: ["SGP BioEnergy", "Honeywell", "Atlantic Biomass", "Bionoid"],
    benefits: ["Carbon neutral fuel production", "Uses non-food crop feedstock", "Reduces aviation industry carbon footprint"],
    commercialization_stage: "Pilot",
    environmental_impact: "Carbon neutral production, reduces aviation emissions, uses agricultural waste"
  },
  {
    name: "Hemp Fiber Circuit Board Substrates",
    description: "Bio-based printed circuit board substrates using hemp fibers as reinforcement, offering sustainable alternative to traditional fiberglass PCBs with improved recyclability.",
    plant_part: "Fiber",
    industry: "Technology",
    sub_industry: "Electronics Components",
    companies: ["Jiva Materials"],
    benefits: ["Sustainable alternative to fiberglass PCBs", "Improved recyclability of electronic components"],
    commercialization_stage: "R&D",
    environmental_impact: "Reduces e-waste toxicity, uses renewable hemp fibers"
  },
  {
    name: "Hemp Prefabricated Building Panels",
    description: "Prefabricated construction panels incorporating hemp fibers and hempcrete for rapid, sustainable building construction with superior thermal and structural properties.",
    plant_part: "Fiber",
    industry: "Construction",
    sub_industry: "Prefab Building Systems",
    companies: ["Lower Sioux Community"],
    benefits: ["Rapid construction assembly", "Superior thermal insulation properties", "Carbon sequestering building material"],
    commercialization_stage: "Commercial",
    environmental_impact: "Carbon negative building material, sustainable construction methods"
  },
  {
    name: "Hemp-Based Graphene Supercapacitors",
    description: "Next-generation supercapacitors using hemp-derived graphene nanosheets that demonstrate superior performance characteristics compared to traditional graphene at significantly lower cost.",
    plant_part: "Fiber",
    industry: "Energy",
    sub_industry: "Advanced Energy Storage",
    companies: ["Premier Graphene", "HydroGraph"],
    benefits: ["Superior performance vs traditional graphene", "1/1000th the cost of conventional graphene"],
    commercialization_stage: "Pilot",
    technical_specs: "Hemp-derived graphene electrodes for high-performance supercapacitors"
  }
];

// Mapping of plant part names to IDs
const PLANT_PART_IDS = {
  "Fiber": 1,
  "Seeds": 2,
  "Hurd/Shiv": 3,
  "Flowers": 4,
  "Leaves": 5,
  "Roots": 6
};

// Mapping of industry names to IDs
const INDUSTRY_IDS = {
  "Construction": 1,
  "Textiles": 2,
  "Food & Nutrition": 3,
  "Food & Beverage": 3,
  "Health & Beauty": 4,
  "Automotive": 5,
  "Energy": 6,
  "Packaging": 7,
  "Agriculture": 8,
  "Technology": 9,
  "Other": 10
};

async function getOrCreateCompany(companyName) {
  // Check if company exists
  const { data: existing } = await supabase
    .from('hemp_companies')
    .select('id')
    .eq('name', companyName)
    .single();
  
  if (existing) {
    return existing.id;
  }
  
  // Create new company
  const { data, error } = await supabase
    .from('hemp_companies')
    .insert({
      name: companyName,
      description: `${companyName} - Industrial hemp products manufacturer`
    })
    .select('id')
    .single();
  
  if (error) throw error;
  return data.id;
}

async function getSubIndustryId(industryName, subIndustryName) {
  const industryId = INDUSTRY_IDS[industryName];
  if (!industryId) return null;
  
  // Try to find sub-industry
  const { data: existing } = await supabase
    .from('industry_sub_categories')
    .select('id')
    .eq('industry_id', industryId)
    .eq('name', subIndustryName)
    .single();
  
  if (existing) {
    return existing.id;
  }
  
  // Create new sub-industry
  const { data, error } = await supabase
    .from('industry_sub_categories')
    .insert({
      industry_id: industryId,
      name: subIndustryName,
      description: `${subIndustryName} in ${industryName} industry`
    })
    .select('id')
    .single();
  
  if (error) throw error;
  return data.id;
}

function generateImagePrompt(product) {
  const name = product.name;
  let prompt = `Professional product photography of ${name}. `;
  
  // Add material/texture hints based on product type
  if (name.toLowerCase().includes('ultracapacitor') || name.toLowerCase().includes('supercapacitor')) {
    prompt += "High-tech energy storage device with sleek design, visible electrodes and connections. ";
  } else if (name.toLowerCase().includes('packaging')) {
    prompt += "Eco-friendly molded packaging containers and trays, natural fiber texture visible. ";
  } else if (name.toLowerCase().includes('protein')) {
    prompt += "White protein powder in a modern container with hemp leaves decoration. ";
  } else if (name.toLowerCase().includes('filament')) {
    prompt += "3D printer filament spool with natural fiber-reinforced material, hemp fibers visible. ";
  } else if (name.toLowerCase().includes('insulation')) {
    prompt += "Thermal insulation batts showing fluffy natural fiber texture, construction material. ";
  } else if (name.toLowerCase().includes('panel') && name.toLowerCase().includes('automotive')) {
    prompt += "Car interior door panel made from sustainable composite material, modern automotive design. ";
  } else if (name.toLowerCase().includes('fuel')) {
    prompt += "Aviation fuel in industrial containers with aircraft in background, sustainable energy. ";
  } else if (name.toLowerCase().includes('circuit')) {
    prompt += "Green printed circuit board with natural fiber substrate, electronic components visible. ";
  } else if (name.toLowerCase().includes('building panel')) {
    prompt += "Prefabricated construction panels stacked at building site, hemp fiber texture visible. ";
  } else if (name.toLowerCase().includes('graphene')) {
    prompt += "Advanced nanomaterial structure visualization, hexagonal patterns, high-tech laboratory setting. ";
  }
  
  prompt += "Professional studio lighting, clean white background, commercial product photography style. High quality, detailed, 4K.";
  
  return prompt;
}

async function main() {
  console.log("🚀 Adding Augment-Discovered Hemp Products");
  console.log("=".repeat(60));
  console.log(`📊 Products to add: ${AUGMENT_PRODUCTS.length}`);
  console.log();
  
  let totalSaved = 0;
  
  for (let i = 0; i < AUGMENT_PRODUCTS.length; i++) {
    const product = AUGMENT_PRODUCTS[i];
    console.log(`\n[${i + 1}/${AUGMENT_PRODUCTS.length}] Processing: ${product.name}`);
    
    try {
      // Check if product already exists
      const { data: existing } = await supabase
        .from('uses_products')
        .select('id')
        .eq('name', product.name)
        .single();
      
      if (existing) {
        console.log("   ⚠️ Product already exists, skipping");
        continue;
      }
      
      // Get IDs for foreign keys
      const plantPartId = PLANT_PART_IDS[product.plant_part];
      const subIndustryId = await getSubIndustryId(product.industry, product.sub_industry || '');
      
      // Create product record
      const productData = {
        name: product.name,
        description: product.description,
        plant_part_id: plantPartId,
        industry_sub_category_id: subIndustryId,
        benefits_advantages: product.benefits || [],
        commercialization_stage: product.commercialization_stage || 'R&D',
        technical_specifications: product.technical_specs ? { specs: product.technical_specs } : null,
        sustainability_aspects: product.environmental_impact ? [product.environmental_impact] : [],
        miscellaneous_info: product.market_size ? { market_size: product.market_size } : null
      };
      
      const { data: newProduct, error: productError } = await supabase
        .from('uses_products')
        .insert(productData)
        .select('id')
        .single();
      
      if (productError) throw productError;
      
      const productId = newProduct.id;
      totalSaved++;
      console.log(`   ✅ Product saved with ID: ${productId}`);
      
      // Add product image
      await supabase
        .from('product_images')
        .insert({
          use_product_id: productId,
          image_url: '/api/placeholder/400/300',
          alt_text: product.name,
          is_primary: true
        });
      console.log("   🖼️ Placeholder image added");
      
      // Add companies
      if (product.companies && product.companies.length > 0) {
        for (const companyName of product.companies) {
          if (companyName && !companyName.includes('Various')) {
            const companyId = await getOrCreateCompany(companyName);
            
            // Link company to product
            await supabase
              .from('hemp_company_products')
              .insert({
                company_id: companyId,
                product_id: productId,
                relationship_type: 'manufacturer',
                is_primary: true
              });
            console.log(`   🏢 Linked company: ${companyName}`);
          }
        }
      }
      
      // Queue image generation
      const imagePrompt = generateImagePrompt(product);
      await supabase
        .from('image_generation_queue')
        .insert({
          product_id: productId,
          prompt: imagePrompt,
          status: 'pending',
          priority: 1
        });
      console.log("   🖼️ Image generation queued");
      
    } catch (error) {
      console.error(`   ❌ Error: ${error.message}`);
    }
  }
  
  console.log("\n" + "=".repeat(60));
  console.log("📊 Summary:");
  console.log(`   📦 Products processed: ${AUGMENT_PRODUCTS.length}`);
  console.log(`   💾 New products saved: ${totalSaved}`);
  console.log(`   🖼️ Images queued: ${totalSaved}`);
  
  console.log("\n📝 Next Steps:");
  console.log("1. View products at http://localhost:3000/all-products");
  console.log("2. Run 'node trigger_image_generation.js' to start image generation");
  console.log("3. Run 'python merge_agent_companies.py --auto' to consolidate companies");
}

// Run the script
main().catch(console.error);