# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AI Image Generation API Keys
# Get your Stability AI key from: https://platform.stability.ai/account/keys
STABILITY_API_KEY=your_stability_api_key_here

# Get your OpenAI API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Get your Google Gemini API key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Image Generation Settings
# Options: placeholder, stable_diffusion, dall_e, imagen_3
IMAGE_GENERATION_PROVIDER=imagen_3

# Optional: Midjourney API (when available)
# MIDJOURNEY_API_KEY=your_midjourney_api_key_here
