#!/usr/bin/env python3
"""
Monitoring and Alerting Script
Can be run from GitHub Actions or cron to check system health
"""

import asyncio
import json
import os
import sys
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.supabase_client import get_supabase_client
from lib.monitoring_service import MonitoringService, AlertSeverity


async def send_alert(alert: dict, config: dict):
    """Send alert via configured channels"""
    # This could be extended to send to:
    # - Slack webhook
    # - Email
    # - Discord
    # - SMS (Twilio)
    # - PagerDuty
    
    # For now, just log to console (will be captured by GitHub Actions)
    severity_emoji = {
        'critical': '🔴',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    }.get(alert['severity'], '❓')
    
    print(f"{severity_emoji} ALERT: {alert['message']}")
    print(f"   Severity: {alert['severity']}")
    print(f"   Current Value: {alert['current_value']}")
    print(f"   Threshold: {alert['threshold']}")
    print(f"   Time: {alert['triggered_at']}")
    
    # If running in GitHub Actions, set output
    if os.environ.get('GITHUB_ACTIONS'):
        # Create annotation
        if alert['severity'] in ['critical', 'error']:
            print(f"::error::{alert['message']}")
        elif alert['severity'] == 'warning':
            print(f"::warning::{alert['message']}")
        else:
            print(f"::notice::{alert['message']}")
            

async def main():
    """Main monitoring function"""
    # Get configuration
    config = {
        'alert_on_severity': os.environ.get('ALERT_SEVERITY', 'warning').split(','),
        'export_metrics': os.environ.get('EXPORT_METRICS', 'false').lower() == 'true',
        'metrics_file': os.environ.get('METRICS_FILE', 'metrics.json')
    }
    
    # Initialize monitoring service
    supabase = get_supabase_client()
    monitoring = MonitoringService(supabase)
    
    # Collect metrics
    print("🔍 Collecting system metrics...")
    metrics = await monitoring.collect_metrics()
    
    # Export metrics if requested
    if config['export_metrics']:
        with open(config['metrics_file'], 'w') as f:
            json.dump(metrics, f, indent=2, default=str)
        print(f"📊 Metrics exported to {config['metrics_file']}")
        
    # Check for alerts
    alerts = metrics.get('alerts', [])
    
    if not alerts:
        print("✅ No alerts triggered - all systems healthy")
        
        # Still output some key metrics
        print(f"\n📈 System Status:")
        print(f"   Uptime: {metrics['system']['uptime']:.1f}%")
        print(f"   Active Agents: {metrics['agents']['active']}/{metrics['agents']['total']}")
        print(f"   Pending Tasks: {metrics['tasks']['by_status'].get('pending', 0)}")
        print(f"   Products: {metrics['system']['total_products_discovered']:,}")
        print(f"   Companies: {metrics['system']['total_companies_found']:,}")
        
    else:
        print(f"\n🚨 {len(alerts)} alerts triggered!")
        
        # Filter alerts by severity
        filtered_alerts = [
            alert for alert in alerts 
            if alert['severity'] in config['alert_on_severity']
        ]
        
        if filtered_alerts:
            print(f"📢 Sending {len(filtered_alerts)} alerts...")
            for alert in filtered_alerts:
                await send_alert(alert, config)
                
        # Set exit code based on severity
        has_critical = any(alert['severity'] == 'critical' for alert in alerts)
        has_error = any(alert['severity'] == 'error' for alert in alerts)
        
        if has_critical:
            sys.exit(2)  # Critical alerts
        elif has_error:
            sys.exit(1)  # Error alerts
            
    # Generate summary report
    print("\n📋 Summary Report:")
    print(f"   Timestamp: {metrics['timestamp']}")
    print(f"   Total Automation Runs: {metrics['system']['total_automation_runs']:,}")
    print(f"   Success Rate: {metrics['system']['automation_success_rate']:.1f}%")
    print(f"   Image Generation Queue: {metrics['images']['queue'].get('pending', 0)} pending")
    print(f"   Database Growth (24h): {metrics['database']['growth_24h']} new products")
    
    # Performance warnings
    if metrics['tasks']['avg_wait_time'] > 300:
        print(f"\n⚡ Performance Warning: High task wait time ({metrics['tasks']['avg_wait_time']:.0f}s)")
        
    if metrics['images']['queue'].get('failed', 0) > 50:
        print(f"\n⚡ Performance Warning: High image generation failures ({metrics['images']['queue']['failed']})")
        

if __name__ == "__main__":
    asyncio.run(main())