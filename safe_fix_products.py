#!/usr/bin/env python3
"""
Safe version of product fix that handles foreign key constraints
"""

import os
import sys
from supabase import create_client

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

# Product to company mappings
PRODUCT_COMPANY_MAPPINGS = {
    # Simple mappings where company is in the name
    "Manitoba Harvest Hemp Hearts": ("Hemp Hearts (Shelled Hemp Seeds)", ["Manitoba Harvest"]),
    "Nutiva Organic Hemp Oil": ("Cold-Pressed Hemp Seed Oil", ["Nutiva"]),
    "Good Hemp Seed Oil": ("Cold-Pressed Hemp Seed Oil", ["Good Hemp"]),
    "Victory Hemp V-70 Hemp Protein": ("Hemp Protein Powder (70% Protein)", ["Victory Hemp Foods"]),
    "Fresh Hemp Foods Hemp Oil Capsules": ("Hemp Oil Capsules", ["Fresh Hemp Foods"]),
    "Hemp Foods Australia Organic Hemp Seeds": ("Organic Hemp Seeds (Hulled)", ["Hemp Foods Australia"]),
    "Buddha Teas Organic Hemp Leaf Tea": ("Hemp Leaf Tea", ["Buddha Teas"]),
    "Patagonia Hemp Canvas Pants": ("Hemp Canvas Pants", ["Patagonia"]),
    "WAMA Hemp Underwear": ("Hemp Underwear", ["WAMA Underwear"]),
    "Hemp Fortex Hemp Denim Fabric": ("Hemp Denim Fabric", ["Hemp Fortex"]),
    "Toad&Co Hemp Trail Shorts": ("Hemp Trail Shorts", ["Toad&Co"]),
    "tentree Hemp Hoodie": ("Hemp Hoodie", ["tentree"]),
    "Hempsmith Hemp Work Shirt": ("Hemp Work Shirt", ["Hempsmith Clothing"]),
    "Hempitecture HempWool Insulation": ("Hemp Fiber Insulation", ["Hempitecture"]),
    "HempTraders Hempcrete Blocks": ("Hempcrete Building Blocks", ["HempTraders"]),
    "BioFiber Industries Hemp-Lime Plaster": ("Hemp-Lime Plaster", ["BioFiber Industries"]),
    "Doylestown Hemp Company Hempcrete Spray System": ("Hempcrete Spray Insulation", ["Doylestown Hemp Company"]),
    "HempWood Flooring Planks": ("Hemp Wood Flooring", ["HempWood"]),
    "Kiehl's Cannabis Sativa Seed Oil Concentrate": ("Hemp Seed Oil Face Serum", ["Kiehl's"]),
    "The Body Shop Hemp Hand Protector": ("Hemp Hand Cream", ["The Body Shop"]),
}

def get_or_create_company(supabase, company_name):
    """Get existing company or create new one"""
    # Check if company exists
    result = supabase.table('hemp_companies').select('id').eq('name', company_name).execute()
    
    if result.data:
        return result.data[0]['id']
    
    # Create new company
    new_company = {
        'name': company_name,
        'description': f'{company_name} - Hemp product manufacturer and brand',
        'verified': True,
        'company_type': 'manufacturer'
    }
    
    try:
        result = supabase.table('hemp_companies').insert(new_company).execute()
        if result.data:
            return result.data[0]['id']
    except Exception as e:
        print(f"Error creating company {company_name}: {e}")
    return None

def link_product_to_company(supabase, product_id, company_id):
    """Create product-company relationship if it doesn't exist"""
    # Check if relationship exists
    existing = supabase.table('hemp_company_products')\
        .select('id')\
        .eq('product_id', product_id)\
        .eq('company_id', company_id)\
        .execute()
    
    if not existing.data:
        relationship = {
            'product_id': product_id,
            'company_id': company_id,
            'is_primary': True,
            'verified': True,
            'relationship_type': 'manufacturer'
        }
        try:
            result = supabase.table('hemp_company_products').insert(relationship).execute()
            return result.data is not None
        except Exception as e:
            print(f"Error creating relationship: {e}")
    return False

def safe_fix_products():
    """Safely fix products without breaking constraints"""
    # Initialize Supabase
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials!")
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("🔧 Safe Product-Company Fix")
    print("=" * 60)
    
    # Get all products
    products = supabase.table('uses_products').select('id, name').execute()
    
    if not products.data:
        print("No products found!")
        return
    
    products_updated = 0
    companies_created = 0
    relationships_created = 0
    
    # Process each product
    for product in products.data:
        product_id = product['id']
        current_name = product['name']
        
        # Check if this product needs fixing
        if current_name in PRODUCT_COMPANY_MAPPINGS:
            clean_name, companies = PRODUCT_COMPANY_MAPPINGS[current_name]
            
            print(f"\n📦 Processing: {current_name}")
            
            # Only update name if it's different
            if clean_name != current_name:
                try:
                    # Update product name
                    result = supabase.table('uses_products')\
                        .update({'name': clean_name})\
                        .eq('id', product_id)\
                        .execute()
                    
                    if result.data:
                        print(f"   ✅ Renamed to: {clean_name}")
                        products_updated += 1
                except Exception as e:
                    print(f"   ❌ Error updating name: {e}")
            
            # Add company relationships
            for company_name in companies:
                company_id = get_or_create_company(supabase, company_name)
                if company_id:
                    companies_created += 1
                    if link_product_to_company(supabase, product_id, company_id):
                        print(f"   ✅ Linked to: {company_name}")
                        relationships_created += 1
    
    # Handle duplicates by renaming them instead of deleting
    print("\n\n🔄 Handling duplicate products...")
    
    # Find products with similar names
    all_products = supabase.table('uses_products').select('id, name').execute()
    
    # Group by base name
    product_groups = {}
    for product in all_products.data:
        base_name = product['name'].split('(')[0].strip()
        if base_name not in product_groups:
            product_groups[base_name] = []
        product_groups[base_name].append(product)
    
    # Mark duplicates
    for base_name, products in product_groups.items():
        if len(products) > 1:
            print(f"\n   Found {len(products)} products with base name: {base_name}")
            # Keep the first one, mark others as duplicates
            for i, product in enumerate(products[1:], 1):
                try:
                    new_name = f"{product['name']} (Variant {i})"
                    supabase.table('uses_products')\
                        .update({'name': new_name})\
                        .eq('id', product['id'])\
                        .execute()
                    print(f"   ✅ Marked variant: {new_name}")
                except Exception as e:
                    print(f"   ❌ Error marking variant: {e}")
    
    # Summary
    print(f"\n\n📊 Summary:")
    print(f"   - Products renamed: {products_updated}")
    print(f"   - Companies created/found: {companies_created}")
    print(f"   - Relationships created: {relationships_created}")
    
    # Check orphaned products
    print(f"\n🔍 Checking for products without companies...")
    orphaned_count = 0
    
    for product in all_products.data:
        rel = supabase.table('hemp_company_products')\
            .select('id')\
            .eq('product_id', product['id'])\
            .execute()
        
        if not rel.data:
            orphaned_count += 1
            if orphaned_count <= 5:  # Show first 5
                print(f"   - {product['name']}")
    
    if orphaned_count > 5:
        print(f"   ... and {orphaned_count - 5} more")
    
    print(f"\n   Total products without companies: {orphaned_count}")

if __name__ == "__main__":
    safe_fix_products()