import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY environment variable is not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Custom placeholder image URL
const CUSTOM_PLACEHOLDER_URL = '/images/unknown-hemp-image.png';

async function updatePlantTypesImages() {
  try {
    console.log('Updating plant types images...');
    
    // Update all plant types to use the custom placeholder (except ID 1 which has a special image)
    const { error: updateError } = await supabase
      .from('hemp_plant_archetypes')
      .update({ image_url: CUSTOM_PLACEHOLDER_URL })
      .like('image_url', '%placeholder.com%');
    
    if (updateError) {
      console.error('Error updating plant types:', updateError);
      return;
    }
    
    console.log('✓ Successfully updated all plant types images');
    
    // Verify the update
    const { data: plantTypes, error: fetchError } = await supabase
      .from('hemp_plant_archetypes')
      .select('id, name, image_url');
    
    if (!fetchError) {
      console.log('\nUpdated plant types:');
      plantTypes.forEach(type => {
        console.log(`  - ${type.name}: ${type.image_url}`);
      });
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the update
updatePlantTypesImages();