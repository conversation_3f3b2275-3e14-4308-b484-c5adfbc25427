import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0b3F6bnFtbG54cnR2dWJld3l6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTE3NzYsImV4cCI6MjA2NDA2Nzc3Nn0.Cyu74ipNL2Fq6wTqzFOGCLW9mg46fRGJqkapgsumUGs';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function populateProducts() {
  console.log('Populating sample products...\n');
  
  try {
    // First, get some IDs we need
    const { data: plantParts } = await supabase
      .from('plant_parts')
      .select('id, name')
      .limit(5);
    
    const { data: industries } = await supabase
      .from('industry_sub_categories')
      .select('id, name')
      .limit(10);
    
    if (!plantParts || !industries || plantParts.length === 0 || industries.length === 0) {
      console.error('Missing required data: plant parts or industries');
      return;
    }
    
    console.log(`Found ${plantParts.length} plant parts and ${industries.length} industries`);
    
    // Sample products data
    const sampleProducts = [
      {
        name: "Hemp Fiber Insulation Panels",
        description: "High-performance thermal insulation made from hemp fiber",
        plant_part_id: plantParts[0].id,
        industry_sub_category_id: industries[0].id,
        benefits_advantages: ["Excellent thermal insulation", "Fire resistant", "Mold resistant", "Carbon negative"],
        commercialization_stage: "Established",
        sustainability_aspects: ["Carbon negative production", "100% biodegradable", "Non-toxic materials"],
        technical_specifications: {"R-value": "3.5 per inch", "Density": "30-40 kg/m³", "Fire rating": "Class A"}
      },
      {
        name: "Hemp Concrete Blocks",
        description: "Sustainable building blocks made from hemp hurds and lime",
        plant_part_id: plantParts[1]?.id || plantParts[0].id,
        industry_sub_category_id: industries[1]?.id || industries[0].id,
        benefits_advantages: ["Lightweight yet strong", "Fire resistant", "Pest resistant", "Carbon sequestering"],
        commercialization_stage: "Growing",
        sustainability_aspects: ["Sequesters CO2", "Renewable material", "Low embodied energy"]
      },
      {
        name: "Hemp Bioplastic Packaging",
        description: "Biodegradable packaging material made from hemp cellulose",
        plant_part_id: plantParts[2]?.id || plantParts[0].id,
        industry_sub_category_id: industries[2]?.id || industries[0].id,
        benefits_advantages: ["100% biodegradable", "Food safe", "Customizable properties"],
        commercialization_stage: "Research",
        sustainability_aspects: ["Replaces petroleum plastics", "Compostable", "Marine safe"]
      },
      {
        name: "Hemp Seed Oil Supplements",
        description: "Nutritional supplements rich in omega fatty acids",
        plant_part_id: plantParts[0].id,
        industry_sub_category_id: industries[3]?.id || industries[0].id,
        benefits_advantages: ["High in omega-3 and omega-6", "Complete protein source", "Rich in minerals"],
        commercialization_stage: "Established",
        technical_specifications: {"Omega-3": "3g per serving", "Omega-6": "10g per serving", "Protein": "5g per serving"}
      },
      {
        name: "Hemp Textile Fabric",
        description: "Durable, breathable fabric made from hemp fibers",
        plant_part_id: plantParts[0].id,
        industry_sub_category_id: industries[4]?.id || industries[0].id,
        benefits_advantages: ["3x stronger than cotton", "Naturally antimicrobial", "UV resistant"],
        commercialization_stage: "Growing",
        sustainability_aspects: ["Uses 50% less water than cotton", "No pesticides needed", "Biodegradable"]
      },
      {
        name: "Hemp Paper Products",
        description: "High-quality paper made from hemp fiber",
        plant_part_id: plantParts[0].id,
        industry_sub_category_id: industries[5]?.id || industries[0].id,
        benefits_advantages: ["4x more paper per acre than trees", "Naturally acid-free", "Stronger than wood paper"],
        commercialization_stage: "Established"
      },
      {
        name: "Hemp Biocomposite Materials",
        description: "Lightweight composite materials for automotive industry",
        plant_part_id: plantParts[1]?.id || plantParts[0].id,
        industry_sub_category_id: industries[6]?.id || industries[0].id,
        benefits_advantages: ["30% lighter than fiberglass", "Impact resistant", "Moldable"],
        commercialization_stage: "Growing",
        technical_specifications: {"Weight reduction": "30%", "Tensile strength": "35 MPa"}
      },
      {
        name: "Hemp Supercapacitors",
        description: "Energy storage devices using hemp-derived carbon nanosheets",
        plant_part_id: plantParts[1]?.id || plantParts[0].id,
        industry_sub_category_id: industries[7]?.id || industries[0].id,
        benefits_advantages: ["Outperforms graphene", "Cost-effective", "Fast charge/discharge"],
        commercialization_stage: "Research"
      },
      {
        name: "Hemp Animal Bedding",
        description: "Absorbent, dust-free bedding for horses and pets",
        plant_part_id: plantParts[1]?.id || plantParts[0].id,
        industry_sub_category_id: industries[8]?.id || industries[0].id,
        benefits_advantages: ["4x more absorbent than straw", "Low dust", "Natural odor control"],
        commercialization_stage: "Established"
      },
      {
        name: "Hemp CBD Extract",
        description: "Pharmaceutical-grade CBD extract for medical use",
        plant_part_id: plantParts[3]?.id || plantParts[0].id,
        industry_sub_category_id: industries[9]?.id || industries[0].id,
        benefits_advantages: ["Non-psychoactive", "Anti-inflammatory", "Neuroprotective"],
        commercialization_stage: "Established",
        technical_specifications: {"CBD concentration": "99%+", "THC content": "<0.3%"}
      }
    ];
    
    // Insert products
    const { data: insertedProducts, error } = await supabase
      .from('uses_products')
      .insert(sampleProducts)
      .select();
    
    if (error) {
      console.error('Error inserting products:', error);
    } else {
      console.log(`\n✅ Successfully inserted ${insertedProducts.length} products!`);
      console.log('\nInserted products:');
      insertedProducts.forEach(p => {
        console.log(`  - ${p.name} (Stage: ${p.commercialization_stage})`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

populateProducts();