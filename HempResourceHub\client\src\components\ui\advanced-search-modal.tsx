import React, { useState } from "react";
import { useLocation } from "wouter";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SlidersHorizontal, X, Search } from "lucide-react";
import { usePlantParts, useIndustries } from "@/hooks/use-plant-data";

interface AdvancedSearchFilters {
  query: string;
  plantParts: number[];
  industries: number[];
  stages: string[];
  sustainabilityRange: [number, number];
  sortBy: string;
  groupBy: string;
}

interface AdvancedSearchModalProps {
  onSearch: (filters: AdvancedSearchFilters) => void;
  currentFilters?: Partial<AdvancedSearchFilters>;
}

export function AdvancedSearchModal({ onSearch, currentFilters }: AdvancedSearchModalProps) {
  const [location, setLocation] = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const { data: plantParts } = usePlantParts();
  const { data: industries } = useIndustries();

  const [filters, setFilters] = useState<AdvancedSearchFilters>({
    query: currentFilters?.query || "",
    plantParts: currentFilters?.plantParts || [],
    industries: currentFilters?.industries || [],
    stages: currentFilters?.stages || [],
    sustainabilityRange: currentFilters?.sustainabilityRange || [0, 10],
    sortBy: currentFilters?.sortBy || "relevance",
    groupBy: currentFilters?.groupBy || "none"
  });

  const stages = [
    { id: "growing", label: "Growing", color: "bg-green-500" },
    { id: "established", label: "Established", color: "bg-blue-500" },
    { id: "research", label: "Research", color: "bg-purple-500" },
    { id: "speculative", label: "Speculative", color: "bg-orange-500" }
  ];

  const handleSubmit = () => {
    // Build URL with all filters
    const params = new URLSearchParams();
    
    if (filters.query) params.set('search', filters.query);
    if (filters.plantParts.length) params.set('parts', filters.plantParts.join(','));
    if (filters.industries.length) params.set('industries', filters.industries.join(','));
    if (filters.stages.length) params.set('stages', filters.stages.join(','));
    if (filters.sustainabilityRange[0] > 0 || filters.sustainabilityRange[1] < 10) {
      params.set('sustainability', filters.sustainabilityRange.join('-'));
    }
    if (filters.sortBy !== 'relevance') params.set('sort', filters.sortBy);
    if (filters.groupBy !== 'none') params.set('group', filters.groupBy);

    // Navigate to Products with all filters
    setLocation(`/products?${params.toString()}`);
    setIsOpen(false);
    onSearch(filters);
  };

  const clearFilters = () => {
    setFilters({
      query: "",
      plantParts: [],
      industries: [],
      stages: [],
      sustainabilityRange: [0, 10],
      sortBy: "relevance",
      groupBy: "none"
    });
  };

  const activeFilterCount = 
    (filters.query ? 1 : 0) +
    filters.plantParts.length +
    filters.industries.length +
    filters.stages.length +
    (filters.sustainabilityRange[0] > 0 || filters.sustainabilityRange[1] < 10 ? 1 : 0) +
    (filters.sortBy !== 'relevance' ? 1 : 0) +
    (filters.groupBy !== 'none' ? 1 : 0);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <SlidersHorizontal className="h-4 w-4" />
          Advanced Search
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-1">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Advanced Product Search</DialogTitle>
          <DialogDescription>
            Use multiple filters to find exactly what you're looking for
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Search Query */}
          <div className="space-y-2">
            <Label htmlFor="search-query">Search Terms</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="search-query"
                placeholder="Enter product names, benefits, or keywords..."
                value={filters.query}
                onChange={(e) => setFilters({ ...filters, query: e.target.value })}
                className="pl-10"
              />
            </div>
          </div>

          {/* Plant Parts */}
          <div className="space-y-2">
            <Label>Plant Parts</Label>
            <div className="grid grid-cols-2 gap-2">
              {plantParts?.map((part) => (
                <div key={part.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`part-${part.id}`}
                    checked={filters.plantParts.includes(part.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFilters({ ...filters, plantParts: [...filters.plantParts, part.id] });
                      } else {
                        setFilters({ ...filters, plantParts: filters.plantParts.filter(id => id !== part.id) });
                      }
                    }}
                  />
                  <Label htmlFor={`part-${part.id}`} className="text-sm cursor-pointer">
                    {part.name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Industries */}
          <div className="space-y-2">
            <Label>Industries</Label>
            <div className="grid grid-cols-2 gap-2">
              {industries?.map((industry) => (
                <div key={industry.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`industry-${industry.id}`}
                    checked={filters.industries.includes(industry.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFilters({ ...filters, industries: [...filters.industries, industry.id] });
                      } else {
                        setFilters({ ...filters, industries: filters.industries.filter(id => id !== industry.id) });
                      }
                    }}
                  />
                  <Label htmlFor={`industry-${industry.id}`} className="text-sm cursor-pointer">
                    {industry.name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Commercialization Stages */}
          <div className="space-y-2">
            <Label>Commercialization Stage</Label>
            <div className="grid grid-cols-2 gap-2">
              {stages.map((stage) => (
                <div key={stage.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`stage-${stage.id}`}
                    checked={filters.stages.includes(stage.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFilters({ ...filters, stages: [...filters.stages, stage.id] });
                      } else {
                        setFilters({ ...filters, stages: filters.stages.filter(id => id !== stage.id) });
                      }
                    }}
                  />
                  <Label htmlFor={`stage-${stage.id}`} className="text-sm cursor-pointer flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${stage.color}`} />
                    {stage.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Sustainability Range */}
          <div className="space-y-2">
            <Label>Sustainability Score Range</Label>
            <div className="px-4">
              <Slider
                value={filters.sustainabilityRange}
                onValueChange={(value) => setFilters({ ...filters, sustainabilityRange: value as [number, number] })}
                min={0}
                max={10}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-gray-500 mt-1">
                <span>{filters.sustainabilityRange[0]}</span>
                <span>to</span>
                <span>{filters.sustainabilityRange[1]}</span>
              </div>
            </div>
          </div>

          {/* Sort By */}
          <div className="space-y-2">
            <Label htmlFor="sort-by">Sort By</Label>
            <Select value={filters.sortBy} onValueChange={(value) => setFilters({ ...filters, sortBy: value })}>
              <SelectTrigger id="sort-by">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">Relevance</SelectItem>
                <SelectItem value="name">Name (A-Z)</SelectItem>
                <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                <SelectItem value="stage">Stage</SelectItem>
                <SelectItem value="sustainability">Sustainability Score</SelectItem>
                <SelectItem value="newest">Newest First</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Group By */}
          <div className="space-y-2">
            <Label htmlFor="group-by">Group Results By</Label>
            <Select value={filters.groupBy} onValueChange={(value) => setFilters({ ...filters, groupBy: value })}>
              <SelectTrigger id="group-by">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No Grouping</SelectItem>
                <SelectItem value="plantPart">Plant Part</SelectItem>
                <SelectItem value="industry">Industry</SelectItem>
                <SelectItem value="stage">Stage</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="ghost" onClick={clearFilters}>
            Clear All Filters
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              Apply Filters
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}