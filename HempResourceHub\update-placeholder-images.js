import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY environment variable is not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Placeholder image URL (will be served from your app)
const PLACEHOLDER_IMAGE_URL = '/images/unknown-hemp-image.png';

async function updatePlaceholderImages() {
  try {
    console.log('Fetching products without images...');
    
    // Get all products that don't have an image_url or have a null/empty image_url
    const { data: products, error: fetchError } = await supabase
      .from('uses_products')
      .select('id, name, image_url')
      .or('image_url.is.null,image_url.eq.');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products without images`);
    
    if (products.length === 0) {
      console.log('All products already have images!');
      return;
    }
    
    // Update each product with the placeholder image
    let updateCount = 0;
    let errorCount = 0;
    
    for (const product of products) {
      const { error: updateError } = await supabase
        .from('uses_products')
        .update({ image_url: PLACEHOLDER_IMAGE_URL })
        .eq('id', product.id);
      
      if (updateError) {
        console.error(`Error updating product ${product.id} (${product.name}):`, updateError);
        errorCount++;
      } else {
        console.log(`✓ Updated product ${product.id}: ${product.name}`);
        updateCount++;
      }
    }
    
    console.log('\n=== Update Summary ===');
    console.log(`Total products found: ${products.length}`);
    console.log(`Successfully updated: ${updateCount}`);
    console.log(`Errors: ${errorCount}`);
    
    // Also check for products with placeholder URLs that might need updating
    const { data: placeholderProducts, error: placeholderError } = await supabase
      .from('uses_products')
      .select('id, name, image_url')
      .like('image_url', '%placeholder%');
    
    if (!placeholderError && placeholderProducts.length > 0) {
      console.log(`\nFound ${placeholderProducts.length} products with existing placeholder images`);
      console.log('Consider updating these as well if needed.');
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the update
updatePlaceholderImages();