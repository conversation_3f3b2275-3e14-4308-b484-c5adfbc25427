import React from 'react';
import { motion } from 'framer-motion';

interface HempGrowingLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
}

export const HempGrowingLoader: React.FC<HempGrowingLoaderProps> = ({ 
  size = 'md', 
  message = 'Growing your hemp data...' 
}) => {
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32'
  };

  const containerVariants = {
    animate: {
      transition: {
        staggerChildren: 0.3,
        repeat: Infinity,
        repeatType: "loop" as const,
        duration: 3
      }
    }
  };

  const stemVariants = {
    initial: { scaleY: 0, opacity: 0 },
    animate: { 
      scaleY: 1, 
      opacity: 1,
      transition: { duration: 1, ease: "easeOut" }
    }
  };

  const leafVariants = {
    initial: { scale: 0, rotate: -45, opacity: 0 },
    animate: { 
      scale: 1, 
      rotate: 0, 
      opacity: 1,
      transition: { duration: 0.8, ease: "backOut" }
    }
  };

  const rootVariants = {
    initial: { scaleX: 0, opacity: 0 },
    animate: { 
      scaleX: 1, 
      opacity: 0.6,
      transition: { duration: 1.2, ease: "easeOut" }
    }
  };

  const swayVariants = {
    animate: {
      rotate: [-2, 2, -2],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <motion.div
        className={`relative ${sizeClasses[size]} mb-4`}
        variants={containerVariants}
        initial="initial"
        animate="animate"
      >
        {/* Soil base */}
        <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-amber-800 to-amber-900 rounded-full"></div>
        
        {/* Root system */}
        <motion.svg
          className="absolute bottom-0 left-1/2 transform -translate-x-1/2"
          width="60%"
          height="30%"
          viewBox="0 0 60 30"
          variants={rootVariants}
        >
          <path
            d="M30 0 Q20 10 10 15 M30 0 Q25 8 15 12 M30 0 Q35 8 45 12 M30 0 Q40 10 50 15"
            stroke="#8B4513"
            strokeWidth="1.5"
            fill="none"
            opacity="0.6"
          />
        </motion.svg>

        {/* Main stem */}
        <motion.div
          className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-t from-green-800 to-green-600 rounded-full"
          style={{ height: '70%', transformOrigin: 'bottom' }}
          variants={stemVariants}
        />

        {/* Hemp plant with sway animation */}
        <motion.div
          className="absolute bottom-2 left-1/2 transform -translate-x-1/2"
          variants={swayVariants}
          style={{ transformOrigin: 'bottom' }}
        >
          {/* Leaves */}
          <motion.div
            className="absolute bottom-8 -left-3"
            variants={leafVariants}
          >
            <svg width="24" height="16" viewBox="0 0 24 16">
              <path
                d="M2 8 Q8 2 12 8 Q16 2 22 8 Q16 14 12 8 Q8 14 2 8"
                fill="#22c55e"
                opacity="0.9"
              />
            </svg>
          </motion.div>

          <motion.div
            className="absolute bottom-12 -right-2"
            variants={leafVariants}
            transition={{ delay: 0.2 }}
          >
            <svg width="20" height="14" viewBox="0 0 20 14">
              <path
                d="M2 7 Q6 2 10 7 Q14 2 18 7 Q14 12 10 7 Q6 12 2 7"
                fill="#16a34a"
                opacity="0.8"
              />
            </svg>
          </motion.div>

          <motion.div
            className="absolute bottom-16 -left-2"
            variants={leafVariants}
            transition={{ delay: 0.4 }}
          >
            <svg width="18" height="12" viewBox="0 0 18 12">
              <path
                d="M2 6 Q5 2 9 6 Q13 2 16 6 Q13 10 9 6 Q5 10 2 6"
                fill="#15803d"
                opacity="0.7"
              />
            </svg>
          </motion.div>

          {/* Top bud/flower */}
          <motion.div
            className="absolute -top-2 left-1/2 transform -translate-x-1/2"
            variants={leafVariants}
            transition={{ delay: 0.6 }}
          >
            <div className="w-3 h-4 bg-gradient-to-t from-green-600 to-green-400 rounded-full opacity-80"></div>
          </motion.div>
        </motion.div>

        {/* Floating particles */}
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-green-400 rounded-full"
            initial={{ 
              x: Math.random() * 60 - 30, 
              y: 20, 
              opacity: 0 
            }}
            animate={{ 
              y: -20, 
              opacity: [0, 1, 0],
              x: Math.random() * 60 - 30
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "easeOut"
            }}
          />
        ))}
      </motion.div>

      {/* Loading message */}
      <motion.p
        className="text-green-400 text-sm font-medium text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        {message}
      </motion.p>

      {/* Progress dots */}
      <motion.div
        className="flex space-x-1 mt-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
      >
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-green-400 rounded-full"
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: i * 0.2
            }}
          />
        ))}
      </motion.div>
    </div>
  );
};

export default HempGrowingLoader;
