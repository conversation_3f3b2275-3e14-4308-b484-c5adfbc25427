#!/usr/bin/env python3
"""
Fix specific problematic URLs in the database
"""
import os
import requests
from dotenv import load_dotenv

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
}

# Specific URL fixes
url_fixes = {
    "Bob's Red Mill": "https://www.bobsredmill.com",  # Fix apostrophe
    "365 Whole Foods": "https://www.wholefoodsmarket.com/brands/365-by-whole-foods-market",  # Correct URL
    "NOW Sports": "https://www.nowfoods.com/sports-nutrition",  # Correct subdomain
    "Hemp Hearts": "https://www.manitobaharvest.com",  # Use parent company
}

def fix_specific_urls():
    """Fix specific problematic URLs"""
    print("🔧 Fixing specific problematic URLs...")
    
    fixed_count = 0
    
    for company_name, correct_url in url_fixes.items():
        # Find company by name
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/hemp_companies",
            headers=headers,
            params={"name": f"eq.{company_name}", "select": "id,name,website"}
        )
        
        companies = response.json()
        
        if companies:
            company = companies[0]
            
            # Update URL
            update_response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/hemp_companies?id=eq.{company['id']}",
                headers=headers,
                json={"website": correct_url}
            )
            
            if update_response.status_code in [200, 204]:
                fixed_count += 1
                print(f"✅ Fixed: {company_name} → {correct_url}")
            else:
                print(f"❌ Failed to fix: {company_name}")
        else:
            print(f"⚠️  Company not found: {company_name}")
    
    print(f"\n📊 Fixed {fixed_count} specific URLs")
    
    return fixed_count

if __name__ == "__main__":
    fix_specific_urls()