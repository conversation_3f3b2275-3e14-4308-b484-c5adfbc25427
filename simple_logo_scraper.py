#!/usr/bin/env python3
"""
Simple logo scraper that just saves logo URLs without attribution
"""
import os
import requests
import time
from datetime import datetime, timezone
from dotenv import load_dotenv
from urllib.parse import urljoin, urlparse

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

class SimpleLogoScraper:
    def __init__(self):
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        
    def extract_logo(self, company_name: str, website: str) -> str:
        """Extract logo URL from website"""
        # Ensure website has a scheme
        if website and not website.startswith(('http://', 'https://')):
            website = f"https://{website}"
            
        try:
            response = requests.get(website, 
                                  headers={'User-Agent': self.user_agent}, 
                                  timeout=10,
                                  allow_redirects=True)
            
            if response.status_code == 200:
                html = response.text
                
                # Method 1: Open Graph image
                import re
                og_patterns = [
                    r'property="og:image"\s+content="([^"]+)"',
                    r'property=\'og:image\'\s+content=\'([^\']+)\'',
                    r'content="([^"]+)"\s+property="og:image"',
                    r'content=\'([^\']+)\'\s+property=\'og:image\''
                ]
                
                for pattern in og_patterns:
                    match = re.search(pattern, html, re.IGNORECASE)
                    if match:
                        logo_url = match.group(1)
                        return urljoin(website, logo_url)
                
                # Method 2: Look for common logo patterns
                logo_patterns = [
                    r'<img[^>]*class=["\'][^"\']*logo[^"\']*["\'][^>]*src=["\']([^"\']+)["\']',
                    r'<img[^>]*id=["\'][^"\']*logo[^"\']*["\'][^>]*src=["\']([^"\']+)["\']',
                    r'<img[^>]*src=["\']([^"\']*logo[^"\']+\.(png|jpg|jpeg|svg))["\']',
                    r'<img[^>]*alt=["\'][^"\']*logo[^"\']*["\'][^>]*src=["\']([^"\']+)["\']'
                ]
                
                for pattern in logo_patterns:
                    matches = re.findall(pattern, html, re.IGNORECASE)
                    for match in matches:
                        logo_url = match[0] if isinstance(match, tuple) else match
                        if logo_url and not any(bad in logo_url.lower() for bad in ['placeholder', 'loading', 'spinner']):
                            return urljoin(website, logo_url)
                
        except Exception as e:
            print(f"   Error: {str(e)[:80]}...")
            
        return None
    
    def update_company_logos(self, limit: int = 50):
        """Update companies without logos"""
        # Get companies without logos but with websites
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/hemp_companies",
            headers=headers,
            params={
                "logo_url": "is.null",
                "website": "not.is.null",
                "select": "id,name,website",
                "limit": limit
            }
        )
        
        companies = response.json()
        print(f"🔍 Found {len(companies)} companies without logos\n")
        
        updated = 0
        
        for company in companies:
            print(f"🏢 {company['name']}")
            print(f"   Website: {company['website']}")
            
            logo_url = self.extract_logo(company['name'], company['website'])
            
            if logo_url:
                # Just update the logo_url field
                update_data = {
                    "logo_url": logo_url,
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                response = requests.patch(
                    f"{SUPABASE_URL}/rest/v1/hemp_companies?id=eq.{company['id']}",
                    headers=headers,
                    json=update_data
                )
                
                if response.status_code in [200, 204]:
                    updated += 1
                    print(f"   ✅ Found logo: {logo_url[:80]}...")
                else:
                    print(f"   ❌ Failed to save: {response.text[:80]}...")
            else:
                print(f"   ⚠️  No logo found")
                
            print()  # Empty line between companies
            time.sleep(1)  # Rate limiting
        
        return updated


def main():
    """Run simple logo scraping"""
    print("🚀 Simple Logo Scraper\n")
    
    scraper = SimpleLogoScraper()
    
    # Update logos
    updated = scraper.update_company_logos(limit=30)
    
    print(f"\n✅ Updated {updated} company logos")
    
    # Show stats
    response = requests.get(
        f"{SUPABASE_URL}/rest/v1/hemp_companies?select=id&logo_url=not.is.null", 
        headers={**headers, "Prefer": "count=exact"}
    )
    
    if 'content-range' in response.headers:
        total_with_logos = response.headers['content-range'].split('/')[1]
        print(f"📊 Total companies with logos: {total_with_logos}")


if __name__ == "__main__":
    main()