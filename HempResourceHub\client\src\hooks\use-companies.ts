import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase-client';

export interface HempCompany {
  id: number;
  name: string;
  description?: string;
  website?: string;
  country?: string;
  city?: string;
  state_province?: string;
  postal_code?: string;
  founded_year?: number;
  company_type?: string;
  verified: boolean;
  logo_url?: string;
  created_at: string;
  updated_at: string;
  product_count?: number;
  latitude?: number;
  longitude?: number;
}

export function useCompanies() {
  return useQuery<HempCompany[]>({
    queryKey: ['hemp-companies'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('hemp_companies')
        .select(`
          *,
          hemp_company_products(count)
        `)
        .order('name');

      if (error) {
        console.error('Error fetching companies:', error);
        throw error;
      }

      return (data || []).map(company => ({
        ...company,
        product_count: company.hemp_company_products?.[0]?.count || 0
      }));
    },
  });
}

export interface CompanyProduct {
  id: number;
  name: string;
  description?: string;
  commercialization_stage?: string;
  plant_part?: string;
  industry?: string;
  sub_industry?: string;
  relationship_type?: string;
  is_primary?: boolean;
}

export interface HempCompanyWithProducts extends HempCompany {
  products?: CompanyProduct[];
}

export function useCompany(id: number) {
  return useQuery<HempCompanyWithProducts>({
    queryKey: ['hemp-company', id],
    queryFn: async () => {
      // Don't query if ID is invalid
      if (id <= 0) {
        return null;
      }
      
      const { data, error } = await supabase
        .from('hemp_companies')
        .select(`
          *,
          hemp_company_products!inner(
            relationship_type,
            is_primary,
            uses_products!inner(
              id,
              name,
              description,
              commercialization_stage,
              plant_parts(name),
              industry_sub_categories(
                name,
                industries(name)
              )
            )
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching company:', error);
        throw error;
      }

      // Transform the data to flatten the structure
      const products = data.hemp_company_products?.map((cp: any) => ({
        id: cp.uses_products.id,
        name: cp.uses_products.name,
        description: cp.uses_products.description,
        commercialization_stage: cp.uses_products.commercialization_stage,
        plant_part: cp.uses_products.plant_parts?.name,
        industry: cp.uses_products.industry_sub_categories?.industries?.name,
        sub_industry: cp.uses_products.industry_sub_categories?.name,
        relationship_type: cp.relationship_type,
        is_primary: cp.is_primary
      })) || [];

      return {
        ...data,
        products
      };
    },
  });
}

export function useCompaniesWithLocation() {
  return useQuery<HempCompany[]>({
    queryKey: ['hemp-companies-with-location'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('hemp_companies')
        .select('*')
        .not('latitude', 'is', null)
        .not('longitude', 'is', null)
        .order('name');

      if (error) {
        console.error('Error fetching companies with location:', error);
        throw error;
      }

      return data || [];
    },
  });
}