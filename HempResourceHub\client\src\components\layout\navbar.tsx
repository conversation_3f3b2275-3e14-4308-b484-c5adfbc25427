import { useState } from "react";
import { Link, useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Menu, X, Shield, Command, User, LogOut, Settings } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { SmartSearch } from "@/components/ui/smart-search";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useAuth } from "@/components/auth/auth-provider";
import HempQuarterzLogo from "@/assets/circle-logo.png?url";

const Navbar = () => {
  const [location, setLocation] = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, signOut, isAdmin, loading } = useAuth();

  const handleSearch = (query: string) => {
    setLocation(`/products?search=${encodeURIComponent(query)}`);
  };

  const handleSignOut = async () => {
    await signOut();
    setLocation('/');
  };

  return (
    <nav className="bg-black/80 backdrop-blur-md shadow-md relative z-20" data-oid="h2fdc:v">
      <div
        className="max-w-7xl mx-auto px-4 sm:px-8 lg:px-16 bg-black"
        data-oid="92uq7ki"
      >
        <div
          className="flex justify-between items-center h-28"
          data-oid="p0vze9n"
        >
          {/* Logo on the left */}
          <div className="flex-shrink-0 flex items-center" data-oid="2jgk4ux">
            <Link href="/" data-oid="zgs56oy">
              <img
                src={HempQuarterzLogo}
                alt="HempQuarterz Logo"
                className="h-24 w-24 cursor-pointer rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300"
                data-oid="46aw9d3"
              />
            </Link>
          </div>

          {/* Navigation menu in the center */}
          <div
            className="hidden sm:flex sm:flex-1 sm:justify-center"
            data-oid="k4k0r5q"
          >
            <div className="flex flex-col items-center" data-oid="rvs.3j4">
              {/* Top row */}
              <div
                className="flex space-x-6 mb-2 items-center"
                data-oid="lfhq:i2"
              >
                <Link href="/" data-oid=".oqojdu">
                  <div
                    className={`${location === "/" ? "border-primary text-primary" : "border-transparent text-white hover:text-primary hover:border-primary"} border-b-2 px-0.5 pt-1 text-lg font-medium whitespace-nowrap cursor-pointer transition-all duration-200 hover:scale-105`}
                    data-oid="vz_4igw"
                  >
                    Home
                  </div>
                </Link>
                <Link href="/about" data-oid=".kwpdto">
                  <div
                    className={`${location === "/about" ? "border-primary text-primary" : "border-transparent text-white hover:text-primary hover:border-primary"} border-b-2 px-0.5 pt-1 text-lg font-medium whitespace-nowrap cursor-pointer transition-all duration-200 hover:scale-105`}
                    data-oid="d14t:g5"
                  >
                    About
                  </div>
                </Link>
                <Link href="/plant-parts" data-oid="oq1i_m4">
                  <div
                    className={`${location.startsWith("/plant-part") ? "border-primary text-primary" : "border-transparent text-white hover:text-primary hover:border-primary"} border-b-2 px-0.5 pt-1 text-lg font-medium whitespace-nowrap cursor-pointer transition-all duration-200 hover:scale-105`}
                    data-oid="q93a8vi"
                  >
                    Parts of Plant
                  </div>
                </Link>
                <Link href="/industries" data-oid="scc3vl.">
                  <div
                    className={`${location === "/industries" ? "border-primary text-primary" : "border-transparent text-white hover:text-primary hover:border-primary"} border-b-2 px-0.5 pt-1 text-lg font-medium whitespace-nowrap cursor-pointer transition-all duration-200 hover:scale-105`}
                    data-oid="tusaxam"
                  >
                    Industries
                  </div>
                </Link>
              </div>

              {/* Bottom row */}
              <div className="flex space-x-6 items-center" data-oid="fqaw6wj">
                <Link href="/hemp-companies" data-oid="hemp-companies-link">
                  <div
                    className={`${location === "/hemp-companies" ? "border-primary text-primary" : "border-transparent text-white hover:text-primary hover:border-primary"} border-b-2 px-0.5 pt-1 text-lg font-medium whitespace-nowrap cursor-pointer transition-all duration-200 hover:scale-105`}
                    data-oid="hemp-companies-nav"
                  >
                    Companies
                  </div>
                </Link>
                <Link href="/research" data-oid="uzsb2jr">
                  <div
                    className={`${location.startsWith("/research") ? "border-primary text-primary" : "border-transparent text-white hover:text-primary hover:border-primary"} border-b-2 px-0.5 pt-1 text-lg font-medium whitespace-nowrap cursor-pointer transition-all duration-200 hover:scale-105`}
                    data-oid="8q.0.zq"
                  >
                    Research
                  </div>
                </Link>
                <Link href="/products" data-oid="products-nav">
                  <div
                    className={`${
                      location === "/products" || location.includes("/products") || location.includes("/hemp-dex")
                        ? "border-primary text-primary"
                        : "border-transparent text-white hover:text-primary hover:border-primary"
                    } border-b-2 px-0.5 pt-1 text-lg font-medium whitespace-nowrap cursor-pointer transition-all duration-200 hover:scale-105`}
                    data-oid="products-nav-link"
                  >
                    Products
                  </div>
                </Link>
              </div>
            </div>
          </div>

          {/* Search and Admin on the right */}
          <div
            className="hidden sm:flex sm:items-center sm:gap-4 sm:pr-2"
            data-oid="9gs1kwm"
          >
            {/* Enhanced Smart Search */}
            <SmartSearch
              className="transition-all duration-300 w-52 hover:scale-105 focus-within:scale-105 hover:shadow-lg focus-within:shadow-lg"
              onSearch={handleSearch}
              showAISuggestions={true}
              showVoiceSearch={true}
              showImageSearch={false}
              placeholder="Search"
            />

            {/* Authentication Controls */}
            {!loading && (
              <>
                {user ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className="border-green-500/50 bg-green-500/10 hover:bg-green-500/20 text-green-400 hover:text-green-300 flex items-center gap-2"
                      >
                        <User className="h-4 w-4" />
                        <span className="hidden md:inline">
                          {user.user_metadata?.first_name || user.email?.split('@')[0] || 'User'}
                        </span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-gray-900 border-gray-700">
                      <DropdownMenuItem className="text-gray-300 hover:bg-gray-800">
                        <User className="mr-2 h-4 w-4" />
                        <span>{user.email}</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator className="bg-gray-700" />
                      {isAdmin && (
                        <>
                          <DropdownMenuItem
                            className="text-gray-300 hover:bg-gray-800 cursor-pointer"
                            onClick={() => setLocation('/admin')}
                          >
                            <Shield className="mr-2 h-4 w-4" />
                            <span>Admin Panel</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-gray-300 hover:bg-gray-800 cursor-pointer"
                            onClick={() => setLocation('/admin-settings')}
                          >
                            <Settings className="mr-2 h-4 w-4" />
                            <span>Settings</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-gray-700" />
                        </>
                      )}
                      <DropdownMenuItem
                        className="text-red-400 hover:bg-gray-800 cursor-pointer"
                        onClick={handleSignOut}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        <span>Sign Out</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <div className="flex items-center gap-2">
                    <Link href="/login">
                      <Button
                        variant="outline"
                        className="border-green-500/50 bg-green-500/10 hover:bg-green-500/20 text-green-400 hover:text-green-300"
                      >
                        Sign In
                      </Button>
                    </Link>
                    <Link href="/register">
                      <Button className="bg-green-600 hover:bg-green-700 text-white">
                        Sign Up
                      </Button>
                    </Link>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="-mr-2 flex items-center sm:hidden" data-oid="sasr.ec">
            <Sheet data-oid="ry8ylhu">
              <SheetTrigger asChild data-oid="6i9wsp8">
                <Button
                  variant="ghost"
                  size="icon"
                  className="inline-flex items-center justify-center p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500 transition-colors"
                  data-oid="d5b:m.l"
                >
                  <span className="sr-only" data-oid="p1yss5v">
                    Open main menu
                  </span>
                  <Menu className="h-6 w-6" data-oid="71ovt:2" />
                </Button>
              </SheetTrigger>
              <SheetContent
                side="right"
                className="bg-black"
                data-oid="2n_u-.d"
              >
                <div className="flex flex-col h-full" data-oid="jjpdw57">
                  <div
                    className="flex items-center justify-between pb-4 border-b border-gray-800"
                    data-oid="n0ay509"
                  >
                    <Link href="/" data-oid="ry8bx-f">
                      <img
                        src={HempQuarterzLogo}
                        alt="HempQuarterz Logo"
                        className="h-24 w-24 cursor-pointer rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300"
                        data-oid="s.sa8u:"
                      />
                    </Link>
                  </div>

                  <div className="mt-6" data-oid="8x3912g">
                    <SmartSearch
                      className="w-full mb-6"
                      onSearch={(query) => {
                        handleSearch(query);
                        setIsMenuOpen(false);
                      }}
                      showAISuggestions={true}
                      showVoiceSearch={true}
                      showImageSearch={false}
                      placeholder="Search"
                    />

                    <nav className="flex flex-col space-y-4" data-oid="c01ht9b">
                      <Link href="/" data-oid="yqn-n_4">
                        <div
                          className={`${location === "/" ? "text-primary font-medium" : "text-white"} hover:text-primary px-3 py-2 text-xl cursor-pointer`}
                          data-oid="_opk:7e"
                        >
                          Home
                        </div>
                      </Link>
                      <Link href="/about" data-oid="egoutpt">
                        <div
                          className={`${location === "/about" ? "text-primary font-medium" : "text-white"} hover:text-primary px-3 py-2 text-xl cursor-pointer`}
                          data-oid="unmdvih"
                        >
                          About
                        </div>
                      </Link>
                      <Link href="/plant-parts" data-oid="qvds8x8">
                        <div
                          className={`${location.startsWith("/plant-part") ? "text-primary font-medium" : "text-white"} hover:text-primary px-3 py-2 text-xl cursor-pointer`}
                          data-oid="x2m31gi"
                        >
                          Parts of Plant
                        </div>
                      </Link>
                      <Link href="/industries" data-oid="w4t0_sn">
                        <div
                          className={`${location === "/industries" ? "text-primary font-medium" : "text-white"} hover:text-primary px-3 py-2 text-xl cursor-pointer`}
                          data-oid="8m:7m_r"
                        >
                          Industries
                        </div>
                      </Link>
                      <Link href="/hemp-companies" data-oid="hemp-companies-mobile">
                        <div
                          className={`${location === "/hemp-companies" ? "text-primary font-medium" : "text-white"} hover:text-primary px-3 py-2 text-xl cursor-pointer`}
                          data-oid="hemp-companies-mobile-nav"
                        >
                          Companies
                        </div>
                      </Link>
                      <Link href="/research" data-oid="zbxxzj2">
                        <div
                          className={`${location.startsWith("/research") ? "text-primary font-medium" : "text-white"} hover:text-primary px-3 py-2 text-xl cursor-pointer`}
                          data-oid="mdzr94x"
                        >
                          Research
                        </div>
                      </Link>
                      <Link href="/products" data-oid="products-mobile">
                        <div
                          className={`${location === "/products" || location.includes("/products") || location.includes("/hemp-dex") ? "text-primary font-medium" : "text-white"} hover:text-primary px-3 py-2 text-xl cursor-pointer`}
                          data-oid="products-mobile-nav"
                        >
                          Products
                        </div>
                      </Link>
                      
                      {/* Admin Link in Mobile Menu */}
                      <div className="border-t border-gray-800 mt-4 pt-4">
                        <Link href="/admin">
                          <div
                            className={`${location === "/admin" ? "text-green-400 font-medium" : "text-gray-400"} hover:text-green-400 px-3 py-2 text-xl cursor-pointer flex items-center gap-2`}
                          >
                            <Shield className="h-5 w-5" />
                            Admin Dashboard
                          </div>
                        </Link>
                      </div>
                    </nav>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
