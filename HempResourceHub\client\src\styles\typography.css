/* Typography Improvements */

/* Heading Hierarchy with New Font Strategy */
h1, .text-h1 {
  @apply text-4xl sm:text-5xl lg:text-6xl leading-tight;
  @apply text-white;
  font-family: 'Inter', system-ui, sans-serif;
  font-weight: 900; /* Black weight */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

h2, .text-h2 {
  @apply text-3xl sm:text-4xl lg:text-5xl leading-tight;
  @apply text-white;
  font-family: 'Inter', system-ui, sans-serif;
  font-weight: 800; /* Extra Bold */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

h3, .text-h3 {
  @apply text-2xl sm:text-3xl lg:text-4xl leading-snug;
  @apply text-white;
  font-family: 'Inter', system-ui, sans-serif;
  font-weight: 700; /* Bold */
}

h4, .text-h4 {
  @apply text-xl sm:text-2xl lg:text-3xl;
  @apply text-gray-100;
  font-family: 'Inter', system-ui, sans-serif;
  font-weight: 600; /* Semi-Bold */
}

h5, .text-h5 {
  @apply text-lg sm:text-xl lg:text-2xl;
  @apply text-gray-200;
  font-family: 'Space Grotesk', system-ui, sans-serif;
  font-weight: 500; /* Medium */
}

h6, .text-h6 {
  @apply text-base sm:text-lg lg:text-xl;
  @apply text-gray-300;
  font-family: 'Space Grotesk', system-ui, sans-serif;
  font-weight: 500; /* Medium */
}

/* Body Text */
.text-body-lg {
  @apply text-base sm:text-lg leading-relaxed text-gray-300;
}

.text-body {
  @apply text-sm sm:text-base leading-relaxed text-gray-400;
}

.text-body-sm {
  @apply text-xs sm:text-sm leading-relaxed text-gray-500;
}

/* Special Text Effects */
.text-gradient-hemp {
  @apply bg-gradient-to-r from-green-400 via-green-300 to-emerald-400 bg-clip-text text-transparent;
}

.text-gradient-primary {
  @apply bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent;
}

.text-glow {
  text-shadow: 0 0 20px rgba(34, 197, 94, 0.5), 0 0 40px rgba(34, 197, 94, 0.3);
}

/* Improve readability on dark backgrounds */
.text-readable-dark {
  @apply text-gray-100;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* Link styles */
a.text-link {
  @apply text-green-400 hover:text-green-300 transition-colors underline-offset-4 hover:underline;
}

/* Label styles */
.text-label {
  @apply text-xs font-medium uppercase tracking-wider text-gray-400;
}

/* Caption styles */
.text-caption {
  @apply text-xs text-gray-500;
}

/* Responsive font scaling */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 1536px) {
  html {
    font-size: 18px;
  }
}