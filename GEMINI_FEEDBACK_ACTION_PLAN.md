# Action Plan for Gemini's Feedback

## Summary of Analysis

After reviewing <PERSON>'s feedback and examining the codebase, I've identified key areas for improvement. This action plan addresses both the automation agents and UI improvements with practical, implementable solutions.

## Key Findings

### 1. Automation Agent Issues
- **Error Handling**: The base agent has basic retry logic with tenacity but lacks notifications and comprehensive error recovery
- **Dependency Management**: Currently using basic requirements.txt without version pinning
- **Agent Orchestration**: LangGraph integration exists but could leverage more advanced features
- **Test Coverage**: Minimal test coverage for agents

### 2. UI/UX Issues
- **HempDex**: Has basic search and sorting but lacks advanced filtering capabilities
- **Performance**: Multiple issues identified in PERFORMANCE_OPTIMIZATION_REPORT.md including no code splitting, poor image optimization, and missing caching
- **Admin Dashboard**: Not reviewed yet but likely needs UX improvements
- **Accessibility**: Listed as low priority in improvement plan

## Priority 1: Agent Improvements (Week 1)

### 1.1 Enhanced Error Handling & Notifications
```python
# agents/core/enhanced_base_agent.py

import asyncio
from typing import Optional
import aiosmtplib
from email.message import EmailMessage

class NotificationService:
    """Handle critical error notifications"""
    
    async def send_email_notification(self, subject: str, body: str):
        """Send email for critical failures"""
        if not os.environ.get('NOTIFICATION_EMAIL'):
            return
            
        message = EmailMessage()
        message['From'] = '<EMAIL>'
        message['To'] = os.environ.get('NOTIFICATION_EMAIL')
        message['Subject'] = f"[Hemp Agent Alert] {subject}"
        message.set_content(body)
        
        try:
            await aiosmtplib.send(
                message,
                hostname=os.environ.get('SMTP_HOST', 'localhost'),
                port=int(os.environ.get('SMTP_PORT', 587))
            )
        except Exception as e:
            print(f"Failed to send notification: {e}")

class EnhancedBaseAgent(BaseAgent):
    """Enhanced base agent with better error handling"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.notification_service = NotificationService()
        self.retry_config = {
            'max_attempts': 3,
            'backoff_base': 2,
            'max_backoff': 60
        }
    
    async def execute_with_retry(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task with exponential backoff retry"""
        last_error = None
        
        for attempt in range(self.retry_config['max_attempts']):
            try:
                return await self.execute(task)
            except Exception as e:
                last_error = e
                wait_time = min(
                    self.retry_config['backoff_base'] ** attempt,
                    self.retry_config['max_backoff']
                )
                
                self.logger.warning(
                    f"Attempt {attempt + 1} failed: {e}. "
                    f"Retrying in {wait_time}s..."
                )
                
                if attempt == self.retry_config['max_attempts'] - 1:
                    # Final attempt failed, send notification
                    await self._handle_critical_failure(task, e)
                
                await asyncio.sleep(wait_time)
        
        raise last_error
    
    async def _handle_critical_failure(self, task: Dict[str, Any], error: Exception):
        """Handle critical failures with notifications"""
        error_context = {
            'task_id': task.get('task_id'),
            'task_type': task.get('type'),
            'agent': self.agent_name,
            'error': str(error),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Log to database
        await self._save_to_database('critical_errors', error_context)
        
        # Send notification
        await self.notification_service.send_email_notification(
            subject=f"{self.agent_name} Critical Failure",
            body=f"Task {task.get('task_id')} failed after all retries:\n\n{error}"
        )
```

### 1.2 Modern Dependency Management with Poetry
```bash
# Initialize Poetry
cd /mnt/c/Users/<USER>/OneDrive/Desktop/HQz-Ai-DB-MCP-3
curl -sSL https://install.python-poetry.org | python3 -
poetry init

# Create pyproject.toml
```

```toml
# pyproject.toml
[tool.poetry]
name = "hemp-resource-hub"
version = "0.1.0"
description = "Industrial hemp resource management platform"
authors = ["HempQuarterz"]

[tool.poetry.dependencies]
python = "^3.9"
supabase = "^2.0.0"
openai = "^1.0.0"
anthropic = "^0.8.0"
python-dotenv = "^1.0.0"
requests = "^2.31.0"
pandas = "^2.0.0"
aiohttp = "^3.8.0"
tenacity = "^8.2.0"
langgraph = "^0.0.20"
aiosmtplib = "^3.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["agents"]
omit = ["tests/*", "venv/*", ".venv/*"]

[tool.black]
line-length = 88
target-version = ['py39']
```

### 1.3 Enhanced LangGraph Orchestration
```python
# agents/orchestration/enhanced_orchestrator.py

from langgraph.graph import StateGraph, END
from langgraph.checkpoint import MemorySaver
from typing import TypedDict, Annotated, Sequence
import operator

class AgentState(TypedDict):
    """Enhanced state with history and branching"""
    messages: Annotated[Sequence[str], operator.add]
    current_agent: str
    task_queue: list
    results: dict
    errors: list
    branch_history: list

class EnhancedOrchestrator:
    """Advanced orchestrator using LangGraph features"""
    
    def __init__(self):
        self.workflow = StateGraph(AgentState)
        self.memory = MemorySaver()
        self._setup_workflow()
    
    def _setup_workflow(self):
        """Setup conditional workflow with branching"""
        # Add nodes for each agent
        self.workflow.add_node("research", self.research_agent_node)
        self.workflow.add_node("content", self.content_agent_node)
        self.workflow.add_node("monetization", self.monetization_agent_node)
        self.workflow.add_node("outreach", self.outreach_agent_node)
        self.workflow.add_node("error_handler", self.error_handler_node)
        
        # Add conditional edges
        self.workflow.add_conditional_edges(
            "research",
            self.route_after_research,
            {
                "content": "content",
                "monetization": "monetization",
                "error": "error_handler",
                "end": END
            }
        )
        
        self.workflow.add_conditional_edges(
            "content",
            self.route_after_content,
            {
                "outreach": "outreach",
                "research": "research",
                "error": "error_handler",
                "end": END
            }
        )
        
        # Set entry point
        self.workflow.set_entry_point("research")
        
        # Compile with checkpointing
        self.app = self.workflow.compile(checkpointer=self.memory)
    
    def route_after_research(self, state: AgentState) -> str:
        """Dynamic routing based on research results"""
        if state.get("errors"):
            return "error"
        
        results = state.get("results", {}).get("research", {})
        
        # Route based on findings
        if results.get("new_products_found", 0) > 10:
            return "content"  # Create content about new products
        elif results.get("market_opportunity"):
            return "monetization"  # Explore monetization
        else:
            return "end"
    
    def route_after_content(self, state: AgentState) -> str:
        """Route based on content creation results"""
        content_results = state.get("results", {}).get("content", {})
        
        if content_results.get("high_quality_content"):
            return "outreach"  # Promote the content
        elif content_results.get("needs_more_research"):
            return "research"  # Back to research
        else:
            return "end"
    
    async def research_agent_node(self, state: AgentState) -> AgentState:
        """Execute research agent with state management"""
        try:
            # Run research agent
            result = await self.research_agent.execute(state["task_queue"][0])
            state["results"]["research"] = result
            state["messages"].append(f"Research completed: {result}")
        except Exception as e:
            state["errors"].append(str(e))
            state["messages"].append(f"Research failed: {e}")
        
        return state
```

### 1.4 Comprehensive Test Suite
```python
# tests/agents/test_enhanced_base_agent.py

import pytest
from unittest.mock import Mock, patch, AsyncMock
from agents.core.enhanced_base_agent import EnhancedBaseAgent

class TestEnhancedBaseAgent:
    """Comprehensive tests for enhanced base agent"""
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client"""
        client = Mock()
        client.table = Mock(return_value=Mock(
            insert=Mock(return_value=Mock(
                execute=AsyncMock(return_value=Mock(data=[{"id": 1}]))
            ))
        ))
        return client
    
    @pytest.fixture
    def agent(self, mock_supabase):
        """Create test agent instance"""
        return EnhancedBaseAgent(mock_supabase, "test_agent")
    
    @pytest.mark.asyncio
    async def test_retry_logic_success(self, agent):
        """Test successful execution after retry"""
        agent.execute = AsyncMock(
            side_effect=[Exception("First fail"), {"status": "success"}]
        )
        
        result = await agent.execute_with_retry({"task_id": "123"})
        
        assert result["status"] == "success"
        assert agent.execute.call_count == 2
    
    @pytest.mark.asyncio
    async def test_retry_logic_max_attempts(self, agent):
        """Test failure after max attempts"""
        agent.execute = AsyncMock(side_effect=Exception("Always fails"))
        agent._handle_critical_failure = AsyncMock()
        
        with pytest.raises(Exception):
            await agent.execute_with_retry({"task_id": "123"})
        
        assert agent.execute.call_count == 3
        agent._handle_critical_failure.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_notification_on_critical_failure(self, agent):
        """Test notification sent on critical failure"""
        with patch('agents.core.enhanced_base_agent.NotificationService') as mock_notif:
            mock_notif.return_value.send_email_notification = AsyncMock()
            
            await agent._handle_critical_failure(
                {"task_id": "123", "type": "research"},
                Exception("Critical error")
            )
            
            mock_notif.return_value.send_email_notification.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_exponential_backoff(self, agent):
        """Test exponential backoff timing"""
        agent.execute = AsyncMock(side_effect=Exception("Fail"))
        
        with patch('asyncio.sleep') as mock_sleep:
            mock_sleep.return_value = None
            
            try:
                await agent.execute_with_retry({"task_id": "123"})
            except:
                pass
            
            # Check backoff times: 1s, 2s, 4s...
            sleep_calls = [call[0][0] for call in mock_sleep.call_args_list]
            assert sleep_calls == [1, 2]  # 2 retries after initial attempt
```

## Priority 2: UI Performance Improvements (Week 2)

### 2.1 Implement Code Splitting
```typescript
// HempResourceHub/client/src/App.tsx

import { lazy, Suspense } from 'react';
import { Route, Switch } from 'wouter';
import LoadingSpinner from '@/components/ui/loading-spinner';

// Lazy load all route components
const HomePage = lazy(() => import('./pages/home'));
const HempDex = lazy(() => import('./pages/hemp-dex'));
const ProductListing = lazy(() => import('./pages/product-listing'));
const Research = lazy(() => import('./pages/research'));
const AdminDashboard = lazy(() => import('./pages/admin/dashboard'));

function App() {
  return (
    <Suspense fallback={<LoadingSpinner fullScreen />}>
      <Switch>
        <Route path="/" component={HomePage} />
        <Route path="/hemp-dex" component={HempDex} />
        <Route path="/products" component={ProductListing} />
        <Route path="/research" component={Research} />
        <Route path="/admin" component={AdminDashboard} />
      </Switch>
    </Suspense>
  );
}
```

### 2.2 Enhanced HempDex with Advanced Filtering
```typescript
// HempResourceHub/client/src/pages/hemp-dex-enhanced.tsx

import React, { useState, useMemo, useCallback } from 'react';
import { usePlantTypes, usePlantParts, useIndustries } from '@/hooks/use-plant-data';
import { PlantType } from '@shared/schema';
import { 
  Select, 
  Checkbox, 
  RangeSlider,
  MultiSelect 
} from '@/components/ui/enhanced-inputs';

interface Filters {
  search: string;
  plantParts: string[];
  industries: string[];
  thcRange: [number, number];
  cbdRange: [number, number];
  harvestTime: string;
  climate: string[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

const HempDexEnhanced = () => {
  const { data: plantTypes = [] } = usePlantTypes();
  const { data: plantParts = [] } = usePlantParts();
  const { data: industries = [] } = useIndustries();
  
  const [filters, setFilters] = useState<Filters>({
    search: '',
    plantParts: [],
    industries: [],
    thcRange: [0, 0.3],
    cbdRange: [0, 25],
    harvestTime: 'all',
    climate: [],
    sortBy: 'name',
    sortOrder: 'asc'
  });
  
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // Memoized filtered results
  const filteredPlants = useMemo(() => {
    let results = [...plantTypes];
    
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      results = results.filter(plant => 
        plant.name.toLowerCase().includes(searchLower) ||
        plant.description?.toLowerCase().includes(searchLower)
      );
    }
    
    // Plant parts filter
    if (filters.plantParts.length > 0) {
      results = results.filter(plant => 
        plant.parts?.some(part => filters.plantParts.includes(part.id))
      );
    }
    
    // Industries filter
    if (filters.industries.length > 0) {
      results = results.filter(plant =>
        plant.industries?.some(ind => filters.industries.includes(ind.id))
      );
    }
    
    // THC range filter
    results = results.filter(plant => {
      const thc = plant.thcContent || 0;
      return thc >= filters.thcRange[0] && thc <= filters.thcRange[1];
    });
    
    // CBD range filter
    results = results.filter(plant => {
      const cbd = plant.cbdContent || 0;
      return cbd >= filters.cbdRange[0] && cbd <= filters.cbdRange[1];
    });
    
    // Sorting
    results.sort((a, b) => {
      let comparison = 0;
      
      switch (filters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'thc':
          comparison = (a.thcContent || 0) - (b.thcContent || 0);
          break;
        case 'cbd':
          comparison = (a.cbdContent || 0) - (b.cbdContent || 0);
          break;
        case 'harvestTime':
          comparison = (a.harvestDays || 0) - (b.harvestDays || 0);
          break;
      }
      
      return filters.sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return results;
  }, [plantTypes, filters]);
  
  // Debounced search handler
  const handleSearch = useCallback(
    debounce((value: string) => {
      setFilters(prev => ({ ...prev, search: value }));
    }, 300),
    []
  );
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <h1 className="text-4xl font-bold text-white mb-8">
          Industrial HempDex - Advanced Search
        </h1>
        
        {/* Quick Search Bar */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <input
            type="text"
            placeholder="Search by name, description, or characteristics..."
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full px-4 py-3 bg-gray-700 text-white rounded-lg"
          />
        </div>
        
        {/* Filter Controls */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center gap-2 text-white mb-4"
          >
            <span>{showAdvanced ? 'Hide' : 'Show'} Advanced Filters</span>
            <ChevronIcon className={`transform ${showAdvanced ? 'rotate-180' : ''}`} />
          </button>
          
          {showAdvanced && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Plant Parts Filter */}
              <div>
                <label className="text-white text-sm mb-2 block">Plant Parts</label>
                <MultiSelect
                  options={plantParts.map(p => ({ value: p.id, label: p.name }))}
                  value={filters.plantParts}
                  onChange={(parts) => setFilters(prev => ({ ...prev, plantParts: parts }))}
                  placeholder="Select plant parts..."
                />
              </div>
              
              {/* Industries Filter */}
              <div>
                <label className="text-white text-sm mb-2 block">Industries</label>
                <MultiSelect
                  options={industries.map(i => ({ value: i.id, label: i.name }))}
                  value={filters.industries}
                  onChange={(inds) => setFilters(prev => ({ ...prev, industries: inds }))}
                  placeholder="Select industries..."
                />
              </div>
              
              {/* THC Range */}
              <div>
                <label className="text-white text-sm mb-2 block">
                  THC Content: {filters.thcRange[0]}% - {filters.thcRange[1]}%
                </label>
                <RangeSlider
                  min={0}
                  max={1}
                  step={0.01}
                  value={filters.thcRange}
                  onChange={(range) => setFilters(prev => ({ ...prev, thcRange: range }))}
                />
              </div>
              
              {/* CBD Range */}
              <div>
                <label className="text-white text-sm mb-2 block">
                  CBD Content: {filters.cbdRange[0]}% - {filters.cbdRange[1]}%
                </label>
                <RangeSlider
                  min={0}
                  max={30}
                  step={0.5}
                  value={filters.cbdRange}
                  onChange={(range) => setFilters(prev => ({ ...prev, cbdRange: range }))}
                />
              </div>
              
              {/* Sort Options */}
              <div>
                <label className="text-white text-sm mb-2 block">Sort By</label>
                <div className="flex gap-2">
                  <Select
                    value={filters.sortBy}
                    onChange={(sortBy) => setFilters(prev => ({ ...prev, sortBy }))}
                    options={[
                      { value: 'name', label: 'Name' },
                      { value: 'thc', label: 'THC Content' },
                      { value: 'cbd', label: 'CBD Content' },
                      { value: 'harvestTime', label: 'Harvest Time' }
                    ]}
                  />
                  <button
                    onClick={() => setFilters(prev => ({ 
                      ...prev, 
                      sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc' 
                    }))}
                    className="px-3 py-2 bg-gray-700 text-white rounded"
                  >
                    {filters.sortOrder === 'asc' ? '↑' : '↓'}
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {/* Active Filters Summary */}
          {(filters.plantParts.length > 0 || filters.industries.length > 0) && (
            <div className="mt-4 flex flex-wrap gap-2">
              {filters.plantParts.map(id => {
                const part = plantParts.find(p => p.id === id);
                return (
                  <span key={id} className="px-3 py-1 bg-green-600 text-white rounded-full text-sm">
                    {part?.name}
                    <button
                      onClick={() => setFilters(prev => ({
                        ...prev,
                        plantParts: prev.plantParts.filter(p => p !== id)
                      }))}
                      className="ml-2"
                    >
                      ×
                    </button>
                  </span>
                );
              })}
            </div>
          )}
        </div>
        
        {/* Results Count */}
        <div className="text-white mb-4">
          Found {filteredPlants.length} of {plantTypes.length} hemp varieties
        </div>
        
        {/* Results Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredPlants.map(plant => (
            <HempDexCard key={plant.id} plant={plant} />
          ))}
        </div>
      </div>
    </div>
  );
};

// Utility function for debouncing
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
```

### 2.3 Image Optimization Component
```typescript
// HempResourceHub/client/src/components/ui/optimized-image.tsx

import { useState, useEffect, useRef } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  
  useEffect(() => {
    if (priority || !window.IntersectionObserver) {
      setIsInView(true);
      return;
    }
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => observer.disconnect();
  }, [priority]);
  
  // Generate srcset for responsive images
  const generateSrcSet = () => {
    const base = src.replace(/\.[^/.]+$/, '');
    const ext = src.match(/\.[^/.]+$/)?.[0] || '';
    
    return `
      ${base}-400w${ext} 400w,
      ${base}-800w${ext} 800w,
      ${base}-1200w${ext} 1200w
    `;
  };
  
  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      {/* Placeholder while loading */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-300 animate-pulse rounded" />
      )}
      
      {/* Actual image */}
      {isInView && (
        <img
          ref={imgRef}
          src={src}
          srcSet={generateSrcSet()}
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={() => setIsLoaded(true)}
          className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        />
      )}
    </div>
  );
};

export default OptimizedImage;
```

### 2.4 Performance Monitoring
```typescript
// HempResourceHub/client/src/lib/performance-monitor.ts

import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private analyticsEndpoint = '/api/analytics/performance';
  
  constructor() {
    this.initializeMonitoring();
  }
  
  private initializeMonitoring() {
    // Core Web Vitals
    getCLS(this.handleMetric);
    getFID(this.handleMetric);
    getFCP(this.handleMetric);
    getLCP(this.handleMetric);
    getTTFB(this.handleMetric);
    
    // Custom metrics
    this.measureResourceTiming();
    this.measureApiResponseTimes();
  }
  
  private handleMetric = (metric: any) => {
    const performanceMetric: PerformanceMetric = {
      name: metric.name,
      value: metric.value,
      rating: this.getRating(metric.name, metric.value)
    };
    
    this.metrics.push(performanceMetric);
    this.sendToAnalytics(performanceMetric);
    
    // Log warnings for poor performance
    if (performanceMetric.rating === 'poor') {
      console.warn(`Poor ${metric.name} performance: ${metric.value}`);
    }
  };
  
  private getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds: Record<string, [number, number]> = {
      CLS: [0.1, 0.25],
      FID: [100, 300],
      FCP: [1800, 3000],
      LCP: [2500, 4000],
      TTFB: [800, 1800]
    };
    
    const [good, poor] = thresholds[name] || [0, 0];
    
    if (value <= good) return 'good';
    if (value <= poor) return 'needs-improvement';
    return 'poor';
  }
  
  private measureResourceTiming() {
    window.addEventListener('load', () => {
      const resources = performance.getEntriesByType('resource');
      
      const slowResources = resources
        .filter(r => r.duration > 1000)
        .map(r => ({
          name: r.name,
          duration: r.duration,
          type: (r as PerformanceResourceTiming).initiatorType
        }));
      
      if (slowResources.length > 0) {
        console.warn('Slow resources detected:', slowResources);
      }
    });
  }
  
  private measureApiResponseTimes() {
    // Intercept fetch to measure API response times
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const response = await originalFetch(...args);
      const duration = performance.now() - startTime;
      
      // Log slow API calls
      if (duration > 1000) {
        console.warn(`Slow API call: ${args[0]} took ${duration}ms`);
      }
      
      return response;
    };
  }
  
  private async sendToAnalytics(metric: PerformanceMetric) {
    try {
      await fetch(this.analyticsEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...metric,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      });
    } catch (error) {
      console.error('Failed to send analytics:', error);
    }
  }
  
  public getMetrics() {
    return this.metrics;
  }
}

// Initialize performance monitoring
export const performanceMonitor = new PerformanceMonitor();
```

## Priority 3: Admin Dashboard UX Improvements (Week 3)

### 3.1 Redesigned Admin Dashboard
```typescript
// HempResourceHub/client/src/pages/admin/dashboard-redesigned.tsx

import { useState } from 'react';
import { 
  BarChart, 
  LineChart, 
  PieChart,
  Card,
  Metric,
  Text,
  Flex,
  Grid,
  Title
} from '@tremor/react';
import { useAgentMetrics, useSystemHealth } from '@/hooks/use-admin-data';

const AdminDashboardRedesigned = () => {
  const [timeRange, setTimeRange] = useState('24h');
  const { data: metrics } = useAgentMetrics(timeRange);
  const { data: health } = useSystemHealth();
  
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header with Quick Actions */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="px-6 py-4">
          <Flex justifyContent="between" alignItems="center">
            <Title>Admin Dashboard</Title>
            <div className="flex gap-2">
              <QuickActionButton icon={RefreshIcon} label="Sync Data" />
              <QuickActionButton icon={PlayIcon} label="Run Agents" />
              <QuickActionButton icon={DownloadIcon} label="Export Report" />
            </div>
          </Flex>
        </div>
      </header>
      
      {/* Key Metrics */}
      <div className="px-6 py-6">
        <Grid numItems={1} numItemsSm={2} numItemsLg={4} className="gap-6">
          <MetricCard
            title="Active Agents"
            metric={metrics?.activeAgents || 0}
            delta={12.3}
            deltaType="increase"
          />
          <MetricCard
            title="Tasks Completed"
            metric={metrics?.tasksCompleted || 0}
            delta={-2.1}
            deltaType="decrease"
          />
          <MetricCard
            title="Success Rate"
            metric={`${metrics?.successRate || 0}%`}
            delta={5.4}
            deltaType="increase"
          />
          <MetricCard
            title="Avg Response Time"
            metric={`${metrics?.avgResponseTime || 0}ms`}
            delta={-15.2}
            deltaType="decrease"
          />
        </Grid>
      </div>
      
      {/* Main Content Area */}
      <div className="px-6 pb-6">
        <Grid numItems={1} numItemsLg={2} className="gap-6">
          {/* Agent Performance Chart */}
          <Card>
            <Title>Agent Performance Over Time</Title>
            <LineChart
              className="h-72 mt-4"
              data={metrics?.performanceHistory || []}
              index="timestamp"
              categories={["Research", "Content", "Monetization", "Outreach"]}
              colors={["blue", "green", "yellow", "purple"]}
              showLegend={true}
              showGridLines={false}
            />
          </Card>
          
          {/* Task Distribution */}
          <Card>
            <Title>Task Distribution by Type</Title>
            <PieChart
              className="h-72 mt-4"
              data={metrics?.taskDistribution || []}
              category="count"
              index="type"
              colors={["blue", "cyan", "indigo", "violet", "fuchsia"]}
              showLabel={true}
            />
          </Card>
        </Grid>
        
        {/* Agent Status Table */}
        <Card className="mt-6">
          <Title>Agent Status & Health</Title>
          <AgentStatusTable agents={health?.agents || []} />
        </Card>
        
        {/* Recent Activities */}
        <Card className="mt-6">
          <Title>Recent Activities</Title>
          <ActivityFeed activities={metrics?.recentActivities || []} />
        </Card>
      </div>
    </div>
  );
};

// Reusable components
const MetricCard = ({ title, metric, delta, deltaType }) => (
  <Card>
    <Text>{title}</Text>
    <Metric>{metric}</Metric>
    <Flex justifyContent="start" alignItems="baseline" space="x-2">
      <DeltaIndicator value={delta} type={deltaType} />
      <Text>from last period</Text>
    </Flex>
  </Card>
);

const AgentStatusTable = ({ agents }) => (
  <div className="mt-4 overflow-x-auto">
    <table className="w-full">
      <thead>
        <tr className="border-b">
          <th className="text-left py-2">Agent</th>
          <th className="text-left py-2">Status</th>
          <th className="text-left py-2">Last Active</th>
          <th className="text-left py-2">Tasks Today</th>
          <th className="text-left py-2">Success Rate</th>
          <th className="text-left py-2">Actions</th>
        </tr>
      </thead>
      <tbody>
        {agents.map(agent => (
          <tr key={agent.id} className="border-b">
            <td className="py-2">{agent.name}</td>
            <td className="py-2">
              <StatusBadge status={agent.status} />
            </td>
            <td className="py-2">{formatTime(agent.lastActive)}</td>
            <td className="py-2">{agent.tasksToday}</td>
            <td className="py-2">{agent.successRate}%</td>
            <td className="py-2">
              <button className="text-blue-600 hover:underline mr-2">View</button>
              <button className="text-green-600 hover:underline">Run</button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);
```

## Priority 4: Accessibility Improvements (Week 4)

### 4.1 Accessibility Audit Component
```typescript
// HempResourceHub/client/src/components/dev/accessibility-audit.tsx

import { useEffect, useState } from 'react';
import axe from '@axe-core/react';

const AccessibilityAudit = () => {
  const [violations, setViolations] = useState([]);
  const [isAuditing, setIsAuditing] = useState(false);
  
  const runAudit = async () => {
    setIsAuditing(true);
    
    try {
      const results = await axe(document.body);
      setViolations(results.violations);
      
      // Log to console for development
      if (results.violations.length > 0) {
        console.group('Accessibility Violations');
        results.violations.forEach(violation => {
          console.error(violation);
        });
        console.groupEnd();
      }
    } catch (error) {
      console.error('Accessibility audit failed:', error);
    } finally {
      setIsAuditing(false);
    }
  };
  
  useEffect(() => {
    // Run audit on mount and route changes
    runAudit();
  }, [window.location.pathname]);
  
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={runAudit}
        className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg"
        aria-label="Run accessibility audit"
      >
        {isAuditing ? 'Auditing...' : `A11y (${violations.length})`}
      </button>
      
      {violations.length > 0 && (
        <div className="absolute bottom-full right-0 mb-2 w-80 max-h-96 overflow-y-auto bg-white rounded-lg shadow-xl p-4">
          <h3 className="font-bold mb-2">Accessibility Issues</h3>
          {violations.map((violation, index) => (
            <div key={index} className="mb-3 text-sm">
              <p className="font-medium text-red-600">{violation.description}</p>
              <p className="text-gray-600">{violation.help}</p>
              <p className="text-xs text-gray-500">
                Impact: {violation.impact} | 
                Elements: {violation.nodes.length}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

## Implementation Timeline

### Week 1: Agent Improvements
- Day 1-2: Implement enhanced error handling with notifications
- Day 3: Set up Poetry for dependency management
- Day 4: Enhance LangGraph orchestration
- Day 5: Write comprehensive test suite

### Week 2: UI Performance
- Day 1: Implement code splitting
- Day 2-3: Build enhanced HempDex with filters
- Day 4: Create optimized image component
- Day 5: Set up performance monitoring

### Week 3: Admin Dashboard
- Day 1-2: Redesign admin dashboard UI
- Day 3: Implement real-time metrics
- Day 4: Add interactive charts
- Day 5: User testing and refinement

### Week 4: Accessibility & Testing
- Day 1-2: Run accessibility audit and fix issues
- Day 3: Add ARIA labels and keyboard navigation
- Day 4: Integration testing
- Day 5: Documentation and deployment

## Success Metrics

1. **Agent Reliability**
   - Error rate < 1%
   - Notification delivery > 99%
   - Test coverage > 80%

2. **UI Performance**
   - Initial load time < 2s
   - Time to Interactive < 3s
   - Lighthouse score > 90

3. **User Experience**
   - HempDex search results < 100ms
   - Admin dashboard refresh < 500ms
   - Zero accessibility violations

4. **Code Quality**
   - All tests passing
   - Type coverage > 95%
   - Zero security vulnerabilities

## Conclusion

This action plan addresses all of Gemini's feedback with practical, implementable solutions. The improvements will significantly enhance both the reliability of the automation agents and the user experience of the UI. By following this timeline, the Hemp Resource Hub will become a more robust, performant, and user-friendly platform.