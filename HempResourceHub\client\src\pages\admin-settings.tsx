import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { 
  Settings, 
  Users, 
  Shield, 
  Database, 
  Bell, 
  Key, 
  Save,
  Trash2,
  UserPlus,
  Crown,
  Mail,
  Calendar,
  Activity
} from 'lucide-react';
import { useAuth } from '@/components/auth/enhanced-auth-provider';
import { useRequireAdmin } from '@/hooks/use-auth';

interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  company: string;
  role: 'admin' | 'user';
  isActive: boolean;
  lastLogin: string;
  createdAt: string;
}

const AdminSettingsPage = () => {
  const { user, updateProfile } = useAuth();
  const { loading } = useRequireAdmin();
  
  const [activeTab, setActiveTab] = useState('profile');
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    bio: '',
  });
  const [systemSettings, setSystemSettings] = useState({
    maintenanceMode: false,
    allowRegistration: true,
    emailNotifications: true,
    autoBackup: true,
    debugMode: false,
  });
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Mock users data - replace with real API call
  useEffect(() => {
    const mockUsers: UserProfile[] = [
      {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        company: 'HempQuarterz',
        role: 'admin',
        isActive: true,
        lastLogin: '2024-01-15T10:30:00Z',
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: '2',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        company: 'Hemp Industries',
        role: 'user',
        isActive: true,
        lastLogin: '2024-01-14T15:45:00Z',
        createdAt: '2024-01-10T00:00:00Z',
      },
      {
        id: '3',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        company: 'Green Solutions',
        role: 'user',
        isActive: false,
        lastLogin: '2024-01-12T09:20:00Z',
        createdAt: '2024-01-08T00:00:00Z',
      },
    ];
    setUsers(mockUsers);

    // Initialize profile data from user
    if (user) {
      setProfileData({
        firstName: user.user_metadata?.first_name || '',
        lastName: user.user_metadata?.last_name || '',
        email: user.email || '',
        company: user.user_metadata?.company || '',
        bio: user.user_metadata?.bio || '',
      });
    }
  }, [user]);

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(null);

    try {
      const { error } = await updateProfile({
        first_name: profileData.firstName,
        last_name: profileData.lastName,
        company: profileData.company,
        bio: profileData.bio,
        full_name: `${profileData.firstName} ${profileData.lastName}`.trim(),
      });

      if (error) {
        setMessage({ type: 'error', text: error.message });
      } else {
        setMessage({ type: 'success', text: 'Profile updated successfully!' });
      }
    } catch (err) {
      setMessage({ type: 'error', text: 'Failed to update profile. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSystemSettingsUpdate = () => {
    setMessage({ type: 'success', text: 'System settings updated successfully!' });
  };

  const toggleUserStatus = (userId: string) => {
    setUsers(prev => prev.map(user => 
      user.id === userId 
        ? { ...user, isActive: !user.isActive }
        : user
    ));
    setMessage({ type: 'success', text: 'User status updated successfully!' });
  };

  const promoteUser = (userId: string) => {
    setUsers(prev => prev.map(user => 
      user.id === userId 
        ? { ...user, role: user.role === 'admin' ? 'user' : 'admin' }
        : user
    ));
    setMessage({ type: 'success', text: 'User role updated successfully!' });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-950">
        <div className="text-center">
          <Settings className="h-8 w-8 animate-spin text-green-400 mx-auto mb-4" />
          <p className="text-gray-400">Loading admin settings...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Admin Settings - HempQuarterz</title>
        <meta name="description" content="Manage your HempQuarterz admin settings and user accounts" />
      </Helmet>

      <div className="min-h-screen bg-gray-950 py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white flex items-center gap-3">
              <Shield className="h-8 w-8 text-green-400" />
              Admin Settings
            </h1>
            <p className="mt-2 text-gray-400">
              Manage your account, users, and system settings
            </p>
          </div>

          {/* Message Alert */}
          {message && (
            <Alert className={`mb-6 ${
              message.type === 'success' 
                ? 'border-green-500/50 bg-green-500/10' 
                : 'border-red-500/50 bg-red-500/10'
            }`}>
              <AlertDescription className={
                message.type === 'success' ? 'text-green-400' : 'text-red-400'
              }>
                {message.text}
              </AlertDescription>
            </Alert>
          )}

          {/* Settings Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 bg-gray-900/50">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                Users
              </TabsTrigger>
              <TabsTrigger value="system" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                System
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                Security
              </TabsTrigger>
            </TabsList>

            {/* Profile Tab */}
            <TabsContent value="profile">
              <Card className="bg-gray-900/80 backdrop-blur-sm border-green-500/30">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Users className="h-5 w-5 text-green-400" />
                    Profile Settings
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    Update your personal information and preferences
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleProfileUpdate} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName" className="text-gray-300">
                          First Name
                        </Label>
                        <Input
                          id="firstName"
                          value={profileData.firstName}
                          onChange={(e) => setProfileData(prev => ({ ...prev, firstName: e.target.value }))}
                          className="bg-gray-800 border-gray-700 text-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName" className="text-gray-300">
                          Last Name
                        </Label>
                        <Input
                          id="lastName"
                          value={profileData.lastName}
                          onChange={(e) => setProfileData(prev => ({ ...prev, lastName: e.target.value }))}
                          className="bg-gray-800 border-gray-700 text-white"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-gray-300">
                        Email Address
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        value={profileData.email}
                        disabled
                        className="bg-gray-800 border-gray-700 text-gray-400"
                      />
                      <p className="text-xs text-gray-500">Email cannot be changed from this interface</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="company" className="text-gray-300">
                        Company
                      </Label>
                      <Input
                        id="company"
                        value={profileData.company}
                        onChange={(e) => setProfileData(prev => ({ ...prev, company: e.target.value }))}
                        className="bg-gray-800 border-gray-700 text-white"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bio" className="text-gray-300">
                        Bio
                      </Label>
                      <Textarea
                        id="bio"
                        value={profileData.bio}
                        onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
                        className="bg-gray-800 border-gray-700 text-white"
                        rows={3}
                        placeholder="Tell us about yourself..."
                      />
                    </div>

                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <Save className="mr-2 h-4 w-4" />
                      {isLoading ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Users Tab */}
            <TabsContent value="users">
              <Card className="bg-gray-900/80 backdrop-blur-sm border-green-500/30">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <UserPlus className="h-5 w-5 text-green-400" />
                    User Management
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    Manage user accounts and permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {users.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg border border-gray-700"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            <div className="h-10 w-10 bg-green-600 rounded-full flex items-center justify-center">
                              <span className="text-white font-medium">
                                {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                              </span>
                            </div>
                          </div>
                          <div>
                            <h3 className="text-white font-medium">
                              {user.firstName} {user.lastName}
                            </h3>
                            <p className="text-gray-400 text-sm">{user.email}</p>
                            <p className="text-gray-500 text-xs">{user.company}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge
                            variant={user.role === 'admin' ? 'default' : 'secondary'}
                            className={user.role === 'admin' ? 'bg-purple-600' : 'bg-gray-600'}
                          >
                            {user.role === 'admin' && <Crown className="h-3 w-3 mr-1" />}
                            {user.role}
                          </Badge>
                          <Badge
                            variant={user.isActive ? 'default' : 'destructive'}
                            className={user.isActive ? 'bg-green-600' : 'bg-red-600'}
                          >
                            {user.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => promoteUser(user.id)}
                            className="border-gray-600 text-gray-300 hover:bg-gray-700"
                          >
                            {user.role === 'admin' ? 'Demote' : 'Promote'}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleUserStatus(user.id)}
                            className="border-gray-600 text-gray-300 hover:bg-gray-700"
                          >
                            {user.isActive ? 'Deactivate' : 'Activate'}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* System Tab */}
            <TabsContent value="system">
              <Card className="bg-gray-900/80 backdrop-blur-sm border-green-500/30">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Database className="h-5 w-5 text-green-400" />
                    System Settings
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    Configure system-wide settings and preferences
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-gray-300">Maintenance Mode</Label>
                        <p className="text-sm text-gray-500">Temporarily disable public access</p>
                      </div>
                      <Switch
                        checked={systemSettings.maintenanceMode}
                        onCheckedChange={(checked) => 
                          setSystemSettings(prev => ({ ...prev, maintenanceMode: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-gray-300">Allow Registration</Label>
                        <p className="text-sm text-gray-500">Allow new users to register</p>
                      </div>
                      <Switch
                        checked={systemSettings.allowRegistration}
                        onCheckedChange={(checked) => 
                          setSystemSettings(prev => ({ ...prev, allowRegistration: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-gray-300">Email Notifications</Label>
                        <p className="text-sm text-gray-500">Send system notifications via email</p>
                      </div>
                      <Switch
                        checked={systemSettings.emailNotifications}
                        onCheckedChange={(checked) => 
                          setSystemSettings(prev => ({ ...prev, emailNotifications: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-gray-300">Auto Backup</Label>
                        <p className="text-sm text-gray-500">Automatically backup database daily</p>
                      </div>
                      <Switch
                        checked={systemSettings.autoBackup}
                        onCheckedChange={(checked) => 
                          setSystemSettings(prev => ({ ...prev, autoBackup: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-gray-300">Debug Mode</Label>
                        <p className="text-sm text-gray-500">Enable detailed logging and debugging</p>
                      </div>
                      <Switch
                        checked={systemSettings.debugMode}
                        onCheckedChange={(checked) => 
                          setSystemSettings(prev => ({ ...prev, debugMode: checked }))
                        }
                      />
                    </div>

                    <Button
                      onClick={handleSystemSettingsUpdate}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save System Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Security Tab */}
            <TabsContent value="security">
              <Card className="bg-gray-900/80 backdrop-blur-sm border-green-500/30">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Key className="h-5 w-5 text-green-400" />
                    Security Settings
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    Manage security settings and API keys
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <Label className="text-gray-300">Change Password</Label>
                      <p className="text-sm text-gray-500 mb-3">
                        Update your account password for better security
                      </p>
                      <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                        Change Password
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-gray-300">Two-Factor Authentication</Label>
                      <p className="text-sm text-gray-500 mb-3">
                        Add an extra layer of security to your account
                      </p>
                      <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                        Enable 2FA
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-gray-300">API Keys</Label>
                      <p className="text-sm text-gray-500 mb-3">
                        Manage API keys for external integrations
                      </p>
                      <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                        Manage API Keys
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-gray-300">Session Management</Label>
                      <p className="text-sm text-gray-500 mb-3">
                        View and manage active sessions
                      </p>
                      <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                        View Sessions
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  );
};

export default AdminSettingsPage;
