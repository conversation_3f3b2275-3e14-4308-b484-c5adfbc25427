"""Multi-provider AI management with fallback support including DeepSeek."""

import os
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import asyncio
from openai import Async<PERSON>penAI
import logging

logger = logging.getLogger(__name__)


class AIProvider(ABC):
    """Abstract base class for AI providers."""
    
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text from prompt."""
        pass
    
    @abstractmethod
    async def embed(self, text: str) -> List[float]:
        """Generate embeddings for text."""
        pass
    
    @abstractmethod
    def get_cost(self, tokens: int, operation: str = 'generation') -> float:
        """Calculate cost for token usage."""
        pass


class DeepSeekProvider(AIProvider):
    """DeepSeek API provider using OpenAI-compatible interface."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "deepseek-chat"):
        # DeepSeek uses OpenAI-compatible API
        self.client = AsyncOpenAI(
            api_key=api_key or os.getenv("DEEPSEEK_API_KEY"),
            base_url="https://api.deepseek.com/v1"
        )
        self.model = model
        
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using DeepSeek."""
        try:
            response = await self.client.chat.completions.create(
                model=kwargs.get('model', self.model),
                messages=[{"role": "user", "content": prompt}],
                temperature=kwargs.get('temperature', 0.7),
                max_tokens=kwargs.get('max_tokens', 2000)
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"DeepSeek generation error: {e}")
            raise
            
    async def embed(self, text: str) -> List[float]:
        """DeepSeek doesn't have embeddings, fallback to OpenAI."""
        raise NotImplementedError("Use OpenAI for embeddings")
        
    def get_cost(self, tokens: int, operation: str = 'generation') -> float:
        """Calculate DeepSeek costs."""
        # DeepSeek pricing (as of 2024)
        costs = {
            'deepseek-chat': {'input': 0.00014, 'output': 0.00028},  # Per 1K tokens
            'deepseek-coder': {'input': 0.00014, 'output': 0.00028}
        }
        
        model_costs = costs.get(self.model, costs['deepseek-chat'])
        input_cost = tokens * 0.75 * model_costs['input'] / 1000
        output_cost = tokens * 0.25 * model_costs['output'] / 1000
        return input_cost + output_cost


class OpenAIProvider(AIProvider):
    """OpenAI API provider."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4o-mini"):
        self.client = AsyncOpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
        self.model = model
        self.embedding_model = "text-embedding-3-small"
        
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using OpenAI."""
        try:
            response = await self.client.chat.completions.create(
                model=kwargs.get('model', self.model),
                messages=[{"role": "user", "content": prompt}],
                temperature=kwargs.get('temperature', 0.7),
                max_tokens=kwargs.get('max_tokens', 2000)
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI generation error: {e}")
            raise
            
    async def embed(self, text: str) -> List[float]:
        """Generate embeddings using OpenAI."""
        response = await self.client.embeddings.create(
            model=self.embedding_model,
            input=text
        )
        return response.data[0].embedding
        
    def get_cost(self, tokens: int, operation: str = 'generation') -> float:
        """Calculate OpenAI costs."""
        costs = {
            'gpt-4o': {'input': 0.005, 'output': 0.015},
            'gpt-4o-mini': {'input': 0.00015, 'output': 0.0006},
            'embedding': 0.00002
        }
        
        if operation == 'embedding':
            return tokens * costs['embedding']
        
        # Rough estimate: 75% input, 25% output
        model_costs = costs.get(self.model, costs['gpt-4o-mini'])
        input_cost = tokens * 0.75 * model_costs['input'] / 1000
        output_cost = tokens * 0.25 * model_costs['output'] / 1000
        return input_cost + output_cost


class MultiProviderAI:
    """AI provider with automatic fallback."""
    
    def __init__(self, primary_provider: str = "deepseek", fallback_providers: List[str] = ["openai"]):
        self.providers = self._initialize_providers(primary_provider, fallback_providers)
        self.current_provider = 0
        
    def _initialize_providers(self, primary: str, fallbacks: List[str]) -> List[AIProvider]:
        """Initialize AI providers."""
        provider_map = {
            'deepseek': DeepSeekProvider,
            'openai': OpenAIProvider
        }
        
        providers = []
        
        # Add primary provider
        if primary in provider_map:
            try:
                provider = provider_map[primary]()
                providers.append(provider)
                logger.info(f"Initialized primary provider: {primary}")
            except Exception as e:
                logger.warning(f"Failed to initialize primary provider {primary}: {e}")
            
        # Add fallback providers
        for fallback in fallbacks:
            if fallback in provider_map and fallback != primary:
                try:
                    provider = provider_map[fallback]()
                    providers.append(provider)
                    logger.info(f"Initialized fallback provider: {fallback}")
                except Exception as e:
                    logger.warning(f"Failed to initialize fallback provider {fallback}: {e}")
                
        if not providers:
            raise ValueError("No valid AI providers configured")
            
        return providers
    
    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text with automatic fallback."""
        for i, provider in enumerate(self.providers):
            try:
                # Skip providers without embedding support for embedding operations
                if kwargs.get('operation') == 'embedding' and isinstance(provider, DeepSeekProvider):
                    continue
                    
                result = await provider.generate(prompt, **kwargs)
                
                # Estimate tokens (rough: 4 chars = 1 token)
                tokens = len(prompt + result) // 4
                cost = provider.get_cost(tokens)
                
                provider_name = provider.__class__.__name__
                logger.info(f"Successfully used {provider_name} for generation (cost: ${cost:.4f})")
                
                return result
                
            except Exception as e:
                logger.warning(f"Provider {provider.__class__.__name__} failed: {e}")
                if i == len(self.providers) - 1:
                    raise Exception(f"All AI providers failed. Last error: {e}")
                continue
    
    async def embed(self, text: str) -> List[float]:
        """Generate embeddings with fallback to OpenAI."""
        # Always use OpenAI for embeddings
        for provider in self.providers:
            if isinstance(provider, OpenAIProvider):
                try:
                    embeddings = await provider.embed(text)
                    cost = provider.get_cost(len(text) // 4, 'embedding')
                    logger.info(f"Generated embeddings using OpenAI (cost: ${cost:.4f})")
                    return embeddings
                except Exception as e:
                    logger.error(f"OpenAI embedding failed: {e}")
                    
        # Fallback: create OpenAI provider just for embeddings
        try:
            openai_provider = OpenAIProvider()
            embeddings = await openai_provider.embed(text)
            cost = openai_provider.get_cost(len(text) // 4, 'embedding')
            logger.info(f"Generated embeddings using fallback OpenAI (cost: ${cost:.4f})")
            return embeddings
        except Exception as e:
            raise Exception(f"Failed to generate embeddings: {e}")


# Convenience function to get AI provider
def get_ai_provider(provider_name: str = None) -> Optional[MultiProviderAI]:
    """Get AI provider with specified preference."""
    try:
        if provider_name == "deepseek":
            return MultiProviderAI(primary_provider="deepseek", fallback_providers=["openai"])
        elif provider_name == "openai":
            return MultiProviderAI(primary_provider="openai", fallback_providers=["deepseek"])
        else:
            # Default: try DeepSeek first, fallback to OpenAI
            return MultiProviderAI(primary_provider="deepseek", fallback_providers=["openai"])
    except Exception as e:
        logger.error(f"Failed to initialize AI providers: {e}")
        return None