#!/usr/bin/env python3
"""
Enhanced scrapers with proper image attribution and better extraction
"""
import os
import requests
import json
from datetime import datetime, timezone
from dotenv import load_dotenv
from typing import Dict, Optional, List
from urllib.parse import urljoin, urlparse
import time

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

# Supabase configuration
SUPABASE_URL = os.getenv("VITE_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

class EnhancedCompanyLogoScraper:
    def __init__(self):
        self.user_agent = 'Mozilla/5.0 (compatible; HempResourceHub/1.0; +https://hempdex.com)'
        
    def extract_logo_with_attribution(self, company_name: str, website: str) -> Dict:
        """Extract logo with full attribution data"""
        # Ensure website has a scheme
        if website and not website.startswith(('http://', 'https://')):
            website = f"https://{website}"
            
        result = {
            "logo_url": None,
            "attribution": {
                "source_url": website,
                "source_name": self.get_domain_name(website),
                "scraped_date": datetime.now(timezone.utc).isoformat(),
                "license": "Fair Use - Company Logo",
                "alt_text": f"{company_name} company logo",
                "attribution_required": True
            }
        }
        
        try:
            response = requests.get(website, headers={'User-Agent': self.user_agent}, timeout=10)
            if response.status_code == 200:
                html = response.text
                
                # Method 1: Open Graph image
                og_image = self.extract_og_image(html)
                if og_image:
                    result["logo_url"] = urljoin(website, og_image)
                    result["attribution"]["method"] = "og:image"
                    return result
                
                # Method 2: JSON-LD structured data
                json_ld_logo = self.extract_json_ld_logo(html)
                if json_ld_logo:
                    result["logo_url"] = urljoin(website, json_ld_logo)
                    result["attribution"]["method"] = "JSON-LD"
                    return result
                
                # Method 3: Common logo patterns
                logo_url = self.extract_by_patterns(html, website)
                if logo_url:
                    result["logo_url"] = logo_url
                    result["attribution"]["method"] = "pattern matching"
                    return result
                    
        except Exception as e:
            print(f"Error extracting logo from {website}: {e}")
            
        return result
    
    def extract_og_image(self, html: str) -> Optional[str]:
        """Extract Open Graph image"""
        patterns = [
            'property="og:image" content="([^"]+)"',
            'property=\'og:image\' content=\'([^\']+)\'',
            'content="([^"]+)" property="og:image"'
        ]
        
        import re
        for pattern in patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                return match.group(1)
        return None
    
    def extract_json_ld_logo(self, html: str) -> Optional[str]:
        """Extract logo from JSON-LD structured data"""
        import re
        
        # Find JSON-LD script tags
        json_ld_pattern = r'<script[^>]*type=["\']application/ld\+json["\'][^>]*>(.*?)</script>'
        matches = re.findall(json_ld_pattern, html, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            try:
                data = json.loads(match)
                # Check for logo in various formats
                if isinstance(data, dict):
                    if data.get('logo'):
                        return data['logo'] if isinstance(data['logo'], str) else data['logo'].get('url')
                    elif data.get('image'):
                        return data['image'] if isinstance(data['image'], str) else data['image'].get('url')
            except:
                continue
        return None
    
    def extract_by_patterns(self, html: str, base_url: str) -> Optional[str]:
        """Extract logo using common patterns"""
        import re
        
        # Common logo patterns
        patterns = [
            r'<img[^>]*class=["\'][^"\']*logo[^"\']*["\'][^>]*src=["\']([^"\']+)["\']',
            r'<img[^>]*id=["\'][^"\']*logo[^"\']*["\'][^>]*src=["\']([^"\']+)["\']',
            r'<img[^>]*src=["\']([^"\']*logo[^"\']*)["\']',
            r'background-image:\s*url\(["\']?([^"\']*logo[^"\')]*)["\']?\)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            for match in matches:
                # Filter out obviously wrong matches
                if match and not any(bad in match.lower() for bad in ['placeholder', 'loading', 'spinner']):
                    return urljoin(base_url, match)
        
        return None
    
    def get_domain_name(self, url: str) -> str:
        """Extract clean domain name from URL"""
        parsed = urlparse(url)
        domain = parsed.netloc.replace('www.', '')
        return domain.split(':')[0]  # Remove port if present
    
    def update_company_logos(self, limit: int = 10):
        """Update companies without logos"""
        # Get companies without logos but with websites
        check_url = f"{SUPABASE_URL}/rest/v1/hemp_companies"
        params = {
            "logo_url": "is.null",
            "website": "not.is.null",
            "select": "id,name,website",
            "limit": limit
        }
        
        response = requests.get(check_url, headers=headers, params=params)
        companies = response.json()
        
        print(f"🔍 Found {len(companies)} companies without logos")
        
        updated = 0
        for company in companies:
            print(f"\n🏢 Processing: {company['name']}")
            
            result = self.extract_logo_with_attribution(company['name'], company['website'])
            
            if result['logo_url']:
                # Store attribution as JSONB
                update_data = {
                    "logo_url": result['logo_url'],
                    "logo_attribution": result['attribution'],
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                update_url = f"{SUPABASE_URL}/rest/v1/hemp_companies?id=eq.{company['id']}"
                response = requests.patch(update_url, headers=headers, json=update_data)
                
                if response.status_code in [200, 204]:
                    updated += 1
                    print(f"✅ Updated logo: {result['logo_url']}")
                    print(f"   Attribution: {result['attribution']['method']}")
                else:
                    print(f"❌ Failed to update: {response.text}")
            else:
                print(f"⚠️  No logo found")
                
            time.sleep(1)  # Rate limiting
        
        return updated


class EnhancedResearchImageScraper:
    def __init__(self):
        self.user_agent = 'Mozilla/5.0 (compatible; HempResourceHub/1.0; +https://hempdex.com)'
    
    def extract_article_image_with_attribution(self, paper: Dict) -> Dict:
        """Extract article image with proper attribution"""
        result = {
            "image_url": None,
            "attribution": {
                "source_name": "PubMed Central",
                "source_url": paper.get('full_text_url', ''),
                "scraped_date": datetime.now(timezone.utc).isoformat(),
                "license": "PMC Open Access",
                "attribution_required": True,
                "alt_text": f"Figure from: {paper.get('title', '')[:100]}"
            }
        }
        
        # Extract PMC ID from URL
        pmc_id = self.extract_pmc_id(paper.get('full_text_url', ''))
        
        if pmc_id:
            # Try multiple figure URLs
            figure_urls = [
                f"https://www.ncbi.nlm.nih.gov/pmc/articles/{pmc_id}/bin/{pmc_id}_fig1.jpg",
                f"https://www.ncbi.nlm.nih.gov/pmc/articles/{pmc_id}/bin/{pmc_id}_001.jpg",
                f"https://www.ncbi.nlm.nih.gov/pmc/articles/{pmc_id}/bin/{pmc_id}.fig1.jpg"
            ]
            
            for url in figure_urls:
                if self.check_image_exists(url):
                    result["image_url"] = url
                    result["attribution"]["figure_type"] = "primary_figure"
                    break
                    
        return result
    
    def extract_pmc_id(self, url: str) -> Optional[str]:
        """Extract PMC ID from URL"""
        import re
        
        if not url:
            return None
            
        # Pattern for PMC URLs
        match = re.search(r'PMC\d+', url)
        if match:
            return match.group(0)
            
        return None
    
    def check_image_exists(self, url: str) -> bool:
        """Check if image URL returns valid image"""
        try:
            response = requests.head(url, timeout=5)
            return response.status_code == 200 and 'image' in response.headers.get('content-type', '')
        except:
            return False
    
    def update_research_images(self, limit: int = 10):
        """Update research entries without images"""
        # Get entries without images
        check_url = f"{SUPABASE_URL}/rest/v1/research_entries"
        params = {
            "image_url": "is.null",
            "full_text_url": "not.is.null",
            "select": "id,title,full_text_url",
            "limit": limit
        }
        
        response = requests.get(check_url, headers=headers, params=params)
        entries = response.json()
        
        print(f"🔍 Found {len(entries)} research entries without images")
        
        updated = 0
        for entry in entries:
            print(f"\n📄 Processing: {entry['title'][:60]}...")
            
            result = self.extract_article_image_with_attribution(entry)
            
            if result['image_url']:
                # Store attribution as JSONB
                update_data = {
                    "image_url": result['image_url'],
                    "image_attribution": result['attribution'],
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                update_url = f"{SUPABASE_URL}/rest/v1/research_entries?id=eq.{entry['id']}"
                response = requests.patch(update_url, headers=headers, json=update_data)
                
                if response.status_code in [200, 204]:
                    updated += 1
                    print(f"✅ Updated image: {result['image_url']}")
                else:
                    print(f"❌ Failed to update: {response.text}")
            else:
                print(f"⚠️  No image found")
                
            time.sleep(0.5)  # Rate limiting
        
        return updated


def main():
    """Run enhanced scrapers with attribution"""
    print("🚀 Starting enhanced image scraping with attribution...\n")
    
    # First, add attribution columns if they don't exist
    print("📊 Checking database schema...")
    
    # Run company logo scraper
    print("\n🏢 === COMPANY LOGOS ===")
    company_scraper = EnhancedCompanyLogoScraper()
    logos_updated = company_scraper.update_company_logos(limit=20)
    print(f"\n✅ Updated {logos_updated} company logos")
    
    # Run research image scraper
    print("\n📚 === RESEARCH IMAGES ===")
    research_scraper = EnhancedResearchImageScraper()
    images_updated = research_scraper.update_research_images(limit=20)
    print(f"\n✅ Updated {images_updated} research images")
    
    # Show final stats
    print("\n📊 === FINAL STATISTICS ===")
    
    # Company stats
    response = requests.get(f"{SUPABASE_URL}/rest/v1/hemp_companies?select=id&logo_url=not.is.null", 
                          headers={**headers, "Prefer": "count=exact"})
    if 'content-range' in response.headers:
        companies_with_logos = response.headers['content-range'].split('/')[1]
        print(f"🏢 Companies with logos: {companies_with_logos}")
    
    # Research stats
    response = requests.get(f"{SUPABASE_URL}/rest/v1/research_entries?select=id&image_url=not.is.null", 
                          headers={**headers, "Prefer": "count=exact"})
    if 'content-range' in response.headers:
        research_with_images = response.headers['content-range'].split('/')[1]
        print(f"📚 Research entries with images: {research_with_images}")


if __name__ == "__main__":
    main()