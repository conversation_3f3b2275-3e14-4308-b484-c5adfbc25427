# GitHub Actions Testing Guide

## Step 1: Push the Latest Changes

Since your remote uses HTTPS, you'll need to push manually in your terminal:

```bash
# In your terminal (Git Bash or WSL):
git push origin main
```

You'll be prompted for your GitHub username and password/token.

## Step 2: Test the Basic Setup Workflow

1. Go to your repository: https://github.com/HempQuarterz/HQz-Ai-DB-MCP-3
2. Click the **Actions** tab
3. On the left sidebar, find **"Test Basic Setup (No API Required)"**
4. Click on it, then click **"Run workflow"** button
5. Leave all defaults and click the green **"Run workflow"** button

This workflow:
- ✅ Doesn't require OpenAI API
- ✅ Tests Python imports
- ✅ Tests hemp CLI
- ✅ Tests Supabase connection
- ✅ Uses MOCK_MODE to avoid API calls

## Step 3: Monitor the Workflow Run

1. Click on the running workflow to see live logs
2. Watch for:
   - ✅ Green checkmarks for each step
   - 📋 Check the summary at the bottom
   - 🔍 Look for any red X marks

## Step 4: Expected Results

You should see:
- ✅ Python imports working
- ✅ Hemp CLI help displayed
- ✅ Supabase connection established
- ✅ Mock mode preventing API calls

## Step 5: If Everything Passes

Try these workflows next (they also work without OpenAI):

### A. Monitoring Workflow
1. Click **"System Monitoring and Alerts"**
2. Run workflow with defaults
3. This checks system health without AI

### B. Monitoring and Reporting
1. Click **"Monitoring and Reporting"**
2. Run with report_type = "health"
3. Generates health reports without AI

## Troubleshooting

### If you see "Module not found" errors:
- The PYTHONPATH fix should prevent this
- Check if requirements.txt has all dependencies

### If you see "Permission denied" errors:
- The chmod +x hemp fix should prevent this
- The workflow uses `python hemp` instead of `./hemp`

### If Supabase connection fails:
- Verify secrets are set in repository settings
- Check SUPABASE_URL and SUPABASE_ANON_KEY

## What's Working Without OpenAI API

These commands work in MOCK_MODE:
- `python hemp --help`
- `python hemp monitor --format health`
- `python hemp monitor --format report`
- `python hemp db validate`
- Basic system checks

## Next Steps After Testing

Once basic workflows pass:
1. Document any errors you encounter
2. We can create more mock modes for other agents
3. Consider using alternative AI providers
4. Or add minimal OpenAI funds for full testing

## Quick Command Reference

```bash
# Push changes
git push origin main

# If you need to set up credentials
git config --global user.name "HempQuarterz"
git config --global user.email "<EMAIL>"

# For HTTPS, use a Personal Access Token instead of password
# Create at: https://github.com/settings/tokens
```