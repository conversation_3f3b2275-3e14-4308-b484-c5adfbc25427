import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, AuthError, Provider } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase-client';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error: AuthError | null }>;
  signInWithOAuth: (provider: Provider) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: any) => Promise<{ error: AuthError | null }>;
  enableMFA: () => Promise<{ error: AuthError | null; qrCode?: string }>;
  verifyMFA: (code: string) => Promise<{ error: AuthError | null }>;
  disableMFA: () => Promise<{ error: AuthError | null }>;
  isAdmin: boolean;
  hasMFA: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an EnhancedAuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function EnhancedAuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasMFA, setHasMFA] = useState(false);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);

      // Check MFA status when user signs in
      if (session?.user) {
        checkMFAStatus();
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkMFAStatus = async () => {
    try {
      const { data, error } = await supabase.auth.mfa.listFactors();
      if (!error && data) {
        setHasMFA(data.totp.length > 0);
      }
    } catch (error) {
      console.error('Error checking MFA status:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    const result = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    setLoading(false);
    return { error: result.error };
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    setLoading(true);
    const result = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
    setLoading(false);
    return { error: result.error };
  };

  const signInWithOAuth = async (provider: Provider) => {
    setLoading(true);
    const result = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });
    setLoading(false);
    return { error: result.error };
  };

  const signOut = async () => {
    setLoading(true);
    const result = await supabase.auth.signOut();
    setHasMFA(false);
    setLoading(false);
    return { error: result.error };
  };

  const resetPassword = async (email: string) => {
    const result = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    return { error: result.error };
  };

  const updateProfile = async (updates: any) => {
    const result = await supabase.auth.updateUser({
      data: updates,
    });
    return { error: result.error };
  };

  const enableMFA = async () => {
    try {
      const { data, error } = await supabase.auth.mfa.enroll({
        factorType: 'totp',
      });
      
      if (error) {
        return { error };
      }

      return { error: null, qrCode: data.qr_code };
    } catch (error) {
      return { error: error as AuthError };
    }
  };

  const verifyMFA = async (code: string) => {
    try {
      const factors = await supabase.auth.mfa.listFactors();
      if (factors.error) {
        return { error: factors.error };
      }

      const totpFactor = factors.data.totp[0];
      if (!totpFactor) {
        return { error: { message: 'No TOTP factor found' } as AuthError };
      }

      const { error } = await supabase.auth.mfa.verify({
        factorId: totpFactor.id,
        challengeId: totpFactor.id,
        code,
      });

      if (!error) {
        setHasMFA(true);
      }

      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  };

  const disableMFA = async () => {
    try {
      const factors = await supabase.auth.mfa.listFactors();
      if (factors.error) {
        return { error: factors.error };
      }

      const totpFactor = factors.data.totp[0];
      if (!totpFactor) {
        return { error: null }; // No factor to disable
      }

      const { error } = await supabase.auth.mfa.unenroll({
        factorId: totpFactor.id,
      });

      if (!error) {
        setHasMFA(false);
      }

      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  };

  // Check if user is admin
  const isAdmin = user?.user_metadata?.role === 'admin' || 
                  user?.email === '<EMAIL>' ||
                  user?.user_metadata?.is_admin === true;

  const value = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signInWithOAuth,
    signOut,
    resetPassword,
    updateProfile,
    enableMFA,
    verifyMFA,
    disableMFA,
    isAdmin,
    hasMFA,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
