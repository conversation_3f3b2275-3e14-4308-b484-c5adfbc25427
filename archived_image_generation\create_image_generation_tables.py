#!/usr/bin/env python3
"""
Create image generation tables for automated image creation
"""

import os
import asyncio
from supabase import create_client, Client
from dotenv import load_dotenv
from datetime import datetime

# Load .env from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
load_dotenv(env_path)

# Get Supabase credentials
SUPABASE_URL = os.getenv('VITE_SUPABASE_URL')
SUPABASE_KEY = os.getenv('VITE_SUPABASE_ANON_KEY')

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ Missing Supabase credentials in .env file")
    exit(1)

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

async def create_tables():
    """Create necessary tables for image generation"""
    
    print("🔧 Creating image generation tables...")
    
    # SQL to create tables
    create_tables_sql = """
    -- Image generation queue table
    CREATE TABLE IF NOT EXISTS image_generation_queue (
        id SERIAL PRIMARY KEY,
        reference_type VARCHAR(50) NOT NULL, -- 'product', 'plant_type', 'plant_part'
        reference_id INTEGER NOT NULL,
        prompt TEXT NOT NULL,
        provider VARCHAR(50) DEFAULT 'imagen_3',
        status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed, applied
        generated_image_url TEXT,
        error_message TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );

    -- Create indexes for performance
    CREATE INDEX IF NOT EXISTS idx_queue_status ON image_generation_queue(status);
    CREATE INDEX IF NOT EXISTS idx_queue_reference ON image_generation_queue(reference_type, reference_id);
    CREATE INDEX IF NOT EXISTS idx_queue_created ON image_generation_queue(created_at);

    -- AI provider configuration table
    CREATE TABLE IF NOT EXISTS ai_provider_config (
        id SERIAL PRIMARY KEY,
        provider_name VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        api_endpoint TEXT,
        cost_per_image DECIMAL(10, 4) DEFAULT 0.02,
        quality_score INTEGER DEFAULT 8, -- 1-10 scale
        is_active BOOLEAN DEFAULT true,
        config JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );

    -- Insert default providers
    INSERT INTO ai_provider_config (provider_name, display_name, cost_per_image, quality_score, config)
    VALUES 
        ('imagen_3', 'Google Imagen 3', 0.02, 9, '{"model": "imagen-3.0-generate-001", "aspectRatio": "1:1"}'),
        ('dall_e_3', 'OpenAI DALL-E 3', 0.04, 9, '{"model": "dall-e-3", "quality": "standard", "size": "1024x1024"}'),
        ('stable_diffusion', 'Stable Diffusion XL', 0.01, 8, '{"model": "stable-diffusion-xl-1024-v1-0"}')
    ON CONFLICT (provider_name) DO UPDATE 
    SET updated_at = NOW();

    -- Image generation history table (for tracking and analytics)
    CREATE TABLE IF NOT EXISTS image_generation_history (
        id SERIAL PRIMARY KEY,
        queue_id INTEGER REFERENCES image_generation_queue(id),
        provider VARCHAR(50),
        prompt TEXT,
        cost DECIMAL(10, 4),
        generation_time_ms INTEGER,
        image_url TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT NOW()
    );
    """
    
    try:
        # Execute SQL through Supabase RPC or direct connection
        # Note: This would normally be done through migrations or database admin
        print("✅ Tables creation SQL generated")
        print("\n📋 SQL to execute in Supabase SQL editor:")
        print("=" * 60)
        print(create_tables_sql)
        print("=" * 60)
        
        # Check if tables exist by querying
        try:
            # Try to query the table
            result = supabase.table('image_generation_queue').select('count').limit(1).execute()
            print("\n✅ image_generation_queue table already exists")
        except:
            print("\n⚠️ image_generation_queue table does not exist - please run the SQL above in Supabase")
        
        try:
            result = supabase.table('ai_provider_config').select('count').limit(1).execute()
            print("✅ ai_provider_config table already exists")
        except:
            print("⚠️ ai_provider_config table does not exist - please run the SQL above in Supabase")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

async def check_existing_setup():
    """Check current image generation setup"""
    print("\n🔍 Checking existing setup...")
    
    try:
        # Check products without images
        products = supabase.table('uses_products').select('count').or_('image_url.is.null', 'image_url.like.%placeholder%').execute()
        if products.data:
            print(f"📦 Products needing images: {products.data[0]['count']}")
        
        # Check queue status
        try:
            queue = supabase.table('image_generation_queue').select('status').execute()
            if queue.data:
                status_counts = {}
                for item in queue.data:
                    status = item['status']
                    status_counts[status] = status_counts.get(status, 0) + 1
                
                print("\n📊 Image Generation Queue Status:")
                for status, count in status_counts.items():
                    print(f"   {status}: {count}")
        except:
            print("⚠️ Image generation queue table not found")
            
    except Exception as e:
        print(f"❌ Error checking setup: {e}")

async def main():
    print("🖼️ Hemp Database - Image Generation Setup")
    print("=" * 60)
    
    # Create tables
    success = await create_tables()
    
    if success:
        print("\n✅ Setup complete!")
        
        # Check existing setup
        await check_existing_setup()
        
        print("\n📝 Next Steps:")
        print("1. Run the SQL above in Supabase SQL editor if tables don't exist")
        print("2. Configure your AI API keys in Supabase Edge Functions")
        print("3. Use the ResearchAgentWithImages for automatic image generation")
        print("4. Or run 'python run_enhanced_agent.py' to use the new agent")
    else:
        print("\n❌ Setup failed")

if __name__ == "__main__":
    asyncio.run(main())