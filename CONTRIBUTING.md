# Contributing to HempQuarterz Database

First off, thank you for considering contributing to the HempQuarterz Database project! It's people like you that make this project such a great tool for the hemp industry.

## Code of Conduct

By participating in this project, you are expected to uphold our Code of Conduct:
- Be respectful and inclusive
- Welcome newcomers and help them get started
- Focus on what is best for the community
- Show empathy towards other community members

## How Can I Contribute?

### Reporting Bugs

Before creating bug reports, please check existing issues as you might find out that you don't need to create one. When you are creating a bug report, please include as many details as possible using our bug report template.

**Note:** If you find a **Closed** issue that seems like it is the same thing that you're experiencing, open a new issue and include a link to the original issue in the body of your new one.

### Suggesting Enhancements

Enhancement suggestions are tracked as GitHub issues. When creating an enhancement suggestion, please use our feature request template and provide:
- A clear and descriptive title
- A detailed description of the proposed enhancement
- Examples of how it would be used
- Why this enhancement would be useful to most users

### Your First Code Contribution

Unsure where to begin contributing? You can start by looking through these issues:
- Issues labeled `good first issue` - issues which should only require a few lines of code
- Issues labeled `help wanted` - issues which may be a bit more involved

### Pull Requests

1. Fork the repo and create your branch from `main`
2. If you've added code that should be tested, add tests
3. If you've changed APIs, update the documentation
4. Ensure the test suite passes
5. Make sure your code follows the existing style
6. Issue that pull request!

## Development Setup

1. **Clone your fork**
   ```bash
   git clone https://github.com/your-username/HQz-Ai-DB-MCP-3.git
   cd HQz-Ai-DB-MCP-3
   ```

2. **Install dependencies**
   ```bash
   cd HempResourceHub
   npm install
   cd ..
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   cp HempResourceHub/.env.example HempResourceHub/.env
   # Edit .env with your Supabase credentials
   ```

4. **Run the development server**
   ```bash
   cd HempResourceHub
   npm run dev
   ```

## Style Guidelines

### JavaScript/TypeScript
- Use 2 spaces for indentation
- Use semicolons
- Use single quotes for strings
- Follow the existing code style

### Python
- Follow PEP 8
- Use 4 spaces for indentation
- Use descriptive variable names
- Add docstrings to functions

### Git Commit Messages
- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests liberally after the first line

### Documentation
- Use Markdown for documentation
- Keep language clear and concise
- Include code examples where helpful
- Update the README.md if needed

## Project Structure

```
HQz-Ai-DB-MCP-3/
├── HempResourceHub/           # Main web application
│   ├── client/               # React frontend
│   ├── server/               # Express backend
│   └── shared/               # Shared schemas
├── agents/                   # AI agents
├── lib/                      # Shared libraries
├── scripts/                  # Utility scripts
└── *.py                      # Python scripts
```

## Testing

- Run TypeScript type checking: `npm run check`
- Run backend tests: `npm test`
- Test Python scripts: `python -m pytest` (if tests exist)

## Questions?

Feel free to open an issue with your question or reach out to the maintainers.

Thank you for contributing! 🌿