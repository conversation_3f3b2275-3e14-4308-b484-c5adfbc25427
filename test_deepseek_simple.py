#!/usr/bin/env python3
"""
Simple test of DeepSeek with the hemp CLI
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from lib.supabase_client import get_supabase_client
from utils.ai_providers import DeepSeekProvider
from agents.research.unified_research_agent import UnifiedResearchAgent, ResearchConfig, ResearchFeatures


# Simple wrapper to make DeepSeek work with the agent
class SimpleAIProvider:
    def __init__(self, deepseek_provider):
        self.provider = deepseek_provider
        
    async def generate(self, prompt: str, **kwargs):
        """Simple generate that just returns text"""
        return await self.provider.generate(prompt, **kwargs)


async def test_deepseek_simple():
    """Test DeepSeek with simplified setup"""
    logger.info("Testing DeepSeek with simple setup...")
    
    # Create DeepSeek provider directly
    deepseek = DeepSeekProvider()
    ai_provider = SimpleAIProvider(deepseek)
    
    # Test generation
    logger.info("\n🧪 Testing simple generation...")
    result = await ai_provider.generate("List 3 hemp construction materials")
    logger.info(f"Result:\n{result}")
    
    # Test with research agent
    logger.info("\n🔬 Testing with research agent...")
    supabase = get_supabase_client()
    
    # Configure to use only non-AI features plus basic discovery
    config = ResearchConfig(
        enabled_features={
            ResearchFeatures.BASIC,
            ResearchFeatures.WEB_SCRAPING,
            ResearchFeatures.FEED_MONITORING
        },
        use_ai_analysis=False,  # Don't use AI for structuring
        company_extraction=False,  # Don't extract companies
        auto_generate_images=False,
        max_results=5
    )
    
    agent = UnifiedResearchAgent(supabase, ai_provider=ai_provider, config=config)
    
    # Test basic discovery with DeepSeek
    logger.info("Testing basic discovery with DeepSeek...")
    basic_products = await agent._basic_discovery("hemp insulation materials", 3)
    logger.info(f"Basic discovery found {len(basic_products)} products")
    
    # Also test feed discovery (no AI needed)
    logger.info("\nTesting feed discovery...")
    feed_products = await agent._feed_discovery("hemp", 5)
    logger.info(f"Feed discovery found {len(feed_products)} products")
    
    # Combine results
    products = basic_products + feed_products[:2]  # Take first 2 from feeds
    
    logger.info(f"\n✅ Discovered {len(products)} products")
    for i, product in enumerate(products, 1):
        logger.info(f"\n{i}. {product.get('name', 'Unknown')}")
        logger.info(f"   Description: {product.get('description', 'No description')[:100]}...")


if __name__ == "__main__":
    try:
        asyncio.run(test_deepseek_simple())
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()