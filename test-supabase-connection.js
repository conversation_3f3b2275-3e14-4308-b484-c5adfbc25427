import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, 'HempResourceHub', '.env') });

console.log('Testing Supabase connection...\n');

// Test with VITE variables (these are working)
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing VITE_SUPABASE_URL or VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey.substring(0, 20) + '...');

// Test connection
const supabase = createClient(supabaseUrl, supabaseKey);

try {
  // Test query
  const { data, error } = await supabase
    .from('uses_products')
    .select('count')
    .limit(1);

  if (error) {
    console.error('❌ Supabase connection failed:', error);
  } else {
    console.log('✅ Supabase connection successful!');
    
    // Get actual count
    const { count } = await supabase
      .from('uses_products')
      .select('*', { count: 'exact', head: true });
      
    console.log(`📦 Total products in database: ${count}`);
  }

  // Also test plant_parts table
  const { data: plantParts, error: plantPartsError } = await supabase
    .from('plant_parts')
    .select('name')
    .limit(5);

  if (!plantPartsError) {
    console.log('\n🌿 Sample plant parts:');
    plantParts.forEach(part => console.log(`   - ${part.name}`));
  }

} catch (err) {
  console.error('Error:', err);
}

console.log('\n💡 Note: The frontend connects directly to Supabase using these credentials.');
console.log('The server-side connection issue does not affect the app functionality.');