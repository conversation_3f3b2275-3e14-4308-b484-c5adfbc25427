# DEPRECATED AGENTS - DO NOT USE

## The Problem
Multiple research agents were re-queuing image generation for existing products,
causing the image_generation_queue to grow exponentially (1,893 entries for 219 products).

## Deprecated Files (DO NOT USE):
- run_agent_with_images.py
- run_simple_agent_with_images.py  
- run_enhanced_agent.py
- agents/research/research_agent_with_images.py (use unified agent instead)
- agents/research/enhanced_research_agent.py (use unified agent instead)

## Use Instead:
```bash
# Use the unified CLI
./hemp agent research "your query" --max-results 10

# Or use the unified research agent directly
from agents.research.unified_research_agent import UnifiedResearchAgent
```

## Key Rules:
1. NEVER re-queue images for products that already have an image_url (even placeholders)
2. Only queue images when creating NEW products
3. Check image_generation_queue before queuing to prevent duplicates
