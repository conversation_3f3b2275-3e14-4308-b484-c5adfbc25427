#!/usr/bin/env python3
"""
Direct agent runner - bypasses orchestrator complexity
Run agents directly to populate the database
"""

import os
import sys
import asyncio
from datetime import datetime

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Manual .env loading
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    if os.path.exists(env_path):
        with open(env_path) as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    try:
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value
                    except ValueError:
                        pass

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase import create_client

async def run_research_agent():
    """Run the research agent to discover new products"""
    print("\n🔬 Running Research Agent...")
    
    try:
        from agents.research.research_agent import HempResearchAgent
        
        # Initialize Supabase
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_ANON_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ Missing Supabase credentials!")
            return
        
        supabase = create_client(supabase_url, supabase_key)
        
        # Create and run agent
        agent = HempResearchAgent(supabase)
        
        # Task to discover products
        task = {
            'action': 'discover_products',
            'params': {
                'limit': 10,  # Start with 10 products
                'categories': ['all']
            }
        }
        
        async with agent:
            result = await agent.execute(task)
            
        print(f"✅ Research Agent Results:")
        print(f"   - Discovered: {result.get('discovered_count', 0)} items")
        print(f"   - Structured: {result.get('structured_count', 0)} products")
        print(f"   - Saved to DB: {result.get('saved_count', 0)} products")
        
        # Log the run
        run_record = {
            'agent_name': 'research_agent_direct',
            'timestamp': datetime.now().isoformat(),
            'products_found': result.get('structured_count', 0),
            'products_saved': result.get('saved_count', 0),
            'companies_saved': 0,
            'status': 'completed' if result.get('saved_count', 0) > 0 else 'no_results'
        }
        
        supabase.table('hemp_agent_runs').insert(run_record).execute()
        
    except Exception as e:
        print(f"❌ Research Agent Error: {e}")
        import traceback
        traceback.print_exc()

async def run_comprehensive_discovery():
    """Run the comprehensive product discovery agent"""
    print("\n🌿 Running Comprehensive Product Discovery...")
    
    try:
        # Import directly to avoid __init__.py
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'agents'))
        import comprehensive_product_discovery_agent
        ComprehensiveProductDiscoveryAgent = comprehensive_product_discovery_agent.ComprehensiveProductDiscoveryAgent
        
        agent = ComprehensiveProductDiscoveryAgent()
        
        # Analyze coverage gaps
        print("📊 Analyzing database coverage...")
        gaps = agent.analyze_coverage_gaps()
        
        if gaps:
            print(f"Found {len(gaps)} gaps in coverage")
            # Discover products for first 3 gaps
            for gap in gaps[:3]:
                print(f"\n🔍 Discovering products for: {gap['plant_part']} × {gap['industry']}")
                products = agent.discover_products_for_combination(
                    gap['plant_part'], 
                    gap['industry'],
                    limit=5
                )
                
                if products:
                    result = agent.save_products(products)
                    print(f"   ✅ Saved {result['saved']} products")
                else:
                    print(f"   ⚠️  No products found")
        else:
            print("✅ Database has good coverage!")
            
    except Exception as e:
        print(f"❌ Comprehensive Discovery Error: {e}")
        import traceback
        traceback.print_exc()

async def check_database_status():
    """Check current database status"""
    print("\n📊 Database Status Check...")
    
    try:
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_ANON_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ Missing Supabase credentials!")
            return
        
        supabase = create_client(supabase_url, supabase_key)
        
        # Count products
        products = supabase.table('uses_products').select('count', count='exact').execute()
        print(f"   Total products: {products.count}")
        
        # Products by plant part
        plant_parts = supabase.table('plant_parts').select('id, name').execute()
        for part in plant_parts.data:
            count = supabase.table('uses_products').select('count', count='exact').eq('plant_part_id', part['id']).execute()
            print(f"   - {part['name']}: {count.count} products")
            
    except Exception as e:
        print(f"❌ Database check error: {e}")

def main():
    """Main entry point"""
    print("=== Hemp AI Agent Direct Runner ===")
    print("This bypasses the orchestrator to run agents directly\n")
    
    # Check environment
    print("🔧 Environment Check:")
    print(f"   SUPABASE_URL: {'✅ Set' if os.environ.get('SUPABASE_URL') else '❌ Missing'}")
    print(f"   SUPABASE_ANON_KEY: {'✅ Set' if os.environ.get('SUPABASE_ANON_KEY') else '❌ Missing'}")
    print(f"   OPENAI_API_KEY: {'✅ Set' if os.environ.get('OPENAI_API_KEY') else '⚠️  Missing (limited functionality)'}")
    
    if not os.environ.get('SUPABASE_URL') or not os.environ.get('SUPABASE_ANON_KEY'):
        print("\n❌ Missing required environment variables!")
        print("Please ensure .env file contains SUPABASE_URL and SUPABASE_ANON_KEY")
        return
    
    print("\nSelect an option:")
    print("1. Check database status")
    print("2. Run Research Agent (discover from web sources)")
    print("3. Run Comprehensive Discovery (systematic coverage)")
    print("4. Run both agents")
    
    choice = input("\nEnter choice (1-4): ")
    
    if choice == '1':
        asyncio.run(check_database_status())
    elif choice == '2':
        asyncio.run(check_database_status())
        asyncio.run(run_research_agent())
    elif choice == '3':
        asyncio.run(check_database_status())
        asyncio.run(run_comprehensive_discovery())
    elif choice == '4':
        asyncio.run(check_database_status())
        asyncio.run(run_research_agent())
        asyncio.run(run_comprehensive_discovery())
    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()