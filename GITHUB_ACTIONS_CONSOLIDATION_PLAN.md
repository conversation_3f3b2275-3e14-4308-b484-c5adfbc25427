# GitHub Actions Consolidation Plan

## Current Issues

### 1. Duplicate Workflows
We have **10 workflow files** with overlapping functionality:
- `image-generation.yml` - Runs every 6 hours
- `hemp-automation.yml` - Runs multiple times daily  
- `automated-operations.yml` - Also includes image generation
- Multiple monitoring workflows doing similar tasks
- Test workflows that should be removed from production

### 2. Image Generation Overpopulation
- **1,893 queue entries** for only **219 products**
- Products have up to **29 duplicate entries**
- Workflows are re-queuing already processed items
- No deduplication or constraint logic

## Consolidation Plan

### Phase 1: Immediate Actions

1. **Run Cleanup Script**
   ```bash
   python cleanup_image_generation_tables.py
   ```

2. **Disable Redundant Workflows**
   ```bash
   # Rename to disable
   mv .github/workflows/hemp-automation.yml .github/workflows/hemp-automation.yml.disabled
   mv .github/workflows/status-check.yml .github/workflows/status-check.yml.disabled
   mv .github/workflows/weekly-summary.yml .github/workflows/weekly-summary.yml.disabled
   ```

### Phase 2: Workflow Consolidation

#### Keep These Workflows:

1. **automated-operations.yml** (Primary)
   - Handles all agent operations
   - Includes image generation
   - Already has scheduling logic

2. **monitoring-and-reporting.yml** (Consolidated monitoring)
   - Combines all monitoring tasks
   - Includes health checks, daily, and weekly reports

3. **simple-test.yml** & **test-basic-clean.yml** (Testing only)
   - Keep for manual testing
   - Not scheduled

#### Remove/Disable These Workflows:

1. **image-generation.yml** - Functionality moved to automated-operations
2. **hemp-automation.yml** - Duplicate of automated-operations
3. **monitoring.yml** - Merged into monitoring-and-reporting
4. **status-check.yml** - Redundant
5. **weekly-summary.yml** - Merged into monitoring-and-reporting

### Phase 3: Update Remaining Workflows

#### automated-operations.yml Updates:
```yaml
# Add deduplication check before image generation
- name: Check if images needed
  run: |
    PRODUCTS_WITHOUT_IMAGES=$(python -c "
    from supabase import create_client
    import os
    client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_ANON_KEY'))
    result = client.table('uses_products').select('id').or_('image_url.is.null,image_url.eq.').execute()
    print(len(result.data) if result.data else 0)
    ")
    echo "products_without_images=$PRODUCTS_WITHOUT_IMAGES" >> $GITHUB_OUTPUT

# Only run if products need images
- name: Generate images
  if: steps.check-images.outputs.products_without_images > 0
  run: |
    # Call Edge Function instead of Python script
    curl -X POST ${{ secrets.SUPABASE_URL }}/functions/v1/hemp-image-generator \
      -H "Authorization: Bearer ${{ secrets.SUPABASE_ANON_KEY }}" \
      -H "Content-Type: application/json" \
      -d '{"batchSize": 20}'
```

### Phase 4: Database Constraints

Apply these constraints to prevent future duplicates:

```sql
-- Prevent multiple active entries per product
CREATE UNIQUE INDEX idx_unique_product_pending 
ON image_generation_queue(product_id) 
WHERE status IN ('pending', 'processing');

-- Auto-cleanup old completed entries (keep only latest)
CREATE OR REPLACE FUNCTION cleanup_old_completed_entries()
RETURNS TRIGGER AS $$
BEGIN
    -- When marking as completed, delete other completed entries for same product
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        DELETE FROM image_generation_queue 
        WHERE product_id = NEW.product_id 
        AND status = 'completed' 
        AND id != NEW.id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cleanup_on_complete
AFTER UPDATE ON image_generation_queue
FOR EACH ROW
EXECUTE FUNCTION cleanup_old_completed_entries();
```

### Phase 5: Update Edge Function

Fix the Edge Function to prevent re-queuing:

```typescript
// Before queuing, check if product already has an image
const { data: product } = await supabase
  .table('uses_products')
  .select('image_url')
  .eq('id', productId)
  .single();

if (product?.image_url && !product.image_url.includes('placeholder.com')) {
  console.log(`Product ${productId} already has image: ${product.image_url}`);
  continue; // Skip this product
}
```

## Expected Results

### Before:
- 10 workflows
- 1,893 queue entries
- Duplicate processing
- Wasted API calls

### After:
- 3 active workflows
- ~20 queue entries (only products without images)
- No duplicates
- Efficient processing

## Implementation Timeline

1. **Immediate** (Today):
   - Run cleanup script
   - Disable redundant workflows
   - Apply database constraints

2. **This Week**:
   - Update automated-operations.yml
   - Update Edge Function
   - Test consolidated workflows

3. **Next Week**:
   - Monitor for any issues
   - Fine-tune scheduling
   - Document new workflow structure

## Cost Savings

- **Before**: ~1,800 unnecessary image generation attempts
- **After**: Only ~20 actual images needed
- **Savings**: 98.9% reduction in unnecessary processing