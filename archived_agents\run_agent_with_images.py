#!/usr/bin/env python3
"""
Run the enhanced research agent with automatic image generation
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Add the project root to sys.path to allow direct imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import directly without going through __init__.py
from agents.research.research_agent_with_images import ResearchAgentWithImages

# Load .env from HempResourceHub directory
env_path = os.path.join(os.path.dirname(__file__), 'HempResourceHub', '.env')
load_dotenv(env_path)

async def main():
    """Run the research agent with image generation"""
    
    # Check for required environment variables
    required_vars = ['VITE_SUPABASE_URL', 'VITE_SUPABASE_ANON_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please add them to your .env file")
        return
    
    # Initialize agent
    agent = ResearchAgentWithImages(
        supabase_url=os.getenv('VITE_SUPABASE_URL'),
        supabase_key=os.getenv('VITE_SUPABASE_ANON_KEY'),
        openai_api_key=os.getenv('OPENAI_API_KEY')  # Optional, will work without it
    )
    
    print("🚀 Hemp Research Agent with Automatic Image Generation")
    print("=" * 60)
    print("This agent will:")
    print("1. Discover new hemp products")
    print("2. Extract company information")
    print("3. Save to database")
    print("4. Automatically generate images for each product")
    print()
    
    # Categories to research
    research_topics = [
        "hemp fiber automotive composites",
        "hemp bioplastics packaging",
        "hemp construction materials",
        "hemp textiles and fabrics",
        "hemp-based foods and nutrition",
        "hemp cosmetics and skincare",
        "hemp paper products",
        "hemp biofuel applications",
        "hemp animal feed and bedding",
        "hemp medical applications"
    ]
    
    total_discovered = 0
    total_saved = 0
    
    print(f"🔍 Researching {len(research_topics)} topics...\n")
    
    for topic in research_topics:
        print(f"📚 Researching: {topic}")
        
        try:
            # Discover products
            products = await agent.discover_products(topic, max_results=5)
            
            if products:
                print(f"   ✅ Found {len(products)} products")
                total_discovered += len(products)
                
                # Save products (automatically queues image generation)
                saved = await agent.save_discovered_products(products)
                total_saved += saved
                print(f"   💾 Saved {saved} new products")
                print(f"   🖼️ Queued image generation for new products")
            else:
                print(f"   ⚠️ No products found")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Brief pause between topics
        await asyncio.sleep(2)
        print()
    
    print("=" * 60)
    print(f"📊 Summary:")
    print(f"   📚 Topics researched: {len(research_topics)}")
    print(f"   🔍 Products discovered: {total_discovered}")
    print(f"   💾 New products saved: {total_saved}")
    print(f"   🖼️ Images queued: {total_saved}")
    
    if total_saved > 0:
        print("\n⏳ Waiting 30 seconds for initial image generation...")
        await asyncio.sleep(30)
        
        # Process any completed images
        print("🔄 Processing completed images...")
        await agent.process_completed_images()
        
        print("\n✅ Initial image processing complete!")
        print("💡 Note: Image generation continues in the background")
        print("   Images will be automatically updated as they complete")
    
    # Show next steps
    print("\n📝 Next Steps:")
    print("1. Check the Supabase dashboard to monitor image generation queue")
    print("2. View your products at http://localhost:3000/all-products")
    print("3. Run 'python merge_agent_companies.py --auto' to consolidate companies")
    print("4. Images will continue generating and auto-update in the background")

if __name__ == "__main__":
    # Check if OpenAI key is available
    if not os.getenv('OPENAI_API_KEY'):
        print("⚠️ Warning: No OpenAI API key found")
        print("The agent will use fallback data sources (web search, hardcoded data)")
        print("For best results, add OPENAI_API_KEY to your .env file")
        print()
        
        response = input("Continue without OpenAI? (y/n): ")
        if response.lower() != 'y':
            print("Exiting...")
            exit(0)
    
    asyncio.run(main())