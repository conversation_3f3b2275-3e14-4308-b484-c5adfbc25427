#!/usr/bin/env python3
"""
Add real hemp products found via web search to the database
Based on actual companies and products found online
"""

import os
import sys
from datetime import datetime
from supabase import create_client

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

# Real products found via web search
SCRAPED_PRODUCTS = [
    # Hemp Seeds - Food Products
    {
        "name": "Manitoba Harvest Hemp Hearts Original",
        "description": "Raw shelled hemp seeds that are a versatile plant-based protein source. Add to smoothies, yogurt, salads, and baked goods for a nutritional boost.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["10g plant-based protein per 30g serving", "Perfect balance of Omega-3 and Omega-6", "Non-GMO Project Verified", "Grown in Canada"],
        "stage": "Established",
        "company": "Manitoba Harvest"
    },
    {
        "name": "Good Hemp Seed Oil",
        "description": "Cold-pressed hemp seed oil from UK-grown hemp. Rich in essential fatty acids and perfect for drizzling over salads or adding to smoothies.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage", 
        "benefits": ["High in Omega-3 ALA", "Nutty flavor profile", "British grown hemp", "Cold-pressed for nutrient retention"],
        "stage": "Established",
        "company": "Good Hemp"
    },
    {
        "name": "Victory Hemp V-70 Hemp Protein",
        "description": "Premium hemp protein powder made in Kentucky from North American grown hemp. Contains 70% protein content for athletes and health-conscious consumers.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["70% protein content", "Made in USA", "Kosher certified", "Single ingredient product"],
        "stage": "Growing",
        "company": "Victory Hemp Foods"
    },
    {
        "name": "Fresh Hemp Foods Hemp Oil Capsules",
        "description": "Convenient hemp seed oil supplement capsules. Each capsule delivers the nutritional benefits of hemp oil without the taste.",
        "plant_part": "Hemp Seed",
        "industry": "Medicine",
        "benefits": ["1000mg hemp oil per capsule", "Easy to swallow", "No hemp taste", "Travel-friendly format"],
        "stage": "Growing",
        "company": "Fresh Hemp Foods"
    },
    {
        "name": "Hemp Foods Australia Organic Hemp Seeds",
        "description": "Australian grown and processed organic hemp seeds. Hulled for easy consumption and certified organic by Australian standards.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["Australian certified organic", "FODMAP friendly", "Source of plant protein", "Gluten-free"],
        "stage": "Established",
        "company": "Hemp Foods Australia"
    },
    
    # Hemp Fiber - Textile Products
    {
        "name": "Hemp Fortex Hemp Denim Fabric",
        "description": "Premium hemp denim fabric manufactured with sustainable processes. Stronger and more durable than traditional cotton denim.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["30% stronger than cotton", "Naturally antimicrobial", "Breathable and comfortable", "Sustainable production"],
        "stage": "Growing",
        "company": "Hemp Fortex"
    },
    {
        "name": "Toad&Co Hemp Trail Shorts",
        "description": "Outdoor shorts made from hemp blend fabric. Designed for hiking and active lifestyles with natural odor resistance.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["Odor resistant", "Quick drying", "UPF 40+ sun protection", "Durable construction"],
        "stage": "Established",
        "company": "Toad&Co"
    },
    {
        "name": "tentree Hemp Hoodie",
        "description": "Sustainable hoodie made with hemp and organic cotton blend. For every item purchased, tentree plants 10 trees.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["10 trees planted per purchase", "Naturally UV resistant", "Anti-microbial properties", "Carbon neutral shipping"],
        "stage": "Growing",
        "company": "tentree"
    },
    {
        "name": "Hempsmith Hemp Work Shirt",
        "description": "Durable work shirt crafted in North Carolina from hemp fabric. Hand screen-printed and designed to last for years.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["Made in USA", "Gets softer with wear", "Naturally pest resistant", "Biodegradable"],
        "stage": "Niche",
        "company": "Hempsmith Clothing"
    },
    {
        "name": "American Hemp LLC Industrial Hemp Yarn",
        "description": "100% American grown and processed hemp yarn for textile manufacturing. Suitable for knitting, weaving, and industrial applications.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["100% US grown", "Industrial strength", "Natural color", "Renewable resource"],
        "stage": "Pilot",
        "company": "American Hemp LLC"
    },
    
    # Hemp Hurds - Construction Products
    {
        "name": "Hempitecture HempWool Insulation",
        "description": "High-performance thermal and acoustic insulation made from hemp fibers. Safe to handle without protective equipment and naturally mold resistant.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["R-3.7 per inch", "No VOCs or formaldehyde", "Carbon negative material", "Class A fire rating"],
        "stage": "Growing",
        "company": "Hempitecture"
    },
    {
        "name": "HempTraders Hempcrete Blocks",
        "description": "Pre-cast hempcrete building blocks for sustainable construction. Made from hemp hurds mixed with lime-based binder.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["Carbon sequestering", "Thermal mass properties", "Pest and mold resistant", "Breathable walls"],
        "stage": "Growing",
        "company": "HempTraders"
    },
    {
        "name": "BioFiber Industries Hemp-Lime Plaster",
        "description": "Natural plaster system using hemp shiv and lime for interior wall finishes. Regulates humidity and improves indoor air quality.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["Humidity regulation", "Natural antimicrobial", "No synthetic additives", "Traditional building method"],
        "stage": "Niche",
        "company": "BioFiber Industries"
    },
    {
        "name": "Doylestown Hemp Company Hempcrete Spray System",
        "description": "Spray-applied hempcrete insulation system for retrofitting existing buildings. Provides continuous insulation without thermal bridges.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["Spray application", "No thermal bridging", "Moisture buffering", "Sound absorption"],
        "stage": "Pilot",
        "company": "Doylestown Hemp Company"
    },
    {
        "name": "HempWood Flooring Planks",
        "description": "Engineered wood flooring made from compressed hemp stalks. Harder than oak with the look of traditional hardwood.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["Harder than white oak", "Formaldehyde free", "Rapidly renewable", "Made in USA"],
        "stage": "Growing",
        "company": "HempWood"
    }
]

def get_plant_part_id(supabase, plant_part_name):
    """Get plant part ID from name"""
    result = supabase.table('plant_parts').select('id').eq('name', plant_part_name).execute()
    if result.data:
        return result.data[0]['id']
    return None

def get_industry_subcategory_id(supabase, industry_name):
    """Get industry subcategory ID"""
    # Map common industry names
    industry_mapping = {
        "Food and Beverage": "Food and Beverage",
        "Textiles": "Textiles",
        "Construction": "Construction", 
        "Medicine": "Medicine",
        "Cosmetics": "Cosmetics"
    }
    
    mapped_industry = industry_mapping.get(industry_name, industry_name)
    
    # First try to get the industry
    industry_result = supabase.table('industries').select('id').eq('name', mapped_industry).execute()
    if industry_result.data:
        industry_id = industry_result.data[0]['id']
        # Get first subcategory for this industry
        subcat_result = supabase.table('industry_sub_categories')\
            .select('id')\
            .eq('industry_id', industry_id)\
            .limit(1)\
            .execute()
        if subcat_result.data:
            return subcat_result.data[0]['id']
    return None

def add_scraped_products():
    """Add scraped products to database"""
    # Initialize Supabase
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials!")
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("🌿 Adding Real Hemp Products from Web Search")
    print("=" * 50)
    
    saved = 0
    skipped = 0
    errors = 0
    
    for product in SCRAPED_PRODUCTS:
        try:
            # Check if product exists
            existing = supabase.table('uses_products')\
                .select('id')\
                .eq('name', product['name'])\
                .execute()
            
            if existing.data:
                print(f"⏭️  Skipping existing: {product['name']}")
                skipped += 1
                continue
            
            # Get IDs
            plant_part_id = get_plant_part_id(supabase, product['plant_part'])
            industry_id = get_industry_subcategory_id(supabase, product['industry'])
            
            if not plant_part_id:
                print(f"❌ Unknown plant part: {product['plant_part']} for {product['name']}")
                errors += 1
                continue
            
            # Prepare data
            db_product = {
                'name': product['name'],
                'description': product['description'],
                'plant_part_id': plant_part_id,
                'industry_sub_category_id': industry_id,
                'benefits_advantages': product['benefits'],
                'commercialization_stage': product['stage'],
                'keywords': [
                    'hemp',
                    product['company'].lower().replace(' ', '-'),
                    product['plant_part'].lower(),
                    product['industry'].lower(),
                    'sustainable',
                    'eco-friendly',
                    'commercial'
                ],
                'sustainability_aspects': [
                    'Renewable agricultural resource',
                    'Low environmental impact',
                    'Carbon negative potential',
                    'Biodegradable material'
                ],
                'data_sources': {
                    'source': 'Web search via Brave API',
                    'company': product['company'],
                    'date_found': datetime.now().isoformat()
                }
            }
            
            # Insert
            result = supabase.table('uses_products').insert(db_product).execute()
            if result.data:
                print(f"✅ Added: {product['name']} ({product['company']})")
                saved += 1
                
        except Exception as e:
            print(f"❌ Error with {product['name']}: {str(e)}")
            errors += 1
    
    print(f"\n📊 Summary:")
    print(f"   - Products added: {saved}")
    print(f"   - Products skipped: {skipped}")
    print(f"   - Errors: {errors}")
    
    # Show current total
    total = supabase.table('uses_products').select('count', count='exact').execute()
    print(f"   - Total in database: {total.count}")
    
    # Show breakdown by plant part
    print(f"\n📈 Products by Plant Part:")
    plant_parts = supabase.table('plant_parts').select('id, name').execute()
    for part in plant_parts.data:
        count = supabase.table('uses_products')\
            .select('count', count='exact')\
            .eq('plant_part_id', part['id'])\
            .execute()
        if count.count > 0:
            print(f"   - {part['name']}: {count.count} products")

if __name__ == "__main__":
    add_scraped_products()