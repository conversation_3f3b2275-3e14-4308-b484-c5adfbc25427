#!/usr/bin/env python3
"""
Run the Company Hunter Agent to discover hemp companies
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lib.supabase_client import get_supabase_client
from agents.discovery.company_hunter_agent import create_company_hunter

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Run the company hunter agent"""
    print("\n🏭 Hemp Company Hunter Agent")
    print("="*50)
    
    # Get Supabase client
    supabase = get_supabase_client()
    
    # Create company hunter
    print("Creating company hunter agent...")
    hunter = create_company_hunter(supabase)
    
    # Search criteria (optional)
    search_criteria = {
        'location': 'USA',  # Optional: focus on specific country
        'product_type': 'textile'  # Optional: focus on specific product type
    }
    
    print(f"\nSearching for hemp companies...")
    print(f"Criteria: {search_criteria}")
    print("-"*50)
    
    try:
        # Discover companies
        async with hunter:
            companies = await hunter.discover_companies(
                search_criteria=search_criteria,
                max_results=20
            )
            
        print(f"\n✅ Found {len(companies)} companies!")
        
        # Display results
        for i, company in enumerate(companies, 1):
            print(f"\n{i}. {company.name}")
            print(f"   Type: {company.type}")
            if company.description:
                print(f"   Description: {company.description[:100]}...")
            if company.website:
                print(f"   Website: {company.website}")
            if company.location:
                print(f"   Location: {company.location}")
            if company.products:
                print(f"   Products: {len(company.products)} found")
                for product in company.products[:3]:
                    print(f"     - {product}")
                    
    except Exception as e:
        print(f"\n❌ Error: {e}")
        logger.error(f"Failed to discover companies: {e}", exc_info=True)
        
    # Check database
    print("\n📊 Checking database for companies...")
    result = supabase.table('hemp_companies').select('*').order(
        'created_at', desc=True
    ).limit(10).execute()
    
    print(f"Recent companies in DB: {len(result.data)}")
    for company in result.data[:5]:
        print(f"  - {company['name']} ({company.get('type', 'Unknown')}) - {company.get('location', 'Unknown location')}")
        
    # Check company-product relationships
    print("\n🔗 Checking company-product relationships...")
    rel_result = supabase.table('hemp_company_products').select(
        '*, hemp_companies(name), uses_products(name)'
    ).order('created_at', desc=True).limit(10).execute()
    
    if rel_result.data:
        print(f"Recent relationships: {len(rel_result.data)}")
        for rel in rel_result.data[:5]:
            company_name = rel.get('hemp_companies', {}).get('name', 'Unknown')
            product_name = rel.get('uses_products', {}).get('name', 'Unknown')
            print(f"  - {company_name} → {product_name}")
    else:
        print("No company-product relationships found yet")


if __name__ == "__main__":
    asyncio.run(main())