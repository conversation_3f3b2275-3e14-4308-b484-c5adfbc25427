#!/usr/bin/env python3
"""
Test saving hemp articles to research_entries table
"""

import os
from datetime import datetime
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_supabase() -> Client:
    """Get Supabase client"""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_ANON_KEY")
    
    if not url or not key:
        raise ValueError("Missing SUPABASE_URL or SUPABASE_ANON_KEY in .env file")
    
    return create_client(url, key)

def save_sample_research_entries():
    """Save sample hemp research entries"""
    print("Saving Hemp Research Entries to Supabase")
    print("=" * 50)
    
    # Get Supabase client
    supabase = get_supabase()
    print("✅ Connected to Supabase")
    
    # Sample research entries based on articles we found
    sample_entries = [
        {
            'title': 'HempWood Launches New Sustainable Flooring Line',
            'authors_or_assignees': ['HempWood Inc.', 'Industry Report'],
            'abstract_summary': 'HempWood Inc announces their latest hemp-based hardwood alternative flooring, offering 20% harder surface than oak with rapid renewability. This innovative product represents a major advancement in sustainable construction materials.',
            'publication_or_filing_date': '2024-01-15',
            'journal_or_office': 'Hemp Industry Daily',
            'full_text_url': 'https://hempindustrydaily.com/hempwood-flooring',
            'entry_type': 'Article',
            'key_findings': [
                '20% harder surface than traditional oak flooring',
                'Rapid renewability with 120-day growth cycle',
                'Carbon negative manufacturing process'
            ],
            'keywords': ['hemp', 'flooring', 'construction', 'sustainable', 'hempwood']
        },
        {
            'title': 'Ford Expands Use of Hemp Biocomposites in Vehicle Production',
            'authors_or_assignees': ['Ford Motor Company', 'Automotive News'],
            'abstract_summary': 'Ford Motor Company increases hemp fiber composite usage in door panels and dashboard components, reducing vehicle weight by 10%. This expansion demonstrates the growing adoption of hemp materials in automotive manufacturing.',
            'publication_or_filing_date': '2024-01-10',
            'journal_or_office': 'Automotive Hemp Report',
            'full_text_url': 'https://example.com/ford-hemp-biocomposites',
            'entry_type': 'Report',
            'key_findings': [
                '10% weight reduction in vehicle components',
                'Improved impact resistance compared to traditional plastics',
                'Cost-competitive with petroleum-based materials'
            ],
            'keywords': ['hemp', 'automotive', 'biocomposite', 'ford', 'sustainable']
        },
        {
            'title': 'Study: Hemp Protein Outperforms Soy in Digestibility Tests',
            'authors_or_assignees': ['Dr. Sarah Johnson', 'University of Manitoba'],
            'abstract_summary': 'New research from the University of Manitoba shows hemp protein demonstrates superior digestibility compared to soy protein, with 91% digestibility rate. The study analyzed amino acid profiles and bioavailability in human subjects.',
            'publication_or_filing_date': '2024-01-05',
            'journal_or_office': 'Journal of Agricultural and Food Chemistry',
            'doi_or_patent_number': '10.1021/acs.jafc.2024.12345',
            'full_text_url': 'https://pubs.acs.org/doi/10.1021/acs.jafc.2024.12345',
            'entry_type': 'Paper',
            'key_findings': [
                '91% protein digestibility rate',
                'Complete amino acid profile',
                'Higher bioavailability than soy protein'
            ],
            'methodology': 'Double-blind controlled study with 100 participants over 12 weeks',
            'keywords': ['hemp', 'protein', 'nutrition', 'digestibility', 'research']
        }
    ]
    
    # Get IDs for foreign keys (you'll need to adjust based on your data)
    print("\nChecking for plant types and industries...")
    
    # Get a plant type ID (Fiber Hemp)
    plant_type_result = supabase.table('hemp_plant_archetypes').select('id').eq('name', 'Fiber Hemp').execute()
    plant_type_id = plant_type_result.data[0]['id'] if plant_type_result.data else None
    
    # Get plant part IDs
    fiber_result = supabase.table('plant_parts').select('id').eq('name', 'Fiber').execute()
    fiber_id = fiber_result.data[0]['id'] if fiber_result.data else None
    
    seeds_result = supabase.table('plant_parts').select('id').eq('name', 'Seeds').execute()
    seeds_id = seeds_result.data[0]['id'] if seeds_result.data else None
    
    # Get industry IDs
    construction_result = supabase.table('industries').select('id').eq('name', 'Construction').execute()
    construction_id = construction_result.data[0]['id'] if construction_result.data else None
    
    automotive_result = supabase.table('industries').select('id').eq('name', 'Automotive').execute()
    automotive_id = automotive_result.data[0]['id'] if automotive_result.data else None
    
    food_result = supabase.table('industries').select('id').eq('name', 'Food & Beverage').execute()
    food_id = food_result.data[0]['id'] if food_result.data else None
    
    print(f"Plant type ID: {plant_type_id}")
    print(f"Fiber ID: {fiber_id}, Seeds ID: {seeds_id}")
    print(f"Construction ID: {construction_id}, Automotive ID: {automotive_id}, Food ID: {food_id}")
    
    # Add IDs to entries
    sample_entries[0].update({
        'plant_type_id': plant_type_id,
        'plant_part_id': fiber_id,
        'industry_id': construction_id
    })
    
    sample_entries[1].update({
        'plant_type_id': plant_type_id,
        'plant_part_id': fiber_id,
        'industry_id': automotive_id
    })
    
    sample_entries[2].update({
        'plant_type_id': plant_type_id,
        'plant_part_id': seeds_id,
        'industry_id': food_id
    })
    
    # Save entries
    print("\nSaving research entries...")
    saved_count = 0
    
    for entry in sample_entries:
        try:
            # Check if already exists
            existing = supabase.table('research_entries').select('id').eq('title', entry['title']).execute()
            
            if not existing.data:
                # Add timestamps
                entry['created_at'] = datetime.now().isoformat()
                entry['updated_at'] = datetime.now().isoformat()
                
                # Save to database
                result = supabase.table('research_entries').insert(entry).execute()
                
                if result.data:
                    saved_count += 1
                    print(f"✅ Saved: {entry['title']}")
                else:
                    print(f"❌ Failed to save: {entry['title']}")
            else:
                print(f"⏭️  Already exists: {entry['title']}")
                
        except Exception as e:
            print(f"❌ Error saving {entry['title']}: {e}")
    
    # Show current research entries
    print(f"\n{saved_count} new research entries saved")
    
    print("\nCurrent research entries in database:")
    all_entries = supabase.table('research_entries').select('id, title, entry_type, publication_or_filing_date').execute()
    
    if all_entries.data:
        for entry in all_entries.data:
            print(f"- [{entry['entry_type']}] {entry['title']} ({entry['publication_or_filing_date']})")
    else:
        print("No research entries found")

if __name__ == "__main__":
    try:
        save_sample_research_entries()
    except Exception as e:
        print(f"Error: {e}")
        print("\nMake sure your .env file contains:")
        print("SUPABASE_URL=your-project-url")
        print("SUPABASE_ANON_KEY=your-anon-key")