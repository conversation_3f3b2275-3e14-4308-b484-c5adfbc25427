import React from 'react';

interface AlphabetFilterProps {
  selectedLetter: string | null;
  onLetterSelect: (letter: string | null) => void;
  className?: string;
}

export function AlphabetFilter({ selectedLetter, onLetterSelect, className = '' }: AlphabetFilterProps) {
  // Generate alphabet array
  const alphabet = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));

  return (
    <div className={`bg-gray-900/30 backdrop-blur-md rounded-2xl p-4 border border-gray-700/30 ${className}`}>
      <div className="flex items-center gap-6">
        {/* Label */}
        <div className="flex items-center gap-2 flex-shrink-0">
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          <span className="text-gray-300 font-medium text-sm">A-Z</span>
        </div>

        {/* Centered Letter Pills Container */}
        <div className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-1 flex-wrap justify-center max-w-4xl">
            {/* All Button */}
            <button
              onClick={() => onLetterSelect(null)}
              className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
                selectedLetter === null
                  ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                  : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300'
              }`}
            >
              All
            </button>

            {/* Letter Pills */}
            {alphabet.map(letter => (
              <button
                key={letter}
                onClick={() => onLetterSelect(letter)}
                className={`w-7 h-7 rounded-full text-xs font-medium transition-all duration-200 flex items-center justify-center ${
                  selectedLetter === letter
                    ? 'bg-green-500 text-white shadow-lg shadow-green-500/25 scale-110'
                    : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/50 hover:text-gray-300 hover:scale-105'
                }`}
              >
                {letter}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}


