// Mobile optimization utilities

// Touch target size constants (following Apple and Google guidelines)
export const TOUCH_TARGET = {
  MIN_SIZE: 44, // Minimum touch target size in pixels
  RECOMMENDED_SIZE: 48, // Recommended touch target size
  SPACING: 8, // Minimum spacing between touch targets
} as const;

// Viewport utilities
export const getViewportInfo = () => {
  if (typeof window === 'undefined') return null;
  
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile: window.innerWidth < 768,
    isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
    isDesktop: window.innerWidth >= 1024,
    orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait',
    pixelRatio: window.devicePixelRatio || 1,
  };
};

// Touch gesture utilities
export interface TouchGestureHandlers {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onPinch?: (scale: number) => void;
  onTap?: () => void;
  onLongPress?: () => void;
}

export class TouchGestureManager {
  private element: HTMLElement;
  private handlers: TouchGestureHandlers;
  private touchStart: { x: number; y: number; time: number } | null = null;
  private touchEnd: { x: number; y: number; time: number } | null = null;
  private longPressTimer: NodeJS.Timeout | null = null;
  private initialDistance: number = 0;

  constructor(element: HTMLElement, handlers: TouchGestureHandlers) {
    this.element = element;
    this.handlers = handlers;
    this.bindEvents();
  }

  private bindEvents() {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
  }

  private handleTouchStart(e: TouchEvent) {
    const touch = e.touches[0];
    this.touchStart = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };

    // Handle multi-touch for pinch
    if (e.touches.length === 2) {
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      this.initialDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
    }

    // Start long press timer
    if (this.handlers.onLongPress) {
      this.longPressTimer = setTimeout(() => {
        this.handlers.onLongPress?.();
      }, 500);
    }
  }

  private handleTouchMove(e: TouchEvent) {
    // Cancel long press on move
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // Handle pinch gesture
    if (e.touches.length === 2 && this.handlers.onPinch) {
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      if (this.initialDistance > 0) {
        const scale = currentDistance / this.initialDistance;
        this.handlers.onPinch(scale);
      }
    }

    const touch = e.touches[0];
    this.touchEnd = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
  }

  private handleTouchEnd(e: TouchEvent) {
    // Clear long press timer
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    if (!this.touchStart || !this.touchEnd) return;

    const deltaX = this.touchEnd.x - this.touchStart.x;
    const deltaY = this.touchEnd.y - this.touchStart.y;
    const deltaTime = this.touchEnd.time - this.touchStart.time;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Tap detection
    if (distance < 10 && deltaTime < 300) {
      this.handlers.onTap?.();
      return;
    }

    // Swipe detection
    const minSwipeDistance = 50;
    const maxSwipeTime = 300;

    if (distance >= minSwipeDistance && deltaTime <= maxSwipeTime) {
      const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
      
      if (Math.abs(angle) <= 45) {
        // Right swipe
        this.handlers.onSwipeRight?.();
      } else if (Math.abs(angle) >= 135) {
        // Left swipe
        this.handlers.onSwipeLeft?.();
      } else if (angle > 45 && angle < 135) {
        // Down swipe
        this.handlers.onSwipeDown?.();
      } else if (angle < -45 && angle > -135) {
        // Up swipe
        this.handlers.onSwipeUp?.();
      }
    }

    this.touchStart = null;
    this.touchEnd = null;
  }

  public destroy() {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
    }
  }
}

// Mobile-specific CSS classes
export const mobileClasses = {
  // Touch targets
  touchTarget: 'min-h-[44px] min-w-[44px] touch-manipulation',
  touchTargetLarge: 'min-h-[48px] min-w-[48px] touch-manipulation',
  
  // Text sizing (prevents zoom on iOS)
  textInput: 'text-[16px] md:text-base',
  
  // Scrolling
  touchScroll: 'touch-pan-y overscroll-contain',
  
  // Gestures
  swipeable: 'touch-pan-x select-none',
  
  // Performance
  willChange: 'will-change-transform',
  gpuAccelerated: 'transform-gpu',
  
  // Safe areas
  safeAreaTop: 'pt-safe-top',
  safeAreaBottom: 'pb-safe-bottom',
  safeAreaLeft: 'pl-safe-left',
  safeAreaRight: 'pr-safe-right',
} as const;

// Performance optimization for mobile
export const optimizeForMobile = () => {
  if (typeof window === 'undefined') return;

  // Disable hover effects on touch devices
  if ('ontouchstart' in window) {
    document.documentElement.classList.add('touch-device');
  }

  // Optimize scrolling
  document.addEventListener('touchstart', () => {}, { passive: true });
  document.addEventListener('touchmove', () => {}, { passive: true });

  // Prevent zoom on double tap
  let lastTouchEnd = 0;
  document.addEventListener('touchend', (event) => {
    const now = Date.now();
    if (now - lastTouchEnd <= 300) {
      event.preventDefault();
    }
    lastTouchEnd = now;
  }, false);

  // Add viewport meta tag if not present
  if (!document.querySelector('meta[name="viewport"]')) {
    const viewport = document.createElement('meta');
    viewport.name = 'viewport';
    viewport.content = 'width=device-width, initial-scale=1.0, viewport-fit=cover';
    document.head.appendChild(viewport);
  }
};

// Accessibility helpers for mobile
export const mobileA11y = {
  // ARIA labels for touch interactions
  swipeHint: 'Swipe left or right to navigate',
  tapHint: 'Tap to select',
  longPressHint: 'Long press for options',
  
  // Screen reader announcements
  announceSwipe: (direction: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = `Swiped ${direction}`;
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  },
};

// Device detection utilities
export const deviceDetection = {
  isIOS: () => /iPad|iPhone|iPod/.test(navigator.userAgent),
  isAndroid: () => /Android/.test(navigator.userAgent),
  isMobile: () => /Mobi|Android/i.test(navigator.userAgent),
  isTablet: () => /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent),
  supportsTouch: () => 'ontouchstart' in window || navigator.maxTouchPoints > 0,
  supportsHover: () => window.matchMedia('(hover: hover)').matches,
  prefersDarkMode: () => window.matchMedia('(prefers-color-scheme: dark)').matches,
  prefersReducedMotion: () => window.matchMedia('(prefers-reduced-motion: reduce)').matches,
};

// Initialize mobile optimizations
if (typeof window !== 'undefined') {
  optimizeForMobile();
}
