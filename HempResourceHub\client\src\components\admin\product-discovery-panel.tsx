import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  Eye, 
  Save,
  Link,
  Sparkles
} from 'lucide-react';
import { ProductDiscoveryAssistant, type DiscoveredProduct } from '@/lib/product-discovery-assistant';
import { useToast } from '@/hooks/use-toast';

export function ProductDiscoveryPanel() {
  const [isSearching, setIsSearching] = useState(false);
  const [discoveredProducts, setDiscoveredProducts] = useState<DiscoveredProduct[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<Set<number>>(new Set());
  const [searchType, setSearchType] = useState<'criteria' | 'source' | 'enrich'>('criteria');
  const { toast } = useToast();

  // Search criteria state
  const [category, setCategory] = useState('');
  const [plantPart, setPlantPart] = useState('');
  const [industry, setIndustry] = useState('');
  const [keywords, setKeywords] = useState('');
  const [sourceUrl, setSourceUrl] = useState('');
  const [productName, setProductName] = useState('');

  const assistant = new ProductDiscoveryAssistant();

  const handleSearch = async () => {
    setIsSearching(true);
    setDiscoveredProducts([]);

    try {
      let products: DiscoveredProduct[] = [];

      if (searchType === 'criteria') {
        products = await assistant.discoverProducts({
          category,
          plantPart,
          industry,
          keywords: keywords.split(',').map(k => k.trim()).filter(Boolean),
          limit: 10
        });
      } else if (searchType === 'source') {
        products = await assistant.discoverFromSource(sourceUrl);
      } else if (searchType === 'enrich') {
        const enriched = await assistant.enrichProduct(productName);
        if (enriched) products = [enriched];
      }

      setDiscoveredProducts(products);
      
      if (products.length === 0) {
        toast({
          title: "No products found",
          description: "Try adjusting your search criteria",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Discovery complete!",
          description: `Found ${products.length} new products`,
        });
      }
    } catch (error) {
      toast({
        title: "Discovery failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleSaveSelected = async () => {
    const selectedProductsList = discoveredProducts.filter((_, index) => 
      selectedProducts.has(index)
    );

    let savedCount = 0;
    
    for (const product of selectedProductsList) {
      const result = await assistant.saveProduct(product);
      if (result) savedCount++;
    }

    toast({
      title: "Products saved!",
      description: `Successfully saved ${savedCount} out of ${selectedProductsList.length} products`,
    });

    // Clear selection and refresh
    setSelectedProducts(new Set());
    setDiscoveredProducts([]);
  };

  const toggleProductSelection = (index: number) => {
    const newSelection = new Set(selectedProducts);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedProducts(newSelection);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            AI Product Discovery
          </CardTitle>
          <CardDescription>
            Use Claude to discover new hemp products from various sources
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={searchType} onValueChange={(v) => setSearchType(v as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="criteria">Search by Criteria</TabsTrigger>
              <TabsTrigger value="source">Analyze Source</TabsTrigger>
              <TabsTrigger value="enrich">Enrich Product</TabsTrigger>
            </TabsList>

            <TabsContent value="criteria" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Category</Label>
                  <Input
                    placeholder="e.g., sustainable materials"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                  />
                </div>
                <div>
                  <Label>Plant Part</Label>
                  <Select value={plantPart} onValueChange={setPlantPart}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select plant part" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fiber">Hemp Fiber</SelectItem>
                      <SelectItem value="seeds">Hemp Seeds</SelectItem>
                      <SelectItem value="flowers">Hemp Flowers</SelectItem>
                      <SelectItem value="hurds">Hemp Hurds</SelectItem>
                      <SelectItem value="roots">Hemp Roots</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Industry</Label>
                  <Input
                    placeholder="e.g., construction, textiles"
                    value={industry}
                    onChange={(e) => setIndustry(e.target.value)}
                  />
                </div>
                <div>
                  <Label>Keywords (comma-separated)</Label>
                  <Input
                    placeholder="e.g., biodegradable, composite, insulation"
                    value={keywords}
                    onChange={(e) => setKeywords(e.target.value)}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="source" className="space-y-4">
              <div>
                <Label>Source URL</Label>
                <Input
                  placeholder="https://example.com/hemp-products-article"
                  value={sourceUrl}
                  onChange={(e) => setSourceUrl(e.target.value)}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Provide a URL to a webpage, article, or PDF about hemp products
                </p>
              </div>
            </TabsContent>

            <TabsContent value="enrich" className="space-y-4">
              <div>
                <Label>Product Name</Label>
                <Input
                  placeholder="e.g., HempWood Flooring"
                  value={productName}
                  onChange={(e) => setProductName(e.target.value)}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Enter a product name to research and enrich with detailed information
                </p>
              </div>
            </TabsContent>
          </Tabs>

          <Button 
            onClick={handleSearch} 
            disabled={isSearching}
            className="w-full mt-4"
          >
            {isSearching ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Discovering Products...
              </>
            ) : (
              <>
                <Search className="mr-2 h-4 w-4" />
                Start Discovery
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Results Section */}
      {discoveredProducts.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Discovered Products</CardTitle>
              <Button 
                onClick={handleSaveSelected}
                disabled={selectedProducts.size === 0}
              >
                <Save className="mr-2 h-4 w-4" />
                Save Selected ({selectedProducts.size})
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {discoveredProducts.map((product, index) => (
                <div 
                  key={index}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedProducts.has(index) ? 'border-green-500 bg-green-50' : ''
                  }`}
                  onClick={() => toggleProductSelection(index)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">{product.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{product.description}</p>
                      
                      <div className="flex flex-wrap gap-2 mt-3">
                        <Badge variant="outline">
                          {product.market_stage}
                        </Badge>
                        <Badge variant="outline">
                          Confidence: {Math.round(product.confidence_score * 100)}%
                        </Badge>
                        {product.sustainability_score && (
                          <Badge variant="outline" className="bg-green-50">
                            Sustainability: {product.sustainability_score}/100
                          </Badge>
                        )}
                      </div>

                      {product.companies && product.companies.length > 0 && (
                        <div className="mt-2">
                          <span className="text-sm font-medium">Companies: </span>
                          <span className="text-sm text-gray-600">
                            {product.companies.join(', ')}
                          </span>
                        </div>
                      )}

                      {product.source_url && (
                        <a 
                          href={product.source_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-1 text-sm text-blue-600 hover:underline mt-2"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Link className="h-3 w-3" />
                          Source
                        </a>
                      )}
                    </div>
                    
                    <div className="ml-4">
                      {selectedProducts.has(index) ? (
                        <CheckCircle className="h-6 w-6 text-green-500" />
                      ) : (
                        <div className="h-6 w-6 border-2 border-gray-300 rounded-full" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}