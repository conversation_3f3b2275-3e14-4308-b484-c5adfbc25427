import { ResearchPaper } from "@shared/schema";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AttributedImage } from "@/components/ui/attributed-image";
import {
  CalendarIcon,
  BookOpen,
  ArrowUpRight,
  FileText,
  ExternalLink,
  Quote,
  Users,
  Tag,
  TrendingUp,
  Globe
} from "lucide-react";
import { Link } from "wouter";
import { format } from "date-fns";

interface ResearchPaperCardProps {
  paper: ResearchPaper;
  plantTypeNames?: Record<number, string>;
  plantPartNames?: Record<number, string>;
  industryNames?: Record<number, string>;
}

const ResearchPaperCard = ({
  paper,
  plantTypeNames = {},
  plantPartNames = {},
  industryNames = {},
}: ResearchPaperCardProps) => {
  // Format the publication date
  const formattedDate = paper.publicationOrFilingDate
    ? format(new Date(paper.publicationOrFilingDate), "MMM d, yyyy")
    : "Date not available";

  // Get entry type styling
  const getEntryTypeStyle = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'paper':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/50';
      case 'article':
        return 'bg-green-500/20 text-green-400 border-green-500/50';
      case 'patent':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/50';
      case 'report':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/50';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/50';
    }
  };

  return (
    <Card className="group overflow-hidden bg-gray-900/40 backdrop-blur-sm border-gray-800/50 hover:border-green-500/30 hover:bg-gray-900/60 transition-all duration-300 hover:shadow-2xl hover:shadow-green-500/10 hover:-translate-y-1">
      {/* Article Image with Attribution */}
      {(paper.imageUrl || paper.image_url) && (
        <div className="relative h-48 overflow-hidden bg-gray-900">
          <AttributedImage
            src={paper.imageUrl || paper.image_url}
            alt={`Figure from: ${paper.title}`}
            attribution={
              paper.imageAttribution || paper.image_attribution || {
                source_name: "PubMed Central",
                source_url: paper.fullTextUrl || paper.full_text_url,
                license: "PMC Open Access",
                attribution_required: true,
                alt_text: `Research figure from: ${paper.title}`
              }
            }
            className="group-hover:scale-105 transition-transform duration-300"
            aspectRatio="16:9"
            fallbackSrc="/images/research-placeholder.png"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent" />
        </div>
      )}
      
      {/* Header with gradient background */}
      <div className="bg-gradient-to-r from-green-800/20 via-green-600/10 to-green-500/20 p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <Badge className={`${getEntryTypeStyle(paper.entryType)} backdrop-blur-sm border text-xs font-medium`}>
            {paper.entryType || 'Research'}
          </Badge>
          {paper.citations && (
            <div className="flex items-center gap-1 text-xs text-gray-400">
              <TrendingUp className="h-3 w-3" />
              <span>{paper.citations} citations</span>
            </div>
          )}
        </div>

        <CardTitle className="text-2xl font-heading font-bold hemp-brand-ultra line-clamp-2 mb-3">
          {paper.title}
        </CardTitle>

        <div className="flex items-center gap-2 text-sm text-gray-300">
          <Users className="h-4 w-4 text-green-400" />
          <span className="line-clamp-1">
            {paper.authorsOrAssignees?.join(", ") || "Unknown authors"}
          </span>
        </div>
      </div>
      <CardContent className="p-6 pt-4">
        {/* Abstract with quote styling */}
        <div className="mb-4">
          <div className="flex items-start gap-2 mb-2">
            <Quote className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-gray-300 leading-relaxed line-clamp-3">
              {paper.abstractSummary}
            </p>
          </div>
        </div>

        {/* Publication info */}
        <div className="flex items-center gap-4 mb-4 text-xs text-gray-400">
          {paper.journalOrOffice && (
            <div className="flex items-center gap-1">
              <BookOpen className="h-3 w-3" />
              <span className="line-clamp-1">{paper.journalOrOffice}</span>
            </div>
          )}
          {formattedDate !== "Date not available" && (
            <div className="flex items-center gap-1">
              <CalendarIcon className="h-3 w-3" />
              <span>{formattedDate}</span>
            </div>
          )}
        </div>

        {/* Keywords */}
        {paper.keywords && paper.keywords.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center gap-1 mb-2">
              <Tag className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-400">Keywords</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {paper.keywords.slice(0, 4).map((keyword, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs bg-green-900/20 text-green-300 border-green-700/30 hover:bg-green-900/30 transition-colors"
                >
                  {keyword}
                </Badge>
              ))}
              {paper.keywords.length > 4 && (
                <Badge
                  variant="secondary"
                  className="text-xs bg-gray-800/50 text-gray-400"
                >
                  +{paper.keywords.length - 4}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Context badges */}
        <div className="flex flex-wrap gap-2 mb-4">
          {paper.plantTypeId && plantTypeNames[paper.plantTypeId] && (
            <Badge variant="outline" className="text-xs bg-blue-900/20 text-blue-300 border-blue-700/30">
              🌱 {plantTypeNames[paper.plantTypeId]}
            </Badge>
          )}
          {paper.plantPartId && plantPartNames[paper.plantPartId] && (
            <Badge variant="outline" className="text-xs bg-green-900/20 text-green-300 border-green-700/30">
              🍃 {plantPartNames[paper.plantPartId]}
            </Badge>
          )}
          {paper.industryId && industryNames[paper.industryId] && (
            <Badge variant="outline" className="text-xs bg-purple-900/20 text-purple-300 border-purple-700/30">
              🏭 {industryNames[paper.industryId]}
            </Badge>
          )}
        </div>
      </CardContent>
      <CardFooter className="p-6 pt-4 border-t border-gray-800/50 bg-gray-900/20">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-3">
            <Link href={`/research/${paper.id}`}>
              <Button
                variant="outline"
                size="sm"
                className="text-green-400 border-green-700/50 hover:bg-green-900/30 hover:text-green-300 hover:border-green-500/50 transition-all"
              >
                <FileText className="h-4 w-4 mr-2" />
                Read More
              </Button>
            </Link>

            {/* Source attribution with proper credit */}
            {paper.fullTextUrl && (
              <a
                href={paper.fullTextUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-green-400 transition-colors"
                >
                  <Globe className="h-4 w-4 mr-2" />
                  <span className="text-xs">View Original Source</span>
                  <ExternalLink className="h-3 w-3 ml-1 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
                </Button>
              </a>
            )}
          </div>

          {/* DOI or Patent Number */}
          {paper.doiOrPatentNumber && (
            <div className="text-xs text-gray-500">
              <span className="font-mono bg-gray-800/50 px-2 py-1 rounded">
                {paper.doiOrPatentNumber}
              </span>
            </div>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default ResearchPaperCard;
