[tool.poetry]
name = "hemp-resource-hub"
version = "0.1.0"
description = "Industrial hemp resource management platform"
authors = ["HempQuarterz"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.9"
supabase = "^2.0.0"
openai = "^1.0.0"
anthropic = "^0.8.0"
python-dotenv = "^1.0.0"
requests = "^2.31.0"
pandas = "^2.0.0"
aiohttp = "^3.8.0"
tenacity = "^8.2.0"
langgraph = "^0.0.20"
aiosmtplib = "^3.0.0"
beautifulsoup4 = "^4.12.0"
lxml = "^4.9.0"
langchain = "^0.3.13"
langchain-openai = "^0.2.14"
langchain-anthropic = "^0.3.0"
langchain-community = "^0.3.13"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"
ipykernel = "^6.25.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["agents"]
omit = ["tests/*", "venv/*", ".venv/*"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
