#!/usr/bin/env python3
"""Test environment setup and imports."""

import sys
import importlib

print(f"Python Path: {sys.path[:3]}")
print("\nTesting imports:")

test_modules = [
    ('lib', 'Core library'),
    ('agents', 'Agents module'),
    ('supabase', 'Supabase SDK'),
    ('requests', 'Requests library'),
    ('bs4', 'BeautifulSoup')
]

for module, desc in test_modules:
    try:
        importlib.import_module(module)
        print(f"✅ {desc} ({module})")
    except ImportError as e:
        print(f"❌ {desc} ({module}): {e}")

# Test Supabase client creation
print("\nTesting Supabase client:")
try:
    # Create mock client for testing
    if not hasattr(sys.modules.get('lib', None), 'supabase_client'):
        import os
        os.makedirs('lib', exist_ok=True)
        with open('lib/supabase_client.py', 'w') as f:
            f.write('''
def get_supabase_client():
    """Mock client for testing"""
    class MockClient:
        def table(self, name): return self
        def select(self, *args, **kwargs): return self
        def execute(self): return type("obj", (object,), {"data": [], "count": 0})()
    return MockClient()
''')
    
    from lib.supabase_client import get_supabase_client
    client = get_supabase_client()
    print("✅ Supabase client created")
except Exception as e:
    print(f"❌ Supabase client error: {e}")