-- Add attribution columns for company logos
ALTER TABLE hemp_companies 
ADD COLUMN IF NOT EXISTS logo_attribution JSONB DEFAULT '{}';

-- Add attribution columns for research images
ALTER TABLE research_entries
ADD COLUMN IF NOT EXISTS image_attribution JSONB DEFAULT '{}';

-- Example attribution structure:
-- {
--   "source_url": "https://example.com/logo.png",
--   "source_name": "Company Website",
--   "license": "Fair Use",
--   "alt_text": "HempWood company logo",
--   "scraped_date": "2025-01-26",
--   "attribution_required": true,
--   "method": "og:image"
-- }

-- Add index for quick lookups of entries with images
CREATE INDEX IF NOT EXISTS idx_companies_with_logos ON hemp_companies(id) WHERE logo_url IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_research_with_images ON research_entries(id) WHERE image_url IS NOT NULL;