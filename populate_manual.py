#!/usr/bin/env python3
"""
Manual product population script - no AI required
Adds real hemp products to the database without needing API calls
"""

import os
import sys
from datetime import datetime
from supabase import create_client

# Load environment variables
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    with open(env_path) as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                try:
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
                except ValueError:
                    pass

# Sample hemp products data
HEMP_PRODUCTS = [
    # Hemp Seeds - Food
    {
        "name": "Manitoba Harvest Hemp Hearts",
        "description": "Raw shelled hemp seeds packed with plant-based protein and omega fatty acids. Perfect for smoothies, yogurt, and salads.",
        "plant_part": "Hemp Seed",
        "industry": "Food and Beverage",
        "benefits": ["10g protein per serving", "Rich in Omega-3 and Omega-6", "Gluten-free", "Non-GMO verified"],
        "stage": "Established"
    },
    {
        "name": "Nutiva Organic Hemp Oil",
        "description": "Cold-pressed hemp seed oil rich in essential fatty acids. Ideal for salad dressings and low-temperature cooking.",
        "plant_part": "Hemp Seed", 
        "industry": "Food and Beverage",
        "benefits": ["Heart-healthy omega fatty acids", "Supports immune system", "Vegan and organic", "No trans fats"],
        "stage": "Growing"
    },
    
    # Hemp Fiber - Textiles
    {
        "name": "Patagonia Hemp Canvas Pants",
        "description": "Durable outdoor pants made from hemp-cotton blend. Naturally resistant to mold and UV rays.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["3x stronger than cotton", "Naturally antimicrobial", "Breathable and moisture-wicking", "Biodegradable"],
        "stage": "Growing"
    },
    {
        "name": "WAMA Hemp Underwear",
        "description": "Comfortable, sustainable underwear made from hemp fabric. Soft, breathable, and long-lasting.",
        "plant_part": "Hemp Bast (Fiber)",
        "industry": "Textiles",
        "benefits": ["Anti-odor properties", "Temperature regulating", "Hypoallergenic", "Gets softer with each wash"],
        "stage": "Niche"
    },
    
    # Hemp Hurds - Construction
    {
        "name": "Hempcrete Building Blocks",
        "description": "Sustainable building material made from hemp hurds and lime. Provides excellent insulation and is carbon-negative.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction",
        "benefits": ["Carbon-negative material", "Excellent thermal insulation", "Fire resistant", "Pest resistant"],
        "stage": "Growing"
    },
    {
        "name": "Hemp Wood Flooring",
        "description": "Engineered wood flooring alternative made from compressed hemp fibers. Harder and more sustainable than oak.",
        "plant_part": "Hemp Hurd (Shivs)",
        "industry": "Construction", 
        "benefits": ["20% harder than oak", "Rapidly renewable resource", "Formaldehyde-free", "Moisture resistant"],
        "stage": "Pilot"
    },
    
    # Hemp Flowers - Cosmetics
    {
        "name": "Kiehl's Cannabis Sativa Seed Oil Concentrate",
        "description": "Facial oil with hemp-derived ingredients to calm and hydrate skin. Suitable for all skin types.",
        "plant_part": "Hemp Flowers",
        "industry": "Cosmetics",
        "benefits": ["Reduces redness", "Non-comedogenic", "Lightweight formula", "Paraben-free"],
        "stage": "Established"
    },
    {
        "name": "The Body Shop Hemp Hand Protector",
        "description": "Intensive hand cream with Community Fair Trade hemp seed oil from France. Provides 24-hour moisture.",
        "plant_part": "Hemp Seed",
        "industry": "Cosmetics",
        "benefits": ["24-hour hydration", "Fast-absorbing", "Vegan formula", "Community Fair Trade ingredient"],
        "stage": "Established"
    },
    
    # Hemp Leaves - Medicine/Wellness
    {
        "name": "Buddha Teas Organic Hemp Leaf Tea",
        "description": "Caffeine-free herbal tea made from organic hemp leaves. Promotes relaxation and overall wellness.",
        "plant_part": "Hemp Leaves",
        "industry": "Food and Beverage",
        "benefits": ["Caffeine-free", "USDA Organic", "Supports calm mood", "Rich in antioxidants"],
        "stage": "Niche"
    },
    {
        "name": "Hemp Leaf Extract Tincture",
        "description": "Alcohol-free tincture made from hemp leaves for general wellness support. Contains naturally occurring compounds.",
        "plant_part": "Hemp Leaves",
        "industry": "Medicine",
        "benefits": ["Full spectrum hemp extract", "Lab-tested for purity", "Non-psychoactive", "Supports overall wellness"],
        "stage": "Growing"
    }
]

def get_plant_part_id(supabase, plant_part_name):
    """Get plant part ID from name"""
    result = supabase.table('plant_parts').select('id').eq('name', plant_part_name).execute()
    if result.data:
        return result.data[0]['id']
    return None

def get_industry_subcategory_id(supabase, industry_name):
    """Get industry subcategory ID"""
    # First try to get the industry
    industry_result = supabase.table('industries').select('id').eq('name', industry_name).execute()
    if industry_result.data:
        industry_id = industry_result.data[0]['id']
        # Get first subcategory for this industry
        subcat_result = supabase.table('industry_sub_categories')\
            .select('id')\
            .eq('industry_id', industry_id)\
            .limit(1)\
            .execute()
        if subcat_result.data:
            return subcat_result.data[0]['id']
    return None

def populate_products():
    """Add products to database"""
    # Initialize Supabase
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials!")
        return
    
    supabase = create_client(supabase_url, supabase_key)
    
    print("🌿 Hemp Product Manual Population")
    print("=" * 50)
    
    saved = 0
    skipped = 0
    
    for product in HEMP_PRODUCTS:
        # Check if product exists
        existing = supabase.table('uses_products')\
            .select('id')\
            .eq('name', product['name'])\
            .execute()
        
        if existing.data:
            print(f"⏭️  Skipping existing product: {product['name']}")
            skipped += 1
            continue
        
        # Get IDs
        plant_part_id = get_plant_part_id(supabase, product['plant_part'])
        industry_id = get_industry_subcategory_id(supabase, product['industry'])
        
        if not plant_part_id:
            print(f"❌ Could not find plant part: {product['plant_part']}")
            continue
        
        # Prepare data
        db_product = {
            'name': product['name'],
            'description': product['description'],
            'plant_part_id': plant_part_id,
            'industry_sub_category_id': industry_id,
            'benefits_advantages': product['benefits'],
            'commercialization_stage': product['stage'],
            'keywords': [
                'hemp', 
                product['plant_part'].lower(), 
                product['industry'].lower(),
                'sustainable',
                'eco-friendly'
            ],
            'sustainability_aspects': [
                'Renewable resource',
                'Low environmental impact',
                'Biodegradable'
            ]
        }
        
        # Insert
        try:
            result = supabase.table('uses_products').insert(db_product).execute()
            if result.data:
                print(f"✅ Added: {product['name']}")
                saved += 1
        except Exception as e:
            print(f"❌ Error adding {product['name']}: {e}")
    
    print(f"\n📊 Summary:")
    print(f"   - Products added: {saved}")
    print(f"   - Products skipped: {skipped}")
    
    # Show current total
    total = supabase.table('uses_products').select('count', count='exact').execute()
    print(f"   - Total in database: {total.count}")

if __name__ == "__main__":
    populate_products()