import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ktoqznqmlnxrtvubewyz.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY environment variable is not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Custom placeholder image URL (will be served from your app)
const CUSTOM_PLACEHOLDER_URL = '/images/unknown-hemp-image.png';

async function updateToCustomPlaceholder() {
  try {
    console.log('Fetching products with placeholder.com images...');
    
    // Get all products that have placeholder.com URLs
    const { data: products, error: fetchError } = await supabase
      .from('uses_products')
      .select('id, name, image_url')
      .like('image_url', '%placeholder.com%');
    
    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return;
    }
    
    console.log(`Found ${products.length} products with placeholder.com images`);
    
    if (products.length === 0) {
      console.log('No products with placeholder.com images found!');
      return;
    }
    
    // Update each product with the custom placeholder image
    let updateCount = 0;
    let errorCount = 0;
    
    for (const product of products) {
      const { error: updateError } = await supabase
        .from('uses_products')
        .update({ image_url: CUSTOM_PLACEHOLDER_URL })
        .eq('id', product.id);
      
      if (updateError) {
        console.error(`Error updating product ${product.id} (${product.name}):`, updateError);
        errorCount++;
      } else {
        console.log(`✓ Updated product ${product.id}: ${product.name}`);
        updateCount++;
      }
    }
    
    console.log('\n=== Update Summary ===');
    console.log(`Total products found: ${products.length}`);
    console.log(`Successfully updated: ${updateCount}`);
    console.log(`Errors: ${errorCount}`);
    console.log(`\nAll placeholder.com images have been replaced with: ${CUSTOM_PLACEHOLDER_URL}`);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the update
updateToCustomPlaceholder();