# Hemp Agent Analysis Report
Generated: January 24, 2025

## Executive Summary
The codebase contains 39 agent-related files with significant overlap and redundancy. Multiple agents are re-queuing image generation for existing products, causing the overpopulation issue.

## Critical Issues

### 1. Research Agent with Images - Re-Queuing Bug
**File**: `agents/research/research_agent_with_images.py`
**Issue**: Re-queues image generation for ANY product with "placeholder" in the URL
**Impact**: Creates duplicate queue entries every time agent runs
**Lines**: 269-278

### 2. Multiple Runner Scripts
The following scripts all trigger agents that can queue images:
- `run_agent_with_images.py` - Runs research agent with automatic image queuing
- `run_simple_agent_with_images.py` - Another variant
- `run_enhanced_agent.py` - Runs enhanced research agent
- `run_agent_orchestrator.py` - Runs agents from task queue
- `run_agent_direct.py`
- `run_agent_example.py`
- `run_research_agent.py`
- `run_research_agent_simple.py`

### 3. Agent Types Found

#### Core Research Agents (5 variants):
1. `agents/research/research_agent.py` - Base research agent
2. `agents/research/enhanced_research_agent.py` - Enhanced version
3. `agents/research/research_agent_with_images.py` - Adds image generation
4. `agents/research/unified_research_agent.py` - Supposed to replace others
5. `agents/comprehensive_product_discovery_agent.py` - Another discovery agent

#### Other Agents:
- `agents/content/content_agent.py` - Content generation
- `agents/seo/seo_agent.py` - SEO optimization
- `agents/outreach/outreach_agent.py` - Partnership outreach
- `agents/monetization/monetization_agent.py` - Revenue opportunities
- `agents/compliance/compliance_agent.py` - Regulatory compliance
- `agents/discovery/company_hunter_agent.py` - Company discovery

#### Legacy/Test Files:
- `hemp_agent.py`
- `hemp_agent_enhanced.py`
- `hemp_agent_modifications.py`
- Multiple test files

## Database Impact Analysis

### Product Addition vs Queue Entries (Last 30 days):
| Date | Products Added | Queue Entries | Overpopulation Factor |
|------|----------------|---------------|----------------------|
| Jan 23 | 20 | 5 | 0.25x (Good!) |
| Jan 21 | 0 | 218 | ∞ (Re-queuing existing!) |
| Jan 20 | 0 | 106 | ∞ (Re-queuing existing!) |
| Jan 16 | 50 | 194 | 3.88x |
| Jan 15 | 0 | 200 | ∞ (Re-queuing existing!) |

## Recommendations

### Immediate Actions:
1. **DISABLE** `research_agent_with_images.py` - It's re-queuing existing products
2. **REMOVE** the placeholder check logic from all agents
3. **USE ONLY** the unified research agent going forward

### Agent Consolidation Plan:

#### Keep These Agents:
1. `agents/research/unified_research_agent.py` - Main research agent
2. `agents/content/content_agent.py` - Content generation
3. `agents/seo/seo_agent.py` - SEO tasks
4. `hemp_cli.py` - Unified CLI interface

#### Remove/Archive These:
1. All other research agent variants
2. All individual runner scripts (use hemp_cli.py instead)
3. Test and example files from production

### Fix the Image Generation Logic:
Images should ONLY be queued when:
1. A NEW product is created (not existing ones)
2. The product has NULL or empty image_url (not placeholder URLs)

### Proper Agent Usage:
```bash
# Use the unified CLI for everything
./hemp agent research "query" --max-results 10

# DO NOT use individual runner scripts like:
# python run_agent_with_images.py (DEPRECATED)
# python run_enhanced_agent.py (DEPRECATED)
```

## Hidden Scripts/Automation
- No cron jobs found in codebase
- GitHub Actions workflows are the main automation (already consolidated)
- Agent orchestrator (`run_agent_orchestrator.py`) polls database for tasks

## Conclusion
The overpopulation is caused by agents re-queuing image generation for existing products that already have placeholder images. Every time an agent runs and finds an existing product, it adds it to the queue again. With multiple agent variants running, this creates exponential growth in queue entries.