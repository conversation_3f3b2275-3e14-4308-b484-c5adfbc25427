// Client-side API wrapper for Claude agents
// This replaces the direct Claude SDK usage with server-side API calls

export interface ClaudeAgent {
  id: string;
  name: string;
  systemPrompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

export interface Message {
  role: 'user' | 'assistant';
  content: string;
}

export interface ConversationContext {
  agentId: string;
  conversationId: string;
  messages: Message[];
}

export class ClaudeAPI {
  private baseUrl: string;
  private apiKey?: string;
  private activeRequests: Set<string> = new Set();

  constructor(baseUrl: string = '/api/ai', apiKey?: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  private async fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...(options.headers || {}),
    };

    if (this.apiKey) {
      headers['x-api-key'] = this.apiKey;
    }

    return fetch(url, {
      ...options,
      headers,
    });
  }

  async getAgents(): Promise<ClaudeAgent[]> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/agents`);
    if (!response.ok) {
      throw new Error(`Failed to fetch agents: ${response.statusText}`);
    }
    return response.json();
  }

  async createConversation(agentId: string): Promise<string> {
    const requestKey = `create-conversation-${agentId}`;
    
    // Check if we're already creating a conversation for this agent
    if (this.activeRequests.has(requestKey)) {
      // Wait a bit and return a placeholder - the hook will retry if needed
      await new Promise(resolve => setTimeout(resolve, 500));
      throw new Error('Conversation creation already in progress');
    }

    this.activeRequests.add(requestKey);

    try {
      const response = await this.fetchWithAuth(`${this.baseUrl}/conversations`, {
        method: 'POST',
        body: JSON.stringify({ agentId }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create conversation: ${response.statusText} - ${errorText}`);
      }
      const data = await response.json();
      return data.conversationId;
    } finally {
      this.activeRequests.delete(requestKey);
    }
  }

  async sendMessage(
    conversationId: string,
    message: string,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    if (onChunk) {
      // Use SSE for streaming responses
      const response = await this.fetchWithAuth(
        `${this.baseUrl}/conversations/${conversationId}/messages`,
        {
          method: 'POST',
          body: JSON.stringify({ message, stream: true }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                break;
              }
              try {
                const parsed = JSON.parse(data);
                if (parsed.text) {
                  onChunk(parsed.text);
                  fullResponse += parsed.text;
                }
              } catch (e) {
                // Ignore parse errors
              }
            }
          }
        }
      }

      return fullResponse;
    } else {
      // Regular non-streaming response
      const response = await this.fetchWithAuth(
        `${this.baseUrl}/conversations/${conversationId}/messages`,
        {
          method: 'POST',
          body: JSON.stringify({ message, stream: false }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`);
      }

      const data = await response.json();
      return data.message;
    }
  }

  async getConversation(conversationId: string): Promise<ConversationContext> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/conversations/${conversationId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch conversation: ${response.statusText}`);
    }
    return response.json();
  }

  async deleteConversation(conversationId: string): Promise<void> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/conversations/${conversationId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete conversation: ${response.statusText}`);
    }
  }

  // High-level agent methods
  async discoverProducts(query: string): Promise<any> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/discover-products`, {
      method: 'POST',
      body: JSON.stringify({ query }),
    });
    if (!response.ok) {
      throw new Error(`Failed to discover products: ${response.statusText}`);
    }
    return response.json();
  }

  async generateCode(specification: string, codeType: string): Promise<string> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/generate-code`, {
      method: 'POST',
      body: JSON.stringify({ specification, codeType }),
    });
    if (!response.ok) {
      throw new Error(`Failed to generate code: ${response.statusText}`);
    }
    const data = await response.json();
    return data.code;
  }

  async analyzeData(query: string, context?: any): Promise<any> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/analyze-data`, {
      method: 'POST',
      body: JSON.stringify({ query, context }),
    });
    if (!response.ok) {
      throw new Error(`Failed to analyze data: ${response.statusText}`);
    }
    return response.json();
  }

  async generateContent(
    topic: string,
    contentType: string,
    keywords?: string[]
  ): Promise<string> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/generate-content`, {
      method: 'POST',
      body: JSON.stringify({ topic, contentType, keywords }),
    });
    if (!response.ok) {
      throw new Error(`Failed to generate content: ${response.statusText}`);
    }
    const data = await response.json();
    return data.content;
  }

  async runMultiAgentTask(
    tasks: Array<{ agentId: string; prompt: string; taskId?: string }>
  ): Promise<Record<string, any>> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/multi-agent-task`, {
      method: 'POST',
      body: JSON.stringify({ tasks }),
    });
    if (!response.ok) {
      throw new Error(`Failed to run multi-agent task: ${response.statusText}`);
    }
    const data = await response.json();
    return data.results;
  }
}

// Singleton instance
let claudeAPIInstance: ClaudeAPI | null = null;

export function getClaudeAPI(): ClaudeAPI {
  if (!claudeAPIInstance) {
    claudeAPIInstance = new ClaudeAPI();
  }
  return claudeAPIInstance;
}