#!/usr/bin/env python3
"""
Simple DeepSeek command for hemp research
Usage: python hemp_deepseek.py "your query"
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from utils.ai_providers import DeepSeekProvider


async def main():
    if len(sys.argv) < 2:
        print("Usage: python hemp_deepseek.py 'your query'")
        return
        
    query = ' '.join(sys.argv[1:])
    
    # Create DeepSeek provider
    deepseek = DeepSeekProvider()
    
    prompt = f"""You are a hemp industry expert. {query}
    
Please provide a concise, informative response focused on commercial and industrial hemp applications."""
    
    try:
        print("🤔 Thinking...")
        response = await deepseek.generate(prompt)
        print("\n" + response)
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())