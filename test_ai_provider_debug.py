#!/usr/bin/env python3
"""Debug AI provider to see what's being called"""

import asyncio
import logging
from dotenv import load_dotenv

load_dotenv()

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

from utils.ai_providers import MultiProviderAI, DeepSeekProvider
from utils.simple_ai_wrapper import SimpleAIWrapper


class DebugWrapper:
    """Wrapper that logs all calls"""
    def __init__(self, provider):
        self.provider = provider
        
    async def generate(self, prompt: str, **kwargs):
        logger.info(f"=== GENERATE CALLED ===")
        logger.info(f"Provider type: {type(self.provider)}")
        logger.info(f"Prompt preview: {prompt[:100]}...")
        logger.info(f"Kwargs: {kwargs}")
        
        try:
            result = await self.provider.generate(prompt, **kwargs)
            logger.info(f"Success! Result type: {type(result)}")
            return result
        except Exception as e:
            logger.error(f"Failed: {e}")
            raise


async def test_provider_chain():
    """Test the exact chain of providers"""
    
    # Create DeepSeek provider
    logger.info("Creating DeepSeek provider...")
    deepseek = DeepSeekProvider()
    
    # Test direct call
    logger.info("\n1. Testing DeepSeek directly:")
    try:
        result = await deepseek.generate("Say hello", response_format="json")
        logger.info(f"DeepSeek with response_format works!")
    except Exception as e:
        logger.error(f"DeepSeek with response_format failed: {e}")
    
    # Create MultiProviderAI
    logger.info("\n2. Testing MultiProviderAI:")
    multi = MultiProviderAI(primary_provider="deepseek", fallback_providers=[])
    
    # Create SimpleAIWrapper
    logger.info("\n3. Testing SimpleAIWrapper:")
    simple = SimpleAIWrapper(multi)
    
    # Create DebugWrapper
    logger.info("\n4. Testing with DebugWrapper:")
    debug = DebugWrapper(simple)
    
    # Test the chain
    result = await debug.generate("List 2 hemp products")
    logger.info(f"Final result preview: {str(result)[:100]}...")


if __name__ == "__main__":
    asyncio.run(test_provider_chain())