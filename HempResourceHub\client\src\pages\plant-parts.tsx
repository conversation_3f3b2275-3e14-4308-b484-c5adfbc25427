import { <PERSON> } from "wouter";
import { Helmet } from "react-helmet";
import { useState, useMemo } from "react";
import { useAllPlantParts } from "@/hooks/use-plant-data";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Search, X } from "lucide-react";
import Breadcrumb from "@/components/ui/breadcrumb";


const PlantPartsPage = () => {
  const { data: plantParts, isLoading } = useAllPlantParts();
  const [searchTerm, setSearchTerm] = useState("");

  // Filter plant parts based on search only
  const filteredPlantParts = useMemo(() => {
    if (!plantParts) return [];

    let filtered = plantParts.filter(plantPart => {
      const matchesSearch = plantPart.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           plantPart.description?.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });

    // Sort alphabetically
    filtered.sort((a, b) => a.name.localeCompare(b.name));
    return filtered;
  }, [plantParts, searchTerm]);

  const handleFilterChange = (filterType: string, value: any) => {
    if (filterType === 'search') setSearchTerm(value);
  };

  return (
    <>
      <Helmet>
        <title>Hemp Plant Parts | HempQuarterz</title>
        <meta
          name="description"
          content="Explore different parts of industrial hemp plants and their applications including stalks, seeds, leaves, and flowers."
        />
      </Helmet>

      {/* Breadcrumb */}
      <div className="bg-black/20 backdrop-blur-sm py-6 border-b border-gray-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Breadcrumb
            items={[{ label: "Home", href: "/" }, { label: "Parts of Plant" }]}
          />
        </div>
      </div>

      {/* Hero section */}
      <div className="bg-gradient-to-b from-black/20 to-black/30 backdrop-blur-sm py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-green-400">
            Hemp Plant Parts
          </h1>
          <p className="mt-4 text-lg text-gray-100 max-w-3xl mx-auto">
            Explore the various parts of the hemp plant and their industrial
            applications, from stalks used in textiles to seeds used in food
            products.
          </p>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="bg-black/20 backdrop-blur-sm py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Search Bar */}
          <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 mb-6 border border-green-500/30">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search plant parts by name or description..."
                value={searchTerm}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-lg text-gray-100 placeholder-gray-400 focus:outline-none focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20 text-base"
              />
              {searchTerm && (
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                >
                  <X className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>


        </div>
      </div>

      {/* Plant parts grid */}
      <div className="bg-black/20 backdrop-blur-sm py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl sm:text-3xl font-heading font-semibold text-green-400">
              Plant Parts
            </h2>
            <div className="text-gray-400 text-sm">
              Showing {filteredPlantParts.length} of {plantParts?.length || 0} parts
            </div>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <div className="aspect-[4/5] relative">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-5/6" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredPlantParts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredPlantParts.map((plantPart: any) => (
                  <Link key={plantPart.id} href={`/plant-part/${plantPart.id}`}>
                    <div className="group cursor-pointer">
                      {/* Square image container */}
                      <div className="aspect-square relative overflow-hidden rounded-xl bg-gray-800 mb-3">
                        <img
                          src={plantPart.image_url || '/images/unknown-hemp-image.png'}
                          alt={plantPart.name}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                          onError={(e) => {
                            e.currentTarget.src = '/images/unknown-hemp-image.png';
                          }}
                        />
                        {/* Subtle gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                      
                      {/* Plant part name and description */}
                      <h3 className="text-lg font-semibold hemp-brand-ultra mb-1">
                        {plantPart.name}
                      </h3>
                      <p className="text-sm text-gray-400 line-clamp-2">
                        {plantPart.description}
                      </p>
                    </div>
                  </Link>
                ))}
            </div>
          ) : (
            <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-8 text-center border border-green-500/30">
              <h3 className="text-xl font-heading font-semibold mb-2 text-white">
                No Plant Parts Found
              </h3>
              <p className="text-gray-400 mb-4">
                No plant parts match your search. Try adjusting your search terms.
              </p>
              <div className="flex justify-center">
                {searchTerm && (
                  <button
                    onClick={() => handleFilterChange('search', '')}
                    className="text-green-400 hover:text-green-300 font-medium transition-colors"
                  >
                    Clear Search
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Plant parts comparison */}
      <div className="bg-black/30 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-heading font-semibold text-green-400 mb-12 text-center">
            Hemp Plant Parts Comparison
          </h2>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-700">
              <thead className="bg-gray-800/40 backdrop-blur-sm">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider"
                  >
                    Plant Part
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-sm font-semibold text-neutral-darkest uppercase tracking-wider"
                  >
                    Primary Uses
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-sm font-semibold text-neutral-darkest uppercase tracking-wider"
                  >
                    Characteristics
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-sm font-semibold text-neutral-darkest uppercase tracking-wider"
                  >
                    Processing Methods
                  </th>
                </tr>
              </thead>
              <tbody className="bg-gray-800/30 backdrop-blur-sm divide-y divide-gray-700/50">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-200">
                      Stalks/Stems
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Textiles, Paper, Building Materials
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Fibrous, durable, high tensile strength
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Retting, decortication, pulping
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-200">
                      Seeds
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Food, Nutritional Supplements, Oil
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      High protein, rich in omega fatty acids
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Hulling, cold pressing, roasting
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-200">
                      Leaves
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Tea, Animal Feed, Compost
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Rich in chlorophyll and nutrients
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Drying, grinding, extraction
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-200">
                      Flowers
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Wellness Products, Extracts
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Contains cannabinoids and terpenes
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-400">
                      Curing, extraction, distillation
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="bg-gradient-to-b from-black/30 to-black/40 backdrop-blur-sm py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-heading font-semibold hemp-brand-ultra mb-4">
            Explore Hemp Industry Applications
          </h2>
          <p className="text-lg text-gray-100 max-w-3xl mx-auto mb-8">
            Discover how different parts of the hemp plant are utilized across
            various industries for sustainable solutions.
          </p>
          <Link href="/industries">
            <Button className="bg-green-600 hover:bg-green-700 text-white">
              Browse Industries
            </Button>
          </Link>
        </div>
      </div>
    </>
  );
};

export default PlantPartsPage;
