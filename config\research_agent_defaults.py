"""
Default configuration for the Unified Research Agent
Defines which features to enable based on AI availability
"""

from agents.research.unified_research_agent import ResearchFeatures

# Default features that work without AI
DEFAULT_FEATURES_NO_AI = [
    ResearchFeatures.WEB_SCRAPING,
    ResearchFeatures.FEED_MONITORING,
    ResearchFeatures.COMPANY_EXTRACTION
]

# Full features when AI is available
DEFAULT_FEATURES_WITH_AI = [
    ResearchFeatures.BASIC,
    ResearchFeatures.COMPANY_EXTRACTION,
    ResearchFeatures.IMAGE_GENERATION,
    ResearchFeatures.DEEP_ANALYSIS,
    ResearchFeatures.WEB_SCRAPING,
    ResearchFeatures.FEED_MONITORING,
    ResearchFeatures.TREND_ANALYSIS
]

# Feature descriptions
FEATURE_DESCRIPTIONS = {
    ResearchFeatures.BASIC: "Basic product discovery using AI",
    ResearchFeatures.COMPANY_EXTRACTION: "Extract and save company information",
    ResearchFeatures.IMAGE_GENERATION: "Auto-generate product images",
    ResearchFeatures.DEEP_ANALYSIS: "Extended AI analysis of products",
    ResearchFeatures.WEB_SCRAPING: "Advanced web scraping from hemp sites",
    ResearchFeatures.FEED_MONITORING: "RSS/news feed monitoring",
    ResearchFeatures.TREND_ANALYSIS: "Market trend analysis"
}

# Hemp industry sources for web scraping
HEMP_SOURCES = {
    'organizations': [
        {
            'name': 'EIHA',
            'url': 'https://eiha.org',
            'type': 'organization',
            'feeds': ['https://eiha.org/feed/'],
            'scrape_patterns': {
                'members': '/members/',
                'products': '/products/',
                'news': '/news/'
            }
        },
        {
            'name': 'Vote Hemp',
            'url': 'https://votehemp.com',
            'type': 'advocacy',
            'feeds': ['https://votehemp.com/feed/'],
            'scrape_patterns': {
                'resources': '/resources/',
                'members': '/members/'
            }
        },
        {
            'name': 'Hemp Industries Association',
            'url': 'https://thehia.org',
            'type': 'association',
            'feeds': [],
            'scrape_patterns': {
                'directory': '/directory/',
                'products': '/hemp-products/'
            }
        }
    ],
    'news_sites': [
        {
            'name': 'Hemp Industry Daily',
            'url': 'https://hempindustrydaily.com',
            'type': 'news',
            'feeds': ['https://hempindustrydaily.com/feed/'],
            'scrape_patterns': {
                'companies': '/company-profiles/',
                'products': '/hemp-products/'
            }
        },
        {
            'name': 'Hemp Business Journal',
            'url': 'https://hempbizjournal.com',
            'type': 'business',
            'feeds': [],
            'scrape_patterns': {
                'market': '/market-data/',
                'companies': '/company-directory/'
            }
        }
    ],
    'marketplaces': [
        {
            'name': 'Hemp Marketplace',
            'url': 'https://www.hempmarketplace.com',
            'type': 'marketplace',
            'scrape_patterns': {
                'products': '/products/',
                'suppliers': '/suppliers/'
            }
        },
        {
            'name': 'Leafly Hemp',
            'url': 'https://www.leafly.com/news/hemp',
            'type': 'marketplace',
            'scrape_patterns': {
                'brands': '/brands/',
                'products': '/products/'
            }
        }
    ],
    'trade_shows': [
        {
            'name': 'NoCo Hemp Expo',
            'url': 'https://nocohempexpo.com',
            'type': 'trade_show',
            'scrape_patterns': {
                'exhibitors': '/exhibitors/',
                'sponsors': '/sponsors/'
            }
        },
        {
            'name': 'Hemp & CBD Expo',
            'url': 'https://hempandcbdexpo.com',
            'type': 'trade_show',
            'scrape_patterns': {
                'vendors': '/vendors/',
                'products': '/product-showcase/'
            }
        }
    ]
}

# Product discovery keywords
PRODUCT_KEYWORDS = [
    # Textiles
    "hemp fabric", "hemp textile", "hemp clothing", "hemp apparel",
    "hemp fiber", "hemp yarn", "hemp denim", "hemp canvas",
    
    # Construction
    "hempcrete", "hemp insulation", "hemp board", "hemp plywood",
    "hemp blocks", "hemp construction", "hemp building materials",
    
    # Food & Nutrition
    "hemp protein", "hemp oil", "hemp seeds", "hemp hearts",
    "hemp milk", "hemp flour", "hemp powder", "hemp supplements",
    
    # Personal Care
    "hemp lotion", "hemp soap", "hemp shampoo", "hemp cosmetics",
    "hemp skincare", "hemp balm", "hemp cream",
    
    # Industrial
    "hemp plastic", "hemp bioplastic", "hemp composite", "hemp resin",
    "hemp paper", "hemp packaging", "hemp biofuel",
    
    # Agriculture
    "hemp farming", "hemp cultivation", "hemp processing",
    "hemp harvesting", "hemp equipment"
]

# Company type classifications
COMPANY_TYPES = [
    "manufacturer",
    "distributor", 
    "retailer",
    "brand",
    "supplier",
    "processor",
    "farm",
    "research",
    "technology"
]