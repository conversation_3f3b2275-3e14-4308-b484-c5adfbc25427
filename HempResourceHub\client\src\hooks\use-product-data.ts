import { useQuery } from "@tanstack/react-query";
import { getHempProductsByPart, getHempProduct, searchHempProducts, getAllHempProducts } from "@/lib/supabase-api";

export function useHempProducts(plantPartId: number | null, industryId?: number | null, page: number = 1, limit: number = 5) {
  return useQuery({
    queryKey: ['/api/hemp-products', plantPartId, industryId, page, limit],
    enabled: !!plantPartId,
    queryFn: async () => {
      if (!plantPartId) return { products: [], pagination: { total: 0, pages: 0, page: 1, limit } };
      
      try {
        const result = await getHempProductsByPart(plantPartId, industryId || undefined, page, limit);
        return {
          products: result.data,
          pagination: {
            total: result.total,
            pages: Math.ceil(result.total / limit),
            page,
            limit
          }
        };
      } catch (error) {
        console.error('Error fetching hemp products:', error);
        return { products: [], pagination: { total: 0, pages: 0, page: 1, limit } };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useAllHempProducts() {
  return useQuery({
    queryKey: ['/api/hemp-products/all'],
    queryFn: async () => {
      try {
        const products = await getAllHempProducts();
        return products;
      } catch (error) {
        console.error('Error fetching all hemp products:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useHempProduct(id: number | null) {
  return useQuery({
    queryKey: ['/api/hemp-products/detail', id],
    enabled: !!id,
    queryFn: async () => {
      if (!id) return null;
      
      try {
        const product = await getHempProduct(id);
        return product;
      } catch (error) {
        console.error('Error fetching hemp product:', error);
        return null;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useHempSearch(query: string) {
  return useQuery({
    queryKey: ['/api/hemp-products/search', query],
    enabled: query.length > 2,
    queryFn: async () => {
      if (query.length <= 2) return [];
      
      try {
        const products = await searchHempProducts(query);
        return products;
      } catch (error) {
        console.error('Error searching hemp products:', error);
        return [];
      }
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}
