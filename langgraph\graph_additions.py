"""Additional graph classes"""

from .graph import Graph

class StateGraph(Graph):
    """State-based graph implementation for stateful workflows"""
    
    def __init__(self, state_type=None):
        super().__init__()
        self.state_type = state_type or dict
        
    def add_node(self, name: str, func: callable) -> 'StateGraph':
        """Add a node to the state graph"""
        super().add_node(name, func)
        return self
        
    def add_edge(self, from_node: str, to_node: str) -> 'StateGraph':
        """Add an edge between nodes in state graph"""
        super().add_edge(from_node, to_node)
        return self